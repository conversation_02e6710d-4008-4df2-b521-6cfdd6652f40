<?php

use AwardForce\Http\Middleware\Api\Apiv1;

$api = app(\Dingo\Api\Routing\Router::class);

$api->version('v1.0', [
    'namespace' => 'AwardForce\Http\Controllers\Api',
    'middleware' => Apiv1::class,
], function (Dingo\Api\Routing\Router $api) {
    $api->any('{any}', 'V1Controller@any')->where('any', '(.*)');
});

$api->version('v2.0', [
    'namespace' => 'AwardForce\Http\Controllers\Api\V2',
], function (Dingo\Api\Routing\Router $api) {
    require base_path('routes/api/v2.php');
});

$api->version('v2.1', [
    'namespace' => 'AwardForce\Http\Controllers\Api\V2',
], function (Dingo\Api\Routing\Router $api) {
    require base_path('routes/api/v2_1.php');
});

$api->version('v2.2', [
    'namespace' => 'AwardForce\Http\Controllers\Api\V2',
], function (Dingo\Api\Routing\Router $api) {
    require base_path('routes/api/v2_2.php');
});

$api->version('v2.3', [
    'namespace' => 'AwardForce\Http\Controllers\Api\V2',
], function (Dingo\Api\Routing\Router $api) {
    require base_path('routes/api/v2_3.php');
});
