<?php

namespace AwardForce\Http\Requests\User;

use AwardForce\Http\Requests\SanitizesMobile;
use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Validation\ValidatesEmailMobile;
use AwardForce\Library\Validation\ValidName;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\Http\Validation\ValidatesFields;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Settings\Services\RegistrationSettings;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Http\FormRequest;
use Platform\Validations\Password;

class RegisterUserRequest extends FormRequest
{
    use SanitizesMobile;
    use ValidatesEmailMobile;
    use ValidatesFields {
        getFields as getValidationFields;
    }

    /**
     * @return array
     */
    public function all($keys = null)
    {
        return $this->sanitizeMobile('registerMobile', parent::all());
    }

    public function rules()
    {
        $roles = app(RoleRepository::class)->getRegistrationSlugs();

        $baseRules = [
            'firstName' => ['required', new ValidName],
            'lastName' => ['required', new ValidName],
            'password' => ['required', new Password],
            'role' => 'in:'.implode(',', array_filter($roles)),
            'acceptAgreementToTerms' => setting('require-agreement-to-terms') ? 'required' : '',
        ];

        return array_merge(
            $baseRules,
            $this->emailMobileRules('registerEmail', 'registerMobile'),
            $this->fieldRules()
        );
    }

    public function authorize()
    {
        return true;
    }

    public function messages()
    {
        return array_merge(
            [
                'unique' => trans('validation.registration_existing_account'),
                'required' => trans('validation.required'),
            ],
            $this->fieldMessages()
        );
    }

    /**
     * This is the string representation of the resource by which queries and checks will be made.
     *
     * @return string|array
     */
    protected function resource()
    {
        return Field::RESOURCE_USERS;
    }

    /**
     * Returns a modified version of the user's registration fields.
     *
     * @param  string  $resource
     * @return mixed
     */
    protected function getFields($resource)
    {
        $role = $this->request->get('role') ?
            app(RoleRepository::class)->getBySlug($this->input('role')) :
            app(RoleRepository::class)->getDefault();

        if (! $role) {
            return new Collection;
        }

        $seasonId = app(SeasonRepository::class)->getActiveId();

        return app(FieldRepository::class)
            ->getAllForRole($role->id, $seasonId)
            ->where('writeAccess', 1);
    }

    /**
     * Get the validator instance for the request and
     * add attach callbacks to be run after validation
     * is completed.
     *
     * @return \Illuminate\Contracts\Validation\Validator
     */
    protected function getValidatorInstance()
    {
        $currentUser = Consumer::user();

        $this->merge([
            'registerMobile' => $this->getRegisterField('registerMobile', $currentUser?->mobile),
            'registerEmail' => $this->getRegisterField('registerEmail', $currentUser?->email),
        ]);

        return parent::getValidatorInstance()->after(
            function ($validator) {
                if ($validator->failed()['registerEmail']['Unique'] ?? false) {
                    $validator->getMessageBag()->add('exists', trans('fields.types.email'));
                }
                if ($validator->failed()['registerMobile']['Unique'] ?? false) {
                    $validator->getMessageBag()->add('exists', trans('fields.types.phone'));
                }
            }
        );
    }

    protected function getRegisterField(string $key, ?string $userValue): ?string
    {
        return app(RegistrationSettings::class)->mobileRegistrationsEnabled()
            ? ($userValue ?? $this->request->get($key))
            : $userValue;
    }
}
