<?php

namespace AwardForce\Http\Requests\Notifications;

trait RecipientValidation
{
    public function addRecipientValidation(array $rules)
    {
        // If recipient option is 'field' make the fieldId field required
        if ($this->input('recipientOption') == 'field') {
            $rules['field'] = 'required';
        }
        // If recipient option is 'recipients' make the recipients field required
        if ($this->input('recipientOption') == 'recipients') {
            $rules['recipients'] = ['required', $this->recipientsValidationRule()];
        }

        return $rules;
    }

    private function recipientsValidationRule(): string
    {
        return feature_enabled('mobile_registration_sms') ? 'multiple_recipients' : 'multiple_emails';
    }
}
