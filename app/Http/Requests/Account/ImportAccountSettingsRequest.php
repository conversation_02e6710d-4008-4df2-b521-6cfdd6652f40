<?php

namespace AwardForce\Http\Requests\Account;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Accounts\Validators\ImportDependencies;

class ImportAccountSettingsRequest extends FormRequest
{
    public function rules()
    {
        return [
            'data.*' => ['bail', new ImportDependencies($this->get('data', []))],
        ];
    }

    public function authorize()
    {
        return true;
    }
}
