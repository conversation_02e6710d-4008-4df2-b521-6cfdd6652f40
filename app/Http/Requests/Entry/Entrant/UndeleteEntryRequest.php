<?php

namespace AwardForce\Http\Requests\Entry\Entrant;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Http\FormRequest;
use AwardForce\Modules\Categories\Validators\CategoryMaxEntriesLimit;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Http\Validation\FormMaxEntriesLimit;
use AwardForce\Modules\Forms\Forms\Services\Settings;
use Platform\Database\Eloquent\Enums\TrashedMode;

class UndeleteEntryRequest extends FormRequest
{
    public function __construct(protected EntryRepository $entries)
    {
        parent::__construct();
    }

    public function rules()
    {
        $rules = [
            'selected' => ['array'],
        ];

        if ($selected = $this->input('selected')) {
            $this->checkCategoryCount($selected, $rules);
            $this->checkFormCount($selected, $rules);
        }

        return $rules;
    }

    protected function checkFormCount(array $selected, array &$rules)
    {
        Settings::for(Form::FORM_TYPE_ENTRY)
            ->settingForForms('maxApplications', $this->formIds($selected))
            ->each(function (Form $form) use (&$rules) {
                $rules['selected'][] = new FormMaxEntriesLimit($form->id, $form->settings->maxApplications ?? 0);
            });
    }

    protected function checkCategoryCount(array $selected, array &$rules)
    {
        // Get the trashed entries for the current user
        $userEntries = $this->entries
            ->user(Consumer::id())
            ->trashed(TrashedMode::Only)
            ->pluck('category_id', 'id');

        collect($selected)
            ->map(fn($entryId) => $userEntries[$entryId] ?? null)
            ->filter()
            ->countBy()
            ->each(function ($count, $categoryId) use (&$rules) {
                $rules['selected'][] = new CategoryMaxEntriesLimit($categoryId, $count);
            });

        $allowedEntries = $userEntries->keys()->implode(',');

        foreach (array_keys($selected) as $index) {
            $rules['selected.'.$index] = ['required', 'in:'.$allowedEntries];
        }
    }

    public function attributes()
    {
        $attr = [];

        if ($selected = $this->get('selected')) {
            foreach (array_keys($selected) as $id) {
                $attr['selected.'.$id] = trans('validation.dynamic_attributes.selected_entry', ['id' => $id]);
            }
        }

        return $attr;
    }

    protected function formIds(array $selected): array
    {
        return $this->entries
            ->primary(...$selected)
            ->trashed(TrashedMode::All)
            ->fields(['entries.form_id'])
            ->groupBy('entries.form_id')
            ->just('form_id');
    }
}
