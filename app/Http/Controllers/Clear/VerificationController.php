<?php

namespace AwardForce\Http\Controllers\Clear;

use AwardForce\Http\Middleware\Verification;
use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Authentication\Exceptions\InvalidSixDigitCodeException;
use AwardForce\Modules\Authentication\Redirection\LoginRedirector;
use AwardForce\Modules\Authentication\Services\SixDigitsCode;
use AwardForce\Modules\Authentication\Views\AuthenticatorsChallenge;
use AwardForce\Modules\Clear\Services\VerificationResendCounter;
use AwardForce\Modules\Clear\Strategy\AuthenticationStrategy;
use AwardForce\Modules\Identity\Users\Commands\ConfirmCommunicationChannelCommand;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Exceptions\UnknownConfirmationException;
use AwardForce\Modules\Identity\Users\Models\UserFactory;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Platform\Support\Values\PhoneNumber;
use Platform\Tokens\TokenManager;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;
use Psr\SimpleCache\InvalidArgumentException;

class VerificationController
{
    use DispatchesJobs;

    public function __construct(
        private SixDigitsCode $sixDigitsCode,
        private AuthenticationStrategy $authenticationStrategy,
        private TokenManager $tokens,
        private UserFactory $userFactory,
        private UserRepository $users,
        private VerificationResendCounter $counter,
        private LoginRedirector $redirector,
    ) {
    }

    /**
     * @return \Illuminate\Contracts\View\View
     */
    public function challenge(AuthenticatorsChallenge $view)
    {
        return view('clear.2fa-challenge', $view);
    }

    public function generateLoginCode(Request $request)
    {
        $user = Consumer::user();
        $channel = Consumer::isTemp() ? Consumer::get()->channel() : $user->preferredContact();
        $token = $this->sixDigitsCode->generateToken($user, $channel, session()->get(Verification::VERIFICATION_REDIRECTION_KEY));
        $this->sixDigitsCode->send($user, $token, $channel);

        if ($request->get('resend', false)) {
            $this->counter->increment($user->preferredContact());

            $message = trans_no_tags('auth.request_login_code.code-resent'.(PhoneNumber::check($user->preferredContact()) ? '-mobile' : ''));
            $type = 'info';

            if ($this->counter->isOverdue($user->preferredContact())) {
                $message .= '<br />'.trans('auth.verification_code_resend_warning');
                $type = 'warning';
            }
            session()->flash('message', $message);
            session()->flash('type', $type);
        }

        return redirect()->route('auth.verify.show', ['token' => $token]);
    }

    public function showLoginCode(Request $request, $token = '')
    {
        VueData::registerTranslations([
            'auth.six-digit-code.labels.default',
            'auth.six-digit-code.errors.incorrect-code',
            'auth.six-digit-code.errors.resend-code',
            'auth.request_login_code.code-not-received',
            'buttons.continue',
            'buttons.cancel',
        ]);

        return view('clear.login-code', [
            'token' => $token,
            'verificationRoute' => route('auth.verify.confirm'),
            'resendRoute' => route('auth.verify.generate').'?resend=1',
        ]);
    }

    /**
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws InvalidArgumentException
     */
    public function confirmLoginCode(Request $request, ?string $code = null, ?string $token = null)
    {
        $token = $token ?: $request->get('token');
        $code = $code ?: $request->get('code');

        if (! $code || ! $token || ! $this->sixDigitsCode->isValid($code, $token, $request->get('recipient'))) {
            throw new InvalidSixDigitCodeException;
        }

        $tokenValue = $this->sixDigitsCode->tokenValue($token);

        if (! Consumer::user()?->ownsCommunicationChannel($tokenValue->login())) {
            throw new InvalidSixDigitCodeException;
        }

        $this->counter->reset($tokenValue->login());

        if (! Consumer::isTemp()) {
            $this->dispatch(new ConfirmCommunicationChannelCommand($tokenValue));

            return redirect()->back();
        }

        Consumer::get()->verify();

        return redirect($tokenValue->redirection() ?: session()->get(Verification::VERIFICATION_REDIRECTION_KEY));
    }

    public function confirmChannel(Request $request, $confirmationToken)
    {
        try {
            $token = $this->sixDigitsCode->getDecryptedToken($confirmationToken);
        } catch (\Throwable $e) {
            throw new UnknownConfirmationException;
        }

        $this->dispatch(new ConfirmCommunicationChannelCommand($token));

        return overridable_redirect($this->redirector->route());
    }
}
