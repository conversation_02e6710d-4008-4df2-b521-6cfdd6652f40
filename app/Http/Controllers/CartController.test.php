<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Cart\CartCompleteRequest;
use AwardForce\Http\Requests\Payment\ProcessPaymentFormRequest;
use AwardForce\Library\Values\Amount;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Ecommerce\Cart\Commands\ProcessCartCommand;
use AwardForce\Modules\Ecommerce\Cart\Costing\EntryAmount;
use AwardForce\Modules\Ecommerce\Cart\EntryItem;
use AwardForce\Modules\Ecommerce\Cart\Services\LocksPayment;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Payments\Commands\ProcessPaymentFormCommand;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\RedirectResponse;
use AwardForce\Modules\Payments\Response;
use AwardForce\Modules\Payments\Services\PaymentService;
use AwardForce\Modules\Settings\Repositories\EloquentSettingRepository;
use Illuminate\Support\Facades\Bus;
use Mockery as m;
use Omnipay\Common\Exception\InvalidCreditCardException;
use Omnipay\Common\Exception\InvalidRequestException;
use PHPUnit\Framework\Attributes\PreserveGlobalState;
use PHPUnit\Framework\Attributes\RunTestsInSeparateProcesses;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

#[RunTestsInSeparateProcesses]
#[PreserveGlobalState(false)]
class CartControllerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    protected function init()
    {
        $this->mockConsumer($this->user = $this->muffin(User::class));
    }

    public function testShouldReleaseCartLockWhenAnExceptionOccursOnPay()
    {
        Bus::spy();
        Bus::shouldReceive('dispatchSync')->once()->with(m::type(ProcessCartCommand::class), null)->andThrow(new InvalidRequestException('Some Message'));
        $locksPayment = LocksPayment::create(current_account_id(), consumer_id());

        $controller = app(CartController::class);
        $cart = app(Cart::class);

        $request = new CartCompleteRequest();
        $request->replace([
            'currency' => 'AUD',
            'country' => 'AR',
            'streetAddress' => 'street',
            'city' => 'city',
            'state' => 'state',
            'postcode' => '1442',
            'paymentMethod' => 'invoice',
        ]);

        $controller->pay($cart, $request);
        $this->assertFalse($locksPayment->processLocked());
    }

    public function testShouldReleaseCartLockWhenAnExceptionOccursOnProcess()
    {
        Bus::spy();
        Bus::shouldReceive('dispatchSync')->once()->with(m::type(ProcessPaymentFormCommand::class), null)->andThrow(new InvalidCreditCardException('Some Message'));
        $locksPayment = LocksPayment::create(current_account_id(), consumer_id());

        $paymentService = $this->mock(PaymentService::class);
        $paymentService->shouldReceive('getGateway')->andReturn('');

        $controller = app(CartController::class);
        $cart = app(Cart::class);

        $request = new ProcessPaymentFormRequest();
        $request->replace($this->buildProcessPaymentFormPayload());

        $controller->process($cart, $request);
        $this->assertFalse($locksPayment->processLocked());
    }

    public function testShouldRedirectIfResponseIsRedirect()
    {
        $redirectResponse = new RedirectResponse($redirectTo = 'htt://redirect.fake.com');
        Bus::spy();
        Bus::shouldReceive('dispatchSync')->once()->with(m::type(ProcessCartCommand::class), null)->andReturn($redirectResponse);
        $locksPayment = LocksPayment::create(current_account_id(), consumer_id());

        $controller = app(CartController::class);
        $cart = app(Cart::class);
        $entryAmount = new EntryAmount(new Entry, $currency = $cart->currency());
        $entryAmount->updateAmount(new Amount(100, $currency));
        $cart->addItem(new EntryItem($entryAmount, 'TestItem', new Price, true));

        $request = new CartCompleteRequest();

        $response = $controller->pay($cart, $request);
        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
        $this->assertEquals($redirectTo, $response->getTargetUrl());
        $this->assertTrue($locksPayment->processLocked());
    }

    public function testShouldReleaseLockOnSuccessfulPayment()
    {
        $paymentResponse = new Response(true);
        Bus::spy();
        Bus::shouldReceive('dispatchSync')->once()->with(m::type(ProcessCartCommand::class), null)->andReturn($paymentResponse);
        $locksPayment = LocksPayment::create(current_account_id(), consumer_id());

        $controller = app(CartController::class);
        $cart = app(Cart::class);

        $request = new CartCompleteRequest();

        $response = $controller->pay($cart, $request);
        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
        $this->assertEquals(route('entry.entrant.complete'), $response->getTargetUrl());
        $this->assertFalse($locksPayment->processLocked());

    }

    public function testNetworkIsPassedToCommand()
    {
        $payload = $this->buildProcessPaymentFormPayload();
        Bus::shouldReceive('dispatchSync')->once()->with(m::on(
            // assert that network was passed to the command
            fn(ProcessPaymentFormCommand $command) => $command->card->network->value == $payload['network']
        ), null)->andReturn(new RedirectResponse('http://redirect.fake.com'));
        $paymentService = $this->mock(PaymentService::class);
        $paymentService->shouldReceive('getGateway')->andReturn('');

        $mockedController = m::mock(CartController::class, [
            m::mock(EloquentSettingRepository::class),
        ])
            ->makePartial();

        $mockedController->shouldReceive('respond')
            ->once()
            ->andReturn('');
        $request = new ProcessPaymentFormRequest();
        $request->replace($payload);

        $mockedController->process(app(Cart::class), $request);
    }

    public function testShouldNotAcquireLockWhenCartIsFree()
    {
        $payload = $this->buildProcessPaymentFormPayload();
        Bus::shouldReceive('dispatchSync')->andReturn(new Response('OK'));
        Bus::shouldReceive('dispatch')->andReturn(new Response('OK'));
        $paymentService = $this->mock(PaymentService::class);
        $paymentService->shouldReceive('getGateway')->andReturn('');

        $locksPayment = m::mock('alias:'.LocksPayment::class);
        $locksPayment->shouldreceive('create')->andReturn($locksPayment);
        $locksPayment->shouldReceive('acquireLock')->never();
        $locksPayment->shouldReceive('releaseLock')->twice();

        $processPaymentRequest = new ProcessPaymentFormRequest($payload);
        $cartCompleteRequest = new CartCompleteRequest($payload);
        $mockedController = m::mock(CartController::class, [m::mock(EloquentSettingRepository::class)])->makePartial();

        $mockedController->process(app(Cart::class), $processPaymentRequest);
        $mockedController->pay(app(Cart::class), $cartCompleteRequest);
    }

    public function testShouldAcquireLockWhenCartIsNotFree()
    {
        $payload = $this->buildProcessPaymentFormPayload();
        Bus::shouldReceive('dispatchSync')->andReturn(new Response('OK'));
        Bus::shouldReceive('dispatch')->andReturn(new Response('OK'));
        $paymentService = $this->mock(PaymentService::class);
        $paymentService->shouldReceive('getGateway')->andReturn('');
        $cart = app(Cart::class);

        $entryAmmount = new EntryAmount(new Entry, $currency = $cart->currency());
        $entryAmmount->updateAmount(new Amount(123, $currency));
        $cart->addItem(new EntryItem($entryAmmount, 'TestItem', new Price, false));

        $locksPayment = m::mock('alias:'.LocksPayment::class);
        $locksPayment->shouldreceive('create')->andReturn($locksPayment);
        $locksPayment->shouldReceive('acquireLock')->twice()->andReturn(true);
        $locksPayment->shouldReceive('releaseLock')->twice()->andReturn(true);

        $processPaymentRequest = new ProcessPaymentFormRequest($payload);
        $cartCompleteRequest = new CartCompleteRequest($payload);
        $mockedController = m::mock(CartController::class, [m::mock(EloquentSettingRepository::class)])->makePartial();

        $mockedController->process($cart, $processPaymentRequest);
        $mockedController->pay($cart, $cartCompleteRequest);
    }

    public function testLocksAreLimitedByAccount(): void
    {
        $paymentResponse = new Response(true);
        Bus::spy();
        Bus::shouldReceive('dispatchSync')->once()->with(m::type(ProcessCartCommand::class), null)->andReturn($paymentResponse);
        $locksPayment = LocksPayment::create(current_account_id() + 1, consumer_id());
        $locksPayment->acquireLock();

        $controller = app(CartController::class);
        $cart = app(Cart::class);
        $entryAmount = new EntryAmount(new Entry(), $currency = $cart->currency());
        $entryAmount->updateAmount(new Amount(123, $currency));
        $cart->addItem(new EntryItem($entryAmount, 'TestItem', new Price, false));

        $request = new CartCompleteRequest();

        $controller->pay($cart, $request);
        $this->assertTrue($locksPayment->processLocked());
    }

    public function testLocksAreLimitedByUser(): void
    {
        $paymentResponse = new Response(true);
        Bus::spy();
        Bus::shouldReceive('dispatchSync')->once()->with(m::type(ProcessCartCommand::class), null)->andReturn($paymentResponse);
        $locksPayment = LocksPayment::create(current_account_id(), consumer_id() + 1);
        $locksPayment->acquireLock();

        $controller = app(CartController::class);
        $cart = app(Cart::class);
        $entryAmount = new EntryAmount(new Entry(), $currency = $cart->currency());
        $entryAmount->updateAmount(new Amount(123, $currency));
        $cart->addItem(new EntryItem($entryAmount, 'TestItem', new Price, false));

        $request = new CartCompleteRequest();

        $controller->pay($cart, $request);
        $this->assertTrue($locksPayment->processLocked());
    }

    public function testLocksPersist(): void
    {
        Bus::spy();
        Bus::shouldNotReceive('dispatchSync');
        $locksPayment = LocksPayment::create(current_account_id(), consumer_id());
        $locksPayment->acquireLock();

        $controller = app(CartController::class);
        $cart = app(Cart::class);
        $entryAmount = new EntryAmount(new Entry(), $currency = $cart->currency());
        $entryAmount->updateAmount(new Amount(123, $currency));
        $cart->addItem(new EntryItem($entryAmount, 'TestItem', new Price, false));

        $request = new CartCompleteRequest();

        $response = $controller->pay($cart, $request);
        $this->assertInstanceOf(\Illuminate\Http\RedirectResponse::class, $response);
        $this->assertEquals(route('cart.view'), $response->getTargetUrl());
        $this->assertTrue($locksPayment->processLocked());
    }

    public function testItemsCountReturnsUsersCartCount(): void
    {
        $controller = app(CartController::class);
        $cart = app(Cart::class);
        $entryAmount = new EntryAmount(new Entry(), $cart->currency());
        $entryAmount->updateAmount(new Amount(123, $cart->currency()));
        $cart->addItem(new EntryItem($entryAmount, 'TestItem', new Price, false));

        $this->assertEquals(1, $controller->itemsCount($cart)['count']);
    }

    protected function buildProcessPaymentFormPayload(): array
    {
        return [
            'name' => 'Test Name',
            'card' => 'visa',
            'cardNumber' => '****************',
            'expiryMonth' => '12',
            'expiryYear' => '25',
            'cvv' => '123',
            'network' => 'cartes_bancaires',
        ];
    }
}
