<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Authentication\AuthenticateTokenRequest;
use AwardForce\Http\Requests\Authentication\AuthenticateUserRequest;
use AwardForce\Http\Requests\Authentication\PasswordResetRequest;
use AwardForce\Http\Requests\Authentication\RequestLoginLinkRequest;
use AwardForce\Library\Authentication\Authenticator;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Library\Values\App;
use AwardForce\Modules\Accounts\Models\GlobalAccount;
use AwardForce\Modules\Accounts\Search\UserAccountSearch;
use AwardForce\Modules\Authentication\Commands\EmulateUserCommand;
use AwardForce\Modules\Authentication\Commands\IncomingAppSwitch;
use AwardForce\Modules\Authentication\Commands\LoginSocialTokenCommand;
use AwardForce\Modules\Authentication\Commands\LoginTokenCommand;
use AwardForce\Modules\Authentication\Commands\LoginUserCommand;
use AwardForce\Modules\Authentication\Commands\LoginUserWithLink;
use AwardForce\Modules\Authentication\Commands\LogoutUserCommand;
use AwardForce\Modules\Authentication\Commands\OutgoingAppSwitch;
use AwardForce\Modules\Authentication\Commands\ProvisionAccountSwitchCommand;
use AwardForce\Modules\Authentication\Commands\RequestLoginLink;
use AwardForce\Modules\Authentication\Commands\ResetUsersPasswordCommand;
use AwardForce\Modules\Authentication\Exceptions\UnknownApp;
use AwardForce\Modules\Authentication\Redirection\LoginRedirector;
use AwardForce\Modules\Authentication\Services\SixDigitsCode;
use AwardForce\Modules\Authentication\Services\SsoCode;
use AwardForce\Modules\Authentication\Services\SsoRememberance;
use AwardForce\Modules\Authentication\Tokens\RequestLoginLinkToken;
use AwardForce\Modules\Authentication\Tokens\ResetPassword;
use AwardForce\Modules\Clear\Commands\SetupTempConsumer;
use AwardForce\Modules\Identity\Roles\Commands\AddDefaultRoleToUserCommand;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Exceptions\UserDoesNotExist;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Models\UserFactory;
use AwardForce\Modules\Identity\Users\Tokens\InvitationToken;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log as InfoLog;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Fluent;
use Illuminate\Support\Str;
use Platform\Authorisation\FeatureRoles\ProgramManager;
use Platform\Http\Controller;
use Platform\Http\Messaging;
use Platform\Language\Language;
use Platform\Support\Values\Email;
use Platform\Tokens\TokenManager;
use Platform\Tokens\TokenNotFoundException;
use Psr\Log\LoggerInterface as Log;
use Ramsey\Uuid\Uuid;

class AuthenticationController extends Controller
{
    use DispatchesJobs;
    use Messaging;

    public function __construct(
        private Authenticator $authenticator,
        private LoginRedirector $redirector,
        private Log $log,
        private SsoCode $ssoCode,
        private SsoRememberance $ssoRememberance,
        private SixDigitsCode $sixDigitsCode,
        private TokenManager $tokens,
        private UserFactory $users
    ) {
    }

    /**
     * Handle authentication.
     *
     * @return mixed
     */
    public function login(AuthenticateUserRequest $request)
    {
        $this->authenticator->authenticate($login = $request->get('login'), $request->get('login_password'));

        $hasRole = $request->filled('role');
        $user = $this->dispatchSync(new LoginUserCommand(
            $login,
            $request->get('remember'),
            $hasRole || setting('app-site-registration-open'),
            ! $hasRole
        ));

        if ($hasRole) {
            $request->session()->put('role.requested', $role = $request->get('role'));

            return redirect()->route('profile.complete.role', ['registrationRole' => $role]);
        }

        $this->dispatch(new AddDefaultRoleToUserCommand($user));

        return redirect($this->redirector->route());
    }

    /**
     * @return mixed
     */
    public function loginToken(AuthenticateTokenRequest $request)
    {
        $role = $request->get('role');
        $hasRole = ! empty($role);

        if ($request->has('error')) {
            InfoLog::info('AuthenticationController::loginToken - $request->token: '.$request->get('token'));
            InfoLog::info('AuthenticationController::loginToken - $request->error: '.$request->get('error'));
            InfoLog::info('AuthenticationController::loginToken - $request->role: '.$request->get('role'));

            $route = $hasRole ? ['register.role', $request->get('role')] : 'home';

            return $this->withMessage(trans($request->get('error')), 'error', $route);
        }

        $user = $this->dispatchSync(new LoginTokenCommand(
            $request->get('token'),
            $hasRole || setting('app-site-registration-open') || setting('enable-3rd-party-authentication'),
            ! $hasRole
        ));
        $rememberCookie = $this->ssoRememberance->remember($user);
        if ($hasRole) {
            $request->session()->put('role.requested', $role);

            return redirect()->route('profile.complete.role', ['registrationRole' => $role])
                ->withCookie($rememberCookie);
        }

        return redirect($this->redirector->route())->withCookie($rememberCookie);
    }

    /**
     * @return mixed
     */
    public function loginSocialAuthToken(AuthenticateTokenRequest $request)
    {
        $role = $request->get('role');
        $hasRole = ! empty($role);

        if ($request->has('error')) {
            InfoLog::info('AuthenticationController::loginToken - $request->token: '.$request->get('token'));
            InfoLog::info('AuthenticationController::loginToken - $request->error: '.$request->get('error'));
            InfoLog::info('AuthenticationController::loginToken - $request->role: '.$request->get('role'));

            $route = $hasRole ? ['register.role', $request->get('role')] : 'home';

            return $this->withMessage(trans($request->get('error')), 'error', $route);
        }

        $user = $this->dispatchSync(new LoginSocialTokenCommand(
            $request->get('token'),
            $hasRole || setting('app-site-registration-open') || setting('enable-3rd-party-authentication'),
            ! $hasRole
        ));

        $rememberCookie = $this->ssoRememberance->remember($user);
        if ($hasRole) {
            $request->session()->put('role.requested', $role);

            return redirect()->route('profile.complete.role', ['registrationRole' => $role])
                ->withCookie($rememberCookie);
        }

        return redirect($this->redirector->route())->withCookie($rememberCookie);
    }

    /**
     * Handle logging out of user.
     *
     * @return mixed
     */
    public function logout()
    {
        $this->dispatch(new LogoutUserCommand);

        return redirect()->route('home');
    }

    /**
     * Handle collecting a list of accounts the current user belongs to.
     *
     * @return mixed
     */
    public function getAccounts(Request $request, UserAccountSearch $accounts)
    {
        return $accounts->forGlobalUser($request->user()->globalUser, ['keywords' => $request->get('q')])
            ->reject(fn(GlobalAccount $account) => $account->isSuspended())
            ->map(function (GlobalAccount $account) {
                return new Fluent([
                    'id' => (string) $account->id,
                    'name' => $account->translatedName(),
                    'domains' => array_filter($account->domains),
                ]);
            });
    }

    /**
     * Handle generation of token and url for switching to another account
     *
     * @return mixed
     */
    public function provisionAccountSwitch(Request $request)
    {
        $command = new ProvisionAccountSwitchCommand(Uuid::fromString($request->globalAccount), $request->user()->globalId);

        return redirect($this->dispatchSync($command));
    }

    /**
     * @throws UnknownApp
     */
    public function outgoingAppSwitch(Request $request, string $app)
    {
        $command = new OutgoingAppSwitch(new App($app), $request->user());

        return redirect($this->dispatchSync($command));
    }

    public function incomingAppSwitch(Request $request)
    {
        $this->dispatch(new IncomingAppSwitch($request->get('token')));

        return redirect($this->redirector->route());
    }

    public function emulate(Request $request)
    {
        try {
            $this->dispatch(new EmulateUserCommand($request->route('token')));
        } catch (TokenNotFoundException $exception) {
            return redirect()->route('home')
                ->with(['message' => trans('auth.errors.switch'), 'type' => 'error']);
        }

        return redirect($this->redirector->route());
    }

    /**
     * Handle the password reset
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function passwordReset(Request $request, TokenManager $tokens, UserRepository $users)
    {
        $token = $request->route('token');

        if (! $tokens->has($token, ResetPassword::class, current_account_id())) {
            return redirect()->route('home')
                ->with(['message' => trans('auth.password.reset.unknown_token'), 'type' => 'error']);
        }

        $userId = $tokens->get($token, ResetPassword::class, current_account_id())->userId;

        if (($user = $users->getById($userId))->wasInvited) {
            return redirect($this->invitationLink($user, $tokens));
        }

        VueData::registerTranslations(['auth.password.reset', 'buttons.save', 'validation.password_complexity', 'miscellaneous.password.hint']);

        $this->layout = view('layouts.splash');

        return $this->respond('clear.password-reset', compact('token'));
    }

    /**
     * Handle saving newly reset password
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePassword(PasswordResetRequest $request)
    {
        try {
            $redirect = $this->dispatchSync(new ResetUsersPasswordCommand(
                current_account(),
                $request->get('token'),
                $request->get('password')
            ));
        } catch (TokenNotFoundException $e) {
            return redirect()->route('home')
                ->with(['message' => trans('auth.password.reset.unknown_token'), 'type' => 'error']);
        }

        return redirect($redirect ?? route('home'))
            ->with(['message' => trans('auth.password.reset.success_message'), 'type' => 'info']);
    }

    /**
     * Handle request login link form.
     *
     * @return mixed
     *
     * @throws \Platform\Language\UnsupportedLanguage
     */
    public function requestLoginLink(UserRepository $users, RequestLoginLinkRequest $request, UserFactory $factory)
    {
        $login = $request->get('requestLogin');
        $this->dispatch(new SetupTempConsumer($login));
        $deliveryChannel = Email::check($login) ? 'email' : 'mobile';
        $translationKey = "auth.request_login_link.{$deliveryChannel}_sent";
        $ssoReferer = Str::contains($request->headers->get('referer'), 'password/confirm');
        $translationKey .= $ssoReferer ? '_for_sso_user' : '';
        $locale = \Consumer::languageCode();

        try {
            $user = $factory->requireUser($login, $login, User::CREATED_LOGIN);
        } catch (UserDoesNotExist $exception) {
            return redirect()->route('auth.verify.show', ['token' => Str::random(32)])
                ->with(['messageRaw' => trans($translationKey, ['login' => $login], $locale), 'type' => 'info']);
        }

        if (\Cookie::has('language')) {
            $language = new Language(\Cookie::get('language'));
            $locale = $language->code;
        }

        $loginToken = $this->dispatchSync(new RequestLoginLink(
            current_account(),
            $login,
            $user,
            $locale,
            route('home')
        ));

        return redirect()->route('auth.verify.show', $loginToken)
            ->with(['messageRaw' => trans($translationKey, ['login' => $login], $locale), 'type' => 'info']);
    }

    /**
     * Handle the login link
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    public function loginLink(Request $request, TokenManager $tokens, UserRepository $users)
    {
        $token = $request->route('token');

        if (! $tokens->has($token, RequestLoginLinkToken::class, current_account_id())) {
            return redirect()->route('home')
                ->with(['message' => trans('auth.request_login_link.unknown_token'), 'type' => 'error']);
        }

        $login = $tokens->get($token, RequestLoginLinkToken::class, current_account_id())->login;

        if (($user = $users->getByEmailOrMobile($login, $login)) && $user->wasInvited) {
            return redirect($this->invitationLink($user, $tokens));
        }

        $this->dispatch(new LoginUserWithLink(
            $login,
            null,
            setting('app-site-registration-open'),
            true
        ));
        session()->put(
            'notify_change_password',
            trans('auth.request_login_link.notify_change_password', [
                'profile_url' => URL::route('profile.show'),
            ])
        );

        $this->log->info('Login link used by user agent: '.$request->userAgent());

        return redirect($this->redirector->route());
    }

    private function confirmPasswordMessage(User $user): array
    {
        if (request()->hasSession() && request()->session()->has('message')) {
            return [];
        }

        $isProgramManager = ProgramManager::appliesTo(new UserConsumer($user));

        return [
            'message' => trans($isProgramManager ? 'auth.password.manager' : 'auth.password.confirm'),
            'type' => 'info',
        ];
    }

    private function invitationLink(User $user, TokenManager $tokens): string
    {
        return route('invitation', ['invitationToken' => $tokens->create(new InvitationToken($user, current_account(), expiry: 5))]);
    }
}
