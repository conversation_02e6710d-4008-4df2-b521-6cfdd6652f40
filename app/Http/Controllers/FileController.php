<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\File\DeleteOwnRequest;
use AwardForce\Http\Requests\File\UploadRequest;
use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Files\Commands\DeleteFileCommand;
use AwardForce\Modules\Files\Commands\ProcessFileCommand;
use AwardForce\Modules\Files\Commands\UpdateFileMetadata;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\AllowedDownloadsService;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Platform\Http\Controller;

class FileController extends Controller
{
    use DispatchesJobs;

    /**
     * @var AllowedDownloadsService
     */
    protected $downloads;

    /**
     * @var FileRepository
     */
    protected $files;

    public function __construct(AllowedDownloadsService $downloads, FileRepository $files)
    {
        $this->downloads = $downloads;
        $this->files = $files;
    }

    /**
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function download($id)
    {
        if (! $this->downloads->isAllowed($id)) {
            abort(404);
        }

        $file = $this->files->requireById($id);

        return redirect(cloud_asset_url($file->file, filename: $file->original));
    }

    public function upload(UploadRequest $request): array
    {
        $file = File::prepare(
            $request->get('original'),
            $request->get('name'),
            $request->get('resource'),
            $request->resourceId(),
            $request->foreignId(),
            $request->string('language'),
            $request->string('mime'),
        );

        $this->dispatch(new ProcessFileCommand($file, $request->get('tabId')));
        $this->downloads->allow($file);

        return ['file' => $file->id, 'token' => $file->token];
    }

    public function status(int $file)
    {
        $file = $this->files->requireByIdForUser($file, Consumer::id());

        return array_merge([
            'fileUrl' => $file->ready() ? cloud_asset_url($file->file, true, filename: $file->original) : '',
            'imageUrl' => $file->ready() && $file->isImage() ? imgix($file->file, $file->original) : '',
            'source' => $file->ready() ? cloud_asset_url($file->file, filename: $file->original) : '',
            'attachmentId' => (int) $file->resourceId ?: null,
        ], $file->only('id', 'status', 'statusMessage'));
    }

    public function error(Request $request)
    {
        Log::notice(
            'URL path : '.$request->urlPath."\n"
            .'Message : '.$request->message."\n"
            .'Response : '.$request->response."\n"
            .'File : '.json_encode($request->file)."\n"
        );

        return response()->json(['status' => 'ok']);
    }

    public function deleteOwn(DeleteOwnRequest $request, File $file)
    {
        $this->dispatch(new DeleteFileCommand($file));

        return [];
    }

    public function metadata(int $fileId)
    {
        $file = $this->files->getById($fileId);

        if (! $file) {
            abort(404);
        }

        $modal = view('entry.common.cards.sections.metadata-modal', [
            'metadata' => $metadata = $file->metadata->empty() ?
                $this->dispatchSync(new UpdateFileMetadata($fileId)) :
                $file->metadata,
        ]);

        return $this->formatJson([
            'size' => (string) $metadata->size,
            'dataContent' => (string) obfuscate($modal->render()),
        ]);
    }
}
