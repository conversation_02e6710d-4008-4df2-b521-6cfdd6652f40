<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Http\Requests\Entry\Manager\MarkAsDuplicateAndArchiveRequest;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Entries\Commands\ConfirmAndArchiveDuplicates;
use AwardForce\Modules\Entries\Commands\FindDuplicates;
use AwardForce\Modules\Entries\Commands\MarkAsDuplicateAndArchive;
use AwardForce\Modules\Entries\Commands\UnconfirmNotDuplicate;
use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\LocksFindDuplicates;
use AwardForce\Modules\Entries\View\Manager\CompareDuplicate;
use AwardForce\Modules\Entries\View\Manager\DuplicatesList;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\Request;
use Platform\Http\Controller;

class ManageDuplicatesController extends Controller
{
    use DispatchesJobs;

    public static $resource = 'EntriesAll';

    public function index(LocksFindDuplicates $locker)
    {
        if ($locker->processLocked()) {
            return $this->respond('entry.manager.duplicates.scanning');
        }

        return $this->respond('entry.manager.duplicates.index', app(DuplicatesList::class));
    }

    public function scan(LocksFindDuplicates $locker)
    {
        if (! $locker->acquireLock()) {
            return redirect()->route('entry.duplicates.manage');
        }

        $this->dispatch(new FindDuplicates(CurrentAccount::activeSeasonId()));

        return redirect()->route('entry.duplicates.manage');
    }

    public function notDuplicate(Entry $entry)
    {
        $entry->duplicate->confirmNotDuplicate();

        return redirect()->back();
    }

    public function confirm(Entry $entry)
    {
        $this->dispatch(new ConfirmAndArchiveDuplicates((array) $entry->duplicate->id, $entry->id));

        return redirect()->route('entry.duplicates.manage');
    }

    public function primary(Entry $entry)
    {
        $entry->duplicate->forcePrimary();

        return redirect()->route('entry.duplicates.manage');
    }

    public function confirmGroup(Entry $primary, DuplicateRepository $duplicates)
    {
        $this->dispatch(new ConfirmAndArchiveDuplicates($duplicates->unconfirmedDuplicates($primary)->pluck('id')->all(), $primary->id));

        return redirect()->route('entry.duplicates.manage');
    }

    /**
     * Set duplicate and archive for bulk or overflow actions
     *
     * @return mixed
     */
    public function setDuplicateAndArchive(MarkAsDuplicateAndArchiveRequest $request)
    {
        $this->dispatch(new MarkAsDuplicateAndArchive($request->selected, $request->primary));

        return redirect()->route('entry.manager.index');
    }

    /**
     * Retrieve entries for lookahead
     *
     * @return mixed
     */
    public function autocomplete(Request $request, EntryRepository $repository)
    {
        return $repository->getPrimaryEntriesForSeason(current_account()->activeSeason()->id, $request->name);
    }

    public function unarchiveNotDuplicate(Entry $entry)
    {
        $this->dispatch(new UnconfirmNotDuplicate((array) $entry->duplicate->id));

        return redirect()->back();
    }

    public function compare()
    {
        return view('entry.manager.duplicates.compare', app(CompareDuplicate::class));
    }
}
