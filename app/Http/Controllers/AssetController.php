<?php

namespace AwardForce\Http\Controllers;

use AwardForce\Modules\Theme\Services\Favicons;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Response;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;

class AssetController extends Controller
{
    public function favicon()
    {
        if ($favicon = app(Favicons::class)->getFaviconValue()) {
            return response()->streamDownload(function () use ($favicon) {
                echo Http::get($favicon)->body();
            }, 'favicon.ico');
        }

        return response()->download(brand_icon(current_account_brand(), 'favicon.64'), 'favicon.ico');
    }

    public function faviconLogo(Request $request): \Illuminate\Http\Response|StreamedResponse
    {
        $favicon = app(Favicons::class)->getFaviconValue();

        if (! $favicon) {
            return response()->noContent();
        }

        return response()->streamDownload(function () use ($favicon) {
            echo file_get_contents($favicon);
        }, 'favicon');
    }

    public function asset(Request $request)
    {
        // Replace the git commit hash from the request. This will give us the raw file to request and return
        $file = preg_replace('/-[a-z0-9]{40}/i', '', $request->path());

        $this->validateAsset($file);

        // Genearte a new response object and set the appropriate headers
        return $this->constructResponse($file);
    }

    /**
     * Return the appropriate content type for the file.
     *
     * @return string
     */
    private function header(string $file)
    {
        switch (file_extension($file)) {
            case 'css':
                return 'text/css';
            case 'js':
                return 'text/javascript';
            case 'ttc':
            case 'ttf':
                return 'application/x-font-ttf';
            case 'otf':
                return 'application/x-font-otf';
            case 'woff':
                return 'application/font-woff';
            case 'eot':
                return 'application/vnd.ms-fontobject';
        }
    }

    private function constructResponse($file): \Illuminate\Http\Response
    {
        $response = Response::make(File::get(public_path($file)));
        $response->header('Content-Type', $this->header($file));
        $response->header('Access-Control-Allow-Origin', '*');

        return $response;
    }

    private function validateAsset(string $file)
    {
        $realPath = realpath(public_path($file));
        $basePath = realpath(public_path()).'/';

        if (! starts_with($realPath, $basePath) || ! file_exists($realPath)) {
            throw new NotFoundHttpException;
        }
    }
}
