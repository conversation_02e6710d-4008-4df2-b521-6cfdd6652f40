<?php

namespace AwardForce\Http\Controllers\Kessel;

use AwardForce\Http\Requests\Account\ImportAccountSettingsRequest;
use AwardForce\Http\Requests\Kessel\Account\CacheDealRequest;
use AwardForce\Http\Requests\Kessel\Account\CreateAccountRequest;
use AwardForce\Http\Requests\Kessel\Account\SuspendAccountRequest;
use AwardForce\Http\Requests\Kessel\Account\UpdateAccountOwnerRequest;
use AwardForce\Http\Requests\Kessel\Account\UpdateAccountRequest;
use AwardForce\Modules\Accounts\Commands\CacheHubSpotDealCommand;
use AwardForce\Modules\Accounts\Commands\CreateAccountCommand;
use AwardForce\Modules\Accounts\Commands\DestroyAccountCommand;
use AwardForce\Modules\Accounts\Commands\ExportAccountSettings;
use AwardForce\Modules\Accounts\Commands\FindManagedAccounts;
use AwardForce\Modules\Accounts\Commands\ImportAccountSettings;
use AwardForce\Modules\Accounts\Commands\SetDestructionStatus;
use AwardForce\Modules\Accounts\Commands\ToggleAccountSuspensionCommand;
use AwardForce\Modules\Accounts\Commands\UpdateAccountAttributesCommand;
use AwardForce\Modules\Accounts\Commands\UpdateAccountOwnerCommand;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Accounts\Enums\CopyStatus;
use AwardForce\Modules\Accounts\Enums\DestructionStatus;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Services\Copy\Values\CopySettings;
use AwardForce\Modules\Accounts\Services\LocksAccountCopy;
use AwardForce\Modules\Accounts\View\AccountView;
use AwardForce\Modules\Accounts\View\Kessel\AccountsList;
use AwardForce\Modules\Accounts\View\Kessel\SuspendedAccountsList;
use AwardForce\Modules\Audit\Commands\DestroyForAccount;
use AwardForce\Modules\Categories\Commands\RebuildCategoryTree;
use AwardForce\Modules\Categories\Services\LocksRebuildCategoryTreeProcess;
use AwardForce\Modules\Features\Data\Features;
use AwardForce\Modules\Identity\Users\Models\GlobalUser;
use AwardForce\Modules\NewDashboard\DataObjects\Dashboards;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller;

class AccountController extends Controller
{
    use DispatchesJobs;
    use FalconAuditor;

    private $accounts;

    public function __construct(AccountRepository $accounts)
    {
        $this->accounts = $accounts;
    }

    public function update(UpdateAccountRequest $request)
    {
        $this->jediTokenIfPresent($request);

        $this->dispatch(new UpdateAccountAttributesCommand(
            $this->accounts->getByGlobalId($request->globalAccount),
            $request->all(),
            $request->array('feature'),
            Dashboards::fromArray($request->array('dashboards', []))
        ));

        return response()->noContent();
    }

    public function suspend(SuspendAccountRequest $request)
    {
        $this->jediTokenIfPresent($request);
        $this->dispatch(new ToggleAccountSuspensionCommand($request->globalAccount, $request->get('suspended')));

        return response()->noContent();
    }

    /**
     * Setup a new account, making use of the existing account creation tools.
     *
     * @return mixed
     */
    public function setup(CreateAccountRequest $request)
    {
        $this->jediTokenIfPresent($request);

        return $this->dispatchSync(new CreateAccountCommand(
            $request->get('owner'),
            $request->get('name'),
            $request->get('domain'),
            $request->get('brand'),
            $request->get('vertical'),
            $request->get('product'),
            $request->get('supportedLanguages'),
            $request->get('defaultLanguageCode'),
            Dashboards::fromArray($request->get('dashboards', [])),
            $request->get('attributes', [])
        ));
    }

    /**
     * Update account owner.
     *
     * @return mixed
     */
    public function updateAccountOwner(UpdateAccountOwnerRequest $request)
    {
        if ($request->filled('owner')) {
            $this->jediTokenIfPresent($request);
            $this->dispatch(new UpdateAccountOwnerCommand($request->globalAccount, $request->owner));
        }

        return response()->noContent();
    }

    public function destroy(Request $request)
    {
        $this->dispatch(new DestroyAccountCommand($request->consigner));
        current_account()->setDestructionStatus(DestructionStatus::Queued);

        return response()->noContent();
    }

    /**
     * @return array
     */
    public function view(AccountView $view)
    {
        return ['account' => $view->account()];
    }

    /**
     * Searches for accounts across all shards
     */
    public function search(Request $request)
    {
        if ($request->boolean('suspended_only')) {
            return response()->json(
                app(SuspendedAccountsList::class)
                    ->results()
            );
        }

        return response()->json(
            app(AccountsList::class)
                ->results()
        );
    }

    /**
     * The accounts in the selected region where the user is the owner or a program manager.
     */
    public function managed(GlobalUser $globalUser)
    {
        $accounts = $this->dispatchSync(new FindManagedAccounts($globalUser->id));

        return response()->json($accounts);
    }

    /**
     * @param  Account  $account
     */
    public function cacheHubSpotDeal(CacheDealRequest $request)
    {
        $this->dispatch(
            new CacheHubSpotDealCommand(
                $this->accounts->getByGlobalId($request->globalAccount),
                $request->get('data')
            )
        );
    }

    public function rebuildCategoryTree()
    {
        $rebuildLock = new LocksRebuildCategoryTreeProcess(current_account()->activeSeason()->id);

        if ($rebuildLock->acquireLock()) {
            $this->dispatch(new RebuildCategoryTree(current_account()->activeSeason()->id));

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => true]);
    }

    public function export(Request $request)
    {
        $this->dispatch(new ExportAccountSettings(new CopySettings($request->all())));

        return [];
    }

    public function import(ImportAccountSettingsRequest $request)
    {
        //        $locker = new LocksAccountCopy;
        //        if (! $locker->acquireLock()) {
        //            return response(['message' => trans('accounts.copy.in-progress')], 423);
        //        }
        //        $this->dispatch(new ImportAccountSettings(new CopySettings($request->get('settings')), $request->get('data')));

        return [];
    }

    public function copyStatus()
    {
        $copyLog = current_account()->latestCopyLog()?->only('status', 'statusMessage', 'createdAt', 'updatedAt') ?? [];

        if (app()->isProduction() && $copyLog) {
            $copyLog['statusMessage'] = $copyLog['status'] === CopyStatus::FAILED ? trans('accounts.copy.error') : $copyLog['statusMessage'];
        }

        $copyLog['locked'] = (new LocksAccountCopy)->processLocked();

        return $copyLog;
    }

    public function features(Request $request)
    {
        $status = $request->get('status');

        return current_account()
            ->features
            ->when($status, fn(Features $features) => $features->status($status))
            ->statusMap();
    }

    public function cancelDestroy()
    {
        $this->dispatchSync(new SetDestructionStatus(DestructionStatus::Cancelled));

        return response()->noContent();
    }

    public function deleteEventLogs(): JsonResponse
    {
        $this->dispatch(new DestroyForAccount(current_account_id()));

        return response()->json(['success' => true]);
    }
}
