<?php

namespace AwardForce\Console\Commands;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Settings\Models\Setting;
use Illuminate\Support\Facades\Artisan;
use Tests\IntegratedTestCase;

class MigrateGlobalSettingsToExistingFormsTest extends IntegratedTestCase
{
    public function testFormsHaveSettingValueBasedOnCurrentGlobalSettings()
    {
        $settings[$this->account->id] = [
            'enable-moderation-for-chapter-managers' => true,
            'entry-similarity-percentage' => 80,
            'enable-copy-entries' => false,
            'allow-blank-pdf-download' => true,
            'allow-entry-pdf-download' => false,
            'display-entryid-to-entrants' => true,
            'max-entries' => 5,
        ];
        $this->settingForAccount($this->account, $settings[$this->account->id]);

        $accountWithoutSetting = $this->muffin(Account::class);

        Setting::where('account_id', $accountWithoutSetting->id)->delete();

        Artisan::call('forms:migrate-global-settings-to-existing-forms');

        /** @var Form $form */
        foreach (Form::all() as $form) {
            $form = $form->fresh();
            if (isset($settings[$form->accountId])) {
                foreach ($settings[$form->accountId] as $key => $expectedValue) {
                    if ($this->appliesOnlyToEntry($key) && ! $form->isEntry()) {
                        continue;
                    }

                    $this->assertSame($expectedValue, $form->settings->{$this->mapKey($key)});
                }
            }
        }
    }

    public function testItDefaultsFormSettingToFalseOrZeroWhenAccountLacksSetting(): void
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create([
            'moderationForChapterManagers' => true,
            'minimumSimilarityPercentage' => 90,
            'enableCopy' => true,
            'allowBlankPdfDownload' => true,
            'allowApplicationPdfDownload' => true,
            'displayId' => true,
            'maxApplications' => 10,
        ]),
        ]);

        Setting::where('account_id', $form->accountId)
            ->whereIn('key', [
                'enable-moderation-for-chapter-managers',
                'entry-similarity-percentage',
                'enable-copy-entries',
                'allow-blank-pdf-download',
                'allow-entry-pdf-download',
                'display-entryid-to-entrants',
                'max-entries',
            ])
            ->delete();

        $this->artisan('forms:migrate-global-settings-to-existing-forms');

        $form = $form->fresh();
        $this->assertFalse($form->settings->moderationForChapterManagers);
        $this->assertSame(0, $form->settings->minimumSimilarityPercentage);
        $this->assertFalse($form->settings->enableCopy);
        $this->assertFalse($form->settings->displayId);
        $this->assertSame(null, $form->settings->maxApplications);
    }

    public function testItKeepsOldFormSettingsAfterExecutingTheCommand()
    {
        $form = $this->muffin(Form::class);
        $form->settings = FormSettings::create(['lockWhenSubmitted' => true]);
        $form->save();

        $this->artisan('forms:migrate-global-settings-to-existing-forms');

        $this->assertTrue($form->fresh()->settings->lockWhenSubmitted);
    }

    protected function settingForAccount(Account $account, array $settings)
    {
        foreach ($settings as $key => $value) {
            $this->muffin(Setting::class, [
                'account_id' => $account->id,
                'key' => $key,
                'value' => $value,
            ]);
        }
    }

    private function appliesOnlyToEntry(string $key): bool
    {
        return in_array($key, [
            'enable-moderation-for-chapter-managers',
            'entry-similarity-percentage',
            'enable-copy-entries',
            'display-entryid-to-entrants',
            'max-entries',
        ]);
    }

    private function mapKey(string $key): string
    {
        return match ($key) {
            'enable-moderation-for-chapter-managers' => 'moderationForChapterManagers',
            'entry-similarity-percentage' => 'minimumSimilarityPercentage',
            'enable-copy-entries' => 'enableCopy',
            'allow-blank-pdf-download' => 'allowBlankPdfDownload',
            'allow-entry-pdf-download' => 'allowApplicationPdfDownload',
            'display-entryid-to-entrants' => 'displayId',
            'max-entries' => 'maxApplications',
            default => $key,
        };
    }
}
