<?php

namespace AwardForce\Library\Zip;

use AwardForce\Library\Exceptions\ArchiveCreationException;
use League\Flysystem\FileExistsException;
use ZipArchive;

class ZipGenerator
{
    /**
     * Create a ZIP file based on the contents of a directory,
     * and return the full path to the ZIP file.
     *
     * The $rootFolderName is the root folder all content will be
     * placed within inside the ZIP archive. This is required so
     * tidy extracting across all OS's.
     *
     * @param  string  $directory
     * @param  string  $root
     * @return string
     *
     * @throws ArchiveCreationException
     */
    public function createFromDirectory($directory, $root)
    {
        $archive = $this->open($file = $directory.'.zip');

        foreach ($this->mapFiles($directory, $root) as $relative => $fullPath) {
            try {
                $archive->addFile($fullPath, $relative);
                if ($this->skipCompression($relative)) {
                    $archive->setCompressionName($relative, ZipArchive::CM_STORE);
                }
            }
            // If the file already exists, it's possible that a user has uploaded the same file twice, or a file that has the same name
            catch (FileExistsException $e) {
            }
        }

        $archive->close();

        return $file;
    }

    /**
     * Similar to createFromDirectory but encrypts each file using
     * the password provided.
     *
     *
     * @return string
     *
     * @throws ArchiveCreationException
     */
    public function encryptedArchive($directory, $root, string $password)
    {
        $archive = $this->open($file = $directory.'.zip');
        $archive->setPassword($password);

        foreach ($this->mapFiles($directory, $root) as $relative => $fullPath) {
            try {
                $archive->addFile($fullPath, $relative);
                $archive->setEncryptionName($relative, ZipArchive::EM_AES_256);
                if ($this->skipCompression($relative)) {
                    $archive->setCompressionName($relative, ZipArchive::CM_STORE);
                }
            } catch (FileExistsException $e) {
            }
        }

        $archive->close();

        return $file;
    }

    protected function open($file): ZipArchive
    {
        $archive = new ZipArchive;
        $archive->open($file, ZipArchive::CREATE | ZipArchive::OVERWRITE);

        return $archive;
    }

    /**
     * Map all files in the root directory (recursively).
     *
     * This method is required for two reasons:
     *  1. We want to recursively add all files/folders to the ZIP
     *  2. We don't want to include the full OS path to each file inside the ZIP
     *
     * @param  string  $zipRoot
     * @param  string  $rootFolder
     * @param  array  $files
     * @return array
     *
     * @throws ArchiveCreationException
     */
    private function mapFiles($zipRoot, $rootFolder, $files = [])
    {
        $root = new \DirectoryIterator($zipRoot);

        foreach ($root as $folder) {
            if (! $folder->isDot() && $folder->isDir()) {
                $newRoot = $rootFolder.'/'.$folder->getBasename();
                $files = array_merge($files, $this->mapFiles($folder->getRealPath(), $newRoot, $files));
            } elseif ($folder->isFile() && $file = $folder) {
                $relative = $rootFolder.'/'.$file->getBasename();
                $files[$relative] = $file->getRealPath();
            }
        }

        if (empty($files)) {
            throw new ArchiveCreationException('Cannot create an empty archive');
        }

        return $files;
    }

    protected function skipCompression($file)
    {
        return in_array(
            file_extension($file),
            array_merge(
                array_keys(config('filetypes.archives')),
                array_keys(config('filetypes.video'))
            )
        );
    }
}
