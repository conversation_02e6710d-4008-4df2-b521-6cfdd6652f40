<?php

namespace AwardForce\Library\Composers;

use AwardForce\Library\Html\BreadcrumbResolver;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Menu\Services\Generators\MenuGenerator;
use Illuminate\Contracts\View\View;
use Platform\Events\EventDispatcher;

class MainMenuComposer
{
    use EventDispatcher;

    public function __construct(
        private BreadcrumbResolver $breadcrumbs,
        private MenuGenerator $mainMenuGenerator
    ) {
    }

    public function compose(View $view)
    {
        $generatedMenu = $this->mainMenuGenerator->generate();

        $view->contexts = collect($generatedMenu->contextMenu());
        $view->baseMainMenu = collect($generatedMenu->mainMenu());
        $view->currentBreadcrumb = $this->breadcrumbs->current();
        VueData::registerTranslations(['menu.home', 'miscellaneous.updates', 'ecommerce.cart.titles.header']);
        VueData::registerRoutes(['updates.unread.count', 'cart.items.count']);
    }
}
