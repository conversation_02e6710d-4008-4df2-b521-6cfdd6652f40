<?php

namespace AwardForce\Library\Composers;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Services\Emulation\UserEmulatorService;
use Mockery as m;
use Platform\Features\Features;
use Platform\Menu\Manager as ManagerMenu;
use Platform\Menu\Menu;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class UserMenuComposerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    protected function init()
    {
        current_account()->defineFeatures(new Features([new \Platform\Features\Feature('organisations', 'enabled')]));
    }

    public function testUserMenuForProgramManager(): void
    {
        $user = $this->setupUserWithRole('ProgramManager');
        $manager = new Manager;
        $manager->set($this->consumer($user));
        app()->instance(ManagerMenu::class, $menu = m::spy(ManagerMenu::class));
        (new UserMenuComposer($manager))->compose();

        $menu->shouldHaveReceived('register')->once()->withArgs(function (Menu $menu) {
            $this->assertSame(8, count($menu->children()));
            $texts = collect($menu->children())->pluck('text');
            $this->assertTrue($texts->contains(trans('header.profile')));
            $this->assertTrue($texts->contains(trans('header.my_af')));
            $this->assertTrue($texts->contains(trans('downloads.titles.main')));
            $this->assertTrue($texts->contains(trans('header.help')));
            $this->assertTrue($texts->contains(trans('header.logout')));
            $this->assertTrue($texts->contains(trans('organisations.titles.main')));
            $this->assertTrue($texts->contains('')); // Divider item

            return true;
        });
    }

    public function testUserMenuForJudge(): void
    {
        $user = $this->setupUserWithRole('Judge');
        $manager = new Manager;
        $manager->set($this->consumer($user));
        app()->instance(ManagerMenu::class, $menu = m::spy(ManagerMenu::class));
        (new UserMenuComposer($manager))->compose();

        $menu->shouldHaveReceived('register')->once()->withArgs(function (Menu $menu) {
            $this->assertSame(4, count($menu->children()));
            $texts = collect($menu->children())->pluck('text');
            $this->assertTrue($texts->contains(trans('header.profile')));
            $this->assertFalse($texts->contains(trans('downloads.titles.main')));
            $this->assertTrue($texts->contains(trans('header.logout')));
            $this->assertTrue($texts->contains(trans('organisations.titles.main')));
            $this->assertTrue($texts->contains('')); // Divider item

            return true;
        });
    }

    public function testUserMenuForEntrant(): void
    {
        $user = $this->setupUserWithRole('Entrant');
        $manager = new Manager;
        $manager->set($this->consumer($user));
        app()->instance(ManagerMenu::class, $menu = m::spy(ManagerMenu::class));
        (new UserMenuComposer($manager))->compose();

        $menu->shouldHaveReceived('register')->once()->withArgs(function (Menu $menu) {
            $this->assertSame(4, count($menu->children()));
            $texts = collect($menu->children())->pluck('text');
            $this->assertTrue($texts->contains(trans('header.profile')));
            $this->assertFalse($texts->contains(trans('downloads.titles.main')));
            $this->assertTrue($texts->contains(trans('header.logout')));
            $this->assertTrue($texts->contains(trans('organisations.titles.main')));
            $this->assertTrue($texts->contains('')); // Divider item

            return true;
        });
    }

    public function testHideMyAfWhenInPreviewMode(): void
    {
        $emulator = m::mock(UserEmulatorService::class);
        $emulator->shouldReceive('active')->andReturn(true);
        $emulator->shouldReceive('emulatingUser')->andReturn($this->muffin(User::class));
        app()->instance(UserEmulatorService::class, $emulator);
        $user = $this->setupUserWithRole('ProgramManager');
        $manager = new Manager;
        $manager->set($this->consumer($user));
        app()->instance(ManagerMenu::class, $menu = m::spy(ManagerMenu::class));
        (new UserMenuComposer($manager))->compose();

        $menu->shouldHaveReceived('register')->once()->withArgs(function (Menu $menu) {
            $this->assertSame(7, count($menu->children()));
            $texts = collect($menu->children())->pluck('text');
            $this->assertFalse($texts->contains(trans('header.my_af')));

            return true;
        });
    }

    public function testUserMenuBilling(): void
    {
        $user = $this->setupUserWithRole('ProgramManager');
        $manager = new Manager;
        $manager->set($this->consumer($user));

        current_account()->subscriptionId = 'aSubscriptionId';
        current_account()->subscriptionProvider = 'awardforce';

        app()->instance(ManagerMenu::class, $menu = m::spy(ManagerMenu::class));
        (new UserMenuComposer($manager))->compose();

        $menu->shouldHaveReceived('register')->once()->withArgs(function (Menu $menu) {
            $this->assertSame(8, count($menu->children()));
            $texts = collect($menu->children())->pluck('text');
            $this->assertTrue($texts->contains(trans('header.billing')));

            return true;
        });
    }

    public function testItHasOrganisationsMenuItemWhenFeatureIsEnabled(): void
    {
        $this->setupUserWithRole('ProgramManager', true);
        Feature::shouldReceive('enabled')
            ->with('organisations')
            ->andReturn(true);

        app()->instance(ManagerMenu::class, $menu = m::spy(ManagerMenu::class));

        (new UserMenuComposer(consumer()))->compose();

        $menu->shouldHaveReceived('register')->once()->withArgs(function (Menu $menu) {
            $texts = collect($menu->children())->pluck('text');
            $this->assertTrue($texts->contains(trans('organisations.titles.main')));

            return true;
        });
    }
}
