<?php

namespace AwardForce\Library\Bus\Pipeline;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Bus\WithoutContext;
use AwardForce\Library\Context\Context;
use AwardForce\Library\Context\Contextual;
use AwardForce\Library\Request\RequestAudit;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Authentication\Services\Emulator\Facades\JediEmulator;
use AwardForce\Modules\Authentication\Services\Emulator\Values\Jedi;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Closure;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Platform\Bus\Pipeline\QueuedOrSync;

class QueueContext
{
    use QueuedOrSync;

    public function handle($command, Closure $next)
    {
        if ($this->isSync($command) || $command instanceof WithoutContext) {
            return $next($command);
        }

        if ($command instanceof Contextual) {
            $this->restore($command->context());
        }

        return $next($command);
    }

    private function restore(Context $context): void
    {
        Log::info('Current account is being set to ['.$context->account->id.']');
        CurrentAccount::set(translate($context->account));
        Consumer::set($context->consumer);
        SeasonFilter::set($context->showAllSeasons ? SeasonFilterService::FILTER_ALL : $context->seasonId);
        FormSelector::set($context->showAllForms ? FormSelector::FILTER_ALL : $context->formId);
        URL::forceScheme('https');
        URL::forceRootUrl($context->account->redirectUrl());
        RequestAudit::set($context->request);
        if ($context->jediId) {
            JediEmulator::setJedi(new Jedi($context->jediId, $context->jediEmail));
        }

        if ($context->sessionId) {
            session()->setId($context->sessionId);
        }
    }
}
