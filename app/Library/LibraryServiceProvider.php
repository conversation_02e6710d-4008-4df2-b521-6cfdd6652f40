<?php

namespace AwardForce\Library;

use AwardForce\Application;
use AwardForce\Library\Authentication\LoginLimiter;
use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Cloud\Aws\Adapters\AwsCredentialsAdapter;
use AwardForce\Library\Html\Sanitiser\AddDataRedirector;
use AwardForce\Library\Html\Sanitiser\FilterIframe;
use AwardForce\Library\Html\VueDataService;
use AwardForce\Library\Localisation\CurrentLocaleService;
use AwardForce\Library\Providers\Providable;
use AwardForce\Library\Request\RequestAuditService;
use AwardForce\Library\Security\SecureUrlGenerator;
use AwardForce\Library\Trackers\Heap;
use AwardForce\Library\Trackers\Tracker;
use AwardForce\Library\Updates\Announcekit;
use AwardForce\Library\Updates\Updates;
use AwardForce\Library\UrlShortener\UrlShortenerService;
use AwardForce\Modules\Localisation\Services\DefaultLanguageCode;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Illuminate\Cache\RateLimiter;
use Illuminate\Contracts\Events\Dispatcher;
use Illuminate\Support\AggregateServiceProvider;
use Maatwebsite\Excel\Excel;
use Platform\Kessel\Hyperdrive;
use Platform\Strings\HtmlSanitiser;
use Platform\Strings\Sanitiser;
use Symfony\Component\HtmlSanitizer\HtmlSanitizer as SymfonyHtmlSanitizer;
use Symfony\Component\HtmlSanitizer\HtmlSanitizerConfig;

class LibraryServiceProvider extends AggregateServiceProvider
{
    use Providable;

    protected $aliases = [
        'CurrentLocale' => 'AwardForce\Library\Facades\CurrentLocale',
    ];
    protected $providers = [
        \AwardForce\Library\AIAgents\AIAgentsServiceProvider::class,
        \AwardForce\Library\Branding\BrandingServiceProvider::class,
        \AwardForce\Library\Database\DatabaseServiceProvider::class,
        \AwardForce\Library\Region\RegionServiceProvider::class,
        \AwardForce\Library\Html\HtmlServiceProvider::class,
        \AwardForce\Library\Html\Blade\BladeServiceProvider::class,
        \AwardForce\Library\Imports\ImportsServiceProvider::class,
        \AwardForce\Library\Mail\MailServiceProvider::class,
        \AwardForce\Library\PDF\PDFServiceProvider::class,
        \AwardForce\Library\Providers\EncryptionServiceProvider::class,
        \AwardForce\Library\Providers\LocalisationServiceProvider::class,
        \Platform\Menu\MenuServiceProvider::class,
        \AwardForce\Library\Broadcasting\BroadcastServiceProvider::class,
        \AwardForce\Library\View\ViewServiceProvider::class,
        \AwardForce\Library\Providers\ApiRoutingServiceProvider::class,
        \AwardForce\Library\Database\Firebase\FirebaseServiceProvider::class,
        \AwardForce\Library\Encrypter\EncrypterServiceProvider::class,
        \AwardForce\Library\Identifier\IdentifierServiceProvider::class,
    ];
    protected $files = [
        __DIR__.'/macros.php',
    ];

    /**
     * Register the service provider.
     *
     * @return void
     */
    public function register()
    {
        parent::register();

        $this->registerUtility();
        $this->registerConsumer();
        $this->registerCurrentLocale();
        $this->registerExcel();
        $this->registerKVStore();
        $this->registerLoginLimiter();
        $this->registerUrlRedirector();
        $this->registerUrlShortener();
        $this->registerVueData();
        $this->registerDefaultLanguageCode();
        $this->registerAwsCredentialsAdapter();
        $this->registerTrackers();
        $this->registerRequestTracking();
        $this->registerReleaseService();
        $this->registerHtmlSanitiser();
    }

    public function boot()
    {
        $this->registerCustomValidationRules();
        $this->bootFiles();
    }

    protected function registerConsumer()
    {
        $this->app->scoped('consumer', Manager::class);
    }

    protected function registerCurrentLocale()
    {
        $this->app->scoped('currentLocale', CurrentLocaleService::class);
    }

    /**
     * Register Utility binding
     *
     * @returns void
     */
    protected function registerUtility()
    {
        $this->app->scoped('AwardForce\Library\Utility');
    }

    protected function registerEventDispatcher()
    {
        $this->app->scoped(Dispatcher::class, 'Platform\Events\Dispatcher');
    }

    protected function registerCustomValidationRules()
    {
        // Add new validation rules
        $this->app['Illuminate\Validation\Factory']
            ->extend('arrayCountMin', 'AwardForce\Library\Validation\ArrayCountValidationRule@arrayCountMin');
        $this->app['Illuminate\Validation\Factory']
            ->extend('arrayCountMax', 'AwardForce\Library\Validation\ArrayCountValidationRule@arrayCountMax');
    }

    private function registerExcel()
    {
        $this->app->alias('excel', Excel::class);
    }

    private function registerKVStore()
    {
        $this->app->bind(\Platform\KVStore\Store::class, function ($app) {
            return new \Platform\KVStore\Consul($app['config']->get('services.consul.host'));
        });
    }

    private function registerLoginLimiter()
    {
        $this->app->scoped(LoginLimiter::class, function ($app) {
            return new LoginLimiter(
                app(RateLimiter::class),
                $app['config']->get('auth.temporary_lock.max_attempts'),
                $app['config']->get('auth.temporary_lock.duration') * 60 // Rate limiter uses seconds
            );
        });
    }

    /**
     * Re-binds the UrlGenerator with a SecureUrlGenerator.
     *
     * Unfortunately I had to duplicate all this initialisation code from the RoutingServiceProvider as I couldn't
     * find a way to re-use it in a cleaner way.
     */
    private function registerUrlRedirector()
    {
        $this->app->singleton('url', function (Application $app) {
            $routes = $app['router']->getRoutes();

            $app->instance('routes', $routes);

            $url = new SecureUrlGenerator(
                $routes,
                $app->rebinding(
                    'request',
                    $this->requestRebinder()
                ),
                $app['config']['app.asset_url']
            );

            $url->setSessionResolver(function () {
                return $this->app['session'] ?? null;
            });

            $url->setKeyResolver(function () {
                return $this->app->make('config')->get('app.key');
            });

            $app->rebinding('routes', function ($app, $routes) {
                $app['url']->setRoutes($routes);
            });

            return $url;
        });
    }

    protected function requestRebinder()
    {
        return function ($app, $request) {
            $app['url']->setRequest($request);
        };
    }

    protected function registerUrlShortener()
    {
        $this->app->scoped(UrlShortenerService::class, function ($app) {
            return new UrlShortenerService($app->make(Hyperdrive::class));
        });
    }

    private function registerVueData()
    {
        $this->app->scoped(VueDataService::class, function ($app) {
            return new VueDataService($app->make(SettingRepository::class));
        });
    }

    private function registerDefaultLanguageCode()
    {
        $this->app->scoped(DefaultLanguageCode::class, fn() => new DefaultLanguageCode);
    }

    private function registerAwsCredentialsAdapter(): void
    {
        $this->app->scoped(AwsCredentialsAdapter::class, fn() => new AwsCredentialsAdapter($this->app['config']));
    }

    private function registerTrackers(): void
    {
        $this->app->bind(Tracker::class, Heap::class);
    }

    private function registerRequestTracking()
    {
        $this->app->scoped(RequestAuditService::class, fn() => new RequestAuditService);
    }

    private function registerReleaseService(): void
    {
        $this->app->bind(Updates::class, Announcekit::class);
    }

    private function registerHtmlSanitiser(): void
    {
        // Keep this list of allowed elements and attributes in sync and alphabetical order with resources/assets/js/src/domain/utils/DataSanitiser.ts
        $this->app->scoped(Sanitiser::class, function () {
            $htmlSanitizerConfig = (new HtmlSanitizerConfig)
                ->withMaxInputLength(-1)
                ->allowRelativeLinks()
                ->allowSafeElements()
                ->allowElement('a', ['class', 'data-attr', 'data-redirector', 'href', 'name', 'rel', 'role', 'target', 'title'])
                ->allowElement('af-field', [])
                ->allowElement('b', [])
                ->allowElement('blockquote', [])
                ->allowElement('br', [])
                ->allowElement('button', ['aria-controls', 'aria-expanded', 'aria-haspopup', 'class', 'data-content', 'id', 'role', 'type'])
                ->allowElement('code', [])
                ->allowElement('div', ['aria-labelledby', 'class', 'data-oembed-url', 'id', 'role', 'style'])
                ->allowElement('del', [])
                ->allowElement('edit-interface-text', ['class', 'payload'])
                ->allowElement('em', [])
                ->allowElement('figcaption', [])
                ->allowElement('figure', ['class', 'style'])
                ->allowElement('h1', ['class', 'id'])
                ->allowElement('h2', ['class', 'id'])
                ->allowElement('h3', ['class', 'id'])
                ->allowElement('h4', ['class', 'id'])
                ->allowElement('h5', ['class', 'id'])
                ->allowElement('h6', ['class', 'id'])
                ->allowElement('i', ['aria-hidden', 'class', 'id'])
                ->allowElement('iframe', ['allow', 'allowfullscreen', 'aria-label', 'autoplay', 'encrypted-media', 'frameborder', 'height', 'mozallowfullscreen', 'src', 'style', 'title', 'webkitallowfullscreen', 'width'])
                ->allowElement('img', ['alt', 'class', 'height', 'loading', 'src', 'style', 'title', 'width'])
                ->allowElement('li', ['class', 'id'])
                ->allowElement('oembed', [])
                ->allowElement('ol', ['class', 'id', 'start'])
                ->allowElement('p', ['class', 'style'])
                ->allowElement('span', ['class'])
                ->allowElement('strong', [])
                ->allowElement('table', ['class', 'id'])
                ->allowElement('tbody', ['class', 'id'])
                ->allowElement('td', ['class', 'colspan', 'id', 'rowspan'])
                ->allowElement('tfoot', ['class', 'id'])
                ->allowElement('th', ['class', 'colspan', 'id', 'rowspan'])
                ->allowElement('thead', ['class', 'id'])
                ->allowElement('tr', ['class', 'id'])
                ->allowElement('u', [])
                ->allowElement('ul', ['class', 'id']);

            return new HtmlSanitiser(
                sanitiser: new SymfonyHtmlSanitizer($htmlSanitizerConfig),
                filters: [new AddDataRedirector, new FilterIframe]
            );
        });
    }
}
