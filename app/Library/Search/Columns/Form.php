<?php

namespace AwardForce\Library\Search\Columns;

use AwardForce\Library\Search\Traits\StrictDefaultLanguage;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use Platform\Search\ApiColumn;
use Platform\Search\ApiVisibility;
use Platform\Search\Defaults;

class Form extends TranslatedColumnWithFallback implements ApiColumn
{
    use StrictDefaultLanguage;

    /** @var bool */
    private $force;

    public function __construct($force = false)
    {
        $this->force = $force;
    }

    public function title()
    {
        return trans('form.selector.label');
    }

    public function name(): string
    {
        return 'form';
    }

    public function html($record)
    {
        return $this->value($record);
    }

    public function default(): Defaults
    {
        return $this->force || FormSelector::viewingAll() ? new Defaults('search') : new Defaults('export');
    }

    public function priority(): int
    {
        return 62;
    }

    public function sortable(): bool
    {
        return true;
    }

    public function fieldName(): string
    {
        return 'name';
    }

    public function joinColumn(): string
    {
        return 'form_id';
    }

    public function visible(): bool
    {
        return feature_enabled('multiform');
    }

    public function apiName()
    {
        return 'form';
    }

    public function apiValue($record)
    {
        return (string) ($record->form->slug ?? null);
    }

    public function apiVisibility(): ApiVisibility
    {
        return new ApiVisibility('all');
    }
}
