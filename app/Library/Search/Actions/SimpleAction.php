<?php

namespace AwardForce\Library\Search\Actions;

use Illuminate\Support\HtmlString;
use Platform\Features\Feature;

abstract class SimpleAction extends ActionOverflowAction
{
    private ?Feature $feature = null;

    public function __construct(protected string $routeResource, private string $permissionResource, private string $key = 'id')
    {
    }

    abstract public function action(): string;

    protected function permissionAction(): string
    {
        return $this->action();
    }

    public function feature(Feature $feature): static
    {
        $this->feature = $feature;

        return $this;
    }

    public function viewData($record): array
    {
        return [
            'resource' => $this->routeResource,
            'selected' => (string) $record->{$this->key},
            'record' => $record,
            'routeIdentifier' => $this->routeIdentifier(),
            'params' => [
                'redirect' => request()->fullUrl(),
            ],
        ];
    }

    protected function appliesFeature(): bool
    {
        return $this->feature->enabled()
            ? feature_enabled((string) $this->feature)
            : feature_disabled((string) $this->feature);
    }

    protected function appliesPermission(): bool
    {
        return \Consumer::can($this->permissionAction(), $this->permissionResource);
    }

    public function applies($record = null): bool
    {
        if (! $this->feature) {
            return $this->appliesPermission();
        }

        return $this->appliesPermission() && $this->appliesFeature();
    }

    public function render($record): HtmlString
    {
        return new HtmlString(view('partials.list-actions.'.$this->action(), $this->viewData($record))->render());
    }

    protected function routeIdentifier(): string
    {
        return 'slug';
    }
}
