<?php

namespace AwardForce\Library\Search\Actions;

use AwardForce\Modules\ReviewFlow\Data\ReviewTask;

class ResendReviewTaskNotificationActionTest extends SimpleActionTestBase
{
    public function action(): ActionOverflowAction
    {
        return new ResendReviewTaskNotificationAction('review-flow.task.manage', 'ReviewTask');
    }

    public function record()
    {
        return $this->muffin(ReviewTask::class);
    }
}
