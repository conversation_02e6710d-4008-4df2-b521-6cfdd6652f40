<?php

namespace AwardForce\Library\Search\Actions;

use AwardForce\Modules\ReviewFlow\Data\ReviewTask;

class ResetDecisionActionTest extends SimpleActionTestBase
{
    public function action(): ActionOverflowAction
    {
        return new ResetDecisionAction('review-flow.task.manage', 'EntriesAll');
    }

    public function record()
    {
        return $this->muffin(ReviewTask::class, [
            'actionTaken' => ReviewTask::ACTION_PROCEED,
        ]);
    }
}
