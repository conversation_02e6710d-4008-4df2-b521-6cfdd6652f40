<?php

namespace AwardForce\Library\Search\Filters;

use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use Illuminate\Support\Arr;
use Platform\Search\ApiVisibility;
use Platform\Search\Filters\ColumnatorFilter;
use Platform\Search\Filters\SearchFilterValidation;
use Platform\Search\Services\SearchFilterValidator;

class FormFilter implements ColumnatorFilter, SearchFilterValidation
{
    use SearchFilterValidator;

    public function __construct(
        protected array $input,
        protected ?string $field = null
    ) {
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        $field = ($this->field ?: $query->getModel()->getTable().'.form_id');

        $query->leftJoin('forms', function ($join) use ($field) {
            $join->on($field, '=', 'forms.id');
        })->whereNull('forms.deleted_at');

        if (! $this->viewingAll()) {
            $this->applyFormConditions($query, $field, $this->formId());
        }

        $this->limitFormTypes($query);

        return $query;
    }

    protected function applyFormConditions($query, $field, $formId)
    {
        $query->where($field, $formId);
    }

    protected function viewingAll()
    {
        $form = $this->input['form'] ?? null;

        if (is_api_consumer()) {
            return is_null($form) || $form == FormSelector::FILTER_ALL;
        }

        return ($form == FormSelector::FILTER_ALL) || FormSelector::viewingAll();
    }

    protected function limitFormTypes($query)
    {
        return $query;
    }

    protected function formId(): int
    {
        if (is_null($form = $this->input['form'] ?? null)) {
            $form = $this->defaultFormId();
        }

        return is_numeric($form) ? $form : id_from_slug($form, app(FormRepository::class));
    }

    protected function defaultFormId()
    {
        if (($form = FormSelector::get()) && $form->isReport()) {
            return FormSelector::setDefaultForSeason()->id;
        }

        return FormSelector::getId();
    }

    /**
     * Return true if this search filter applies to the search in any way.
     */
    public function applies(): bool
    {
        return is_multiform() && ! Arr::get($this->input, 'slug');
    }

    public function validateFilterName(string $filterName): bool
    {
        return $filterName == 'form';
    }

    public function validateFilterValue(string $filterName, string $filterValue)
    {
        if ($filterValue != FormSelector::FILTER_ALL) {
            $this->validateValueIsSlug($filterName, $filterValue);
        }
    }

    public function validateApiScope(ApiVisibility $scope)
    {
        return true;
    }
}
