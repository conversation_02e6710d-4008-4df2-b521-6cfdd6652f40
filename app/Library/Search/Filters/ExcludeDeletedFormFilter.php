<?php

namespace AwardForce\Library\Search\Filters;

use AwardForce\Library\Database\Eloquent\JoinAwareness;
use Platform\Search\Filters\ColumnatorFilter;

class ExcludeDeletedFormFilter implements ColumnatorFilter
{
    use JoinAwareness;

    public function __construct(protected $originTable)
    {
    }

    /**
     * Apply the given search filter to an Eloquent query.
     *
     * @return mixed
     */
    public function applyToEloquent($query)
    {
        if (! $this->tableJoined($query->getQuery(), 'forms')) {
            $query->join('forms', 'forms.id', "{$this->originTable}.form_id");
        }

        $query->whereNull('forms.deleted_at');

        return $query;
    }

    public function applies(): bool
    {
        return true;
    }
}
