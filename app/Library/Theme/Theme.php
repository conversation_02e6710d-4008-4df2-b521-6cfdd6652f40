<?php

namespace AwardForce\Library\Theme;

use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Theme\Repositories\ThemeRepository;

/**
 * The theme class acts as both a theme manager, for all required themes, as well as the interface through
 * which theme files can be requested (such as for header images).
 *
 * To use, simply inject the Theme class into your classes, and utilise the appropriate method. If, for
 * example you want to access a particular colour, use $theme->colour('colour-key'). If you'd like to
 * retrieve a file for a given file setting, use $theme->file('file-key').
 */
class Theme
{
    /**
     * @var ThemeRepository
     */
    private $theme;

    /**
     * @var FileRepository
     */
    private $files;

    /**
     * @var array
     */
    private $defaults;

    public function __construct(ThemeRepository $theme, FileRepository $files)
    {
        $this->theme = $theme;
        $this->files = $files;
        $this->defaults = config('theme.defaults.'.current_account_brand());
    }

    /**
     * Return the colour for a given theme setting, or grab a default.
     *
     * @param  string  $setting
     * @param  null  $default
     * @return null
     */
    public function colour($setting, $default = null)
    {
        $themeColour = $this->theme->getByKey($setting);

        return '#'.($themeColour && ! empty($themeColour->value) ? $themeColour->value : $default);
    }

    /**
     * Returns the colour for the primary button.
     *
     * @return string
     */
    public function primaryButton()
    {
        return $this->colour('primary-button', $this->defaults['colors']['primary']);
    }

    /**
     * Returns the colour for the secondary button.
     *
     * @return string
     */
    public function secondaryButton()
    {
        return $this->colour('secondary-button', $this->defaults['colors']['secondary']);
    }

    /**
     * Convert a hex value to RGB.
     *
     * Taken from: http://bavotasan.com/2011/convert-hex-color-to-rgb-using-php/
     *
     * @param  string  $hex
     * @param  int  $alpha
     * @return array
     */
    public function hex2rgb($hex, $alpha = 1)
    {
        $hex = str_replace('#', '', $hex);

        if (strlen($hex) == 3) {
            $r = hexdec(substr($hex, 0, 1).substr($hex, 0, 1));
            $g = hexdec(substr($hex, 1, 1).substr($hex, 1, 1));
            $b = hexdec(substr($hex, 2, 1).substr($hex, 2, 1));
        } else {
            $r = hexdec(substr($hex, 0, 2));
            $g = hexdec(substr($hex, 2, 2));
            $b = hexdec(substr($hex, 4, 2));
        }

        return "rgba($r, $g, $b, $alpha)";
    }

    /**
     * Will seek out a theme setting, and if one exists and a valid file record exists for that
     * setting value, will return the file object for that key.
     *
     * @param  string  $key
     * @return null|File
     */
    public function file($key)
    {
        $fileId = $this->$key;

        if (! $fileId) {
            return null;
        }

        return $this->files->getById($fileId);
    }

    /**
     * Magic get method to return any theme value, if the key exists.
     * If a default is required, use get().
     *
     * @param  string  $key
     */
    public function __get($key)
    {
        $themeSetting = $this->theme->getByKey(str_slug(snake_case($key)));

        if ($themeSetting) {
            return $themeSetting->value;
        }
    }
}
