<?php

namespace AwardForce\Library\Html\Sanitiser;

use Platform\Strings\SanitiserFilter;

class AddDataRedirector implements SanitiserFilter
{
    private const array VALID_HREF_PREFIXES = ['http://', 'https://', '/'];
    private const int MAX_CACHE_AGE = 10080; // a week in minutes

    public function handle(string $html): string
    {
        return preg_replace_callback(
            '/<a\s+[^>]*href="([^"]+)"[^>]*>/i',
            $this->parse(...),
            $html
        );
    }

    private function parse(array $matches): string
    {
        [$linkTag, $href] = $matches;

        if (! $this->hasEmptyDataRedirector($linkTag)) {
            return $linkTag;
        }

        if (! $this->isValidHref($href)) {
            return $linkTag;
        }

        return $this->setRedirectorData($href, $linkTag);
    }

    private function setRedirectorData(mixed $url, mixed $linkTag): string
    {
        $redirectUrl = external_url($url, self::MAX_CACHE_AGE);

        return preg_replace(
            '/\bdata-redirector(?:\s*=\s*"")?\b/i',
            'data-redirector="'.$redirectUrl.'"',
            $linkTag
        );
    }

    private function hasEmptyDataRedirector(string $linkTag): bool
    {
        return preg_match('/\bdata-redirector(?:\s*=\s*"")?\b/i', $linkTag)
            && ! preg_match('/\bdata-redirector\s*=\s*"[^"]+"/i', $linkTag);
    }

    private function isValidHref(string $url): bool
    {
        foreach (self::VALID_HREF_PREFIXES as $prefix) {
            if (str_starts_with($url, $prefix)) {
                return true;
            }
        }

        return false;
    }
}
