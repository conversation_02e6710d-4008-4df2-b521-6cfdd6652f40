<?php

namespace AwardForce\Library\Html\Sanitiser;

use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class AddDataRedirectorTest extends BaseTestCase
{
    use Laravel;

    public function testFillsOnlyEmptyDataRedirector(): void
    {
        $withEmptyAttr = '<a data-redirector href="https://example.com">Example</a>';
        $withFilledAttr = '<a data-redirector="https://already.set" href="https://example.com">Example</a>';
        $withoutAttr = '<a href="https://example.com">Example</a>';

        $expected = '<a data-redirector="'.external_url('https://example.com').'" href="https://example.com">Example</a>';

        $result = (new AddDataRedirector())->handle($withEmptyAttr.$withFilledAttr.$withoutAttr);

        $this->assertStringContainsString($expected, $result);
        $this->assertStringContainsString($withFilledAttr, $result);
        $this->assertStringContainsString($withoutAttr, $result);
    }

    #[TestWith(['<a data-redirector href="https://valid.com">Link</a>', true])]
    #[TestWith(['<a data-redirector href="http://valid.com">Link</a>', true])]
    #[TestWith(['<a data-redirector href="/relative/path">Link</a>', true])]
    #[TestWith(['<a target="_blank" rel="noopener noreferrer" data-redirector="" href="/user">here</a>', true])]
    #[TestWith(['<a target="_blank" rel="noopener noreferrer" data-redirector href="/dashboard">here</a>', true])]

    #[TestWith(['<a data-redirector href="mailto:<EMAIL>">Link</a>', false])]
    #[TestWith(['<a data-redirector href="tel:+123456789">Link</a>', false])]
    #[TestWith(['<a data-redirector href="javascript:void(0)">Link</a>', false])]
    #[TestWith(['<a data-redirector href="#anchor">Link</a>', false])]
    #[TestWith(['<a href="https://without-data-redirect-attributte">Link</a>', false])]
    #[TestWith(['<a data-redirector="https://with-data-redirector" href="https://valid.com">Link</a>', false])]
    public function testFillsRedirectorOnlyWhenEmptyAndHrefIsValid(string $input, bool $shouldFill): void
    {
        $output = (new AddDataRedirector())->handle($input);

        if ($shouldFill) {
            $this->assertMatchesRegularExpression(
                '/data-redirector="https?:\/\/.*?"\s+href="/',
                $output,
                "Expected data-redirector to be filled: $input"
            );
        } else {
            $this->assertSame($input, $output, "Expected input to remain unchanged: $input");
        }
    }
}
