<?php

use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\Uploader;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Illuminate\Support\HtmlString;
use Illuminate\Support\Str;

HTML::macro('textSanitised', function ($name, $value = null, $options = []) {
    return html()->text($name, is_array($value) || is_object($value) ? '' : $value)->attributes($options);
});

HTML::macro('multiselectSanitised', function ($name, $options, $selected = null, $parameters = [], $subset = [], $displayFilterBox = true) {
    return html()->multi_select($name, $options, is_array($selected) ? $selected : null, $parameters, $subset, $displayFilterBox);
});

HTML::macro('textareaSanitised', function ($name, $value = null, $options = []) {
    return html()->textarea($name, is_array($value) || is_object($value) ? '' : $value)->attributes($options);
});

/**
 * Builds a Season Selector form element.
 */
HTML::macro('seasonSelect', function ($name = 'seasonId', $selected = null) {
    $seasons = app(SeasonRepository::class)->getNonArchivedList();

    if ($this->model && ! empty($this->model->seasonId)) {
        $selected = $this->model->seasonId;
    }

    return html()->select($name, $seasons, ($selected ?: null))->attributes(['id' => 'seasonId', 'enhance']);
});

/**
 * Renders out the account theme CSS file. If the theme file exists, it will render the file out from the appropriate
 * storage location - whether that's S3 (cloudfront), local, or some other system.
 */
HTML::macro('accountTheme', function () {
    $account = CurrentAccount::get();

    if (($file = $account->cssFile)) {
        $cssUrl = 'https://'.config("services.aws.cloudfront.{$account->region}.domain").'/'.$file;

        if ($cssUrl) {
            return HTML::style($cssUrl, ['crossorigin']);
        }
    }
});

/**
 * Renders a page title for the required component.
 *
 * @param  array|string  $titles
 * @return string
 */
HTML::macro('pageTitle', function ($titles) {
    if (! is_array($titles)) {
        $titles = (array) $titles;
    }

    array_unshift($titles, current_account()->name);

    $titles = array_reverse(array_map(fn($title) => str_replace('&amp;', '&', e(strip_tags($title))), $titles));

    return implode(' | ', $titles);
});

/**
 * Determines the visibility based on whether or not the price is selectable.
 *
 * @param  bool  $selectable
 * @return string
 */
HTML::macro('priceVisibility', function ($selectable) {
    $langString = $selectable ? 'on_cart' : 'hidden';

    return trans('payments.prices.visibility.'.$langString);
});

/**
 * Renders the number of entries the tier applies to.
 *
 * @param  Tier  $tier
 * @return string
 */
HTML::macro('tierEntries', function ($tier) {
    if ($tier->appliesTo == 'first') {
        return trans('payments.tiers.applies-to.first-entries', ['entries' => $tier->entries]);
    }

    if ($tier->appliesTo == 'next') {
        return trans('payments.tiers.applies-to.next-entries', ['entries' => $tier->entries]);
    }

    return trans('payments.tiers.applies-to.remaining-entries');
});

/**
 * Displays consensus (Qualified/Failed/Undecided).
 *
 * @param  string  $consensus
 * @return string
 */
HTML::macro('upload', function ($label, $name, ?File $file = null, ?Uploader $uploader = null, $standalone = false) {
    $id = 'uploader-'.Str::random(8);

    return view('html.form.uploader.file', compact('id', 'label', 'name', 'file', 'uploader', 'standalone'));
});

/**
 * Genererates an icon, that can be optionally used as a tooltip trigger.
 *
 * @param  string  $icon
 * @param  string  $tooltipContent
 * @return string
 */
HTML::macro('icon', function ($icon, $tooltipContent = null, $toolTipBehaviour = 'click') {
    if ($tooltipContent) {
        return new HtmlString('<span class="af-icons af-icons-'.$icon.' trigger-tooltip" data-content="'.$tooltipContent.'" data-trigger="'.$toolTipBehaviour.'"></span>');
    }

    return new HtmlString('<span class="af-icons af-icons-'.$icon.'"></span>');
});

/**
 * Returns the appropriate view dependent on the view at the time.
 *
 * @param  string  $resource
 * @param  array  $params
 * @param  array  $labels
 * @return string
 */
HTML::macro('archive', function ($resource, $params = [], $labels = []) {
    if (! Request::filled('archived') || Request::get('archived') == 'none') {
        return view('partials.list-actions.archiver', compact('resource', 'params', 'labels'));
    }

    return view('partials.list-actions.unarchiver', compact('resource', 'params'));
});

/**
 * Same as the HTML::userLink macro, without the link.
 * show the user's full name, if the user does not have a current
 * membership to the application, they will be shown as deleted.
 *
 * @param  User|null  $user
 * @return string
 */
HTML::macro('userName', function (?User $user = null) {
    if (! $user) {
        return '';
    }

    if ($user->guest()) {
        return trans('users.guest').' '.$user->slug;
    }

    $output = $user->fullName();

    if (! $user->currentMembership) {
        return deleted($output);
    }

    return $output;
});

/**
 * Render the print button html
 *
 * @param  array  $attributes
 * @return string
 */
HTML::macro('printButton', function ($attributes = []) {
    $htmlStringAttributes = new HtmlString((new \Spatie\Html\Attributes())->setAttributes($attributes)->render());

    return view('html.buttons.print-button', compact('htmlStringAttributes'))->render();
});
