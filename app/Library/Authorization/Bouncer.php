<?php

namespace AwardForce\Library\Authorization;

use AwardForce\Modules\Identity\Roles\Contracts\AuthoriserInterface;
use Illuminate\Support\Str;

/**
 * Class Bouncer
 *
 * The bouncer acts like a club bouncer. It'll ask for the identity of a given consumer
 * and then based on their identity and permissions, and the route that they are trying to access
 * will determine whether or not they are allowed in.
 */
final class Bouncer
{
    /**
     * @var string
     */
    private $method;

    /**
     * @var string
     */
    private $controller;

    /**
     * Construct using the request method.
     *
     * @param  string  $method
     * @param  string  $controller
     */
    public function __construct($method, $controller)
    {
        $this->method = Str::lower($method);
        $this->controller = $controller;
    }

    /**
     * Returns true if the consumer is permitted to enter based on their permissions,
     * and the required permission set for entry.
     *
     * @return bool
     */
    public function permits(AuthoriserInterface $consumer)
    {
        return array_reduce($this->resources(), function ($result, $resource) use ($consumer) {
            return $result && $consumer->can($this->action(), $resource);
        }, true);
    }

    /**
     * Defines the action to be used for the permission check, based off the method.
     *
     * @return string
     */
    public function action()
    {
        $actionMap = [
            'get' => 'view',
            'head' => 'view',
            'post' => 'create',
            'put' => 'update',
            'delete' => 'delete',
        ];

        return $actionMap[$this->method];
    }

    /**
     * Returns the reosurce to be used for the permission check.
     *
     * @return string
     */
    public function resources()
    {
        $controller = $this->controller;

        if (class_exists($controller) && isset($controller::$resource)) {
            $resource = $controller::$resource;
        } else {
            $class = class_basename($controller);
            $resource = Str::plural(str_replace('Controller', '', $class));
        }

        return array_flatten([$resource]);
    }
}
