<?php

namespace AwardForce\Library;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Facades\Platform\Strings\Output;
use HTML;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

trait VariableSubstitutions
{
    use CommonVariableSubstitutions;

    protected bool $allowMarkdown = true;

    /**
     * Simple template variable replacement.
     */
    protected function replaceVars(string $content, array $data, ?string $language = null, $trigger = null, bool $applyInnerStyle = false)
    {
        $this->language = $language;

        foreach ($data as $key => $value) {
            if ($this->hasInnerMarkdown($key, $trigger)) {
                $value = Output::html($value);
            }

            if ($applyInnerStyle && $view = $this->getInnerStyle($key)) {
                $value = inline_styles(Output::html($value), view($view)->render());
            }

            $content = preg_replace('/({|%7B)'.snake_case($key).'(}|%7D)/', str_replace('$', '\$', $value ?? ''), $content);
        }

        $content = $this->matchEntryFields($content, $data);
        $content = $this->matchApplicationFields($content, $data);
        $content = $this->matchUserFields($content, $data);
        $content = $this->matchContractFields($content, $data);
        $content = $this->matchReportFields($content, $data);

        return $content;
    }

    public function allowMarkdown(bool $allow): static
    {
        $this->allowMarkdown = $allow;

        return $this;
    }

    private function replaceFieldTag(string $content, string $key, Field $field): string
    {
        if ($this->allowMarkdown && $field->useMarkdown()) {
            return str_replace("{{$key}}", Output::html($field->value), $content);
        }

        if (is_array($field->value)) {
            return str_replace("{{$key}}", $this->render($this->extractFieldValueFromArray($field->value)), $content);
        }

        return str_replace("{{$key}}", $this->render($field->value), $content);
    }

    /**
     * Returns a renderable version of a user's field input.
     *
     * @param  string  $input
     * @return string
     */
    private function render($input)
    {
        if ($input instanceof Htmlable) {
            return $input->toHtml();
        }

        return e($input);
    }

    /**
     * Check if template contains a placeholder
     */
    protected function detectPlaceholders(string $content, array $placeholders): bool
    {
        return Str::contains($content, array_map(fn($placeholder) => '{'.snake_case($placeholder).'}', $placeholders));
    }

    /**
     * Can be used for any value modifications we need
     *
     * @return Field
     */
    private function modifyValue(Field $field)
    {
        $locale = setting('default-locale') ?: 'international';
        $value = is_array($field->value) ? $this->extractFieldValueFromArray($field->value) : $field->value;

        match ($field->type) {
            'date' => $field->value = HTML::localisedDate($value, $locale),
            'datetime' => $field->value = HTML::localisedDateTime($value, $locale),
            'time' => $field->value = HTML::localisedTime($value, $locale),
            'checkboxlist', 'checkbox', 'drop-down-list', 'radio' => $field->value = $field->value !== null ? HTML::formatFieldValue(translate($field), $this->language) : null,
            'table' => $field->value = $field->getConfiguration()->forMarkdown(),
            'currency' => $field->value = format_currency($value, $field->currency),
            default => $field
        };

        return $field;
    }
}
