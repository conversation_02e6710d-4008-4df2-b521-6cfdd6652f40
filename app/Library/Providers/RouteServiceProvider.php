<?php

namespace AwardForce\Library\Providers;

use AwardForce\Library\Authorization\TempConsumer;
use AwardForce\Library\Imports\ImportRepository;
use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Agreements\Models\AgreementRepository;
use AwardForce\Modules\AllocationPayments\Repositories\AllocationPaymentsRepository;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Awards\Data\AwardRepository;
use AwardForce\Modules\Broadcasts\Contracts\BroadcastRepository;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Comments\Models\CommentRepository;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Content\Terms\Facades\Term;
use AwardForce\Modules\Documents\Repositories\DocumentRepository;
use AwardForce\Modules\DocumentTemplates\Repositories\DocumentTemplateRepository;
use AwardForce\Modules\Ecommerce\Cart\Database\Repositories\CartRepository;
use AwardForce\Modules\Ecommerce\Orders\Data\OrderRepository;
use AwardForce\Modules\Entries\Contracts\ContractRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Forms\Collaboration\Repositories\CollaboratorsRepository;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use AwardForce\Modules\Funding\Data\FundRepository;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use AwardForce\Modules\Grants\Repositories\GrantStatusRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Integrations\Data\IntegrationRepository;
use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\Panels\Models\PanelRepository;
use AwardForce\Modules\PaymentMethods\Repositories\PaymentMethodsRepository;
use AwardForce\Modules\Payments\Repositories\DiscountRepository;
use AwardForce\Modules\Payments\Repositories\PriceRepository;
use AwardForce\Modules\Payments\Repositories\TaxRepository;
use AwardForce\Modules\Payments\Repositories\TierRepository;
use AwardForce\Modules\PaymentScheduleTemplates\Repositories\PaymentScheduleTemplateRepository;
use AwardForce\Modules\Reports\ReportRepository;
use AwardForce\Modules\ReviewFlow\Data\ReviewStageRepository;
use AwardForce\Modules\ReviewFlow\Data\ReviewTaskRepository;
use AwardForce\Modules\ReviewFlow\Data\Token;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\ScoringCriteria\Repositories\ScoringCriterionRepository;
use AwardForce\Modules\Search\SearchRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Tags\Contracts\TagRepository;
use Eloquence\Behaviours\Slug;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Facades\Route;

class RouteServiceProvider extends ServiceProvider
{
    protected $namespace = 'AwardForce\Http\Controllers';

    /**
     * A collection of the application's route middleware (previously known as Filters in L4)
     *
     * @var array
     */
    protected $routeMiddleware = [
        'awardforce.account.exception' => 'AwardForce\Library\Middleware\AccountExceptionMiddleware',
        'awardforce.auth' => 'AwardForce\Library\Middleware\AuthMiddleware',
        'awardforce.install' => 'AwardForce\Library\Middleware\InstallationMiddleware',
        'authentication.exceptions' => 'AwardForce\Modules\Authentication\Middleware\ExceptionHandler',
        'authentication.resolve-auth-token' => 'AwardForce\Modules\Authentication\Middleware\ResolveAuthToken',
        'authentication.resolve-social-auth-token' => 'AwardForce\Modules\Authentication\Middleware\ResolveSocialAuthToken',
        'user.confirmed' => 'AwardForce\Modules\Identity\Users\Middleware\UserConfirmed',
        'saml.auth.redirector' => 'AwardForce\Modules\Authentication\Middleware\SamlAuthRedirector::class',
    ];

    /**
     * Define your route model bindings, pattern filters, etc.
     *
     * @param  \Illuminate\Routing\Router  $router
     * @return void
     */
    public function boot()
    {
        parent::boot();

        foreach ($this->routeMiddleware as $key => $middleware) {
            $this->app['router']->aliasMiddleware($key, $middleware);
        }

        Route::bind('account', function ($slug) {
            return app(AccountRepository::class)->requireBySlug($slug);
        });

        Route::bind('agreement', function ($slug) {
            return app(AgreementRepository::class)->requireBySlug($slug);
        });

        Route::bind('allocationPayment', function ($slug) {
            return app(AllocationPaymentsRepository::class)->requireBySlug($slug);
        });

        Route::bind('assignment', function ($id) {
            return app(AssignmentRepository::class)->requireById($id);
        });

        Route::bind('award', function ($slug) {
            return app(AwardRepository::class)->requireBySlug($slug);
        });

        Route::bind('broadcast', function ($slug) {
            return app(BroadcastRepository::class)->requireBySlug($slug);
        });

        Route::bind('cart', function ($slug) {
            return app(CartRepository::class)->forSlug(new Slug($slug));
        });

        Route::bind('category', function ($slug) {
            return app(CategoryRepository::class)->requireBySlugWithRelations($slug);
        });

        Route::bind('chapter', function ($slug) {
            return app(ChapterRepository::class)->requireBySlug($slug);
        });

        Route::bind('commentInstance', function ($slug) {
            return app(CommentRepository::class)->requireBySlug($slug);
        });

        Route::bind('contentBlock', function ($slug) {
            return app(ContentBlockRepository::class)->requireBySlug($slug);
        });

        Route::bind('contract', function ($slug) {
            return app(ContractRepository::class)->requireBySlug($slug);
        });

        Route::bind('discount', function ($slug) {
            return app(DiscountRepository::class)->requireBySlug($slug);
        });

        Route::bind('document', function ($slug) {
            return app(DocumentRepository::class)->requireBySlug($slug);
        });

        Route::bind('documentTemplate', function ($slug) {
            return app(DocumentTemplateRepository::class)->requireBySlug($slug);
        });

        Route::bind('duplicateEntry', function ($slug) {
            return app(EntryRepository::class)->requireBySlug($slug);
        });

        Route::bind('entry', function ($slug) {
            return app(EntryRepository::class)->requireBySlug($slug);
        });

        Route::bind('collaborator', function ($value) {
            return app(CollaboratorsRepository::class)->requireBySlug($value);
        });

        Route::bind('grantReport', function ($slug) {
            return app(GrantReportRepository::class)->requireBySlug($slug);
        });

        Route::bind('exportLayout', function ($slug) {
            return app(SearchRepository::class)->requireBySlug($slug);
        });

        Route::bind('field', function ($slug) {
            return app(FieldRepository::class)->requireBySlug($slug);
        });

        Route::bind('file', function ($fileId) {
            return app(FileRepository::class)->requireById($fileId);
        });

        Route::bind('form', function ($slug) {
            return app(FormRepository::class)->requireBySlug($slug);
        });

        Route::bind('fund', function ($slug) {
            return app(FundRepository::class)->requireBySlug($slug);
        });

        Route::bind('fundAllocation', function ($slug) {
            return app(AllocationRepository::class)->requireBySlug($slug);
        });

        Route::bind('grantStatus', function ($slug) {
            return app(GrantStatusRepository::class)->requireBySlug($slug);
        });

        Route::bind('import', function ($slug) {
            return app(ImportRepository::class)->requireBySlug($slug);
        });

        Route::bind('integration', function ($slug) {
            return app(IntegrationRepository::class)->requireBySlug($slug);
        });

        Route::bind('notification', function ($slug) {
            return app(NotificationRepository::class)->requireBySlug($slug);
        });

        Route::bind('order', function ($slug) {
            return app(OrderRepository::class)->requireBySlug($slug);
        });

        Route::bind('panel', function ($slug) {
            return app(PanelRepository::class)->requireBySlugWithRelations($slug);
        });

        Route::bind('paymentMethod', function ($slug) {
            return app(PaymentMethodsRepository::class)->requireBySlug($slug);
        });

        Route::bind('paymentScheduleTemplate', function ($slug) {
            return app(PaymentScheduleTemplateRepository::class)->requireBySlug($slug);
        });

        Route::bind('price', function ($slug) {
            return app(PriceRepository::class)->requireBySlug($slug);
        });

        Route::bind('report', function ($slug) {
            return app(ReportRepository::class)->requireBySlug($slug);
        });

        Route::bind('registrationRole', function ($slug) {
            return app(RoleRepository::class)->requireRegistration($slug);
        });

        Route::bind('reviewStage', function ($slug) {
            return app(ReviewStageRepository::class)->requireBySlug($slug);
        });

        Route::bind('reviewTask', function ($slug) {
            return app(ReviewTaskRepository::class)->requireByToken(new Token($slug));
        });

        Route::bind('role', function ($slug) {
            return app(RoleRepository::class)->requireBySlug($slug);
        });

        Route::bind('round', function ($slug) {
            return app(RoundRepository::class)->requireBySlug($slug);
        });

        Route::bind('scoringCriterion', function ($slug) {
            return app(ScoringCriterionRepository::class)->requireBySlugWithRelations($slug);
        });

        Route::bind('scoreSet', function ($slug) {
            return app(ScoreSetRepository::class)->requireBySlugWithRelations($slug);
        });

        Route::bind('search', function ($slug) {
            return app(SearchRepository::class)->requireBySlug($slug);
        });

        Route::bind('season', function ($slug) {
            return app(SeasonRepository::class)->requireBySlug($slug);
        });

        Route::bind('tab', function ($slug) {
            return app(TabRepository::class)->requireBySlug($slug);
        });

        Route::bind('tag', function ($slug) {
            return app(TagRepository::class)->requireBySlug($slug);
        });

        Route::bind('tax', function ($slug) {
            return app(TaxRepository::class)->requireBySlug($slug);
        });

        Route::bind('tier', function ($slug) {
            return app(TierRepository::class)->requireBySlug($slug);
        });

        Route::bind('term', function ($key) {
            return Term::get($key);
        });

        Route::bind('user', function ($slug) {
            return app(UserRepository::class)->requireBySlug($slug);
        });

        $this->bootLimiters();
    }

    /**
     * Define the routes for the application.
     *
     * @return void
     */
    public function map()
    {
        $this->mapApiRoutes();
        $this->mapAccountlessRoutes();
        $this->mapKesselRoutes();
        $this->mapAuthRoutes();
        $this->mapWebRoutes();
        $this->mapInternalRoutes();
        $this->mapAssetRoutes();
    }

    private function mapApiRoutes()
    {
        Route::middleware('api')
            ->group(base_path('routes/api.php'));
    }

    private function mapAccountlessRoutes()
    {
        Route::middleware([])
            ->group(base_path('routes/accountless.php'));
    }

    private function mapKesselRoutes()
    {
        Route::middleware(['bindings', 'kessel'])
            ->group(base_path('routes/kessel.php'));
    }

    private function mapAuthRoutes()
    {
        Route::middleware('web')
            ->group(base_path('routes/auth.php'));
    }

    protected function mapWebRoutes()
    {
        Route::middleware(['web', 'account'])
            ->namespace($this->namespace)
            ->group(base_path('routes/web.php'));
    }

    protected function mapAssetRoutes()
    {
        Route::namespace($this->namespace)
            ->group(base_path('routes/assets.php'));
    }

    protected function mapInternalRoutes()
    {
        Route::namespace($this->namespace)
            ->group(base_path('routes/internal.php'));
    }

    private function bootLimiters(): void
    {
        RateLimiter::for('auth.verify.generate', function () {
            return new Limit('auth.verify.generate.'.session()->get(TempConsumer::TEMP_CONSUMER_LOGIN, session()->getId()), 5, 15 * 60);
        });

        RateLimiter::for('auth.verify.confirm', function () {
            return new Limit('auth.verify.confirm.'.session()->get(TempConsumer::TEMP_CONSUMER_LOGIN, session()->getId()), 10, 15 * 60);
        });

        RateLimiter::for('auth.consent', function () {
            return new Limit('auth.consent.'.session()->getId(), 10, 60);
        });
    }
}
