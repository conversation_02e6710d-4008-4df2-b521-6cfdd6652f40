<?php

namespace AwardForce\Library\Database\Eloquent;

use AwardForce\Library\Exceptions\ModelDoesNotImplementConfigurationExporter;
use AwardForce\Library\Values\Services\Transformable;
use AwardForce\Library\Values\Services\ValueTransformer;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

abstract class Repository extends \Platform\Database\Eloquent\Repository
{
    /**
     * Restrict all queries by the current account (by default).
     *
     * @var bool
     */
    protected $restrictByAccount = true;

    /**
     * Relaxes the repository for a set of code execution and then returns it back to its default state.
     * Throws an error if $restrictByAccount is already false, as that would make use of this method
     * illegitimate and potentially dangerous.
     *
     * @return mixed
     *
     * @throws NonRestrictedRepository
     */
    public function relaxed(\Closure $callback)
    {
        if (! $this->restrictByAccount) {
            throw new NonRestrictedRepository;
        }

        try {
            $this->restrictByAccount = false;

            return $callback($this);
        } catch (\Throwable $e) {
            throw $e;
        } finally {
            $this->restrictByAccount = true;
        }
    }

    /**
     * Return a new query object to be executed.
     *
     * @return mixed
     */
    protected function getQuery()
    {
        $query = $this->model->newInstance()->newQuery();

        if ($this->restrictByAccount) {
            $query->where("{$this->model->getTable()}.account_id", '=', $this->currentAccountId());
        }

        return $query;
    }

    /**
     * Return the full records, or a list of values if $list is provided.
     * If $list is null, will use get()
     * If $list is string, will use lists('string')
     * if $list is array, will use lists($list[0], $list[1])
     *
     * @param  \Illuminate\Database\Query\Builder|\Illuminate\Database\Eloquent\Builder  $query
     * @param  string|array  $list
     * @return \Illuminate\Support\Collection
     */
    protected function getOrList($query, $list = null)
    {
        if (! $list) {
            return $query->get();
        }

        if (is_string($list)) {
            return $query->pluck($list);
        }

        return $query->pluck($list[0], $list[1]);
    }

    /**
     * A single method to return the currentAccountId. This is the account id that represents
     * the current request's account, domain.etc.
     *
     * @return int
     */
    protected function currentAccountId()
    {
        return current_account_id();
    }

    /**
     * Returns the id of the active season.
     *
     * @return int
     */
    protected function activeSeasonId()
    {
        return CurrentAccount::activeSeason()->id;
    }

    /**
     * Saves the resource provided to the database.
     *
     * @return resource
     */
    public function save($resource)
    {
        if (! $resource->exists && $this->restrictByAccount && ! $resource->accountId) {
            $resource->accountId = $this->currentAccountId();
        }

        return parent::save($resource);
    }

    public function getBySlugOrId($idOrSlug)
    {
        return is_numeric($idOrSlug) ? $this->getById($idOrSlug) : $this->getBySlug($idOrSlug);
    }

    /**
     * @throws ModelDoesNotImplementConfigurationExporter
     */
    public function configurationExport(?int $seasonId = null, ?int $formId = null)
    {
        if (! ($this->getModel() instanceof ConfigurationExporter)) {
            throw new ModelDoesNotImplementConfigurationExporter;
        }

        return $this->getQuery()
            ->forConfiguration()
            ->when($seasonId && $this->belongsToSeason(), function ($query) use ($seasonId) {
                $query->whereSeasonId($seasonId);
            })
            ->when($formId && $this->belongsToForm(), function ($query) use ($formId) {
                $query->whereFormId($formId);
            })
            ->orderBy('id')
            ->get();
    }

    private function belongsToSeason(): bool
    {
        return method_exists($this->getModel(), 'season') && $this->model->season() instanceof BelongsTo;
    }

    private function belongsToForm(): bool
    {
        return method_exists($this->getModel(), 'form') && $this->model->form() instanceof BelongsTo;
    }

    public function import($data)
    {
        $model = $this->getModel()->newInstance();
        foreach ($data as $key => $value) {
            if ($model->$key instanceof Transformable) {
                $value = (new ValueTransformer(get_class($model->$key), $value))->transform();
            }

            $model->$key = $value;
        }
        $this->save($model);

        return $model->fresh();
    }

    public function isSafe(): bool
    {
        return $this->restrictByAccount;
    }

    /**
     * @throws NonRestrictedRepository
     */
    public function destroyOrphanedByAccount(bool $dry, int $max = 100): int
    {
        $table = $this->model->getTable();

        $orphanIds = \DB::table($table)
            ->select("$table.id")
            ->leftJoin('accounts', 'accounts.id', "$table.account_id")
            ->whereNull('accounts.id')
            ->whereNotNull("$table.account_id")
            ->limit($max)
            ->pluck('id');

        if ($dry) {
            return $orphanIds->count();
        }

        return \DB::table($table)
            ->whereIn('id', $orphanIds)
            ->delete();
    }
}
