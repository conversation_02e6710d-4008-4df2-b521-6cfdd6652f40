<?php

namespace AwardForce\Library\Database\Eloquent\Caching;

use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use Platform\Database\Eloquent\Enums\TrashedMode;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Stubs\EloquentTabRepositoryWithCacheStub;

class RepositoryRequestCacheTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private EloquentTabRepositoryWithCacheStub $repository;
    private mixed $original;

    public function init()
    {
        $this->repository = new EloquentTabRepositoryWithCacheStub(new Tab);
        $this->original = app(TabRepository::class);
    }

    public function testItResolvesRepositoryMethodWithNoArguments()
    {
        $this->assertEquals($this->original->countAll(), $this->repository->requestCache()->countAll());
    }

    public function testResolvesRepositoryMethodWithArguments()
    {
        $this->assertEquals(0, $this->repository->requestCache()->methodWithArguments(1, true, 'test', ['test1', 'test2', ['asdas' => 'asdasdsad']], current_account()));
        $this->assertEquals(0, $this->repository->requestCache()->methodWithArguments(resources: []));
    }

    public function testItCachesCalls()
    {
        $cachedCount = $this->repository->requestCache()->countAll();
        $this->assertEquals($cachedCount, $this->repository->requestCache()->countAll());

        $this->muffin(Tab::class);
        $this->assertEquals($cachedCount, $this->repository->requestCache()->countAll());
        $this->assertEquals($cachedCount + 1, $this->original->countAll());
    }

    public function testRequestCacheKeyWithString(): void
    {
        $method = 'find';
        $args = ['1'];
        $expectedKey = current_account_id().':'.get_class($this->repository).':'.$method.':'.implode(':', $args);

        $this->assertEquals($expectedKey, $this->repository->requestCache()->getCacheKey($method, $args));
    }

    public function testRequestCacheKeyWithArray(): void
    {
        $method = 'findBy';
        $args = ['name', 'test', ['one', 'two']];
        $expectedKey = current_account_id().':'.get_class($this->repository).':'.$method.':name:test:one:two';

        $this->assertEquals($expectedKey, $this->repository->requestCache()->getCacheKey($method, $args));
    }

    public function testArgumentsKeyWithArrayArguments(): void
    {
        $arrayArgument = ['value1', 'value2', 'value3'];
        $expected = 'value1:value2:value3';

        $this->assertEquals($expected, $this->repository->requestCache()->getParameterKey([$arrayArgument]));
    }

    public function testArgumentsKeyWithNestedArrayArguments(): void
    {
        $multidimensionalArrayArgument = [['value1A', 'value2A'], 'value1', 'value3'];
        $expected = 'value1A:value2A:value1:value3';

        $this->assertEquals($expected, $this->repository->requestCache()->getParameterKey([$multidimensionalArrayArgument]));
    }

    public function testArgumentsKeyWithModelArguments(): void
    {
        $this->assertEquals(current_account_id(), $this->repository->requestCache()->getParameterKey([current_account()]));
    }

    public function testArgumentsKeyWithBooleanArguments(): void
    {
        $this->assertEquals('true', $this->repository->requestCache()->getParameterKey([true]));
        $this->assertEquals('false', $this->repository->requestCache()->getParameterKey([false]));
    }

    public function testArgumentsKeyWithMixedArguments(): void
    {
        $mixedArgument = ['value1', current_account(), ['value2', true], false];
        $expected = 'value1:'.current_account_id().':value2:true:false';

        $this->assertEquals($expected, $this->repository->requestCache()->getParameterKey($mixedArgument));
    }

    public function testItCanResolveBackedEnumArguments(): void
    {
        $enumArgument = BackedEnumStub::One;
        $expected = 'one';

        $this->assertEquals($expected, $this->repository->requestCache()->getParameterKey([$enumArgument]));
    }

    public function testItCanResolveUnitEnumArguments(): void
    {
        $enumArgument = UnitEnumStub::One;
        $expected = 'One';

        $this->assertEquals($expected, $this->repository->requestCache()->getParameterKey([$enumArgument]));
    }

    public function testItCanHandleChainedBuilderMethods(): void
    {
        $this->repository->requestCache()
            ->fields(['id'])
            ->trashed(TrashedMode::None)
            ->resource('testParameter')
            ->just('id');

        $expectedCacheKey = current_account_id().':'.$this->repository::class.':methodChain:fields:id:trashed:None:resource:testParameter:just:id';
        $this->assertEquals($expectedCacheKey, $this->repository->requestCache()->getCacheKeys()[0]);
    }
}

enum UnitEnumStub
{
    case One;
    case Two;
}

enum BackedEnumStub: string
{
    case One = 'one';
    case Two = 'two';
}
