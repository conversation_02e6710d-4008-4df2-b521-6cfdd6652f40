<?php

use AwardForce\Library\Authorization\ApiConsumer;
use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Database\Eloquent\HtmlTranslatable;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Facades\CurrentLocale;
use AwardForce\Library\Html\DeletedHtmlString;
use AwardForce\Library\PaymentSubscriptions\Contracts\PaymentSubscriptionGateway;
use AwardForce\Library\UrlShortener\UrlShortenerService;
use AwardForce\Library\Values\Amount;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Accounts\Contracts\DomainRepository;
use AwardForce\Modules\Accounts\Contracts\GlobalAccountRepository;
use AwardForce\Modules\Accounts\Contracts\SupportedCurrencyRepository;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Comments\Models\CommentCollection;
use AwardForce\Modules\Comments\Services\UserTags;
use AwardForce\Modules\Content\InterfaceText\Services\InterfaceTextService;
use AwardForce\Modules\Content\InterfaceText\Services\TranslationsWithoutOverrides;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Entries\Models\Link;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Localisation\Services\DefaultLanguageCode;
use AwardForce\Modules\Menu\Services\ContextService;
use AwardForce\Modules\Payments\Repositories\CurrencyRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use AwardForce\Modules\Settings\Enums\LocalIdShortcodeFormat;
use Eloquence\Behaviours\Slug;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Foundation\Application;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Facades\Route;
use Illuminate\Support\Str;
use Imgix\UrlBuilder;
use Platform\Categories\CategoryList;
use Platform\Categories\CategoryListGenerator;
use Platform\Categories\CategorySlugListGenerator;
use Platform\Http\GenericMessage;
use Platform\Menu\Context\Items\Guest;
use SebastianBergmann\Timer\Duration;
use Tectonic\LaravelLocalisation\Facades\Translator;

if (! function_exists('lang')) {
    /**
     * Manages the output for a translation of a given model object, and the required field. It will
     * use the language that is being used by the user and then return the value for that given field and
     * language code.
     *
     * @param  null|Model  $model
     * @param  string  $field
     * @param  string  $lang
     * @param  bool  $fallbackLang
     * @return string
     */
    function lang($model, $field, $lang = null, $fallbackLang = false, $emptyString = 'NTA')
    {
        if (is_null($model)) {
            return null;
        }

        if (is_null($lang)) {
            $translation = $model->$field;
        } else {
            $translation = $model->getTranslation($field, $lang);
        }

        if (is_array($translation)) {
            $fallbackTranslation = $fallbackLang && ! \Consumer::usesDefaultLanguage()
                ? $model->getTranslation($field, default_language_code())
                : [];
            foreach ($translation as $key => $part) {
                if (empty_string($part)) {
                    $translation[$key] = $fallbackTranslation[$key] ?? $emptyString;
                }
            }
        }

        if (! is_array($translation) && empty_string($translation)) {
            $translation = ! $fallbackLang ? $emptyString : $model->$field;
        }

        return $translation;
    }
}

if (! function_exists('account_lang')) {
    /**
     * Hack, temporary method to deal with language strings on the accounts pages for App managers (us).
     *
     * @TODO: remove!!!
     *
     * @param  string  $field
     * @return null|string
     */
    function account_lang(Account $account, $field)
    {
        if (is_null($account)) {
            return null;
        }

        $lang = 'en_GB';

        if (! $account->hasTranslation($field, $lang)) {
            $lang = $account->defaultLanguage()->code;
        }

        return $account->getTranslation($field, $lang);
    }
}

if (! function_exists('has_lang')) {
    /**
     * Simply checks to see whether or not a given field has a value for a required translation.
     *
     * @param  object  $model
     * @param  string  $field
     * @return bool
     *
     * @throws Exception
     */
    function has_lang($model, $field)
    {
        return $model->hasTranslation($field);
    }
}

if (! function_exists('mb_ucfirst')) {
    /**
     * Uppercase first letter of string in unicode safe way.
     *
     * @param  string  $string
     * @return string
     */
    function mb_ucfirst($string)
    {
        return mb_strtoupper(mb_substr($string, 0, 1)).mb_substr($string, 1);
    }
}

if (! function_exists('for_category_select')) {
    /**
     * Returns a select-friendly grouped array based on the collection of categories provided.
     *
     * @param  bool  $divisions Include category divisions in the list
     * @param  bool  $useSlugs
     * @return array
     */
    function for_category_select(Collection $categories, bool $divisions = false, $useSlugs = false)
    {
        $generator = $useSlugs ? app(CategorySlugListGenerator::class) : app(CategoryListGenerator::class);
        $categories = $generator->generate($categories, $divisions, true)->toArray();

        $noParent = $categories[CategoryList::NOPARENT];
        unset($categories[CategoryList::NOPARENT]);

        return ['' => ''] + $noParent + $categories;
    }
}

if (! function_exists('get_parent_categories_as_a_string')) {
    /**
     * Returns a string of friendly concatenated parent categories' names by a category.
     *
     * @param  string  $separator The separator after the string
     */
    function get_parent_categories_as_a_string(?Category $category, $separator = ': '): string
    {
        if (! $category || ! $category->parent_id) {
            return '';
        }

        return key(array_filter(for_category_select(collect([$category])))).$separator;
    }
}

if (! function_exists('message')) {
    /**
     * Wrapper helper method for the GenericMessage class.
     *
     * @param  string  $message
     * @param  string|null  $title
     * @param  string|null  $forwardTo
     * @param  string|null  $buttonText
     * @return GenericMessage
     */
    function message($message, $title = null, $forwardTo = null, $buttonText = null)
    {
        $messenger = new GenericMessage($message);

        if (! is_null($title)) {
            $messenger->withTitle($title);
        }

        if (! is_null($forwardTo)) {
            $messenger->forwardTo($forwardTo);
        }

        if (! is_null($buttonText)) {
            $messenger->withButtonText($buttonText);
        }

        return $messenger;
    }
}

if (! function_exists('file_extension')) {
    /**
     * Returns the file extension, with support for some nested types.
     *
     * @param  string  $file
     * @return string
     */
    function file_extension($file)
    {
        $parts = explode('.', mb_strtolower($file));
        $count = count($parts);

        if ($count < 2) {
            return '';
        }

        if ($parts[$count - 2] == 'tar') {
            return implode('.', array_slice($parts, $count - 2));
        }

        return $parts[$count - 1];
    }
}

if (! function_exists('file_url')) {
    /**
     * Returns the full url for the specified file.
     *
     * @return string
     */
    function file_url(File $file)
    {
        $config = config('filesystems.disks.'.config('filesystems.default').'.bucket_url');

        return $config.$file->file;
    }
}

if (! function_exists('form_field_id')) {
    /**
     * Used to generate form field ids based off field slug values.
     *
     * @param  string  $slug
     * @return string
     */
    function form_field_id($slug)
    {
        return "formField{$slug}";
    }
}

if (! function_exists('current_account')) {
    /**
     * Very simple helper method for retrieving the current account for the request.
     *
     * @return Account
     */
    function current_account()
    {
        return \AwardForce\Modules\Accounts\Facades\CurrentAccount::get();
    }
}

if (! function_exists('current_account_id')) {
    /**
     * Very simple helper method for retrieving the current account id.
     *
     * @return mixed
     */
    function current_account_id()
    {
        $account = current_account();

        return $account ? $account->id : null;
    }
}

if (! function_exists('current_account_slug')) {
    /**
     * Helper method for retrieving the current account slug.
     *
     * @return mixed
     */
    function current_account_slug()
    {
        $account = current_account();

        return $account ? (string) $account->slug : null;
    }
}

if (! function_exists('current_account_global_id')) {
    /**
     * Very simple helper method for retrieving the current account global id.
     *
     * @return mixed
     */
    function current_account_global_id()
    {
        $account = current_account();

        return $account ? $account->globalId : null;
    }
}

if (! function_exists('current_account_falcon_id')) {
    /**
     * Very simple helper method for retrieving the current account falcon id.
     *
     * @return mixed
     */
    function current_account_falcon_id()
    {
        $account = current_account();

        return $account ? $account->falconId : null;
    }
}

if (! function_exists('current_account_name')) {
    /**
     * Helper method for retrieving the current account name.
     *
     * @return string
     */
    function current_account_name()
    {
        return lang(current_account(), 'name');
    }
}

if (! function_exists('current_account_url')) {
    /**
     * Helper method for retrieving the current account url.
     *
     * @return string
     */
    function current_account_url()
    {
        $domain = app(DomainRepository::class)->requestCache()->getDefaultForAccount(current_account_id());

        return $domain ? (app()->environment('local') ? 'http' : 'https').'://'.$domain->domain : '';
    }
}

if (! function_exists('current_account_brand')) {
    /**
     * Very simple helper method for retrieving the current account brand.
     *
     * @param  bool  $returnDefault
     * @return mixed
     */
    function current_account_brand($returnDefault = true)
    {
        $account = current_account();

        $defaultBrand = $returnDefault ? brand_by_domain() : null;

        return $account->brand ?? $defaultBrand;
    }
}

if (! function_exists('is_goodgrants')) {
    /**
     * @return mixed
     */
    function is_goodgrants()
    {
        return current_account_brand() === Account::BRAND_GOODGRANTS;
    }
}

if (! function_exists('is_awardforce')) {
    /**
     * @return mixed
     */
    function is_awardforce()
    {
        return current_account_brand() === Account::BRAND_AWARDFORCE;
    }
}

if (! function_exists('isTrialAccount')) {
    /**
     * @return bool
     */
    function isTrialAccount()
    {
        return current_account()->isTrial();
    }
}

if (! function_exists('brand_config')) {
    /**
     * Retrieve brand configuration under {brand}.php config files
     *
     * @return mixed
     */
    function brand_config($key)
    {
        return config(current_account_brand().'.'.$key);
    }
}

if (! function_exists('localise_collection')) {
    function localise_collection(Collection $collection, $fields)
    {
        return transform_collection($collection, $fields, function ($item, $field) {
            return lang($item, $field);
        });
    }
}

if (! function_exists('transform_collection')) {
    function transform_collection(Collection $collection, $fields, $callback)
    {
        if (! is_array($fields)) {
            $fields = [$fields];
        }

        $collection->each(function ($item) use ($fields, $callback) {
            foreach ($fields as $field) {
                if ($item->$field) {
                    $item->$field = $callback($item, $field);
                }
            }
        });

        return $collection;
    }
}

if (! function_exists('field_label')) {
    /**
     * Returns the label of a field or scoring criterion, falling back to the title.
     *
     * @param  Entity  $field This could be a field, or a scoring criterion.
     * @return string
     *
     * @throws Exception
     */
    function field_label($field)
    {
        return $field->label ?: $field->title;
    }
}

if (! function_exists('is_image')) {
    /**
     * Returns true if the file extension is in the list of known images from the filetypes config list.
     *
     * @param  string  $file
     * @return string
     */
    function is_image($file)
    {
        return Config::has('filetypes.images.'.file_extension($file));
    }
}

if (! function_exists('is_audio')) {
    /**
     * Returns true if the file extension is in the list of known audio files from the filetypes config list.
     *
     * @param  string  $file
     * @return string
     */
    function is_audio($file)
    {
        return Config::has('filetypes.audio.'.file_extension($file));
    }
}

if (! function_exists('is_resizable')) {
    /**
     * Verifies if imgix can be used to resize a file (imgix will not process files heavier than 512MB).
     *
     * @link https://support.imgix.com/hc/en-us/articles/360000178806-HTTP-Status-codes
     *
     * @return bool
     */
    function is_resizable(File $file)
    {
        return $file->size < config('ui.images.max_file_size');
    }
}

if (! function_exists('imgix')) {
    /**
     * Generates a imgix based url with the given parameters.
     * example: imgix('file/path.jpg', ['h' => 100, 'w' => 300])
     *
     * @param  string  $file
     * @return string
     */
    function imgix($file, ?string $filename = null, array $params = [])
    {
        if (! feature_enabled('image_optimisation')) {
            return cloud_asset_url($file, filename: $filename, expiry: ($params['expiry'] ?? 60));
        }

        if ($filename) {
            $params['dl'] = $filename;
        }

        if (! isset($params['auto'])) {
            $params['auto'] = 'format';
        } elseif ($params['auto'] === false) {
            // For some reason even passing false in this parameter, increases the file size (much less than the `format` value)
            unset($params['auto']);
        }

        return app(UrlBuilder::class)->createUrl($file, $params);
    }
}

if (! function_exists('local_id')) {
    /**
     * Generates the local id for the entry.
     *
     * @return string
     */
    function local_id(Submittable $submittable)
    {
        $id = [];

        if ($submittable->localId) {
            $id[] = $submittable->localId;
        }

        if ($submittable->categoryShortcode()) {
            $id[] = $submittable->categoryShortcode();

            if (setting('local-id-shortcode-format') === LocalIdShortcodeFormat::Before->value) {
                $id = array_reverse($id);
            }
        }

        return implode('-', $id);
    }
}

if (! function_exists('entry_category_shortcode')) {
    /**
     * Return an entries category shortcode
     *
     * @param  Submittable  $submittable
     * @return string
     */
    function entry_category_shortcode($submittable)
    {
        if ($submittable->isEntry() && $submittable->categoryShortcode()) {
            return lang($submittable->getCategory(), 'shortcode');
        }

        return null;
    }
}

if (! function_exists('html_lang')) {
    function html_lang()
    {
        return substr(\AwardForce\Library\Facades\CurrentLocale::code(), 0, 2);
    }
}

if (! function_exists('is_rtl')) {
    /**
     * Returns true if the current locale is a RTL (right to left) language or not.
     * Based off the map in the ./config/lang.php file.
     *
     * @return bool
     */
    function is_rtl()
    {
        $code = \AwardForce\Library\Facades\CurrentLocale::code();

        return LanguageConfig::rtl($code);
    }
}

if (! function_exists('ltr_rtl_align')) {
    /**
     * Returns text-direction property based on the current locale RTL config
     */
    function ltr_rtl_align()
    {
        return is_rtl() ? 'right;' : 'left;';
    }
}

if (! function_exists('ltr_rtl_dir')) {
    /**
     * Returns dir property based on the current locale RTL config
     */
    function ltr_rtl_dir()
    {
        return is_rtl() ? 'rtl' : 'ltr';
    }
}

if (! function_exists('setting')) {
    /**
     * Simple helper method to return individual setting values.
     *
     * @param  mixed|null  $default
     * @return mixed|SettingRepository
     */
    function setting(?string $key = null, $default = null)
    {
        $repository = app(SettingRepository::class);

        return $key ? $repository->getValueByKey($key, $default) : $repository;
    }
}

if (! function_exists('setting_explode')) {
    /**
     * Simple helper method to return individual setting values.
     *
     * @param  string  $key
     * @param  string  $divider
     * @return string
     */
    function setting_explode($key, $divider = ',')
    {
        return explode($divider, setting($key, ''));
    }
}

if (! function_exists('consumer')) {
    /**
     * Helper function to either easily check for consumer permissions, or
     * return the consumer manager object.
     *
     * @param  null|string  $action
     * @param  null|string  $resource
     * @return \Illuminate\Foundation\Application|mixed
     */
    function consumer($action = null, $resource = null)
    {
        if (! is_null($action) and ! is_null($resource)) {
            return Consumer::can($action, $resource);
        }

        return app(Manager::class);
    }
}

if (! function_exists('consumer_id')) {
    /**
     * Returns the id of the consumer.
     *
     * @return mixed
     */
    function consumer_id()
    {
        return consumer()->id();
    }
}

if (! function_exists('deleted')) {
    /**
     * Wraps any text provided in the required span tag and class for deleted items.
     *
     * @param  string  $text
     * @return string
     */
    function deleted($text)
    {
        return new DeletedHtmlString('<span class="deleted">'.e($text).'</span>');
    }
}

if (! function_exists('default_language_code')) {
    /**
     * Returns the default language code for the current account.
     *
     * @return string
     */
    function default_language_code()
    {
        return app(DefaultLanguageCode::class)->get();
    }
}

if (! function_exists('convert_bool_to_string')) {
    /**
     * Returns the string representation of a boolean
     *
     *
     * @return string
     */
    function convert_bool_to_string($bool)
    {
        return ucfirst(str_replace("'", '', var_export((bool) $bool, true)));
    }
}

if (! function_exists('protect_value')) {
    /**
     * Used for outputting values such as passwords, authentication tokens, credit cards.etc.
     *
     * The length parameter represents the number of characters (from the right) that you wish to show. For
     * example, if you're outputting a credit card, you may wish to show the last 4 chars.
     *
     * @param  int  $length
     * @return string
     */
    function protect_value($value, $length = 4)
    {
        if (! $length) {
            return str_pad('', strlen($value), '*');
        }

        $stringLength = strlen($value);
        $firstPart = str_pad('', $stringLength - $length, '*');
        $secondPart = substr($value, -$length);

        return $firstPart.$secondPart;
    }
}

if (! function_exists('language_code')) {
    /**
     * Sometimes we need to get the required language code for a specific user.
     *
     * If no language code can be found, it'll return the language code for the current account.
     *
     * @param  User|null  $user
     * @return string code
     */
    function language_code($user)
    {
        if ($user instanceof User) {
            return $user->currentMembership->language ?: default_language_code();
        }

        return default_language_code();
    }
}

if (! function_exists('merge_group_concat')) {
    /**
     * Merges an array of string items which contain GROUP_CONCAT() ids from the database, into a single array.
     *
     * I.e: ["1,2,3", "2,3,4"] => [1, 2, 3, 4]
     *
     * @param  array|Collection  $items
     * @param  bool  $collection  Return a collection of items, instead of an array.
     * @return array
     */
    function merge_group_concat($items, $collection = false)
    {
        if ($items instanceof Collection) {
            $items = $items->toArray();
        }

        $values = [];

        foreach ($items as $item) {
            $values = array_merge($values, is_array($item) ? $item : explode(',', $item ?? ''));
        }

        $values = array_values(array_filter(array_unique($values)));

        return $collection ? new Collection($values) : $values;
    }
}

if (! function_exists('default_currency')) {
    /**
     * Returns the default currency for the account.
     *
     * @return Currency
     */
    function default_currency()
    {
        $currency = app(SupportedCurrencyRepository::class)->getDefault();

        return new Currency($currency->code);
    }
}

if (! function_exists('format_amount')) {
    /**
     * Easy formatting of amount objects. This is generally to be used only for display purposes only.
     * If you want the price in a format that is better understood by apis and programs (ie, a nice, clean
     * decimal point number, simply use the price() method off the Amount value object).
     *
     * @param  string  $separator
     * @return string
     */
    function format_amount(Amount $amount, $separator = ' ', bool $negative = false)
    {
        $value = $negative ? 0 - $amount->value() : $amount->value();

        $price = number_format($value, 2);

        return $amount->currency()->symbol().$separator.$price;
    }
}

if (! function_exists('onsite_gateway')) {
    /**
     * Determine if the user selected payment gateway is on-site.
     * Does NOT redirect to gateway for payment processing.
     *
     * @return bool
     */
    function onsite_gateway()
    {
        $gateway = setting('payment-gateway');

        if (in_array($gateway, array_values(Config::get('payments.onsite_gateways')))) {
            return true;
        }

        return false;
    }
}

if (! function_exists('custom_checkout_form')) {
    /**
     * Determine if the user selected payment gateway requires 3DS2 challenge to be completed.
     */
    function custom_checkout_form(): bool
    {
        $gateway = setting('payment-gateway');

        return in_array($gateway, Config::get('payments.custom_checkout') ?? []);
    }
}

if (! function_exists('onsite_payment_method')) {
    /**
     * Determine if user selected payment method is onsite
     *
     * @return bool
     */
    function onsite_payment_method($paymentMethod)
    {
        return ! in_array($paymentMethod, array_values(Config::get('payments.offsite_payment_methods')));
    }
}

if (! function_exists('invoice_no')) {
    /**
     * Return an invoice number using the custom invoice number attribute
     *
     *
     * @return string
     */
    function invoice_no($order)
    {
        return 'INV-'.$order->invoiceNumber;
    }
}

if (! function_exists('payment_cache_key')) {
    /**
     * Generates the cache key required for storing the payment info in the cache.
     *
     * @param  int  $userId
     * @param  int  $currentMembershipId
     * @return string
     */
    function payment_cache_key($userId, $currentMembershipId)
    {
        return 'payment-'.$userId.'-'.$currentMembershipId;
    }
}

if (! function_exists('default_amount')) {
    /**
     * Retrieves the default amount for a given price. The first argument should be the collection
     * of price amounts.
     *
     * @param  mixed  $amounts
     * @param  string  $currencyCode
     * @return string|void
     */
    function default_amount($amounts, $currencyCode)
    {
        $amount = $amounts->last();

        if (! $amount) {
            return;
        }

        $amounts = $amount->amounts;

        if (isset($amounts[$currencyCode]) && $amounts[$currencyCode] !== '') {
            return format_amount(new Amount($amounts[$currencyCode], new Currency($currencyCode)));
        }
    }
}

if (! function_exists('card_type')) {
    /**
     * Return a card type.
     *
     *
     * @return string
     */
    function card_type(File $file)
    {
        $videoExtensions = feature_enabled('transcoding') ? config('awardforce.extensions.videos') : [];

        $extension = file_extension($file->original);
        switch ($extension) {
            case in_array($extension, config('awardforce.extensions.images')):
                return 'image';
            case 'pdf':
                return 'pdf';
            case in_array($extension, config('awardforce.extensions.audio')):
                return 'audio';
            case in_array($extension, $videoExtensions):
                return 'video';
            default:
                return 'generic';
        }
    }
}

if (! function_exists('get_vimeo_id')) {
    /**
     * Parse url and return video id (Vimeo).
     *
     * https://github.com/lingtalfi/video-ids-and-thumbnails/
     *
     * @return string
     */
    function get_vimeo_id($url)
    {
        if (preg_match(
            '#(?:https?://)?(?:www.)?(?:player.)?vimeo.com/(?:[a-z]*/)*([0-9]{6,11})[?]?.*#',
            $url,
            $matches
        )) {
            return $matches[1];
        }

        return false;
    }
}

if (! function_exists('get_youtube_id')) {
    /**
     * Parse url and return video id (YouTube).
     *
     * https://github.com/lingtalfi/video-ids-and-thumbnails/
     *
     * @return string
     */
    function get_youtube_id($url)
    {
        if (! Str::contains($url, ['youtube.com', 'youtu.be'])) {
            return false;
        }

        $regex = implode('|', [
            '/(?<=(?:v|i)=)[a-zA-Z0-9-]+(?=&)',
            '(?<=(?:v|i)\/)[^&\n]+',
            '(?<=embed\/)[^"&\n]+',
            '(?<=(?:v|i)=)[^&\n]+',
            '(?<=youtu.be\/)[^&\n]+/',
        ]);

        if (preg_match($regex, $url, $matches)) {
            return $matches[0];
        }

        return false;
    }
}

if (! function_exists('get_wistia_id')) {
    /**
     * Parse url and return video id for Wistia
     *
     * https://wistia.com/support/developers/construct-an-embed-code
     *
     * @return string
     */
    function get_wistia_id($url)
    {
        if (preg_match('/https?:\/\/(.+)?(wistia\.com|wi\.st)\/.*/', $url)) {
            $pos = strrpos($url, '/');

            return $pos === false ? $url : substr($url, $pos + 1);
        }

        return false;
    }
}

if (! function_exists('get_tiktok_id')) {
    /**
     * Check if it's TikTok video url
     *
     * @return bool
     */
    function get_tiktok_id($url)
    {
        if (preg_match('/https?:\/\/(?:www\.)?tiktok\.com\/\S*\/video\/(\d+)/', $url, $matches) && isset($matches[1])) {
            return $matches[1];
        }

        return false;
    }
}

if (! function_exists('get_twitch_id')) {
    /**
     * Check if it's Twitch video url
     */
    function get_twitch_id($url): string|bool
    {
        if (preg_match('/https?:\/\/(?:player\.|www\.)?twitch\.tv\/(?:\?channel=)?(\S*)/', $url, $matches) && isset($matches[1])) {
            $channel = explode('&', $matches[1]);

            return $channel[0];
        }

        return false;
    }
}

if (! function_exists('get_instagram_id')) {
    /**
     * Check if it's Instagram post url
     */
    function get_instagram_id($url): string|bool
    {
        if (preg_match('/https?:\/\/www\.instagram\.com\/(?:reel|p)\/([A-Za-z0-9-_]+)/', $url, $matches) && isset($matches[1])) {
            return $matches[1];
        }

        return false;
    }
}

if (! function_exists('qr_code')) {
    /**
     * Generate a QR code
     *
     * @param  int  $size
     * @param  array  $foregroundColour
     * @param  array  $backgroundColour
     * @return string
     */
    function qr_code(
        $text,
        $size = 100,
        $foregroundColour = ['r' => 0, 'g' => 0, 'b' => 0, 'a' => 0],
        $backgroundColour = ['r' => 255, 'g' => 255, 'b' => 255, 'a' => 0]
    ) {
        $qrCode = Endroid\QrCode\Builder\Builder::create()
            ->writer(new Endroid\QrCode\Writer\PngWriter())
            ->data($text)
            ->size($size)
            ->foregroundColor(new Endroid\QrCode\Color\Color($foregroundColour['r'], $foregroundColour['g'], $foregroundColour['b'], $foregroundColour['a']))
            ->backgroundColor(new Endroid\QrCode\Color\Color($backgroundColour['r'], $backgroundColour['g'], $backgroundColour['b'], $backgroundColour['a']))
            ->build();

        return $qrCode->getDataUri();
    }
}

if (! function_exists('flatten_keyed_array')) {
    /**
     * Merges and flattens keyed arrays into a single array.
     *
     * Keyed arrays are transposed like this:
     * IN:  ['key' => ['val1', 'val2', 'val3']
     * OUT: ['key-val1', 'key-val2', 'key-val3']
     *
     * @param  array  ...$array
     * @return array
     */
    function flatten_keyed_array(array ...$arrays)
    {
        $flattened = [];

        foreach ($arrays as $array) {
            foreach ($array as $key => $value) {
                if (is_numeric($key)) {
                    $flattened[] = $value;

                    continue;
                }

                foreach ((array) $value as $item) {
                    $flattened[] = $key.'-'.$item;
                }
            }
        }

        return $flattened;
    }
}

if (! function_exists('strip_specials_chars')) {
    /**
     * Strip all special characters, leaving only letters, numbers and whitespace
     *
     *
     * @return string
     */
    function strip_special_chars($string, bool $toAscii = true)
    {
        $string = $toAscii ? Str::ascii($string) : $string;

        // Remove all characters that are not the separators, letters, numbers, or whitespace.
        $separators = preg_quote('-').preg_quote('_');

        $string = preg_replace('![^'.$separators.'\pL\pN\s]+!u', '', $string);

        // Remove excess white-space
        $string = preg_replace('/(\s)+/', ' ', $string);

        // Remove leading & trailing whitespace
        return trim($string);
    }
}

if (! function_exists('localised_ordinal_number')) {
    /**
     * Format a number by appending a the ordinal component (localised)
     *
     *
     * @return string
     */
    function localised_ordinal_number($number)
    {
        $languageCode = CurrentLocale::code();

        $nf = new NumberFormatter($languageCode, NumberFormatter::ORDINAL);

        return $nf->format($number);
    }
}

if (! function_exists('localised_number_format')) {
    /**
     * Format a number (localised)
     */
    function localised_number_format($number, int $decimals = -1): string
    {
        $languageCode = CurrentLocale::code();

        $nf = new NumberFormatter($languageCode, NumberFormatter::DECIMAL);

        $nf->setAttribute(NumberFormatter::FRACTION_DIGITS, $decimals);

        return $nf->format($number ?? 0);
    }
}

if (! function_exists('social_login_url')) {
    /**
     * Generates the social authentication url for the specified provider.
     *
     * @param  string  $provider
     * @param  string  $roleSlug
     * @return string
     */
    function social_login_url($provider, $roleSlug = null)
    {
        $domain = config('app.auth-domain');
        $account = current_account()->globalId;
        $params = array_filter(['role' => (string) $roleSlug]);

        return "https://{$domain}/social/authenticate/{$provider}/{$account}?".http_build_query($params);
    }
}

if (! function_exists('payments_auth_url')) {
    /**
     * Generates the payment authentication url for the specified provider.
     *
     * @param  string  $provider
     * @param  bool  $testMode
     * @return string
     */
    function payments_auth_url($provider)
    {
        $domain = config('app.auth-domain');
        $account = current_account()->globalId;

        return "https://{$domain}/payments/authenticate/{$provider}/{$account}";
    }
}

if (! function_exists('translate')) {
    /**
     * Translates the required object and returns the result.
     *
     * @param  Model|Collection  $object
     * @return Model|Collection
     */
    function translate($object, ?string $language = null)
    {
        return Translator::translate($object, $language);
    }
}

if (! function_exists('translated_property')) {
    /**
     * @return array|Application|\Illuminate\Contracts\Translation\Translator|mixed|string|null
     */
    function translated_property(array $translations, string $property, ?array $languages = [])
    {
        foreach ($languages as $language) {
            if (Arr::has($translations, "{$language}.{$property}") && ! empty($translations[$language][$property])) {
                return $translations[$language][$property];
            }
        }

        foreach ($translations as $translation) {
            if (isset($translation[$property])) {
                return $translation[$property];
            }
        }

        return trans('miscellaneous.no_translation_available');
    }
}

if (! function_exists('disable_button')) {
    /**
     * Returns a disabled HTML state if the check is null, or true.
     *
     * @param  bool  $check
     * @return string
     */
    function disable_button($check = null)
    {
        if (! is_null($check) && $check) {
            return ' disabled="disabled"';
        }
    }
}

if (! function_exists('stat_change_colour')) {
    /**
     * Returns the positive or negative colour based on the $difference.
     *
     * @param  int  $difference
     * @param  string  $positive
     * @param  string  $negative
     * @return mixed
     */
    function stat_change_colour($difference, $positive, $negative)
    {
        if ($difference > 0) {
            return $positive;
        }

        if ($difference < 0) {
            return $negative;
        }
    }
}

if (! function_exists('app_asset_url')) {
    /**
     * Essentially generates a URL to an asset controller that then manages the file and its location.
     *
     * @return string
     */
    function app_asset_url(string $asset)
    {
        // If a git hash commit is available, it means the application is being served from one of our app servers
        if ($commitHash = config('app.version')) {
            // Grab the cloudfront distribution domain
            $cloudfront = config('services.aws.cloudfront.assets.domain');

            return "//{$cloudfront}/assets/{$commitHash}/{$asset}";
        }

        return "/{$asset}";
    }
}

if (! function_exists('git_commit')) {
    /**
     * Returns the git commit hash for the current deploy
     */
    function git_commit(): ?string
    {
        $file = base_path('.git_commit_nr');

        return file_exists($file) ? trim(file_get_contents($file)) : null;
    }
}

if (! function_exists('public_asset_url')) {
    /**
     * Identical to other functions except only ever returns a publicly-accessible image URL. This is good
     * for features such as social sharing, and will/should be used by images and files that are public anyway,
     * such as theme files, images.etc.
     */
    function public_asset_url(string $key): string
    {
        $region = CurrentAccount::attribute('region');

        return config("filesystems.disks.s3-{$region}.bucket_url").$key;
    }
}

if (! function_exists('cloud_asset_url')) {
    /**
     * Returns a new, signed URL for the given file key if the provided file is meant to
     * be private, otherwise it will return an unsigned direct URL to the file.
     *
     * @param  bool  $redirect Use external redirector
     * @param  int  $expiry Expiry time in minutes
     */
    function cloud_asset_url(string $key, bool $redirect = false, ?string $filename = null, int $expiry = 60): ?string
    {
        $region = CurrentAccount::attribute('region');

        // If no CloudFront distribution is configured, then simply return an S3 url
        if (! config("services.aws.cloudfront.{$region}.domain")) {
            return config("filesystems.disks.s3-{$region}.bucket_url").$key;
        }

        try {
            // Otherwise, sign a cloud front URL
            $cloudFront = new Aws\CloudFront\CloudFrontClient([
                'region' => config("filesystems.disks.s3-{$region}.region"),
                'version' => '2014-11-06',
            ]);

            $unsignedUrl = 'https://'.config("services.aws.cloudfront.{$region}.domain").'/'.$key;

            if ($filename) {
                $encodedFilename = rawurlencode($filename);
                $unsignedUrl .= '?response-content-disposition='.rawurlencode("filename*=UTF-8''$encodedFilename");
            }

            $url = $cloudFront->getSignedUrl([
                'url' => $unsignedUrl,
                'expires' => now()->addMinutes($expiry)->timestamp,
                'private_key' => config("services.aws.cloudfront.{$region}.private_key_path"),
                'key_pair_id' => config("services.aws.cloudfront.{$region}.key_pair_id"),
            ]);

            return $redirect ? external_url($url, $expiry) : $url;
        } catch (\InvalidArgumentException $e) {
            \Illuminate\Support\Facades\Log::error('Cloudfront private key is missing.');

            return null;
        }
    }
}

if (! function_exists('comments_token')) {
    /**
     * Returns a comments token.
     *
     * @param  array  $tags
     * @return string
     */
    function comments_token($tags)
    {
        $userTags = app(UserTags::class);

        return $userTags->getToken(...$userTags->parseTags($tags));
    }
}

if (! function_exists('custom_domain')) {
    /**
     * Returns the custom domain to be used for any account.
     *
     * @return string
     */
    function custom_domain()
    {
        return current_account()->region.config('domains.custom');
    }
}

if (! function_exists('preview_score_set_judge_comments')) {
    /**
     * Accepts a collection of comments (previously retrieved from the DB)
     * as well as a score set and judge, and renders the magical 'preview comments' icon.
     * This icon includes the content for a modal which displays all of the matching comments.
     *
     * It uses the `entry.manager.matrix-comments` view to render the comments and then obfuscates
     * the content (so it doesn't break any markup) before passing into the icon tag.
     * This content is clarified and displayed in a modal on click of the icon/tag.
     *
     * If the optional scoring criteria collection is passed in, it will also find and load
     * all comments for the specified criteria too, so they can be displayed after the entry comments.
     * This is used for VIP Judging when the judge is allowed to comment on individual scoring criteria.
     *
     * @return string
     *
     * @throws Exception
     * @throws Throwable
     */
    function preview_score_set_judge_comments(
        CommentCollection $comments,
        ScoreSet $scoreSet,
        $judge,
        ?Collection $criteria = null,
        ?string $srText = null
    ) {
        $comments = $comments->groupByTypes($scoreSet, $judge, $criteria);

        $content = view('entry.manager.matrix-comments', [
            'comments' => $comments->get('judging'),
            'conflictComments' => $comments->get('conflict'),
            'abstentionComments' => $comments->get('abstention'),
            'scoreSet' => $scoreSet->name,
            'judge' => $judge['name'],
            'criteria' => $comments->get('criteria') ?: new Collection,
        ])->render();

        return view('entry.partials.judge-comment', [
            'content' => obfuscate($content),
            'srText' => $srText,
        ])->render();
    }
}

if (! function_exists('preview_order_comments')) {
    /**
     * The function accepts a collection of comments and an order and renders the clickable 'preview comments'
     * icon that opens a modal with comments.
     *
     * @return string
     */
    function preview_order_comments(CommentCollection $comments, Order $order)
    {
        $comments = $comments->filter(function ($comment) use ($order) {
            return $comment->hasTag('order_'.$order->id);
        });

        $content = view('order.comments', [
            'order' => $order,
            'comments' => $comments,
        ])->render();

        return '<a class="view-simple-modal" data-content="'.obfuscate($content).'"><i class="af-icons af-icons-comments"></i></a>';
    }
}

if (! function_exists('preview_comments')) {
    /**
     * The function accepts a collection of comments and an order and renders the clickable 'preview comments'
     * icon that opens a modal with comments.
     *
     * @return string
     */
    function preview_comments(CommentCollection $comments)
    {
        $content = view('comments.preview', [
            'comments' => $comments,
        ])->render();

        return '<a class="view-simple-modal" data-content="'.obfuscate($content).'"><i class="af-icons af-icons-comments"></i></a>';
    }
}

if (! function_exists('encode_modal_content')) {
    /**
     * Loads a view, renders the content with the provided data and then encodes the entire thing. This is
     * good for storing HTML content in things like data attributes which may be required for loading modals.
     *
     * @param  string  $view
     * @return string
     */
    function encode_modal_content($view, array $data = [])
    {
        return htmlentities(view($view, $data)->render());
    }
}

if (! function_exists('feature_enabled')) {
    /**
     * This is a simple wrapper method for the feature service class.
     *
     * @param  string  $feature
     * @return bool
     */
    function feature_enabled($feature)
    {
        return \AwardForce\Modules\Features\Facades\Feature::enabled($feature);
    }
}

if (! function_exists('feature_disabled')) {
    /**
     * Inversion of the feature enabled function.
     *
     * @param  string  $feature
     * @return bool
     */
    function feature_disabled($feature)
    {
        return ! feature_enabled($feature);
    }
}

if (! function_exists('score_format')) {
    /**
     * Nicely displays a score to the maximum precision.
     *
     * @param  float  $score
     * @param  int  $maxPrecision
     * @return string
     */
    function score_format($score, $maxPrecision = 2)
    {
        if (! is_numeric($score)) {
            return '';
        }

        for ($i = 0; $i < $maxPrecision; $i++) {
            if (round($score, $i) === (float) $score) {
                return number_format($score, $i);
            }
        }

        return number_format($score, $maxPrecision);
    }
}

if (! function_exists('first_or_max')) {
    /**
     * Returns the larger of $first or $second, ignoring $second if null or zero.
     *
     * @param  int  $first
     * @param  int  $second
     * @return int
     */
    function first_or_max($first, $second)
    {
        return $first ? max($first, $second) : $first;
    }
}

if (! function_exists('first_or_min')) {
    /**
     * Returns the smaller of $first or $second, ignoring $second if null or zero.
     *
     * @param  int  $first
     * @param  int  $second
     * @return int
     */
    function first_or_min($first, $second)
    {
        return $second ? min($first, $second) : $first;
    }
}

if (! function_exists('pagination_single_page_item')) {
    /**
     * Calculates the correct page number for a single-item-per-page pagination instance
     * given an existing paginator given the items index on the current page.
     *
     * @param  int  $index
     * @return int
     */
    function pagination_single_page_item(LengthAwarePaginator $paginator, $index)
    {
        $offset = ($paginator->currentPage() - 1) * $paginator->perPage();

        return $offset + $index + 1;
    }
}

if (! function_exists('next_previous_token')) {
    /**
     * Concatenates the search token and paginator index into the token expected by NextPrevious.
     *
     * @param  int  $index
     * @param  string  $token
     * @return string
     */
    function next_previous_token(LengthAwarePaginator $paginator, $index, $token)
    {
        return $token.'-'.pagination_single_page_item($paginator, $index);
    }
}

if (! function_exists('score_set_url')) {
    /**
     * Returns the URL of the given score set's index view.
     *
     * @return string
     */
    function score_set_url(?ScoreSet $scoreSet = null)
    {
        if (! $scoreSet || ! is_null($scoreSet->deletedAt)) {
            return '';
        }

        switch ($scoreSet->mode) {
            case ScoreSet::MODE_QUALIFYING:
                return route('qualifying.index').'?'.http_build_query(['score-set' => (string) $scoreSet->slug]);
            case ScoreSet::MODE_TOP_PICK:
                return route('top-pick.index').'?'.http_build_query(['score-set' => (string) $scoreSet->slug]);
            case ScoreSet::MODE_VOTING:
                return route('voting.index', ['scoreSet' => $scoreSet]);
            case ScoreSet::MODE_GALLERY:
                return route('gallery.index', ['scoreSet' => $scoreSet]);
            default:
                return route('judging.index');
        }
    }
}

if (! function_exists('seeded_shuffle')) {
    /**
     * Applies the Fisher–Yates shuffle to the specified array given the seed value.
     * This ensures we can randomly shuffle an array in the exact same order,
     * for use with randomising judging entry order.
     *
     * @link https://en.wikipedia.org/wiki/Fisher%E2%80%93Yates_shuffle
     * @link http://stackoverflow.com/a/6557893/270041
     */
    function seeded_shuffle(array $array, int $seed): array
    {
        sort($array);
        mt_srand($seed);

        for ($i = count($array) - 1; $i > 0; $i--) {
            $j = mt_rand(0, $i);
            [$array[$i], $array[$j]] = [$array[$j], $array[$i]];
        }

        // Reset seed to ensure we don't affect any future calls to `mt_rand()`
        mt_srand();

        return $array;
    }
}

if (! function_exists('str_array_to_dot_notation')) {
    /**
     * Converts a string representing an array to dot notation.
     *
     * Input: values[ypEwboGD]
     * Output: values.ypEwboGD
     *
     * @param  string  $string
     * @return string
     */
    function str_array_to_dot_notation($string)
    {
        return preg_replace('/\[([^\[\]]+)\]/', '.$1', $string);
    }
}

if (! function_exists('url2png')) {
    /**
     * Returns a png preview of the given url.
     *
     * @param  array  $options
     */
    function url2png($url, $options = []): ?string
    {
        $regex = '_^(https|http)://'. // protocol
            '(?:[a-z\x{00a1}-\x{ffff}0-9]-*)*[a-z\x{00a1}-\x{ffff}0-9]+(?:\.(?:[a-z\x{00a1}-\x{ffff}0-9]-*)*[a-z\x{00a1}-\x{ffff}0-9]+)*'. // domain
            '\.[a-z\x{00a1}-\x{ffff}]{2,}\.?'. // TLD length 2 or more
            '(?:[/?#]\S*)?'. // path and query string
            '$_iuS'; // fragment

        // check if the url is valid before trying to generate a preview
        if (! preg_match($regex, $url)) {
            return null;
        }

        $options['url'] = $url;
        $options['user_agent'] = config('services.url2png.user_agent');
        foreach (config('services.url2png.extra_options') as $option => $value) {
            $options[$option] = $value;
        }

        $queryString = http_build_query($options);
        $token = md5($queryString.config('services.url2png.secret'));

        return 'https://api.url2png.com/v6/'.config('services.url2png.key').'/'.$token.'/png/?'.$queryString;
    }
}

if (! function_exists('explode_options')) {
    /**
     * Explodes an options list into a nicely cleaned array,
     * without any leading or trailing spaces, or empty values.
     *
     * @param  string|null  $options
     */
    function explode_options($options): array
    {
        if (! $options) {
            return [];
        }
        if (is_array($options)) {
            return $options;
        }

        return array_unique(array_filter(preg_split("/\s*\r\n\s*/", $options), function ($option) {
            // Filter out empty strings
            return trim($option) !== '';
        }));
    }
}

if (! function_exists('for_vue')) {
    /**
     * Returns a Vue-friendly JSON structure based on the provided collection and fields.
     */
    function for_vue(Collection $collection, array $fields = ['id', 'name'], $sortBy = 'name'): array
    {
        $items = $sortBy ? $collection->sortBy($sortBy) : $collection;

        return $items->map(function ($item) use ($fields) {
            $array = [];

            foreach ($fields as $field) {
                $array[$field] = $item->{$field} instanceof Slug ? (string) $item->{$field} : $item->{$field};
            }

            return $array;
        })->values()->toArray();
    }
}

if (! function_exists('trashed_filter_active')) {
    /**
     * Returns true if 'trashed' filter is active.
     *
     * @return bool
     */
    function trashed_filter_active()
    {
        return Request::filled('trashed') && Request::get('trashed') !== 'none';
    }
}

if (! function_exists('archived_filter_active')) {
    /**
     * Returns true if 'archived' filter is active.
     *
     * @return bool
     */
    function archived_filter_active()
    {
        return Request::filled('archived') && Request::get('archived') !== 'none';
    }
}

if (! function_exists('debug_time')) {
    /**
     * Outputs and logs the time since the request started, for use debugging slow pages and operations.
     */
    function debug_time(string $name)
    {
        $time = $name.' @ '.Duration::fromMicroseconds(microtime(true) - LARAVEL_START)->asString();

        (new \Symfony\Component\VarDumper\VarDumper)->dump($time);
        Log::debug($time);
    }
}

if (! function_exists('region_queue_name')) {
    /**
     * Find the queue name for the specified region for file processing.
     */
    function region_queue_name(string $region): string
    {
        return config("queue.regions.{$region}.files", config('queue.regions.eu.files'));
    }
}

if (! function_exists('consumer_timezone_offset')) {
    /**
     * Return the consumers timezone offset instandard hour format (e.g +11:00, -04:00, etc)
     *
     * Copied from Carbon v2. We are currently locked to version 1.x
     *
     * @return string
     */
    function consumer_timezone_offset()
    {
        $seconds = now(consumer()->timezone())->offset;

        $symbol = $seconds < 0 ? '-' : '+';
        $minute = abs($seconds) / 60;
        $hour = str_pad(floor($minute / 60), 2, '0', STR_PAD_LEFT);
        $minute = str_pad($minute % 60, 2, '0', STR_PAD_LEFT);

        return "$symbol$hour:$minute";
    }
}

if (! function_exists('sanitise_filename')) {
    /**
     * Takes a given string and sanitises it for filename output.
     *
     * @param  string  $filename
     * @return string
     */
    function sanitise_filename($filename)
    {
        $special_chars = ['?', '[', ']', '/', '\\', '=', '<', '>', ':', ';', ',', "'", '"', '&', '$', '#', '*', '(', ')', '|', '~', '`', '!', '{', '}'];

        $filename = str_replace('/', '-', $filename);
        $filename = str_replace($special_chars, '', $filename);
        $filename = preg_replace('/[\s-]+/', '-', $filename);

        return trim($filename, '.-_');
    }
}

if (! function_exists('excel_sheet_title')) {
    /**
     * Strips all unsupported characters, limits the title to 31 characters.
     */
    function excel_sheet_title(string $title): string
    {
        $sheetTitle = str_replace(['-', '*', ':', '\\', '/', '?', '[', ']'], '', $title);
        $sheetTitle = str_replace("\r\n", ' ', $sheetTitle);
        $sheetTitle = preg_replace('/ {2,}/', ' ', $sheetTitle);

        return Str::limit(trim($sheetTitle), 28);
    }
}

if (! function_exists('overridable_redirect')) {
    /**
     * Returns a redirect response that can be overriden by setting a request parameter.
     *
     * @return \Illuminate\Http\RedirectResponse
     */
    function overridable_redirect(string $url)
    {
        if (request()->filled('redirect')) {
            return redirect(request()->get('redirect'));
        }

        return redirect($url);
    }
}

if (! function_exists('translations_data')) {
    function translations_data(string $language, array $strings, array $terms = []): array
    {
        $translations = [];

        foreach ($strings as $key => $value) {
            if (is_string($key) && is_array($value)) {
                array_set($translations, $key, trans($key, $value));
            } else {
                array_set($translations, $value, trans($value, []));
            }
        }

        foreach ($terms as $term) {
            array_set($translations, 'terms-service.'.$term.'.singular', Term::singular($term));
            array_set($translations, 'terms-service.'.$term.'.plural', Term::plural($term));
        }

        array_walk_recursive($translations, function (&$string) {
            $string = str_replace('&quot;', '"', $string);
        });

        foreach ($translations as $key => $translation) {
            $translations[$language.'.'.$key] = $translation;
            unset($translations[$key]);
        }

        $interfaceTextService = app(InterfaceTextService::class);
        $translations = array_map_recursive($translations, function ($trans, $key) use ($language, $interfaceTextService) {
            $keyWithoutLang = str_replace("$language.", '', $key);

            return $interfaceTextService->translationOrEditText($keyWithoutLang, $trans);
        });

        return $translations;
    }
}

if (! function_exists('translations_for_vue')) {
    function translations_for_vue(string $language, array $strings, array $terms = []): array
    {
        return translations_data($language, $strings, $terms);
    }
}

if (! function_exists('account_features_for_vue')) {
    function account_features_for_vue()
    {
        return \AwardForce\Modules\Features\Facades\Feature::all()->toJson();
    }
}

if (! function_exists('flatten_for_vue')) {
    function flatten_for_vue($map)
    {
        return collect((array_merge(...array_values($map))))->toJson();
    }
}

if (! function_exists('tabs_for_vue')) {
    function tabs_for_vue(Platform\Html\Tabular\Tabular $tabular)
    {
        return collect($tabular->tabs())->map(function (Platform\Html\Tabular\Tab $tab) {
            return [
                'id' => $tab->view(),
                'name' => $tab->name(),
                'disabled' => $tab->isDisabled(),
            ];
        });
    }
}

if (! function_exists('currencies_for_vue')) {
    /**
     * Provides the list of currencies in a format parsable by our SelectField
     *
     * @return array
     */
    function currencies_for_vue()
    {
        $currencies = [
            'default' => country_currency(detect_country()),
            'list' => [],
        ];
        foreach (app(CurrencyRepository::class)->getAll() as $currency) {
            $currencies['list'][] = ['id' => $currency->code, 'name' => $currency->name()];
        }

        return $currencies;
    }
}

if (! function_exists('routes_for_vue')) {
    function routes_for_vue($routes): array
    {
        $routes = (array) $routes;
        $result = [];

        foreach ($routes as $route) {
            $result[$route] = Route::getRoutes()->getByName($route)->uri ?? $route;
        }

        return $result;
    }
}

if (! function_exists('country_currency')) {
    /**
     * returns the currency based on the country provided
     *
     * @return string
     */
    function country_currency($country)
    {
        return config('countries.list.'.$country.'.currency');
    }
}
if (! function_exists('format_currency')) {
    /**
     * returns the formatted currency
     *
     * @param  null  $locale
     * @return string
     */
    function format_currency($value, $currency, $locale = null)
    {
        $formatter = new \NumberFormatter($locale ?? Consumer::languageCode(), \NumberFormatter::CURRENCY);

        return $formatter->formatCurrency((float) $value, $currency);
    }
}

if (! function_exists('escape_string_for_query')) {
    /**
     * Escapes string to be used in a mysql query
     *
     * @return string
     */
    function escape_string_for_query(string $string)
    {
        return trim(DB::getPdo()->quote($string), "'");
    }
}

if (! function_exists('is_api_consumer')) {
    /**
     * Validates if the consumer is instance of ApiConsumer class
     *
     * @return bool
     */
    function is_api_consumer()
    {
        return consumer()->get() instanceof ApiConsumer;
    }
}

if (! function_exists('translations_value')) {
    /**
     * Get string represenation of boolean value
     *
     * @param  bool  $value
     * @return string
     */
    function translations_value(array $translation_keys)
    {
        return json_encode(array_merge(...array_map(function ($value) {
            return [$value => trans($value)];
        }, $translation_keys)));
    }
}

if (! function_exists('boolean_string_value')) {
    /**
     * Get string represenation of boolean value
     *
     * @return string
     */
    function boolean_string_value(?bool $value)
    {
        return $value ? 'true' : 'false';
    }
}

if (! function_exists('to_boolean')) {
    /**
     * Convert to boolean
     */
    function to_boolean(mixed $booleable): bool
    {
        return filter_var($booleable, FILTER_VALIDATE_BOOLEAN, FILTER_NULL_ON_FAILURE);
    }
}

if (! function_exists('full_contact_details')) {
    /**
     * Output full contact details of a user.
     *
     * @return string
     */
    function full_contact_details(User $user)
    {
        return $user->fullName().' ('.$user->preferredContact().')';
    }
}

if (! function_exists('queue_redirect_after_submit_cookie')) {
    /**
     * Queue redirect_after_submit cookie.
     *
     * @param  string  $redirect
     */
    function queue_redirect_after_submit_cookie($redirect)
    {
        if (URL::to('/') !== \Request::url()) {
            \Cookie::queue('redirect_after_submit', $redirect, 1440, '/', null, null, false, false);
        }
    }
}

if (! function_exists('forget_redirect_after_submit_cookie')) {
    /**
     * Forget redirect_after_submit cookie.
     */
    function forget_redirect_after_submit_cookie()
    {
        \Cookie::queue(\Cookie::forget('redirect_after_submit'));
    }
}

if (! function_exists('get_redirect_after_submit_url')) {
    /**
     * Get redirect_after_submit URL.
     *
     * The redirect_after_submit is an unencrypted cookie, check whether the domain matches the default
     * account domain before returning the redirect URL.
     *
     * @return string|null
     */
    function get_redirect_after_submit_url()
    {
        $redirect = \Cookie::get('redirect_after_submit');
        forget_redirect_after_submit_cookie();
        $domain = current_account()->defaultDomain()->domain;

        if (str_is("http://{$domain}*", $redirect) || str_is("https://{$domain}*", $redirect)) {
            return $redirect;
        }
    }
}

if (! function_exists('id_from_slug')) {
    /**
     * Get the id of a resource given the repository and slug
     *
     * @return int
     */
    function id_from_slug(?string $slug, Repository $repository)
    {
        if (! $slug) {
            return null;
        }

        return $repository->getBySlug($slug)->id ?? null;
    }
}
if (! function_exists('slug_from_id')) {
    /**
     * Get the slug of a resource given the repository and id
     */
    function slug_from_id(int $id, Repository $repository): ?string
    {
        $slug = $repository->getById($id)?->slug;

        return $slug ? (string) $slug : null;
    }
}

if (! function_exists('ids_from_slugs')) {
    /**
     * Get the id of a resource given the repository and slug
     *
     * @return array
     */
    function ids_from_slugs(?array $slugs, Repository $repository)
    {
        if (empty($slugs)) {
            return [];
        }

        return $repository->getBySlugs($slugs)->pluck('id')->all() ?? null;
    }
}

if (! function_exists('lang_class')) {
    function lang_class()
    {
        return is_rtl() ? 'pdf-rtl' : 'pdf';
    }
}

if (! function_exists('truncate_url')) {
    /**
     * Return a truncated URL, useful for embedding long URLs in email template.
     *
     * @param  string  $url
     * @param  bool  $html
     * @return string
     */
    function truncate_url($url, $html = false)
    {
        $truncatedUrl = str_limit($url, 60);

        if ($html) {
            return "<a href=\"{$url}\">{$truncatedUrl}</a>";
        }

        return $truncatedUrl;
    }
}

if (! function_exists('plans_by_brand')) {
    function plans_by_brand($brand)
    {
        return collect(products_by_brand($brand))
            ->mapWithKeys(function ($values, $product) {
                $plan = explode('-', $product)[2];

                return [$plan => $plan];
            })->toArray();
    }
}

if (! function_exists('products_by_brand')) {
    function products_by_brand($brand)
    {
        return collect(config('products.available'))->filter(function ($product, $key) use ($brand) {
            return $product['brand'] === $brand && ! Str::contains($key, 'ggenterprise');
        })->toArray();
    }
}

if (! function_exists('product_price')) {
    function product_price($product)
    {
        return app(PaymentSubscriptionGateway::class)->getProductPrice($product);
    }
}

if (! function_exists('make_product')) {
    function make_product($currency, $interval, $plan)
    {
        if (config('products.available.'.$currency.'-'.$interval.'-'.$plan.'-2', false)) {
            return $currency.'-'.$interval.'-'.$plan.'-2';
        }

        if (config('products.available.'.$currency.'-'.$interval.'-'.$plan.'-1', false)) {
            return $currency.'-'.$interval.'-'.$plan.'-1';
        }
    }
}

if (! function_exists('countries_regions')) {
    function countries_regions()
    {
        $items = [];

        foreach (config('countries.list') as $countryKey => $country) {
            if (isset($country['regions']) && is_array($country['regions'])) {
                $regions = ['' => ''];

                foreach ($country['regions'] as $regionKey => $region) {
                    $regions[$region] = trans('countries.'.$countryKey.'.regions.'.$region);
                }

                $items[$countryKey] = $regions;
            }
        }

        return $items;
    }
}

if (! function_exists('hubspot_url')) {
    /**
     * Return Hubspot URL.
     *
     * @param  string  $type
     * @param  int  $id
     * @return string
     */
    function hubspot_url($type, $id)
    {
        $baseUrl = 'https://app.hubspot.com/contacts/5752201';

        switch ($type) {
            case 'deal':
                return "{$baseUrl}/deal/{$id}";
                break;
            case 'company':
                return "{$baseUrl}/company/{$id}";
                break;
        }

        throw new InvalidArgumentException("Unknown HubSpot url type: {$type}");
    }
}

if (! function_exists('truncate_filename')) {
    /**
     * Return truncated filename.
     *
     * @param  string  $filename
     * @param  int  $length
     * @return string
     */
    function truncate_filename($filename, $length)
    {
        if (strlen($filename) <= $length) {
            return $filename;
        }

        $fileInfo = pathinfo($filename);

        return substr($fileInfo['filename'], 0, $length - strlen($fileInfo['extension']) - 1).'.'.$fileInfo['extension'];
    }
}

if (! function_exists('remove_breaking_characters')) {
    /**
     * Return the string without breaking characters.
     *
     * @param  string  $text
     * @return string
     */
    function remove_breaking_characters($text)
    {
        return preg_replace('/[^\P{C}\n]+/u', '', $text);
    }
}

if (! function_exists('remove_control_characters')) {
    /**
     * Return the string without bad characters.
     *
     * @param  string  $text
     * @return string
     */
    function remove_control_characters($text)
    {
        return preg_replace('/[\x00-\x1F\x7F]&&[^\x0A]/', '', $text);
    }
}

if (! function_exists('clean_export_string')) {
    /**
     * Return the string ready for use in export (head, row, table)..
     *
     * @param  string  $text
     * @return string
     */
    function clean_export_string($text)
    {
        return str_replace("\n", ' ', remove_control_characters(remove_breaking_characters((string) $text)));
    }
}

if (! function_exists('elevio_hash_generator')) {
    /**
     * Generates Elevio hash (see https://docs.elevio.help/en/articles/81552-sending-user-data)
     *
     *
     * @return string
     */
    function elevio_hash_generator(string $emailOrMobile, string $key)
    {
        return hash_hmac('sha256', $emailOrMobile, $key);
    }
}

if (! function_exists('is_provisioning_request')) {
    /**
     * Check if the current request host is provisioning (any of provisioning domains) or not
     *
     * @return bool
     */
    function is_provisioning_request()
    {
        return in_array(request()->getHost(), explode(',', config('provisioning.root_domains')));
    }
}

if (! function_exists('trans_no_tags')) {
    /**
     * Translate the given message without any tags. This guarantees to never wrap string with <edit-interface-text>.
     *
     * @param  string|null  $key
     * @param  array  $replace
     * @param  string|null  $locale
     * @return \Illuminate\Contracts\Translation\Translator|string|array|null
     */
    function trans_no_tags($key = null, $replace = [], $locale = null)
    {
        return strip_tags(trans($key, $replace, $locale));
    }
}

if (! function_exists('trans_no_overrides')) {
    /**
     * Translate the given message without database overrides
     *
     * @param  string|null  $key
     * @param  array  $replace
     * @param  string|null  $locale
     * @return \Illuminate\Contracts\Translation\Translator|string|array|null
     */
    function trans_no_overrides($key = null, $replace = [], $locale = null)
    {
        if (is_null($key)) {
            return app(TranslationsWithoutOverrides::class);
        }

        return app(TranslationsWithoutOverrides::class)->get($key, $replace, $locale);
    }
}

if (! function_exists('safe_string')) {
    /**
     * Strip html tags after decoding html entities
     *
     * @return string
     */
    function safe_string($string)
    {
        return strip_tags(html_entity_decode($string));
    }
}

if (! function_exists('available_content_blocks')) {
    /**
     * Return a filtered list of content-blocks
     *
     * @return array
     */
    function available_content_blocks()
    {
        $featureBased = config('awardforce.feature-based-content-blocks');

        return array_filter(array_keys(config('awardforce.content-blocks')), function ($contentBlock) use ($featureBased) {
            return ! array_key_exists($contentBlock, $featureBased) || feature_enabled($featureBased[$contentBlock]);
        });
    }
}

if (! function_exists('old_translatable')) {
    function old_translatable($key)
    {
        return translations_from_request(old($key));
    }
}

if (! function_exists('blacklisted_url2png')) {
    function blacklisted_url2png($url)
    {
        foreach (config('services.url2png.blacklist') as $bl) {
            if (strpos($url, $bl) !== false) {
                return true;
            }
        }

        return false;
    }
}

if (! function_exists('search_area_to_url')) {
    /**
     *  Determine which route to redirect to based on the search area
     *
     * @param $string
     * @return string
     */
    function search_area_to_url($area)
    {
        $route = '';
        switch ($area) {
            case 'allocations.search':
                $route = 'funding.allocation.index';
                break;
            case 'assignments.search':
                $route = 'assignment.index';
                break;
            case 'categories.search':
                $route = 'category.index';
                break;
            case 'chapter.search':
                $route = 'chapter.index';
                break;
            case 'content.search':
                $route = 'content-block.index';
                break;
            case 'contracts.search':
                $route = 'contract.index';
                break;
            case 'event_logs.search':
                $route = 'audit.index';
                break;
            case 'fields.search':
                $route = 'field.index';
                break;
            case 'judging.search':
                $route = 'judging.index';
                break;
            case 'gallery_leaderboard.search':
            case 'qualifying_leaderboard.search':
            case 'top_pick_leaderboard.search':
            case 'vip_judging_leaderboard.search':
            case 'voting_leaderboard.search':
                $route = 'leaderboard.index';
                break;
            case 'manage_entries.search':
                $route = 'entry.manager.index';
                break;
            case 'manage_review_tasks.search':
                $route = 'review-flow.task.manage';
                break;
            case 'orders.search':
                $route = 'order.index';
                break;
            case 'panels.search':
                $route = 'panel.index';
                break;
            case 'gallery_progress.search':
            case 'qualifying_progress.search':
            case 'top_pick_progress.search':
            case 'vip_judging_progress.search':
            case 'voting_progress.search':
                $route = 'leaderboard.progress';
                break;
            case 'score_sets.search':
                $route = 'score-set.index';
                break;
            case 'scoring_criteria.search':
                $route = 'scoring-criteria.index';
                break;
            case 'tabs.search':
                $route = 'tab.index';
                break;
            case 'users.search':
                $route = 'users.index';
                break;
            case 'webhook.search':
                $route = 'webhook.index';
                break;
            case 'notification.search':
                $route = 'notification.index';
                break;
            case 'grant_status.search':
                $route = 'grant.status.index';
                break;
            case 'manage_grants.search':
                $route = 'grant.manager.index';
                break;
        }

        return route($route, [], false);
    }
}

if (! function_exists('account_name')) {
    /**
     * Returns the account name in the current language
     *
     * @return string|null
     */
    function account_name(?Account $account = null)
    {
        $account = $account ?: translate(current_account());

        $account->translated = collect($account->translated)->filter(function ($translations, $code) use ($account) {
            return in_array($code, $account->supportedLanguageCodes());
        })->toArray();

        return lang($account, 'name');
    }
}

if (! function_exists('query_string_with_bindings')) {
    function query_string_with_bindings($query): string
    {
        return Str::replaceArray(
            '?',
            collect($query->getBindings())
                ->map(function ($i) {
                    if (is_object($i)) {
                        $i = (string) $i;
                    } elseif (is_bool($i)) {
                        $i = (int) $i;
                    }

                    return (is_string($i)) ? "'$i'" : $i;
                })->all(),
            $query->toSql()
        );
    }
}

if (! function_exists('is_email')) {
    /**
     * Validates if string is an email
     */
    function is_email(string $value): bool
    {
        return Validator::make(['email' => $value], [
            'email' => 'required|email',
        ])->passes();
    }
}

if (! function_exists('vanity_domain_setup_url')) {
    function vanity_domain_setup_url($vanity)
    {
        $redirect = '/?domain='.urlencode($vanity);

        if (current_account()->isGoodGrants()) {
            return config('domains.setup.goodgrants').$redirect;
        }

        return config('domains.setup.awardforce').$redirect;
    }
}

if (! function_exists('suggested_root_domain')) {
    function suggested_root_domain()
    {
        $brand = brand_by_domain();

        return collect(config('domains.brands.'.$brand))->first();
    }
}

if (! function_exists('suggest_vanity_domain')) {
    function suggest_vanity_domain($name, $rootDomain): array
    {
        $domains = app(GlobalAccountRepository::class);
        $accountName = strip_special_chars(preg_replace("/\s+/", '', $name));

        $subDomain = substr($accountName, 0, 8);
        $fullDomain = $subDomain.'.'.$rootDomain;
        $isAvailable = $domains->isDomainAvailable($fullDomain);
        $suffix = 0;

        while (! $isAvailable) {
            $suffix++;
            $trimLength = 8 - strlen($suffix);
            $subDomain = substr($accountName, 0, $trimLength).$suffix;
            $fullDomain = $subDomain.'.'.$rootDomain;
            $isAvailable = $domains->isDomainAvailable($fullDomain);
        }

        return [
            'subDomain' => $subDomain,
            'rootDomain' => $rootDomain,
            'fullDomain' => $fullDomain,
        ];
    }
}

if (! function_exists('str_kebab')) {
    function str_kebab($string)
    {
        return Str::kebab($string);
    }
}

if (! function_exists('get_file_name')) {
    function get_file_name($filename)
    {
        return pathinfo($filename, PATHINFO_FILENAME);
    }
}

if (! function_exists('is_multiform')) {
    function is_multiform(): bool
    {
        return feature_enabled('multiform') && app(FormRepository::class)->countAllForSeason(
            SeasonFilter::viewingAll() ? null : SeasonFilter::get(),
            [Form::FORM_TYPE_ENTRY, Form::FORM_TYPE_REPORT]
        ) > 1;
    }
}

if (! function_exists('trans_elliptic')) {
    /**
     * Appends ellipsis in the end of the translated string, so we do not bloat the code with trans().'...' all around
     *
     * @param  null  $key
     * @param  array  $replace
     * @param  null  $locale
     */
    function trans_elliptic($key = null, $replace = [], $locale = null): string
    {
        return trans($key, $replace, $locale).'...';
    }
}

if (! function_exists('shorten_url')) {
    function shorten_url(string $url): string
    {
        return app(UrlShortenerService::class)->shorten($url);
    }
}

if (! function_exists('trans_merge')) {
    /**
     * Merges multiple translations together and applies `forced` sentence_case to the final string
     */
    function trans_merge(...$translations): string
    {
        return sentence_case(implode(' ', array_map(function ($translation) {
            if (is_array($translation)) {
                return trans(...$translation);
            }

            return trans($translation);
        }, $translations)), true);
    }
}

if (! function_exists('implode_and_strip_tags')) {
    /**
     * Strip tags array of strings and join by delimiter
     *
     * @param  array  $array
     */
    function implode_and_strip_tags($array = [], $delimiter = ', '): string
    {
        $result = '';

        foreach ($array as $key => $value) {
            $result .= strip_tags($value);
            $result .= $key !== array_key_last($array) ? $delimiter : '';
        }

        return $result;
    }
}

if (! function_exists('safe_double_quotes')) {
    /**
     * Convert the ordinary to double quotes to "right double quotation mark" to make it safe on the frontend.
     *
     * @return string
     */
    function safe_double_quotes($string)
    {
        return str_replace('"', '”', $string);
    }
}

if (! function_exists('empty_string')) {
    function empty_string($value): bool
    {
        return is_null($value) || $value === '';
    }
}

if (! function_exists('allowed_file_extension')) {
    /**
     * Get the allowed extensions from file types
     */
    function allowed_file_extension(string $extension): bool
    {
        foreach (config('filetypes') as $type => $fileTypes) {
            if (in_array($extension, array_keys($fileTypes))) {
                return true;
            }
        }

        return false;
    }
}

if (! function_exists('has_multiple_chapters')) {
    /**
     * Helper method to check if the account has multiple chapters enabled and chapter count > 1.
     */
    function has_multiple_chapters(): bool
    {
        return feature_enabled('multi_chapter') && current_account()->chapterQuantityLimit > 1;
    }
}

if (! function_exists('current_account_subscription_id')) {
    /**
     * Helper method to get current account's subscription customer id.
     */
    function current_account_subscription_id(): ?string
    {
        return current_account()->subscription_id;
    }
}

if (! function_exists('current_account_subscription_provider')) {
    /**
     * Helper method to get current account's subscription provider.
     */
    function current_account_subscription_provider(): ?string
    {
        return current_account()->subscriptionProvider;
    }
}

if (! function_exists('current_account_subscription_customer_id')) {
    /**
     * Helper method to get current account's subscription customer id.
     */
    function current_account_subscription_customer_id(): ?string
    {
        return current_account()->subscription_customer_id;
    }
}

if (! function_exists('add_zero_length_spaces')) {
    /**
     * Return if dates are apply to given range
     */
    function dateIsInRange(?string $from = null, ?string $fromTimezone = null, ?string $to = null, ?string $toTimezone = null, ?Carbon $date = null): bool
    {
        if ($date === null) {
            $date = now();
        }

        $startsAt = ! empty($from) ? convert_date_to_timezone($from, $fromTimezone) : null;
        $endsAt = ! empty($to) ? convert_date_to_timezone($to, $toTimezone) : null;

        if (empty($from) && empty($to)) {
            return true;
        } elseif (empty($from) && ! empty($to)) {
            return $date->lt($endsAt);
        } elseif (! empty($from) && empty($to)) {
            return $date->gt($startsAt);
        }

        return $date->between($startsAt, $endsAt);
    }
}

if (! function_exists('has_multiple_chapters')) {
    /**
     * Helper method to check if the account has multiple chapters enabled and chapter count > 1.
     */
    function has_multiple_chapters(): bool
    {
        return feature_enabled('multi_chapter') && current_account()->chapterQuantityLimit > 1;
    }
}

if (! function_exists('add_zero_length_spaces')) {
    /**
     * Add zero length space character after special characters.
     */
    function add_zero_length_spaces(string $value): string
    {
        return preg_replace('/[^\p{L}\p{N}]/u', '\\0&#8203;', $value);
    }
}

if (! function_exists('is_current_consumer')) {
    /**
     * Check if user with given id is the current consumer.
     */
    function is_current_consumer(?int $userId): bool
    {
        return Consumer::is($userId);
    }
}

if (! function_exists('slugs')) {
    /**
     * Returns array of slugs of a given collection.
     */
    function slugs(Collection $collection): array
    {
        return $collection->map(function ($element) {
            return (string) $element->slug;
        })->toArray();
    }
}

if (! function_exists('array_without_value')) {
    /**
     * Returns an array without a given element exists in values
     */
    function array_without_value(array $array, string $value): array
    {
        return array_values(Arr::except(array_combine($array, $array), $value));
    }
}

if (! function_exists('is_current_consumer')) {
    /**
     * Check if user with given id is the current consumer.
     */
    function is_current_consumer(?int $userId): bool
    {
        return Consumer::is($userId);
    }
}

if (! function_exists('slugs')) {
    /**
     * Returns array of slugs of a given collection.
     */
    function slugs(Collection $collection): array
    {
        return $collection->map(function ($element) {
            return (string) $element->slug;
        })->toArray();
    }
}

if (! function_exists('isGuestContext')) {
    /**
     * Checks if current context is guest
     */
    function isGuestContext(): bool
    {
        return app(ContextService::class)->selectedContext() === Guest::name();
    }
}

if (! function_exists('excel_escape')) {
    /**
     * Prepend a single quote (`) to the string if it starts with =, +, or - symbols.
     */
    function excel_escape(?string $text): string
    {
        return $text && in_array($text[0], ['=', '+', '-']) ? "'".$text : $text;
    }
}

if (! function_exists('get_consumer_locale_for_chargebee')) {
    /**
     * Get current consumer's locale for chargebee. If not found in available, english will be selected as default.
     */
    function get_consumer_locale_for_chargebee(): string
    {
        $locale = str_replace('_', '-', Consumer::languageCode());

        if (! in_array($locale, config('payment-subscriptions.gateways.chargebee.allowed-locales'))) {
            return 'en';
        }

        return $locale ?? 'en';
    }
}

if (! function_exists('emptyToNull')) {
    /**
     * Checks if emptry string convert to null
     *
     * @return bool
     */
    function emptyToNull($string): ?string
    {
        if (trim($string) === '') {
            $string = null;
        }

        return $string;
    }
}

if (! function_exists('remove_query_params')) {
    /**
     * Remove query params from a url
     */
    function remove_query_params(string $url): string
    {
        $parsedUrl = parse_url($url);

        return Arr::get($parsedUrl, 'scheme').'://'.Arr::get($parsedUrl, 'host').Arr::get($parsedUrl, 'path');
    }
}

if (! function_exists('has_active_grant_features')) {
    /**
     * Checks if it has any active grant features
     */
    function has_active_grant_features(): bool
    {
        return feature_enabled('contracts') ||
            feature_enabled('fund_management') ||
            feature_enabled('grants') ||
            feature_enabled('grant_reports');
    }
}

if (! function_exists('collection_mapper')) {
    /**
     * Map the collection with given mappings
     */
    function collection_mapper(Collection $data, array $mappings = []): Collection
    {
        return $data->map(fn($item) => collect($mappings)->mapWithKeys(fn($field, $key) => [is_int($key) ? $field : $key => $item->$field])->toArray())->values();
    }
}

if (! function_exists('array_only_wildcard')) {
    /**
     * Returns an array with keys matching the given wildcard
     */
    function array_only_wildcard(array $array, string $needle): array
    {
        $needle = str_replace('\\*', '.*?', preg_quote($needle, '/'));
        $needle = preg_grep('/^'.$needle.'$/i', array_keys($array));

        return array_intersect_key($array, array_flip($needle));
    }
}

if (! function_exists('array_merge_recursive_distinct')) {
    /**
     * Extracted from spatie/flare-client-php which is a dev dependency through the ignition package, but we use this helper function in our production code
     */
    function array_merge_recursive_distinct(array &$array1, array &$array2): array
    {
        $merged = $array1;
        foreach ($array2 as $key => &$value) {
            if (is_array($value) && isset($merged[$key]) && is_array($merged[$key])) {
                $merged[$key] = array_merge_recursive_distinct($merged[$key], $value);
            } else {
                $merged[$key] = $value;
            }
        }

        return $merged;
    }
}

if (! function_exists('random_color')) {
    function random_color(): string
    {
        return Arr::random(config('awardforce.default-user-colors'));
    }
}

if (! function_exists('thumbnail_src')) {
    function thumbnail_src(string $thumbnail, array $config = []): string
    {
        return Str::isUrl($thumbnail) ? $thumbnail : imgix($thumbnail, params: $config);
    }
}

if (! function_exists('array_to_styling')) {
    function array_to_styling(array $styles): string
    {
        return collect($styles)->map(function ($value, $key) {
            return match ($key) {
                'w' => "width: {$value}px;",
                'h' => "height: {$value}px;",
                'fit' => "object-fit: {$value};",
                'crop' => "object-position: {$value};",
                'auto' => "image-rendering: {$value};",
                'max-h' => "max-height: {$value}px;",
                default => "{$key}: {$value};",
            };
        })->implode(';');
    }
}

if (! function_exists('boot_safe')) {
    /**
     * Catch any RuntimeExceptions, which indicate the app hasn't booted yet.
     */
    function boot_safe(callable $callback): mixed
    {
        try {
            return $callback();
        } catch (\RuntimeException $e) {
            return null; // App hasn't booted yet
        } catch (BindingResolutionException $e) {
            return null; // App hasn't booted yet
        } catch (\Error $e) {
            // If the method was called upon a Facade, then the app hasn't booted yet
            if (Str::match('/^Call to undefined method/', $e->getMessage()) && Str::match('/Facade/', $e->getMessage())) {
                return null;
            }

            throw $e;
        }
    }
}

if (! function_exists('enforce_url_https')) {
    /**
     * Convert a URL to HTTPS if it's not already
     */
    function enforce_url_https(string $url): string
    {
        if (str_starts_with($url, 'http://')) {
            return 'https://'.substr($url, 7);
        }

        if (str_starts_with($url, 'https://')) {
            return $url;
        }

        return 'https://'.$url;
    }
}

if (! function_exists('decode_special_chars')) {
    function decode_special_chars(string $string): string
    {
        return str_replace(['&lt;', '&gt;', '&amp;'], ['<', '>', '&'], $string);
    }
}

if (! function_exists('throttling_disabled')) {
    /**
     * Check if throttling is disabled
     */
    function throttling_disabled(): bool
    {
        return ! config('awardforce.throttle_requests') && ! app()->isProduction();
    }
}

if (! function_exists('is_multiple')) {
    /**
     * Check if a float number is a multiple of another float number within a given epsilon
     */
    function is_multiple(float $number, float $multiple, float $epsilon = 0.000001): bool
    {
        return abs($number - round($number / $multiple) * $multiple) < $epsilon;
    }
}

if (! function_exists('translations_from_request')) {
    /**
     * Transform the translations received by the request:
     * ['subject' => ['en_gb' => 'Subject', 'fr_fr' => 'Sujet']]
     * To the one with the format we use in the frontend:
     * ['en_gb' => ['subject' => 'Subject'], 'fr_fr' => ['subject' => 'Sujet']]
     */
    function translations_from_request(?array $requestTranslations): array
    {
        return collect($requestTranslations ?? [])
            ->reduce(function ($translations, $langTranslations, $title) {
                foreach ($langTranslations as $lang => $value) {
                    $translations[$lang][$title] = $value;
                }

                return $translations;
            }, []);
    }
}

if (! function_exists('htmlify_translations')) {
    /**
     * Convert Markdown to HTML for specified properties in the translated object using Output::HTML.
     *
     * @param  \AwardForce\Library\Database\Eloquent\HtmlTranslatable  $model The input object or array containing a 'translated' property
     * @return array The modified 'translated' property with HTML content
     */
    function htmlify_translations(HtmlTranslatable $model): array
    {
        foreach ($model->htmlFields() as $field) {
            foreach ($model->translated ?? [] as $lang => &$translation) {
                if (is_array($translation) && isset($translation[$field]) && is_string($translation[$field])) {
                    $model->translated[$lang][$field] = Output::html($translation[$field]);
                }
            }
        }

        return $model->translated;
    }
}
