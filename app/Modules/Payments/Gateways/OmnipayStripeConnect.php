<?php

namespace AwardForce\Modules\Payments\Gateways;

use AwardForce\Modules\Payments\Contracts\Stripe as StripeContract;
use AwardForce\Modules\Payments\Exceptions\InvalidCreditCardDataException;
use AwardForce\Modules\Payments\Exceptions\InvalidGatewayConfigException;
use AwardForce\Modules\Payments\OmnipayTrait;
use AwardForce\Modules\Payments\RedirectResponse;
use AwardForce\Modules\Payments\Response;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Omnipay\Common\CreditCard;
use Omnipay\Common\Exception\InvalidCreditCardException;
use Omnipay\Common\GatewayFactory as Omnipay;
use Omnipay\Common\Message\ResponseInterface;

class OmnipayStripeConnect extends AbstractGateway implements StripeContract
{
    use OmnipayTrait;

    /**
     * Stripe Connect client id
     *
     * @var string
     */
    private $clientId;

    // Max 350 characters (https://stripe.com/docs/upgrades#:~:text=The%20description%20field%20on%20customer,length%20limit%20of%20250%20now.)
    protected ?int $descriptionCharacterLimit = 350;

    /**
     * Initialize payment gateway.
     *
     * @throws InvalidGatewayConfigException
     */
    public function __construct(string $clientId, bool $testMode)
    {
        // Stripe Connect hasn't been authorised in settings
        if (empty($clientId)) {
            throw new InvalidGatewayConfigException;
        }

        $apiKey = $testMode ? config('services.stripe_connect.api_key_test') : config('services.stripe_connect.api_key_live');

        $omnipay = new Omnipay;
        $this->gateway = $omnipay->create('\\'.StripePaymentIntents::class);
        $this->gateway->setTestMode($testMode);
        $this->gateway->setApiKey($apiKey);
        $this->gateway->setConnectedStripeAccount($clientId);
    }

    /**
     * Handle making the purchase
     *
     * @param  array  $data
     * @return \AwardForce\Modules\Payments\Contracts\Response
     *
     * @throws \Exception
     */
    public function purchase($amount, $data = [])
    {
        // Step 1: tokenize the credit card
        $card = $this->createCard($data);
        $cardToken = $this->tokenizeCard($card, $data['network'] ?? null);

        // Step 2: authorize the transaction
        // This step also returns the payment intent reference, which should be used in the following steps to reference this PaymentIntent
        $authorization = $this->authorize($amount, $cardToken, $data);

        $paymentIntentReference = $authorization->getPaymentIntentReference();

        $this->storeParams($params = [
            'paymentIntentReference' => $paymentIntentReference,
        ]);

        // Step 3: confirm the purchase
        $response = $this->completePurchase($paymentIntentReference);

        // Step 3.1: Stripe could require the user to confirm the operation in their site, so it should be redirected
        if ($response->isRedirect()) {
            return new RedirectResponse($response->getRedirectUrl());
        }

        $response = $this->tryConfirm($paymentIntentReference, $response);
        $response = $this->tryCapture($paymentIntentReference, $response);

        return new Response(
            $response->isSuccessful(),
            $response->getMessage(),
            $this->getTransactionRef($response),
            $this->getGatewayReturnedAmount($response),
            $this->getGatewayReturnedCurrency($response, $this->getCurrency())
        );
    }

    /**
     * @return Response
     *
     * @throws \Exception
     */
    public function complete()
    {
        $params = $this->retrieveParams();
        $this->clearParams();

        $response = $this->fetchPaymentIntent($paymentIntentReference = $params['paymentIntentReference']);

        if (StripePaymentIntents::isFailed($response)) {
            $response = $this->cancelPaymentIntent($paymentIntentReference);

            return new Response(
                false,
                trans('payments.payment_status.failed', ['url' => route('cart.view')]),
                $this->getTransactionRef($response),
                $this->getGatewayReturnedAmount($response),
                $this->getGatewayReturnedCurrency($response, $this->getCurrency())
            );
        }

        $response = $this->tryConfirm($paymentIntentReference, $response);
        $response = $this->tryCapture($paymentIntentReference, $response);

        return new Response(
            $response->isSuccessful(),
            $response->getMessage(),
            $this->getTransactionRef($response),
            $this->getGatewayReturnedAmount($response),
            $this->getGatewayReturnedCurrency($response, $this->getCurrency())
        );
    }

    /**
     * Return the gateway charged amount - formatted
     *
     * @param  float  $default
     * @return float
     */
    protected function getGatewayReturnedAmount($response, $default = 0.00)
    {
        $data = $response->getData();
        if (isset($data['amount'])) {
            return $this->formatAmount($data['amount']);
        }

        return $default;
    }

    /**
     * Return the gateway currency used
     *
     *
     * @return string
     */
    protected function getGatewayReturnedCurrency($response, $default)
    {
        $data = $response->getData();
        if (isset($data['currency'])) {
            return strtoupper($data['currency']);
        }

        return $default;
    }

    /**
     * Get transaction reference or return an empty string is none exists (because of payment failure).
     *
     *
     * @return string
     */
    protected function getTransactionRef($response)
    {
        $ref = $response->getTransactionReference();
        if (! is_null($ref)) {
            return $ref;
        }

        return '';
    }

    public function setStripeConnectClientId(string $stripeClientId)
    {
        $this->clientId = $stripeClientId;
    }

    /**
     * @return mixed
     */
    public function getStripeConnectClientId()
    {
        return $this->clientId;
    }

    /**
     * Returns credit card token that can be used to make charge request.
     * This method has been introduced due to fact that this functionality has not
     * been defined yet in stable releases of Omnipay/Stripe (available since version 3+).
     *
     * @return string
     *
     * @throws InvalidGatewayConfigException
     * @throws InvalidCreditCardException
     * @throws InvalidCreditCardDataException
     */
    protected function tokenizeCard(CreditCard $card, ?string $network = null)
    {
        $cardParams = $card->getParameters();
        $formCardParams = [
            'number' => $cardParams['number'],
            'exp_month' => $cardParams['expiryMonth'],
            'exp_year' => $cardParams['expiryYear'],
            'cvc' => $cardParams['cvv'],
            'name' => trim($cardParams['billingFirstName'].' '.$cardParams['billingLastName']),
            'address_line1' => trim($cardParams['billingAddress1'].' '.$cardParams['billingCompany']),
            'address_city' => $cardParams['billingCity'],
            'address_state' => $cardParams['billingState'],
            'address_zip' => $cardParams['billingPostcode'],
            'address_country' => $cardParams['billingCountry'],
        ];

        if ($network) {
            $formCardParams['networks']['preferred'] = $network;
        }

        $headers = [
            'Stripe-Version' => config('services.stripe_connect.api_version'),
            'Stripe-Account' => $this->gateway->connectedStripeAccount(),
        ];

        try {
            $response = Http::withHeaders($headers)
                ->throw()
                ->withBasicAuth($this->gateway->getApiKey(), '')
                ->asForm()
                ->post('https://api.stripe.com/v1/tokens', [
                    'card' => $formCardParams,
                ]);

            return $response->json('id');
        } catch (RequestException $e) {
            $message = strtolower($e->getMessage());

            // Try to gracefully show toastr error when client has not properly verified Stripe account
            if (Str::contains($message, 'must verify')) {
                throw new InvalidGatewayConfigException;
            } // Handle invalid credit card number

            if (Str::contains($message, ['invalid_number', 'incorrect_number'])) {
                throw new InvalidCreditCardException;
            }

            // Handle generic invalid credit card details e.g. "invalid_expiry_year" etc
            throw new InvalidCreditCardDataException;
        }
    }

    /**
     * @return ResponseInterface
     */
    public function tryConfirm($paymentIntentReference, ResponseInterface $response)
    {
        // Step 3.2: The operation could need additional confirmation
        if (StripePaymentIntents::requiresConfirmation($response)) {
            $response = $this->completePurchase($paymentIntentReference);
        }

        return $response;
    }

    /**
     * @return ResponseInterface
     */
    public function tryCapture($paymentIntentReference, ResponseInterface $response)
    {
        // Step 4: Stripe could require to capture an existing PaymentMethod (see https://stripe.com/docs/api/payment_intents/capture)
        if (StripePaymentIntents::requiresCapture($response)) {
            $response = $this->capture($paymentIntentReference);
        }

        return $response;
    }

    /**
     * Since we are using tokens, this method it is not required
     * To use the createPaymentMethod, it should replace the createCard and tokenizeCard, and then use the paymentMethodId
     * from this response as a 'paymentMethod' parameter in the `authorize()` method (instead of `token` parameter)
     *
     * @return ResponseInterface
     */
    protected function createPaymentMethod(array $data)
    {
        return $this->gateway->createCard($data)->send();
    }

    /**
     * @return ResponseInterface
     */
    protected function authorize($amount, $token, array $data = [])
    {
        return $this->gateway->authorize([
            'amount' => $amount,
            'currency' => $this->getCurrency(),
            'description' => $this->trimDescriptionToCharacterLimit(array_get($data, 'paymentDescription')),
            'token' => $token,
            'returnUrl' => current_account_url().$this->returnUrl(),
            'confirm' => false,
        ])->send();
    }

    /**
     * @return ResponseInterface
     */
    protected function completePurchase($paymentIntentReference)
    {
        return $this->gateway->completePurchase([
            'paymentIntentReference' => $paymentIntentReference,
            'returnUrl' => current_account_url().$this->returnUrl(),
            'connectedStripeAccountHeader' => $this->getStripeConnectClientId(),
        ])->send();
    }

    /**
     * @return ResponseInterface
     */
    protected function capture($paymentIntentReference)
    {
        return $this->gateway->capture([
            'paymentIntentReference' => $paymentIntentReference,
        ])->send();
    }

    /**
     * @return ResponseInterface
     */
    protected function fetchPaymentIntent($paymentIntentReference)
    {
        return $this->gateway->fetchPaymentIntent([
            'paymentIntentReference' => $paymentIntentReference,
        ])->send();
    }

    /**
     * @return ResponseInterface
     */
    protected function cancelPaymentIntent($paymentIntentReference)
    {
        return $this->gateway->cancelPaymentIntent([
            'paymentIntentReference' => $paymentIntentReference,
        ])->send();
    }

    protected function gatewayName(): string
    {
        return 'stripe-connect';
    }
}
