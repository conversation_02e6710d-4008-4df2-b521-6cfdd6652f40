<?php

namespace AwardForce\Modules\Payments\Gateways;

use AwardForce\Http\Controllers\Payment\PaymentRedirect;
use AwardForce\Modules\Ecommerce\Cart\Cart;
use AwardForce\Modules\Payments\Contracts\Gateway;
use AwardForce\Modules\Payments\Exceptions\InvalidGatewayConfigException;
use AwardForce\Modules\Payments\RedirectResponse;
use AwardForce\Modules\Payments\Response;
use Eloquence\Behaviours\Slug;
use Stripe\Checkout\Session;
use Stripe\Event;
use Stripe\Exception\SignatureVerificationException;
use Stripe\Webhook;

class StripeCheckout extends AbstractGateway implements Gateway
{
    protected string $apiKey;
    private string $currency;
    private array $gatewayParameters = [];

    use PaymentRedirect;

    const string NAME = 'stripe-checkout';

    /**
     * @throws InvalidGatewayConfigException
     */
    public function __construct(protected string $clientId, protected bool $testMode)
    {
        if (empty($clientId)) {
            throw new InvalidGatewayConfigException;
        }

        $this->apiKey = $testMode ? config('services.stripe_connect.api_key_test') : config('services.stripe_connect.api_key_live');
    }

    public function purchase($amount, $data = [])
    {
        try {
            \Stripe\Stripe::setApiKey($this->apiKey);
            $checkoutSession = Session::create($this->paymentParams($amount, $data), $this->paymentOptions());

            return new RedirectResponse($checkoutSession->url);
        } catch (\Throwable $e) {
            return new Response(false, $e->getMessage());
        }
    }

    public function complete()
    {
        if (array_get($this->gatewayParameters, 'type') == Event::PAYMENT_INTENT_SUCCEEDED) {
            return new Response(
                true,
                '',
                array_get($this->gatewayParameters, 'data.object.latest_charge'),
                $this->formatAmount(array_get($this->gatewayParameters, 'data.object.amount', 0)),
                $this->currency,
            );
        }

        return new Response(false, array_get($this->gatewayParameters, 'data.object.last_payment_error.message'));
    }

    public function getTestMode()
    {
        return $this->testMode;
    }

    public function setTestMode($mode)
    {
        $this->testMode = $mode;
    }

    public function getCurrency()
    {
        return $this->currency;
    }

    public function setCurrency($currency)
    {
        $this->currency = $currency;
    }

    private function paymentParams($amount, $data = [])
    {
        return [
            'payment_method_types' => [$this->gatewayName()],
            'line_items' => [
                [
                    'price_data' => [
                        'currency' => $this->getCurrency(),
                        'product_data' => [
                            'name' => array_get($data, 'itemsDescription'),
                        ],
                        'unit_amount' => (int) ((float) $amount * 100), // Amount in cents
                    ],
                    'quantity' => 1,
                ],
            ],
            'mode' => 'payment',
            'success_url' => $this->successRedirect($cart = app(Cart::class))->getTargetUrl(),
            'cancel_url' => route('payment.gateway.cancel', ['gateway' => $this->gatewayName()]),
            'payment_intent_data' => [
                'metadata' => [
                    'cart' => (string) $cart->slug(),
                    'global_account_id' => current_account()->globalId,
                ],
            ],
        ];
    }

    private function paymentOptions()
    {
        return [
            'stripe_account' => $this->clientId,
        ];
    }

    public function setGatewayParameters(array $parameters = [])
    {
        $this->gatewayParameters = $parameters;
    }

    /**
     * Extracts a cart slug from the request in the format defined in the paymentParams method in this class
     */
    public function cart(array $requestData): ?Slug
    {
        if ($slug = array_get($requestData, 'data.object.metadata.cart')) {
            return new Slug($slug);
        }

        return null;
    }

    /**
     * Use Stripe's Webhook object to validate the request signature
     */
    public function validate(string $payload, array $headers): bool
    {
        try {
            Webhook::constructEvent(
                $payload,
                array_first($headers['stripe-signature'] ?? []),
                config('services.stripe.webhook_secret')
            );

            return true;
        } catch (SignatureVerificationException $e) {
            \Log::notice('Failed to validate Stipe webhook signature');

            return false;
        }
    }

    /**
     * We cannot differentiate regions with Stripe, so we need to send webhooks for every event to all regions and
     * only process them in the region where the global account exists.
     *
     * Webhooks are also sent for events generated by our onsite Stripe integration (OmnipayStripeConnect). We need to
     * skip those and the easiest way to identify them is that they won't have a global account in their metadata.
     */
    public static function globalAccountId(array $requestData): ?string
    {
        return array_get($requestData, 'data.object.metadata.global_account_id');
    }

    protected function gatewayName(): string
    {
        return self::NAME;
    }
}
