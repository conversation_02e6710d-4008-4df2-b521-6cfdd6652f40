<?php

namespace AwardForce\Modules\Tags\Commands;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Tags\Events\TagWasAdded;
use AwardForce\Modules\Tags\Services\TagManager;
use Illuminate\Support\Facades\Event;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class AutoTagWithFieldValuesTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItDispatchesTagWasAddedEventForFieldsWithValues(): void
    {
        $model = $this->muffin(Entry::class);
        $model->fields = new Fields([
            $this->newField(true, 'checkboxlist', ['tag1']),
            $this->newField(true, 'checkboxlist', ['tag2']),
        ]);

        Event::fake();

        $tagManager = app(TagManager::class);
        $autoTagWithFieldValues = new AutoTagWithFieldValues($model, $tagManager);
        $autoTagWithFieldValues->handle();

        Event::assertDispatchedTimes(TagWasAdded::class, 2);
    }

    public function testItTagsOnlyOptionableWithAutoTagEnabled(): void
    {
        $model = $this->muffin(Entry::class);
        $model->fields = new Fields([
            $this->newField(true, 'checkboxlist', ['tag1']),
            $this->newField(true, 'checkboxlist', ['tag2']),
            $this->newField(true, 'text', ['tag3']),
            $this->newField(false, 'checkboxlist', ['tag4']),
            $this->newField(true, 'number', ['tag5']),
        ]);

        $autoTagWithFieldValues = new AutoTagWithFieldValues($model, app(TagManager::class));
        $autoTagWithFieldValues->handle();

        $this->assertEquals(
            ['tag1', 'tag2'],
            $model->tags->just('tag')
        );
    }

    public function testItDoesNotGenerateDuplicateTags(): void
    {
        $model = $this->muffin(Entry::class);
        $model->fields = new Fields([
            $this->newField(true, 'checkboxlist', ['tag1']),
            $this->newField(true, 'checkboxlist', ['tag2']),
            $this->newField(true, 'checkboxlist', ['tag1']),
        ]);

        $autoTagWithFieldValues = new AutoTagWithFieldValues($model, app(TagManager::class));

        $autoTagWithFieldValues->handle();
        $this->assertEquals(
            ['tag1', 'tag2'],
            $model->tags->just('tag')
        );
    }

    public function testItDoesNotApplyTagsWhenFieldBelongsToOtherCategory(): void
    {
        $model = $this->muffin(Entry::class);
        $field1 = $this->newField(true, 'checkboxlist', ['tag1']);

        $field2 = $this->newField(true, 'checkboxlist', ['tag2']);
        $field2->categoryOption = 'select';
        $field2->categories()->sync($model->categoryId);

        $field3 = $this->newField(true, 'checkboxlist', ['tag3']);
        $field3->categoryOption = 'select';
        $field3->categories()->sync($this->muffin(Category::class)->id);

        $model->fields = new Fields([
            $field1,
            $field2,
            $field3,
        ]);

        $autoTagWithFieldValues = new AutoTagWithFieldValues($model, app(TagManager::class));
        $autoTagWithFieldValues->handle();

        $this->assertEquals(
            ['tag1', 'tag2'],
            $model->tags->just('tag')
        );
    }

    private function newField(bool $autoTag, string $type, mixed $value): Field
    {
        $field = $this->muffin(Field::class, [
            'autoTag' => $autoTag,
            'type' => $type,
            'resource' => Field::RESOURCE_FORMS,
        ]);
        $field->value = $value;

        return $field;
    }
}
