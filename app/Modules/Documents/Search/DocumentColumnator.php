<?php

namespace AwardForce\Modules\Documents\Search;

use AwardForce\Library\Search\Actions\DeleteAction;
use AwardForce\Library\Search\Actions\DownloadAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Modules\Documents\Repositories\DocumentRepository;
use AwardForce\Modules\Documents\Search\Actions\EditDocumentAction;
use AwardForce\Modules\Documents\Search\Columns\Allocation;
use AwardForce\Modules\Documents\Search\Columns\Created;
use AwardForce\Modules\Documents\Search\Columns\Description;
use AwardForce\Modules\Documents\Search\Columns\Entry;
use AwardForce\Modules\Documents\Search\Columns\File;
use AwardForce\Modules\Documents\Search\Columns\FileType;
use AwardForce\Modules\Documents\Search\Columns\Name;
use AwardForce\Modules\Documents\Search\Columns\Shared;
use AwardForce\Modules\Documents\Search\Columns\Template;
use AwardForce\Modules\Documents\Search\Columns\Updated;
use AwardForce\Modules\Documents\Search\Columns\User;
use AwardForce\Modules\Documents\Search\Enhancers\Files;
use AwardForce\Modules\Documents\Search\Filters\AssociatableFilter;
use AwardForce\Modules\Entries\Search\Filters\EntrantFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Slug;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\KeywordFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class DocumentColumnator extends Columnator implements ApiColumnator
{
    use ApiColumns;

    public function resource(): string
    {
        return 'Documents';
    }

    protected function baseColumns(): Columns
    {
        return new Columns([
            new ReactiveMarker,
            $this->actionOverflow(),
            new Name,
            new FileType,
            new File,
            new User,
            new Shared,
            new Entry,
            new Allocation,
            new Description,
            new Template,
            new Created,
            new Updated,
            Slug::forResource('documents'),
        ]);
    }

    public function availableDependencies(Defaults $view): Dependencies
    {
        $columns = $this->columns($view);
        $dependencies = new Dependencies;

        $dependencies->add((new ColumnFilter(...$columns))->with(
            'documents.id',
            'documents.user_id',
            'documents.document_template_id',
            'documents.associatable_type',
            'documents.associatable_id',
            'documents.slug',
            'documents.file_type',
            'documents.shared',
            'documents.deleted_at',
            'documents.created_at',
            'documents.updated_at',
        ));
        $dependencies->add(new IncludeFilter([
            'documentTemplate',
            'translations',
        ]));
        $dependencies->add(new AssociatableFilter);
        $dependencies->add(new EntrantFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new GroupingFilter('documents.id'));
        $dependencies->add(new KeywordFilter($this->input['keywords'] ?? '', ['documents.slug', 'tf_documentname_table.value']));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns(['document.name']), 'Document', current_account_id(), []))
            ->restrictLanguage(consumer()->languageCode()));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns(['document_template.name', 'document_template.description']), 'DocumentTemplate', current_account_id(), []))
            ->restrictLanguage(consumer()->languageCode()));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view), $this->input, 'document.updated')->uniqueColumn('documents.id'));

        //Enhancers
        $dependencies->add(app(Files::class));

        return $dependencies;
    }

    public static function key(): string
    {
        return 'document.search';
    }

    public static function exportKey(): string
    {
        return 'document.export';
    }

    public function repository(): Repository
    {
        return app(DocumentRepository::class);
    }

    private function actionOverflow(): ActionOverflow
    {
        return (new ActionOverflow($this->key()))
            ->addAction(new DownloadAction('document', $this->resource()))
            ->addAction(new EditDocumentAction('document', $this->resource()))
            ->addAction(new DeleteAction('document', $this->resource()));
    }
}
