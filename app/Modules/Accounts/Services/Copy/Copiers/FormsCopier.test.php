<?php

namespace AwardForce\Modules\Accounts\Services\Copy\Copiers;

use AwardForce\Modules\Accounts\Services\Copy\CopierService;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;

class FormsCopierTest extends TestBaseCopier
{
    public function init()
    {
        $this->copier = app(FormsCopier::class);
        $this->setUpData();
    }

    public function testExport(): void
    {
        foreach ($this->forms as $form) {
            $this->copier->export($this->season->id, $form->id);
        }
        $exported = collect(array_values($this->copier->getExported()));
        $this->assertCount(4, $exported);

        foreach ($this->forms as $form) {
            $export = $exported->firstWhere('id', $form->id);
            $this->assertNotNull($export);
            $this->assertEquals($form->type, $export['type']);
            $this->assertEquals($form->chapterOption, $export['chapterOption']);
            $this->assertEquals($form->settings->toArray(), $export['settings']);
        }

        $this->assertNotEmpty($exported->firstWhere('id', $this->forms[1]->id)['files']);
        $this->assertCount(count($this->chapters), $exported->firstWhere('id', $this->forms[2]->id)['chapters']);
    }

    // Multiform new season
    public function testImport(): void
    {
        $this->multiform(true);
    }

    public function testImportSingleFormNewSeason(): void
    {
        $this->singleForm(true);
    }

    public function testImportExistingSeason(): void
    {
        $this->multiform(false);
    }

    public function testImportSingleFormExistingSeason(): void
    {
        $this->singleForm(false);
    }

    private function multiform(bool $newSeason): void
    {
        $features = Feature::spy();
        $features->shouldReceive('enabled')->with('multiform')->andReturn(true);
        $exported = array_values($this->importSetup($this->season->id, $this->forms->pluck('id')->toArray(), newSeason: $newSeason));
        $seasonId = app(SeasonCopier::class)->getImportedSeasonId();
        $existing = $this->copier->repository()->getAllForSeasonId($seasonId);

        foreach ($exported as $export) {
            $this->copier->import($export, $seasonId);
        }

        $imported = translate($this->copier->repository()->getAllForSeasonId($seasonId));
        $this->assertCount($existing->count() + 4, $imported);

        foreach ($this->forms as $form) {
            $import = $imported->firstWhere('id', $this->copier->getImportedId($form->id));
            $this->assertNotNull($import);
            $this->assertEquals($form->type, $import->type);
            $this->assertEquals($form->settings->browsable, $import->settings->browsable);
            $this->assertEquals($form->settings->invitationOnly, $import->settings->invitationOnly);
            $this->assertEquals($form->settings->promoted, $import->settings->promoted);
            $this->assertEquals($form->settings->countdown, $import->settings->countdown);
            $this->assertEquals($form->chapterOption, $import->chapterOption);
            $this->assertEquals($form->name.' '.$this->copyTerm, $import->name);
            $this->assertEquals($form->settings, $import->settings);
            if ($form->contentBlock) {
                $this->assertNotNull($import->contentBlock);
                $this->assertNotSame($import->contentBlock->id, $form->contentBlock->id);
            }
        }

        $this->assertTrue(app(CopierService::class)->hasQueuedFiles());
    }

    private function singleForm(bool $newSeason): void
    {
        $features = Feature::spy();
        $features->shouldReceive('enabled')->with('multiform')->andReturn(false);
        $exported = $this->importSetup($this->season->id, [$this->forms[1]->id], newSeason: $newSeason);
        $seasonId = app(SeasonCopier::class)->getImportedSeasonId();
        $activeSeasonId = SeasonFilter::getId();

        $formId = FormSelector::defaultForSeason($activeSeasonId)->id;
        $existing = $this->copier->repository()->getAllForSeasonId($seasonId);

        foreach ($exported as $export) {
            $this->copier->import($export, $seasonId, $formId);
        }

        $imported = translate($this->copier->repository()->getAllForSeasonId($seasonId));
        $this->assertCount($existing->count() ?: 1, $imported);

        $this->assertEquals($seasonId, $activeSeasonId);

        $form = $this->forms[1];
        $import = $imported->firstWhere('id', $this->copier->getImportedId($form->id));

        $this->assertNotNull($import);
        $this->assertEquals($form->type, $import->type);
        $this->assertEquals($form->settings->browsable, $import->settings->browsable);
        $this->assertEquals($form->settings->invitationOnly, $import->settings->invitationOnly);
        $this->assertEquals($form->settings->promoted, $import->settings->promoted);
        $this->assertEquals($form->settings->countdown, $import->settings->countdown);
        $this->assertEquals($form->chapterOption, $import->chapterOption);
        $this->assertEquals($form->name.' '.$this->copyTerm, $import->name);
        $this->assertEquals($form->settings, $import->settings);
        $this->assertTrue(app(CopierService::class)->hasQueuedFiles());
        if ($form->contentBlock) {
            $this->assertNotNull($import->contentBlock);
            $this->assertNotSame($import->contentBlock->id, $form->contentBlock->id);
        }
        if ($form->roles) {
            $this->assertNotContains($form->roles->first()->id, $import->roles->pluck('id'));
        }
    }

    private function setUpData()
    {
        $this->forms = collect();
        $this->forms[0] = $this->muffin(Form::class, ['settings' => FormSettings::create([
            'browsable' => true,
            'allowApplicationPdfDownload' => true,
            'minimumSimilarityPercentage' => 90,
        ])]);
        $this->forms[0]->contentblockId = $this->muffin(ContentBlock::class)->id;
        $this->forms[0]->save();

        $this->forms[1] = $this->muffin(Form::class, ['settings' => FormSettings::create([
            'collaborative' => true,
            'maxApplications' => 10,
            'displayId' => true,
        ])]);

        $this->muffin(File::class, ['resource' => File::RESOURCE_FORM, 'resource_id' => $this->forms[1]->id]);

        $role = $this->muffin(Role::class);
        $this->forms[1]->roles()->attach($role->id);

        $this->chapters = $this->muffins(2, Chapter::class);
        $this->forms->push($this->muffin(Form::class, ['chapter_option' => Form::CHAPTER_OPTION_ALL]));
        $this->copier->repository()->syncChapters($this->forms[2], [$this->chapters[0]->id, $this->chapters[1]->id]);

        $this->forms->push($this->muffin(Form::class, ['settings' => FormSettings::create(['lockWhenSubmitted' => true])]));
    }
}
