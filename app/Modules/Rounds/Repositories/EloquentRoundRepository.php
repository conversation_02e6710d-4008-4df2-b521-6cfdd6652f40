<?php

namespace AwardForce\Modules\Rounds\Repositories;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Database\Eloquent\Caching\HasRequestCache;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Database\Repository\Builder\HasSlugBuilder;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Forms\Forms\Database\Behaviours\DeletedForms;
use AwardForce\Modules\Forms\Forms\Traits\HasFormBuilder;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Rounds\Models\RoundCollection;
use AwardForce\Modules\Seasons\Repositories\EloquentSeasonalRepository;
use AwardForce\Modules\Seasons\Traits\HasSeasonalBuilder;
use Carbon\Carbon;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentRoundRepository extends Repository implements RoundRepository
{
    use DeletedForms;
    use EloquentSeasonalRepository;
    use HasFormBuilder;
    use HasQueryBuilder;
    use HasRequestCache;
    use HasSeasonalBuilder;
    use HasSlugBuilder;

    public function __construct(Round $round)
    {
        $this->model = $round;
    }

    /**
     * Returns a collection of rounds for the provided season.
     *
     * @param  int  $seasonId
     * @return \Illuminate\Support\Collection
     */
    public function getForSeason($seasonId)
    {
        return $this->getQuery()->whereSeasonId($seasonId)->get();
    }

    /**
     * Synchronise the database records with the provided chapter ids.
     *
     * @return mixed
     */
    public function syncChapters(Round $round, array $chapterIds)
    {
        $round->chapters()->sync($chapterIds);
    }

    /**
     * Returns a collection of judging rounds for the given season.
     *
     * @param  int  $seasonId
     * @return \Illuminate\Support\Collection
     */
    public function getJudgingForSeason($seasonId)
    {
        return $this->getQuery()
            ->whereSeasonId($seasonId)
            ->whereRoundType('judge')
            ->get();
    }

    /**
     * Returns a collection of judging rounds for the given season.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getJudgingForForm(int $formId)
    {
        return $this->getForForm($formId, 'judge');
    }

    public function getForForm(int $formId, string $type)
    {
        return $this->getQuery()
            ->whereFormId($formId)
            ->whereRoundType($type)
            ->get();
    }

    /**
     * Return the current, active rounds.
     *
     * @param  string  $type 'entry', 'judging' or 'feedback'.
     * @param  null|int|array  $chapterIds
     * @param  bool  $includeResubmission
     * @return mixed
     */
    public function getAllActive($type, $chapterIds = null, bool $activeChapters = true)
    {
        return $this->allActiveQuery($type, $chapterIds, $activeChapters)->get();
    }

    public function getAllActiveByForm(string $type, int $formId, $chapterIds = null, bool $activeChapters = true)
    {
        return $this->allActiveQuery($type, $chapterIds, $activeChapters)->where('form_id', $formId)->get();
    }

    public function getActiveByForm(string $type, int $formId)
    {
        return $this->getQuery()
            ->whereFormId($formId)
            ->whereRoundType($type)
            ->whereEnabled(true)
            ->where(fn($query) => $query->whereNull('ends_at')->orWhere('ends_at', '>=', now()->toDateTimeString()))
            ->get();
    }

    protected function allActiveQuery(string $type, $chapterIds = null, bool $activeChapters = true)
    {
        $query = $this->getActiveQuery($type, $chapterIds, $activeChapters);

        if ($this->hasChapters($type)) {
            $query->select(['rounds.*', DB::raw('GROUP_CONCAT(chapters.id) AS chapter_ids')]);
        }

        return $query;
    }

    /**
     * Return active rounds including ones that are only active in terms of `Review / Resubmission` not being closed.
     *
     * @return mixed
     */
    public function getActiveResubmissionRoundsByForm(string $type, int $formId, ?array $chapterIds = null)
    {
        return $this->activeResubmissionRoundsQuery($type, $chapterIds)
            ->where('form_id', $formId)
            ->get();
    }

    protected function activeResubmissionRoundsQuery(string $type, ?array $chapterIds)
    {
        $query = $this->getActiveResubmissionQuery($type, $chapterIds);

        if ($this->hasChapters($type)) {
            $query->select(['rounds.*', DB::raw('GROUP_CONCAT(chapters.id) AS chapter_ids')]);
        }

        return $query;
    }

    public function hasActiveForEntries(string $type, array $selectedEntriesIds): bool
    {
        return $this->getActiveQuery($type)
            ->leftJoin('forms', 'rounds.form_id', '=', 'forms.id')
            ->leftJoin('entries', 'forms.id', '=', 'entries.form_id')
            ->whereIn('entries.id', $selectedEntriesIds)
            ->exists();
    }

    /**
     * Return the current, active rounds.
     *
     * @param  string  $type 'entry', 'judging' or 'feedback'.
     * @param  null|int|array  $chapterIds
     * @param  bool  $activeChapters
     * @return mixed
     */
    public function getAllActiveIds($type, $chapterIds = null, $activeChapters = true)
    {
        return $this->getActiveQuery($type, $chapterIds, $activeChapters)->pluck('id');
    }

    /**
     * Counts the number of currently active rounds.
     *
     * @param  string  $type
     * @param  int|array  $chapterIds
     * @return int
     */
    public function countActive($type, int $formId, $chapterIds = null)
    {
        $query = $this->getActiveQuery($type, $chapterIds)->where('rounds.form_id', $formId);

        return $query->count();
    }

    /**
     * @param  string  $type
     * @return int
     */
    public function countAllActive($type, array $formIds)
    {
        $query = $this->getActiveQuery($type)->whereIn('rounds.form_id', $formIds);

        return $query->count();
    }

    /**
     * @param  bool  $includeResubmission
     * @return mixed
     */
    protected function getActiveQuery($type = null, $chapterIds = null, bool $activeChapters = true)
    {
        $query = $this->baseActiveQuery($type, $chapterIds, $activeChapters);

        $endDateConditions = function ($query) {
            $query->whereNull('rounds.ends_at');
            $query->orWhere('rounds.ends_at', '>=', (new Carbon)->toDateTimeString());
        };

        $query->where($endDateConditions);

        return $query;
    }

    /**
     * Counts the number of rounds with currently active resubmission / review.
     *
     * @param  string  $type
     * @param  int|array  $chapterIds
     * @return int
     */
    public function countActiveResubmission($type, int $formId, $chapterIds = null)
    {
        $query = $this->getActiveResubmissionQuery($type, $chapterIds)->where('rounds.form_id', $formId);

        return $query->count();
    }

    /**
     * @param  bool  $activeChapters
     * @return mixed
     */
    protected function getActiveResubmissionQuery($type = null, $chapterIds = null, $activeChapters = true)
    {
        $query = $this->baseActiveQuery($type, $chapterIds, $activeChapters);

        $endDateConditions = function ($query) {
            $now = (new Carbon)->toDateTimeString();

            $query->where(function ($query) use ($now) {
                $query->whereNull('rounds.review_ends_at');
                $query->orWhere(function ($query) use ($now) {
                    $query->orWhereNull('rounds.ends_at');
                    $query->orWhere('rounds.ends_at', '>=', $now);
                });
            });

            $query->orWhere('rounds.review_ends_at', '>=', $now);
        };

        $query->where($endDateConditions);

        return $query;
    }

    /**
     * Require a round by its slug value.
     *
     * @param  string  $slug
     * @return Field
     */
    public function requireBySlug($slug)
    {
        $round = parent::requireBy('slug', $slug);
        $round->load('season');

        return $round;
    }

    /**
     * Returns the active feedback rounds, plus the related chapter and category ids.
     *
     * @param  int  $chapterId
     * @param  int  $categoryId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getActiveFeedbackRounds($chapterId = null, $categoryId = null)
    {
        $query = $this->getActiveQuery(Round::ROUND_TYPE_FEEDBACK);
        $this->feedbackRoundsQuery($query);

        if ($chapterId) {
            $query->where('chapters.id', $chapterId);
        }

        if ($categoryId) {
            $query->where('round_feedback_options.category_id', $categoryId);
        }

        return $query->get();
    }

    /**
     * Returns the active feedback rounds, plus the related chapter and category ids including inactive chapters.
     *
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getActiveFeedbackRoundsWithInactiveChapters()
    {
        $query = $this->getActiveQuery(Round::ROUND_TYPE_FEEDBACK, null, false);
        $this->feedbackRoundsQuery($query);

        return $query->get();
    }

    private function feedbackRoundsQuery(&$query)
    {
        $query->join('round_feedback_options', 'round_feedback_options.round_id', '=', 'rounds.id')
            ->select([
                'rounds.*',
                DB::raw('GROUP_CONCAT(DISTINCT chapters.id) AS chapter_ids'),
                DB::raw('GROUP_CONCAT(DISTINCT round_feedback_options.category_id) AS category_ids'),
            ]);
    }

    /**
     * Returns a list of all open round IDs
     */
    public function allOpenIds(): array
    {
        return $this->getActiveQuery()->pluck('rounds.id')->toArray();
    }

    /**
     * @param  string  $type
     * @param  int|array  $chapterIds
     * @param  bool  $activeChapters
     * @return mixed
     */
    private function baseActiveQuery($type = null, $chapterIds = null, $activeChapters = true)
    {
        $now = (new Carbon)->toDateTimeString();

        $startDateConditions = function ($query) use ($now) {
            $query->orWhereNull('rounds.starts_at');
            $query->orWhere('rounds.starts_at', '<=', $now);
        };

        $query = $this->getQuery()
            ->whereNull('rounds.deleted_at')
            ->where('rounds.enabled', true)
            ->where($startDateConditions);

        $this->limitQueryByTypeAndSeason($query, $type);

        if ($this->hasChapters($type)) {
            $this->limitQueryByChapters($query, $chapterIds, $activeChapters);
        }

        return $query;
    }

    /**
     * Returns all the active rounds for a review task requirement.
     *
     * @return mixed
     */
    public function getActiveReviewTaskRounds(ReviewTask $reviewTask)
    {
        $query = $this->baseActiveQuery(Round::ROUND_TYPE_ENTRY, [$reviewTask->entry->chapterId], true);

        $endDateConditions = function ($query) {
            $now = (new Carbon)->toDateTimeString();

            $query->where(function ($query) use ($now) {
                $query->whereNull('rounds.review_ends_at');
                $query->orWhere('rounds.review_ends_at', '>=', $now);
            });
        };

        $query->where('form_id', $reviewTask->entry->formId);
        $query->where($endDateConditions);

        return $query->get();
    }

    /**
     * Returns the subset of IDs of the specified type, based on the original ID list
     */
    public function onlyType(array $ids, string $type): array
    {
        return $this->getQuery()
            ->whereIn('id', $ids)
            ->whereRoundType($type)
            ->pluck('id')
            ->all();
    }

    private function limitQueryByChapters($query, $chapterIds, $activeChapters)
    {
        $query->leftJoin('chapter_round', 'chapter_round.round_id', '=', 'rounds.id')
            ->leftJoin('chapters', 'chapters.id', '=', 'chapter_round.chapter_id')
            ->groupBy('rounds.id');

        if ($activeChapters) {
            $query->where(function ($query) {
                $query->where('chapters.active', 1)
                    ->orWhere('rounds.round_type', Round::ROUND_TYPE_JUDGE);
            });
        }

        if (is_null($chapterIds)) {
            return;
        }

        $query->whereIn('chapter_round.chapter_id', (array) $chapterIds);
    }

    /**
     * Optionally limits rounds to the given type and limits entry and judging rounds to the active season.
     *
     * @return mixed
     */
    private function limitQueryByTypeAndSeason($query, $type)
    {
        if (is_null($type)) {
            return $query->where(function ($query) {
                return $query
                    ->orWhere('rounds.round_type', Round::ROUND_TYPE_FEEDBACK)
                    ->orWhere(function ($query) {
                        return $query
                            ->where('rounds.round_type', '<>', Round::ROUND_TYPE_FEEDBACK)
                            ->where('rounds.season_id', $this->activeSeasonId());
                    });
            });
        }

        if (in_array($type, [Round::ROUND_TYPE_ENTRY, Round::ROUND_TYPE_JUDGE])) {
            $query->where('rounds.season_id', $this->activeSeasonId());
        }

        return $query->where('rounds.round_type', $type);
    }

    private function hasChapters($type): bool
    {
        return $type != Round::ROUND_TYPE_JUDGE;
    }

    /**
     * Get all active rounds by ids
     *
     * @return mixed
     */
    public function getActiveByIds(array $ids)
    {
        return $this->getActiveQuery()->whereIn('rounds.id', $ids)->get();
    }

    /**
     * Get all existing rounds by IDs and specified type
     */
    public function getByIdAndType(array $ids, string $type): Collection
    {
        return $this->getQuery()
            ->whereIn('id', $ids)
            ->whereRoundType($type)
            ->get();
    }

    /**
     * Return all rounds that are associated with score sets.
     *
     * @param  array|int  $scoreSetIds
     */
    public function forScoreSets($scoreSetIds): Collection
    {
        return $this->getQuery()
            ->select(['rounds.*', 'panels.score_set_id'])
            ->join('panel_round', 'panel_round.round_id', 'rounds.id')
            ->join('panels', function ($join) use ($scoreSetIds) {
                $join->on('panels.id', '=', 'panel_round.panel_id');
                $join->whereNull('panels.deleted_at');
                $join->whereIn('score_set_id', (array) $scoreSetIds);
            })
            ->get();
    }

    /**
     * Return all rounds that are associated with score sets.
     */
    public function getScoreSetRounds(int $seasonId, array $scoreSetIds = []): RoundCollection
    {
        $roundIds = $this->getJudgingForSeason($seasonId)->pluck('id');

        $panelRoundCollection = $this->getQuery()
            ->select(['rounds.*', 'score_set_id'])
            ->join('panel_round', 'panel_round.round_id', 'rounds.id')
            ->join('panels', function ($join) use ($scoreSetIds) {
                $join->on('panels.id', '=', 'panel_round.panel_id');
                $join->whereNull('panels.deleted_at');
                $join->whereIn('score_set_id', $scoreSetIds);
            })
            ->whereIn('rounds.id', $roundIds)
            ->get();
        translate($panelRoundCollection);

        $panelRoundCollection = $panelRoundCollection->groupBy('score_set_id');

        $manualAssignmentsRounds = $this->getQuery()
            ->select(['rounds.*', DB::raw('group_concat(DISTINCT score_set_id) as score_set_ids')])
            ->join('assignment_round', 'assignment_round.round_id', 'rounds.id')
            ->join('assignments', function (JoinClause $join) use ($scoreSetIds) {
                $join->on('assignments.id', '=', 'assignment_round.assignment_id')
                    ->whereIn('score_set_id', $scoreSetIds)
                    ->whereIn('method', [
                        Assignment::METHOD_MANUAL,
                        Assignment::METHOD_RANDOM,
                    ]);

                if ($user = Consumer::user()) {
                    $join->where('judge_id', $user->id);
                }
            })
            ->whereIn('assignment_round.round_id', $roundIds)
            ->groupBy('rounds.id')
            ->get();

        translate($manualAssignmentsRounds);

        /** @var Round $manualAssignmentRound */
        foreach ($manualAssignmentsRounds as $manualAssignmentRound) {
            foreach (explode(',', $manualAssignmentRound->score_set_ids) as $roundScoreSetId) {
                if (! in_array($roundScoreSetId, $panelRoundCollection->keys()->toArray())) {
                    $panelRoundCollection[$roundScoreSetId] = new RoundCollection([$manualAssignmentRound]);
                } else {
                    if (! $panelRoundCollection->firstWhere('id', $manualAssignmentRound->id)) {
                        $panelRoundCollection[$roundScoreSetId][] = $manualAssignmentRound;
                    }
                }
            }
        }

        return $panelRoundCollection
            ->map(function (RoundCollection $roundCollection) {
                return $roundCollection->unique('id');
            })
            ->pipeInto(RoundCollection::class);
    }

    /**
     * This method is used to modify the current query to only include rounds
     * where the 'enabled' field is set to true.
     * This is useful when you want to fetch only the enabled rounds from the database.
     */
    public function enabled(): self
    {
        $this->query()->where('rounds.enabled', true);

        return $this;
    }

    /**
     * This method adjusts the current query to include rounds based on their 'starts_at' and 'ends_at' fields,
     * by checking if they fall within a specified range of days from the current date.
     */
    public function daySpan(int $days): self
    {
        $from = Carbon::now()->subDays($days)->toDateTimeString();
        $to = Carbon::now()->addDays($days)->toDateTimeString();

        $dateConditions = function ($query) use ($from, $to) {
            $query->where(function ($query) use ($from) {
                $query->whereNull('rounds.ends_at');
                $query->orWhere('rounds.ends_at', '>=', $from);
            })->where(function ($query) use ($to) {
                $query->whereNull('rounds.starts_at');
                $query->orWhere('rounds.starts_at', '<=', $to);
            });
        };

        $this->query()->where($dateConditions);

        return $this;
    }

    public function notAssignAble(): self
    {
        $this->query()
            ->where('rounds.round_type', '!=', Round::ROUND_TYPE_JUDGE);

        return $this;
    }

    public function notStartedYet(): self
    {
        $this->query()->where('rounds.starts_at', '>', Carbon::now()->toDateTimeString());

        return $this;
    }

    public function type(string $type): self
    {
        $this->query()->where('rounds.round_type', $type);

        return $this;
    }
}
