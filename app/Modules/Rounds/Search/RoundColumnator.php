<?php

namespace AwardForce\Modules\Rounds\Search;

use AwardForce\Library\Search\Actions\DeleteAction;
use AwardForce\Library\Search\Actions\EditAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\Form;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Library\Search\Filters\EntryFormFilter;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\Rounds\Search\Columns\Chapters;
use AwardForce\Modules\Rounds\Search\Columns\RoundEndDate;
use AwardForce\Modules\Rounds\Search\Columns\RoundName;
use AwardForce\Modules\Rounds\Search\Columns\RoundSeason;
use AwardForce\Modules\Rounds\Search\Columns\RoundStartDate;
use AwardForce\Modules\Rounds\Search\Columns\RoundType;
use AwardForce\Modules\Rounds\Search\Filters\RoundSlugFilter;
use AwardForce\Modules\Rounds\Search\Filters\RoundTypeFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Slug;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class RoundColumnator extends Columnator implements ApiColumnator
{
    use ApiColumns;

    /**
     * @return string|null
     */
    public function resource()
    {
        return 'Rounds';
    }

    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;
        $dependencies->add((new ColumnFilter(...($columns = $this->columns($view))))->with(
            'rounds.id',
            'rounds.season_id',
            'rounds.form_id',
            'rounds.slug',
            'rounds.starts_tz',
            'rounds.ends_tz',
            'rounds.deleted_at'
        ));
        $dependencies->add(new RoundSlugFilter($this->input));
        $dependencies->add(new RoundTypeFilter($this->input));
        $dependencies->add(new IncludeFilter('season', 'form', 'chapters:id,slug'));
        $dependencies->add(SeasonalFilter::withInput($this->input, 'rounds.season_id'));
        $dependencies->add(new EntryFormFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new GroupingFilter('rounds.id'));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('round.name'), 'Round', current_account_id(), $this->input))->restrictLanguage($language = consumer()->languageCode()));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('form'), 'Form', current_account_id()))->restrictLanguage($language));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view), $this->input, 'rounds.updated')->uniqueColumn('rounds.id'));

        return $dependencies;
    }

    /**
     * @return mixed
     */
    protected function baseColumns()
    {
        return new Columns([
            new ReactiveMarker,
            $this->actionOverflow(),
            new RoundName,
            Slug::forResource('rounds'),
            new RoundSeason(trans('rounds.table.columns.season'), 'round.season'),
            new Form,
            new RoundType,
            new RoundStartDate,
            new RoundEndDate,
            new Chapters,
            Updated::forResource('rounds', consumer()->dateLocale()),
            Created::forResource('rounds', consumer()->dateLocale()),
        ]);
    }

    public static function key(): string
    {
        return 'round.search';
    }

    public static function exportKey(): string
    {
        return 'round.export';
    }

    public function repository(): Repository
    {
        return app(RoundRepository::class);
    }

    private function actionOverflow(): ActionOverflow
    {
        return (new ActionOverflow($this->key()))
            ->addAction(new EditAction('round', $this->resource()))
            ->addAction(new DeleteAction('round', $this->resource()));
    }
}
