<?php

namespace AwardForce\Modules\Api\V2\Commands;

use AwardForce\Modules\Forms\Fields\Services\ValuesService;

class ApiProcessFieldFileHandler extends ApiProcessFileHandler
{
    /** @var ValuesService */
    private $values;

    public function __construct(ValuesService $values)
    {
        $this->values = $values;
    }

    protected function syncValues(ApiProcessFile $command)
    {
        if ($command instanceof ApiProcessFieldFile) {
            $this->values->syncValuesForObject(
                [(string) $command->field()->slug => $command->file()->token],
                $command->resource()
            );
        }
    }
}
