<?php

namespace AwardForce\Modules\Contract\Search;

use AwardForce\Library\Search\Actions\DeleteAction;
use AwardForce\Library\Search\Actions\SimpleLinkAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Modules\Contract\Search\Columns\ContractTemplate;
use AwardForce\Modules\Contract\Search\Columns\Download;
use AwardForce\Modules\Contract\Search\Columns\EntryTitle;
use AwardForce\Modules\Contract\Search\Columns\SignedAt;
use AwardForce\Modules\Contract\Search\Columns\Signer;
use AwardForce\Modules\Contract\Search\Columns\Status;
use AwardForce\Modules\Contract\Search\Filters\ContentBlockFilter;
use AwardForce\Modules\Contract\Search\Filters\ContractStatusFilter;
use AwardForce\Modules\Contract\Search\Filters\EntryEntrantFilter;
use AwardForce\Modules\Contract\Search\Filters\IncludeContentBlockFilter;
use AwardForce\Modules\Entries\Contracts\ContractRepository;
use AwardForce\Modules\Entries\Search\Filters\FieldColumnOrderFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\KeywordFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class ContractColumnator extends Columnator
{
    /**
     * Each columnator must have a unique key that can be used to identify it when loading from settings.etc.
     */
    public static function key(): string
    {
        return 'contracts.search';
    }

    /**
     * @return string|null
     */
    public function resource()
    {
        return 'ContractAll';
    }

    /**
     * @return mixed
     */
    protected function baseColumns()
    {
        return new Columns([
            new ReactiveMarker,
            $this->actionOverflow(),
            new Signer,
            new ContractTemplate,
            new EntryTitle,
            new Status,
            new SignedAt,
            new Download,
        ]);
    }

    public function availableDependencies(Defaults $view): Dependencies
    {
        $columns = $this->columns($view);
        $dependencies = new Dependencies;

        $dependencies->add((new ColumnFilter(...$columns))->with(
            'contracts.id',
            'contracts.slug',
            'contracts.full_name',
            'contracts.account_id',
            'contracts.entry_id',
            'contracts.signed_at',
            'contracts.content_block_id',
            'contracts.email',
            'contracts.ip',
            'contracts.deleted_at'
        ));
        $dependencies->add(new IncludeFilter([
            'account',
            'entry',
            'entry.entrant',
        ]));
        $dependencies->add(new IncludeContentBlockFilter);
        $dependencies->add(new ContractStatusFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));
        $dependencies->add(new ContentBlockFilter($this->input));
        $dependencies->add(KeywordFilter::fromSplitKeywords($this->input['keywords']
            ?? '', ['entries.title', 'users.first_name', 'users.last_name']));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(FieldColumnOrderFilter::fromColumns($columns, $this->input, 'contracts.signed_at')->uniqueColumn('contracts.id'));
        $dependencies->add((new TranslatedColumnSearchFilter(
            $columns->translatedColumns('contracts.contract_template'),
            'ContentBlock',
            current_account_id()
        ))->setJoinTable('content_blocks')
            ->restrictLanguage($language = consumer()->languageCode()));
        $dependencies->add(new EntryEntrantFilter($this->input));

        return $dependencies;
    }

    public static function exportKey(): string
    {
        return 'contracts.export';
    }

    public function repository(): Repository
    {
        return app(ContractRepository::class);
    }

    private function actionOverflow(): ActionOverflow
    {
        $actionOverflow = new ActionOverflow($this->key());

        $actionOverflow->addAction(new SimpleLinkAction(
            'buttons.download',
            function ($record) {
                return route('entry.manager.contract-pdf', [
                    'entry' => $record->entry,
                    'contract' => $record,
                ]);
            },
            function ($record) {
                return $record->isSigned();
            }
        ));

        $actionOverflow->addAction(new DeleteAction('contract', $this->resource()));

        return $actionOverflow;
    }
}
