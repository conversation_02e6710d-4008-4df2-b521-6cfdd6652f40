<?php

namespace AwardForce\Modules\Forms\Fields\Events;

use AwardForce\Library\Database\Firebase\Locator;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\AttachmentField;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\ContributorField;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\FormField;
use AwardForce\Modules\Forms\Collaboration\Firebase\Locator\RefereeField;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Traits\ResolveFieldWebhooks;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Webhooks\Contracts\TriggersWebhooks;
use Platform\Search\HasValues;
use RuntimeException;

class FieldValueUpdated implements TriggersWebhooks
{
    use ResolveFieldWebhooks;

    public function __construct(public readonly HasValues $hasValues, public readonly Field $field, public $value)
    {
    }

    public function submittable(): Submittable
    {
        return once(function () {
            if ($this->field->entryField()) {
                return $this->hasValues;
            }

            return $this->hasValues->submittable;
        });
    }

    public function locator(): Locator
    {
        return match (true) {
            $this->field->entryField() => new FormField($this->hasValues, $this->field),
            $this->field->contributorField() => new ContributorField($this->hasValues, $this->field),
            $this->field->refereeField() => new RefereeField($this->hasValues, $this->field),
            $this->field->attachmentField() => new AttachmentField($this->hasValues, $this->field),
            default => throw new RuntimeException('Unknown field type'),
        };
    }

    public function fieldId(): int
    {
        return $this->field->id;
    }
}
