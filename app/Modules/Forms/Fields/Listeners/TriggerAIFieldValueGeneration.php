<?php

namespace AwardForce\Modules\Forms\Fields\Listeners;

use AwardForce\Modules\Forms\Fields\Contracts\TriggersAIFieldGeneration;
use AwardForce\Modules\Forms\Fields\Services\AIFieldValueGeneration;

readonly class TriggerAIFieldValueGeneration
{
    public function __construct(
        private AIFieldValueGeneration $aiFieldValueGeneration,
    ) {
    }

    public function handle(TriggersAIFieldGeneration $event): void
    {
        if (feature_disabled('ai_agents')) {
            return;
        }

        $this->aiFieldValueGeneration->triggerGeneration($event->fieldTriggerData());
    }
}
