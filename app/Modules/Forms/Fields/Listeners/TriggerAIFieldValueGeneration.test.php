<?php

namespace AwardForce\Modules\Forms\Fields\Listeners;

use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Fields\Contracts\TriggersAIFieldGeneration;
use AwardForce\Modules\Forms\Fields\Data\FieldTriggerData;
use AwardForce\Modules\Forms\Fields\Services\AIFieldValueGeneration;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class TriggerAIFieldValueGenerationTest extends BaseTestCase
{
    use Laravel;

    protected function init(): void
    {
        Feature::shouldReceive('enabled')->with('ai_agents')->andReturnTrue()->byDefault();
    }

    public function testDelegatesToService(): void
    {
        $event = $this->mock(TriggersAIFieldGeneration::class);

        $fieldTriggerData = new FieldTriggerData(
            trigger: AIFieldTrigger::EntrySubmitted,
            resource: new Resource(ResourceType::Entry, 123),
            formId: 456,
        );
        $event->shouldReceive('fieldTriggerData')->once()->andReturn($fieldTriggerData);

        $service = $this->mock(AIFieldValueGeneration::class);
        $service->shouldReceive('triggerGeneration')->once()->with($fieldTriggerData);

        $listener = new TriggerAIFieldValueGeneration($service);
        $listener->handle($event);
    }

    public function testItSkipsGenerationWhenFeatureIsDisabled(): void
    {
        $event = $this->mock(TriggersAIFieldGeneration::class);

        $service = $this->mock(AIFieldValueGeneration::class);
        $service->shouldNotReceive('triggerGeneration');
        Feature::shouldReceive('enabled')->with('ai_agents')->andReturnFalse();

        $listener = new TriggerAIFieldValueGeneration($service);
        $listener->handle($event);
    }
}
