<?php

namespace AwardForce\Modules\Forms\Fields\Configurations;

use AwardForce\Library\AIAgents\Enums\Model;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Illuminate\Database\Eloquent\Casts\Json;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class AiTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testGetPrompt(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $field->configuration = Json::encode([
            'prompt' => $prompt = 'Generate a helpful response',
        ]);
        $aiField = $field->getConfiguration();

        $this->assertEquals($prompt, $aiField->prompt());
    }

    public function testGetContexts(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $field->configuration = Json::encode([
            'contexts' => $contexts = [AIFieldContext::Entry->value,  AIFieldContext::Judging->value],
        ]);
        $aiField = $field->getConfiguration();

        $this->assertEquals($contexts, $aiField->contexts());
    }

    public function testContextsDefaultsToEmptyArray(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $aiField = $field->getConfiguration();

        $this->assertEquals([], $aiField->contexts());
    }

    public function testGetTriggers(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $field->configuration = Json::encode([
            'triggers' => $triggers = [AIFieldTrigger::EntrySubmitted->value, AIFieldTrigger::ManualOnDemand->value],
        ]);
        $aiField = $field->getConfiguration();

        $this->assertEquals($triggers, $aiField->triggers());
    }

    public function testTriggersDefaultsToEmptyArray(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $aiField = $field->getConfiguration();

        $this->assertEquals([], $aiField->triggers());
    }

    public function testGetModel(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $aiField = $field->getConfiguration();

        $this->assertEquals(Model::Claude35Sonnet, $aiField->model());
    }

    public function testToArray(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $field->configuration = Json::encode([
            'prompt' => $prompt = 'Generate a helpful response',
            'contexts' => $contexts = [AIFieldContext::Entry->value,  AIFieldContext::Judging->value],
            'triggers' => $triggers = [AIFieldTrigger::EntrySubmitted->value],
        ]);
        $aiField = $field->getConfiguration();

        $this->assertSame([
            'prompt' => $prompt,
            'contexts' => $contexts,
            'triggers' => $triggers,
            'model' => Model::Claude35Sonnet,
        ], $aiField->toArray());
    }

    public function testJsonSerialize(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $field->configuration = Json::encode([
            'prompt' => $prompt = 'Generate a helpful response',
            'contexts' => $contexts = [AIFieldContext::Entry->value, AIFieldContext::Judging->value],
            'triggers' => $triggers = [AIFieldTrigger::EntrySubmitted->value, 'onLoad'],
        ]);
        $aiField = $field->getConfiguration();

        $this->assertSame([
            'prompt' => $prompt,
            'contexts' => $contexts,
            'triggers' => $triggers,
            'model' => Model::Claude35Sonnet,
        ], $aiField->jsonSerialize());
    }

    public function testTranslated(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $aiField = $field->getConfiguration();

        $this->assertSame([], $aiField->translated());
    }

    public function testPromptDefaultsToNull(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $aiField = $field->getConfiguration();

        $this->assertNull($aiField->prompt());
    }

    public function testOption(): void
    {
        $field = $this->muffin(Field::class, ['type' => Field::TYPE_AI]);
        $field->configuration = Json::encode([
            'customOption' => 'customValue',
        ]);
        $aiField = $field->getConfiguration();

        $this->assertEquals('customValue', $aiField->option('customOption', 'defaultValue'));
        $this->assertEquals('defaultValue', $aiField->option('nonExistentOption', 'defaultValue'));
    }
}
