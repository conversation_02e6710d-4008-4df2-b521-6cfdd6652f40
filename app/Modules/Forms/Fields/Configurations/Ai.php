<?php

namespace AwardForce\Modules\Forms\Fields\Configurations;

use AwardForce\Library\AIAgents\Enums\Model;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;

class Ai implements Configuration
{
    private ?array $configuration;

    public function __construct(private Field $field)
    {
        $this->configuration = json_decode($field->configuration ?? '', true);
    }

    public function toArray(): array
    {
        return [
            'prompt' => $this->prompt(),
            'contexts' => $this->contexts(),
            'triggers' => $this->triggers(),
            'model' => $this->model(),
        ];
    }

    public function translated(): array
    {
        return [];
    }

    public function jsonSerialize(): array
    {
        return $this->toArray();
    }

    public function prompt(): ?string
    {
        return $this->option('prompt', null);
    }

    public function contexts(): array
    {
        return $this->option('contexts');
    }

    public function triggers(): array
    {
        return $this->option('triggers');
    }

    public function model(): Model
    {
        // TODO: pull from the aiAgent relationship when available, i.e: Model::from($this->field->aiAgent?->model)
        // Be sure to save the Enum name instead of the value in the database cause we might have to update the values when new models are added in Bedrock.
        return Model::Claude35Sonnet;
    }

    public function aiAgentId(): int
    {
        return $this->option('aiAgentId');
    }

    public function option(string $key, $default = [])
    {
        return array_get($this->configuration, $key, $default);
    }
}
