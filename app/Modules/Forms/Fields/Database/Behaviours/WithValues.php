<?php

namespace AwardForce\Modules\Forms\Fields\Database\Behaviours;

use AwardForce\Modules\Forms\Fields\Database\Repositories\RepositoryWithValues;
use AwardForce\Modules\Forms\Fields\Exceptions\CannotLoadFieldValue;
use Closure;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Search\HasValues;

trait WithValues
{
    /** @var Closure */
    private $filter;

    /**
     * @throws CannotLoadFieldValue
     */
    public function loadValuesForUpdate(HasValues $original)
    {
        if ($original->id) {
            $retrieved = $this->getQuery()
                ->select('values', 'hashes', 'protected')
                ->whereId($original->id)
                ->lockForUpdate()
                ->first();

            if ($retrieved) {
                $original->values = $retrieved->values ?? [];
                $original->hashes = $retrieved->hashes ?? [];
                $original->protected = $retrieved->protected ?? [];
            } else {
                throw new CannotLoadFieldValue;
            }
        } else {
            $original->values = [];
            $original->hashes = [];
            $original->protected = [];
        }
    }

    public function loadFieldValuesForObjects(array $objectIds, array $fieldSlugs, array $requiredColumns = []): Collection
    {
        if (empty($fieldSlugs) || empty($objectIds)) {
            return collect([]);
        }

        $slugs = array_map([$this, 'makeSlugKey'], $fieldSlugs, array_keys($fieldSlugs));
        $slugs = array_merge(...$slugs);

        return $this->getQuery()
            ->addSelect(DB::raw('id, '.implode(', ', array_map([$this, 'makeSlugSelect'], $slugs, array_keys($slugs)))))
            ->addSelect($requiredColumns)
            ->whereIn('id', $objectIds)
            ->get()
            ->mapWithKeys(function ($object) use ($slugs) {
                $values = [];
                foreach ($slugs as $key => $slug) {
                    if ($this->filter && ($this->filter)($object, $slug)) {
                        continue;
                    }
                    $value = json_decode($object->{$key} ?? '');
                    if (! empty($value)) {
                        $values[$slug] = $value;
                    }
                }

                return [$object->id => $values];
            });
    }

    public function updateSingleField(HasValues $object, string $fieldSlug, $value, $hash, $protected)
    {
        $value = DB::escape($value);
        $hash = DB::escape($hash);
        $protected = DB::escape($protected);

        return $this->getQuery()
            ->whereId($object->id)
            ->update([
                'values' => DB::raw("JSON_SET(IF(JSON_TYPE(`values`) = 'OBJECT', `values`, '{}'), '$.{$fieldSlug}', $value)"),
                'hashes' => DB::raw("JSON_SET(IF(JSON_TYPE(`hashes`) = 'OBJECT', `hashes`, '{}'), '$.{$fieldSlug}', $hash)"),
                'protected' => DB::raw("JSON_SET(IF(JSON_TYPE(`protected`) = 'OBJECT', `protected`, '{}'), '$.{$fieldSlug}', $protected)"),
            ]);
    }

    /**
     * Provide a method to exclude loading field values based on certain conditions.
     *
     * @return RepositoryWithValues
     */
    public function setFilter(Closure $filter)
    {
        $this->filter = $filter;

        return $this;
    }

    private function makeSlugSelect($slug, $index)
    {
        return "`values`->'$.{$slug}' AS '{$index}'";
    }

    private function makeSlugKey($slug, $index)
    {
        return ["slug-{$index}" => (string) $slug];
    }
}
