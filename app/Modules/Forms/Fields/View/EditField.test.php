<?php

namespace AwardForce\Modules\Forms\Fields\View;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Modules\Audit\Data\EventLogRepository;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Configurations\Table;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Forms\Fields\FieldsResource;
use AwardForce\Modules\Forms\Fields\Services\CompatibleTypes;
use AwardForce\Modules\Forms\Fields\Services\ConditionalFieldsListBuilder;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Fields\Traits\TableFieldConfigurator;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Integrations\Data\IntegrationRepository;
use AwardForce\Modules\Payments\Repositories\CurrencyRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Contracts\Config\Repository;
use Illuminate\Http\Request;
use Mockery as m;
use PHPUnit\Framework\Attributes\TestWith;
use Tectonic\LaravelLocalisation\Translator\Engine;
use Tests\IntegratedTestCase;

final class EditFieldTest extends IntegratedTestCase
{
    private $request;
    private $config;
    private $view;
    private $field;

    public function init()
    {
        $this->request = m::mock(Request::class);
        $this->request->shouldReceive('all');

        $this->consumer = m::mock(Manager::class);
        $this->config = m::mock(Repository::class);

        $this->field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);

        $this->translator = m::mock(Engine::class);

        $this->view = new EditField(
            $this->request,
            $this->config,
            $this->translator,
            m::mock(EventLogRepository::class),
            m::mock(CategoryRepository::class),
            m::mock(FieldRepository::class),
            m::mock(RoleRepository::class),
            m::mock(TabRepository::class),
            m::mock(SeasonRepository::class),
            m::mock(ConditionalFieldsListBuilder::class),
            m::mock(CompatibleTypes::class),
            m::mock(IntegrationRepository::class),
            m::mock(CurrencyRepository::class),
            app(EntryRepository::class)
        );
    }

    public function testFieldIsReadOnly(): void
    {
        $this->request->shouldReceive('route')->with('field')->once()->andReturn($this->field);
        $this->translator->shouldReceive('translate')->andReturn($this->field);

        $this->field->season->status = Season::STATUS_ARCHIVED;

        $this->assertTrue($this->view->readOnly());

        $this->field->season->status = Season::STATUS_ACTIVE;
        $this->assertFalse($this->view->readOnly());
    }

    public function testUserFieldAcrossSeasonsIsEditable(): void
    {
        $season = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS, 'seasonal' => false, 'season_id' => $season->id]);

        $this->request->shouldReceive('route')->with('field')->once()->andReturn($field);
        $this->translator->shouldReceive('translate')->andReturn($this->field);

        $this->assertFalse($this->view->readOnly());
    }

    public function testItUsesTableFieldConfiguratorTrait(): void
    {
        $this->assertTrue(in_array(TableFieldConfigurator::class, class_uses_recursive(EditField::class)));
    }

    public function testHasEntriesShouldReturnTrueWhenATableFieldExistsOnAnEntry()
    {
        $field = $this->getField();
        $field->configuration = json_encode(['rows' => ['col1', 'col2', 'col3']]);

        app(ValuesService::class)->setValuesForObject([
            ((string) $field->slug) => ['potatoes', 'eggs', 'spaghetti', 'oranges'],
        ], $this->muffin(Entry::class));

        $this->assertTrue($this->view->hasEntries());
    }

    public function testHasEntriesShouldReturnFalseWhenATableFieldDoesNotExistOnAnyEntry()
    {
        $this->getField();
        $this->muffin(Entry::class);

        $this->assertFalse($this->view->hasEntries());
    }

    public function testHasEntriesShouldReturnFalseWhenANonTableFieldExistsOnAnyEntry()
    {
        $field = $this->getField(Field::TYPE_TEXTAREA);
        app(ValuesService::class)->setValuesForObject([
            ((string) $field->slug) => ['TEXT'],
        ], $this->muffin(Entry::class));

        $this->assertFalse($this->view->hasEntries());
    }

    public function testConfigurationForTableField()
    {
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => Field::TYPE_TABLE]);
        $this->request->shouldReceive('route')->with('field')->once()->andReturn($field);
        $this->translator->shouldReceive('translate')
            ->with(m::type(Field::class))
            ->andReturn($field);
        $this->translator->shouldReceive('translate')
            ->with(m::type(Table::class))
            ->andReturn($field->getConfiguration());

        $this->assertIsArray($this->view->configuration());
    }

    public function testItHidesCategoriesWhenReportFormField()
    {
        $form = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'form_id' => $form->getKey()]);
        $this->request->shouldReceive('route')->with('field')->andReturn($field);
        $this->translator->shouldReceive('translate')
            ->with(m::type(Field::class))
            ->andReturn($field);

        $this->assertFalse($this->view->allowsCategorySelection());
    }

    public function testItShowsCategoriesWhenEntryFormField()
    {
        $form = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_ENTRY]);
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'form_id' => $form->getKey()]);
        $this->request->shouldReceive('route')->with('field')->andReturn($field);
        $this->translator->shouldReceive('translate')
            ->with(m::type(Field::class))
            ->andReturn($field);

        $this->assertTrue($this->view->allowsCategorySelection());
    }

    public function testItShowsCategoriesWhenNotAFormField()
    {
        $form = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_ENTRY]);
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES, 'form_id' => $form->getKey()]);
        $this->request->shouldReceive('route')->with('field')->andReturn($field);
        $this->translator->shouldReceive('translate')
            ->with(m::type(Field::class))
            ->andReturn($field);

        $this->assertTrue($this->view->allowsCategorySelection());
    }

    public function testItHidesCategoriesWhenUserOrOrganisationsField()
    {
        $resources = collect([FieldsResource::Users, FieldsResource::Organisations]);

        $resources->each(function (FieldsResource $resource) {
            $form = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_ENTRY]);
            $field = $this->muffin(Field::class, ['resource' => $resource->value, 'form_id' => $form->getKey()]);
            $this->request->shouldReceive('route')->with('field')->andReturn($field);
            $this->translator->shouldReceive('translate')
                ->with(m::type(Field::class))
                ->andReturn($field);

            $this->assertFalse($this->view->allowsCategorySelection());
        });
    }

    private function getField(string $type = Field::TYPE_TABLE): Field
    {
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => $type]);
        $this->request->shouldReceive('route')->with('field')->once()->andReturn($field);
        $this->translator->shouldReceive('translate')->andReturn($field);

        return $field;
    }

    #[TestWith([Field::RESOURCE_USERS, false])]
    #[TestWith([Field::RESOURCE_ORGANISATIONS, false])]
    #[TestWith([Field::RESOURCE_FORMS, true])]
    public function testItHasFormForSpecificResources(string $resource, bool $expected): void
    {
        $field = $this->muffin(Field::class, ['resource' => $resource]);
        $this->request->shouldReceive('route')->with('field')->once()->andReturn($field);
        $this->translator->shouldReceive('translate')->andReturn($field);

        $this->assertEquals($expected, $this->view->hasForm());
    }
}
