<?php

namespace AwardForce\Modules\Forms\Fields\Http\Validation;

use AwardForce\Modules\Forms\Fields\Configurations\Configuration;
use AwardForce\Modules\Forms\Fields\Configurations\Table\Filter;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Illuminate\Contracts\Validation\Rule;
use Illuminate\Support\Str;
use JsonSchema\Validator;

class TableFieldValidator implements Rule
{
    /** @var Field */
    protected $field;

    /** @var Configuration */
    protected $configuration;

    /** @var string */
    protected $message;

    public function __construct(Field $field)
    {
        $this->field = $field;
        $this->configuration = $this->field->getConfiguration();
    }

    /**
     * Determine if the validation rule passes.
     *
     * @param  string  $attribute
     * @param  mixed  $value
     * @return bool
     */
    public function passes($attribute, $value)
    {
        if (! $this->validateFormat($value)) {
            $this->message = 'validation.table';

            return false;
        }

        // Format is valid, extract the values
        $values = array_get(json_decode($value, true), 'values');

        if ($this->required() && empty($values)) {
            $this->message = 'validation.required';

            return false;
        }

        if (! $this->validateValues($values)) {
            $this->message = 'validation.table_values';

            return false;
        }

        return true;
    }

    /**
     * @param  mixed  $value
     * @return bool
     */
    private function validateFormat($value)
    {
        $validator = new Validator;
        $json = json_decode($value);
        $validator->validate($json, (object) $this->schema());

        return $validator->isValid();
    }

    /**
     * Return JSON schema
     *
     * @link: http://json-schema.org/
     *
     * @return mixed
     */
    private function schema()
    {
        return json_decode('{
            "$schema": "http://json-schema.org/schema#",
            "type": "object",
            "properties": {
                "dynamicRows": {
                    "type": "array",
                    "uniqueItems": true,
                    "items": {
                        "type": "string",
                        "pattern": "^row-[A-Za-z]{5}$"
                    }
                },
                "values": {
                    "anyOf": [
                        {
                            "type": "object",
                            "patternProperties": {
                                "^row-[A-Za-z]{5}$": {
                                    "type": "object",
                                    "patternProperties": {
                                        "^column-[A-Za-z]{5}$": {
                                            "type": "string"
                                        }
                                    },
                                    "additionalProperties": false
                                }
                            },
                            "additionalProperties": false
                        },
                        {
                            "type": "array",
                            "items": {
                                "type": "object",
                                "patternProperties": {
                                    "^row-[A-Za-z]{5}$": {
                                        "type": "object",
                                        "patternProperties": {
                                            "^column-[A-Za-z]{5}$": {
                                                "type": "string"
                                            }
                                        },
                                        "additionalProperties": false
                                    }
                                },
                                "additionalProperties": false
                            }
                        }
                    ]
                }
            },
            "required": ["dynamicRows", "values"],
            "additionalProperties": false
        }');
    }

    /**
     * @param  array  $values
     * @return bool
     */
    private function validateValues($values)
    {
        $invalidValues = [];

        $filters = $this->configuration->filters();

        foreach ($values as $row) {
            foreach ($row as $column => $value) {
                if (! $this->validateValue($value, $filters->columnType($column))) {
                    $invalidValues[] = $value;
                }
            }
        }

        return empty($invalidValues);
    }

    /**
     * @param string value
     * @param string type
     */
    private function validateValue($value, $type): bool
    {
        if (Filter::isNumericType($type) && ! is_numeric($value)) {
            return false;
        }

        switch ($type) {
            case Filter::TYPE_INTEGER:
                return preg_match('/^-?[0-9]*$/', $value);
                break;
            case Filter::TYPE_DECIMAL:
                return $this->decimalPlaces($value) == 1;
                break;
            case Filter::TYPE_DECIMAL_PRECISE:
                return $this->decimalPlaces($value) == 2;
                break;
            default:
                return true;
        }
    }

    /**
     * @param  string  $value
     */
    private function decimalPlaces($value): int
    {
        return strlen(substr(strrchr($value, '.'), 1));
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans($this->message, ['attribute' => Str::lower($this->field->title)]);
    }

    public function configuration(): Configuration
    {
        return $this->configuration;
    }

    protected function required(): bool
    {
        return $this->field->required;
    }
}
