<?php

namespace AwardForce\Modules\Forms\Fields\Http\Validation\Configurations;

class TableConfigurationValidator extends ConfigurationValidator
{
    /**
     * Return JSON schema
     *
     * @link: http://json-schema.org/
     *
     * @return mixed
     */
    public function schema()
    {
        return json_decode('{
          "$schema": "http://json-schema.org/schema#",
          "type": "object",
          "properties": {
            "columns": {
              "type": "array",
              "uniqueItems": true,
              "minItems": 1,
              "items": {
                "type": "string",
                "pattern": "^column-[A-Za-z]{5}$"
              }
            },
            "rows": {
              "type": "array",
              "uniqueItems": true,
              "minItems": 1,
              "items": {
                "type": "string",
                "pattern": "^row-[A-Za-z]{5}$"
              }
            },
            "filters": {
              "type": "array",
              "items": {
                "type": "object",
                "properties": {
                  "column": {
                    "type": [
                      "string",
                      "null"
                    ],
                    "pattern": "^column-[A-Za-z]{5}$"
                  },
                  "row": {
                    "type": [
                      "string",
                      "null"
                    ],
                    "pattern": "^row-[A-Za-z]{5}$"
                  },
                  "rules": {
                    "type": "object",
                    "properties": {
                      "type": {
                        "enum": [
                          "label",
                          "text",
                          "integer",
                          "decimal",
                          "decimal-precise",
                          "currency"
                        ]
                      },
                      "currency": {
                        "type": [
                          "string"
                        ],
                        "pattern": "^[A-Z]{3}"
                      }
                    },
                    "oneOf": [
                      {
                        "properties": {
                          "type": {
                            "enum": [
                              "currency"
                            ]
                          }
                        },
                        "required": [
                          "currency"
                        ]
                      },
                      {
                        "properties": {
                          "type": {
                            "enum": [
                              "label",
                              "text",
                              "integer",
                              "decimal",
                              "decimal-precise"
                            ]
                          }
                        }
                      }
                    ],
                    "required": [
                      "type"
                    ],
                    "additionalProperties": false
                  }
                },
                "required": [
                  "column",
                  "row",
                  "rules"
                ],
                "additionalProperties": false
              }
            },
            "dynamicRowsEnabled": {
              "type": "boolean"
            },
            "calculations": {
              "type": "array",
              "uniqueItems": true,
              "items": {
                "type": "object",
                "properties": {
                  "enable": {
                    "type": "boolean"
                  },
                  "column": {
                    "type": "string",
                    "pattern": "^column-[A-Za-z]{5}$"
                  },
                  "calculation": {
                    "enum": [
                      null,
                      "sum",
                      "average",
                      "count",
                      "min",
                      "max"
                    ]
                  }
                },
                "required": [
                  "enable",
                  "column",
                  "calculation"
                ],
                "additionalProperties": false
              }
            }
          },
          "required": [
            "columns",
            "rows",
            "filters",
            "dynamicRowsEnabled",
            "calculations"
          ],
          "additionalProperties": false
        }');
    }

    /**
     * Get the validation error message.
     *
     * @return string
     */
    public function message()
    {
        return trans('fields.validation_error.table');
    }
}
