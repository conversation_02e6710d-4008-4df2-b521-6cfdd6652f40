<?php

namespace AwardForce\Modules\Forms\Fields\Http\Validation\Configurations;

use AwardForce\Modules\Forms\Fields\Http\Validation\Configurations\TableConfigurationValidator as Validator;
use Tests\LightUnitTestCase;

final class TableConfigurationValidatorTest extends LightUnitTestCase
{
    public function testValidConfiguration(): void
    {
        $this->assertTrue((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[],"dynamicRowsEnabled":true}'));
        $this->assertTrue((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[],"dynamicRowsEnabled":false}'));
        $this->assertTrue((new Validator)->passes('configuration', '{"columns":["column-<PERSON>ILVU"],"rows":["row-gLddY"],"filters":[{"column":"column-ZILVU","row":null,"rules":{"type":"label"}}],"calculations":[],"dynamicRowsEnabled":true}'));
        $this->assertTrue((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[{"column":"column-ZILVU","row":null,"rules":{"type":"currency", "currency": "AUD"}}],"calculations":[],"dynamicRowsEnabled":true}'));

        // Calculations
        $this->assertTrue((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":true,"column":"column-ZILVU","calculation":"sum"}],"dynamicRowsEnabled":true}'));
        $this->assertTrue((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":false,"column":"column-ZILVU","calculation":null}],"dynamicRowsEnabled":false}'));
    }

    public function testInvalidConfiguration(): void
    {
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":[],"rows":[],"filters":[],"calculations":[],"dynamicRowsEnabled":"no"}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["ZILVU"],"rows":[],"filters":[],"calculations":[],"dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[{"column":"column-ZILVU","row":null,"rules":{"type":"-"}}],"calculations":[],"dynamicRowsEnabled":true}'));

        // Currency type with missing currency
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[{"column":"column-ZILVU","row":null,"rules":{"type":"currency"}}],"calculations":[],"dynamicRowsEnabled":true}'));

        // Calculations
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":true,"column":"column-ZILVU","calculation":"sum"},{"enable":true,"column":"column-ZILVU","calculation":"sum"}],"dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":"-","dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":"-","column":"column-ZILVU","calculation":"sum"}],"dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":true,"column":"-","calculation":"sum"}],"dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":true,"column":"column-ZILVU","calculation":"-"}],"dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"column":"column-ZILVU","calculation":"sum"}],"dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":true,"calculation":"sum"}],"dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":true,"column":"column-ZILVU"}],"dynamicRowsEnabled":true}'));
        $this->assertFalse((new Validator)->passes('configuration', '{"columns":["column-ZILVU"],"rows":["row-gLddY"],"filters":[],"calculations":[{"enable":true,"column":"column-ZILVU","calculation":"sum","extra":"-"}],"dynamicRowsEnabled":true}'));
    }
}
