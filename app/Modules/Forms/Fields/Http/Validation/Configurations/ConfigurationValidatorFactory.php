<?php

namespace AwardForce\Modules\Forms\Fields\Http\Validation\Configurations;

class ConfigurationValidatorFactory
{
    public static function forFieldType(string $type): ConfigurationValidator
    {
        return match ($type) {
            'table' => new TableConfigurationValidator,
            'formula' => new FormulaConfigurationValidator,
            default => new SimpleConfigurationValidator,
        };
    }
}
