<?php

namespace AwardForce\Modules\Forms\Fields\Http\Validation;

use AwardForce\Modules\Forms\Fields\Configurations\Table\Filter;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Http\Validation\TableFieldValidator as Validator;
use Illuminate\Support\Collection;
use Tests\IntegratedTestCase;

final class TableFieldValidatorTest extends IntegratedTestCase
{
    private $field;

    public function init()
    {
        $this->field = $this->muffin(Field::class, ['type' => 'table']);
    }

    public function testFormatValidation(): void
    {
        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{}}'));
        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":["row-CdFdw"],"values":{}}'));
        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":["row-CdFdw"],"values":{"row-CNzIX":{"column-gsgkV":"abc"}}}'));

        $this->assertFalse((new Validator($this->field))->passes('value', '{}'));
        $this->assertFalse((new Validator($this->field))->passes('value', '{"staticRows":[],"values":{}}'));
        $this->assertFalse((new Validator($this->field))->passes('value', '{"dynamicRows":["row-CdFdw"],"values":{"row-CNzIX":{"column-X":"abc"}}}'));
    }

    public function testRequiredValidation(): void
    {
        $this->field->required = true;
        $this->assertFalse((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{}}'));

        $this->field->required = false;
        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{}}'));
    }

    public function testValuesValidation(): void
    {
        $this->field->getConfiguration()
            ->setFilters(new Collection([
                new Filter(['type' => 'decimal'], 'column-gsgkV', null),
            ]));

        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"100.0"}}}'));
        $this->assertFalse((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"100.00"}}}'));
        $this->assertFalse((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"abc"}}}'));
    }

    public function testIntegerValues(): void
    {
        $this->field->getConfiguration()
            ->setFilters(new Collection([
                new Filter(['type' => 'integer'], 'column-gsgkV', null),
            ]));

        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"169"}}}'));
        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"0"}}}'));
        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"-18"}}}'));
        $this->assertFalse((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"-0.5"}}}'));
        $this->assertFalse((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"text"}}}'));
    }

    public function testCurrencyValues(): void
    {
        $this->field->getConfiguration()
            ->setFilters(new Collection([
                new Filter(['type' => 'currency', 'currency' => 'EUR'], 'column-gsgkV', null),
            ]));

        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"169"}}}'));
        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"0"}}}'));
        $this->assertTrue((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"1673.86"}}}'));
        $this->assertFalse((new Validator($this->field))->passes('value', '{"dynamicRows":[],"values":{"row-CNzIX":{"column-gsgkV":"text"}}}'));
    }
}
