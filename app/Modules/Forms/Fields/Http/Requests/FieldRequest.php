<?php

namespace AwardForce\Modules\Forms\Fields\Http\Requests;

use AwardForce\Library\Http\FormRequest;
use AwardForce\Library\Validation\ExistingIdRetriever;
use AwardForce\Library\Validation\TranslationUnique;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Validation\Rule;

abstract class FieldRequest extends FormRequest
{
    use ValidatesTranslations;

    public function messages(): array
    {
        return [
            'conditionalField.required_if' => trans('fields.form.conditional.validation.conditionalField'),
            'conditionalPattern.required_if' => trans('fields.form.conditional.validation.conditionalPattern'),
            'conditionalValue.required' => trans('fields.form.conditional.validation.conditionalValue'),
        ];
    }

    public function attributes(): array
    {
        return [
            'imageDimensionConstraints.maxWidth' => trans('validation.attributes.maxImageWidth'),
            'imageDimensionConstraints.minWidth' => trans('validation.attributes.minImageWidth'),
            'imageDimensionConstraints.maxHeight' => trans('validation.attributes.maxImageHeight'),
            'imageDimensionConstraints.minHeight' => trans('validation.attributes.minImageHeight'),
        ];
    }

    protected function prepareForValidation(): void
    {
        if ($this->type !== 'formula') {
            return;
        }

        $this->merge([
            'configuration' => $this->formulaConfiguration ?? $this->configuration,
        ]);
    }

    public function authorize(): bool
    {
        if (! parent::authorize()) {
            return false;
        }

        return $this->type !== 'formula' || feature_enabled('formula_field');
    }

    protected function titleRules(?int $ignore = null)
    {
        if ($this->resource == Field::RESOURCE_USERS) {
            return [
                new TranslationUnique(
                    new ExistingIdRetriever(app(FieldRepository::class), 'season_id', SeasonFilter::getId()),
                    'Field',
                    $ignore
                ),
            ];
        }

        return [
            Rule::when(
                $this->filled('formId'),
                fn() => new TranslationUnique(
                    new ExistingIdRetriever(app(FieldRepository::class), 'form_id', $this->get('formId')),
                    'Field',
                    $ignore
                )
            ),
        ];
    }
}
