<?php

use AwardForce\Modules\Forms\Fields\Http\Controllers\FieldController;
use Illuminate\Support\Facades\Route;

Route::group(['middleware' => 'auth.role'], function () {
    Route::get('field')
        ->middleware('remembrance')
        ->name('field.index')
        ->uses([FieldController::class, 'index']);

    Route::get('field/new/resource')
        ->name('field.resource')
        ->uses([FieldController::class, 'getResource']);

    Route::get('field/new')
        ->middleware('resourceFeature')
        ->name('field.new')
        ->uses([FieldController::class, 'getNew']);

    Route::post('field/preview')
        ->name('field.preview')
        ->uses([FieldController::class, 'preview']);

    Route::post('field')
        ->name('field.add')
        ->uses([FieldController::class, 'add']);

    Route::get('field/{field}')
        ->name('field.edit')
        ->uses([FieldController::class, 'edit']);

    Route::put('field/{field}')
        ->middleware('outdated')
        ->name('field.update')
        ->uses([FieldController::class, 'update']);

    Route::delete('field')
        ->name('field.delete')
        ->uses([FieldController::class, 'delete']);

    Route::put('field')
        ->name('field.undelete')
        ->uses([FieldController::class, 'undelete']);

    Route::post('field/copy')
        ->name('field.copy')
        ->uses([FieldController::class, 'copy']);

    Route::get('field/by-form/{form}')
        ->name('field.by-form')
        ->uses([FieldController::class, 'getByForm']);
});
