<?php

namespace AwardForce\Modules\Forms\Fields\Traits;

use AwardForce\Modules\Forms\Fields\Configurations\Table\Calculation;

trait TableFieldConfigurator
{
    public function tableFieldConfiguratorLabels(): array
    {
        $labels = [];

        $keys = [
            'add_row',
            'dynamic_rows',
            'configure_column',
            'insert_left',
            'insert_right',
            'delete_column',
            'column',
            'column_label',
            'column_type',
            'row_label',
            'display_calculation',
            'calculation_label',
        ];

        foreach ($keys as $key) {
            $labels[camel_case($key)] = trans('fields.table_field_configurator.'.$key);
        }

        $labels['save'] = trans('buttons.save');
        $labels['cancel'] = trans('buttons.cancel');
        $labels['alertDeleteRow'] = trans('fields.table.alerts.row-delete');
        $labels['alertDeleteColumn'] = trans('fields.table.alerts.column-delete');

        return $labels;
    }

    public function tableFieldInputTypes(): array
    {
        return [
            ['id' => 'label', 'name' => trans('fields.table_field_input_types.label')],
            ['id' => 'text', 'name' => trans('fields.table_field_input_types.text')],
            ['id' => 'integer', 'name' => trans('fields.table_field_input_types.integer')],
            ['id' => 'decimal', 'name' => trans('fields.table_field_input_types.decimal')],
            ['id' => 'decimal-precise', 'name' => trans('fields.table_field_input_types.decimal_precise')],
            ['id' => 'currency', 'name' => trans('fields.table_field_input_types.currency')],
        ];
    }

    public function tableFieldCalculationFunctions(): array
    {
        return [
            ['id' => Calculation::SUM, 'name' => trans('fields.table_field_calculation_functions.sum')],
            ['id' => Calculation::AVERAGE, 'name' => trans('fields.table_field_calculation_functions.average')],
            ['id' => Calculation::COUNT, 'name' => trans('fields.table_field_calculation_functions.count')],
            ['id' => Calculation::MIN, 'name' => trans('fields.table_field_calculation_functions.min')],
            ['id' => Calculation::MAX, 'name' => trans('fields.table_field_calculation_functions.max')],
        ];
    }
}
