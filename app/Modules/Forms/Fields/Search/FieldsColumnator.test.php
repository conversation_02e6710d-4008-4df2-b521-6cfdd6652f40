<?php

namespace AwardForce\Modules\Forms\Fields\Search;

use AwardForce\Library\Authorization\ApiConsumer;
use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Search\Columns\Form as FormColumn;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Search\Search;
use AwardForce\Modules\Search\Services\ActiveSettings;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use Platform\Features\Feature;
use Platform\Features\Features;
use Platform\Search\ColumnatorSearch;
use Tests\IntegratedTestCase;

final class FieldsColumnatorTest extends IntegratedTestCase
{
    private $columns = [
        'fields.title',
        'fields.type',
        'fields.resource',
        'fields.tab',
        'fields.categories',
        'fields.category_count',
    ];

    private function search(array $columns, array $input)
    {
        $search = Search::configureForSearch(
            $this->account->id,
            $this->account->activeSeason()->id,
            consumer_id() ?? $this->account->userId,
            $this->muffin(Form::class)->id,
            'fields.search',
            $columns
        );

        app(ActiveSettings::class)->setActive('fields.search', $search->id);

        $columnator = app(ColumnatorFactory::class)->forArea('fields.search', $input);

        return (new ColumnatorSearch($columnator))->search();
    }

    public function testCanSearchFields(): void
    {
        $this->muffins(3, Field::class);

        $items = $this->search($this->columns, []);

        $this->assertCount(3, $items);
    }

    public function testCanSearchByKeyword(): void
    {
        $fooField = $this->muffin(Field::class);
        $fooField->saveTranslation(default_language_code(), 'title', 'foo', $fooField->accountId);

        $barField = $this->muffin(Field::class);
        $barField->saveTranslation(default_language_code(), 'title', 'bar', $barField->accountId);

        $items = $this->search($this->columns, ['keywords' => 'foo']);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $fooField->id);
    }

    public function testCanSearchByType(): void
    {
        $numericField = $this->muffin(Field::class, ['type' => 'numeric']);
        $checkboxField = $this->muffin(Field::class, ['type' => 'checkbox']);
        $radioField = $this->muffin(Field::class, ['type' => 'radio']);

        $items = $this->search($this->columns, ['type' => 'checkbox']);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $checkboxField->id);
    }

    public function testCanSearchByResource(): void
    {
        $userField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS]);
        $entryField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $attachmentField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_ATTACHMENTS]);

        $items = $this->search($this->columns, ['resource' => Field::RESOURCE_ATTACHMENTS]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $attachmentField->id);
    }

    public function testCanSearchByTab(): void
    {
        $this->muffin(Field::class, ['tab_id' => null]);

        $tab = $this->muffin(Tab::class);
        $field = $this->muffin(Field::class, ['tab_id' => $tab->id]);

        $items = $this->search($this->columns, ['tab' => $tab->id]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $field->id);
    }

    public function testCanSearchByCategory(): void
    {
        $this->makeFieldInCategory();

        [$field, $category] = $this->makeFieldInCategory();

        $items = $this->search($this->columns, ['category' => $category->id]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $field->id);
    }

    public function testIncludesFieldsInDeletedCategories(): void
    {
        [$field, $category] = $this->makeFieldInCategory();
        $category->deleted_at = now();
        $category->save();

        $items = $this->search($this->columns, []);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $field->id);
    }

    private function makeFieldInCategory()
    {
        $field = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'category_option' => Field::CATEGORY_OPTION_SELECT,
        ]);

        $category = $this->muffin(Category::class);

        $field->categories()->attach($category);

        return [$field, $category];
    }

    public function testCanSearchByMultipleFilters(): void
    {
        $this->muffin(Field::class);

        $category = $this->muffin(Category::class);
        $tab = $this->muffin(Tab::class);

        $field = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'category_option' => Field::CATEGORY_OPTION_SELECT,
            'type' => 'numeric',
            'tab_id' => $tab->id,
        ]);

        $field->saveTranslation(default_language_code(), 'title', 'Niun Niggung', $field->accountId);

        $field->categories()->attach($category);

        $items = $this->search($this->columns, [
            'resource' => Field::RESOURCE_FORMS,
            'type' => 'numeric',
            'category' => $category->id,
            'tab' => $tab->id,
            'keywords' => 'niggung',
        ]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $field->id);
    }

    public function testCategoryCountWhenDeletingCategory(): void
    {
        $field = $this->muffin(Field::class);
        $category = $this->muffin(Category::class);
        $field->categories()->save($category);

        $fields = $this->search($this->columns, []);

        $this->assertEquals(1, $fields[0]->categories->count());

        $category->delete();

        $fields = $this->search($this->columns, []);

        $this->assertEquals(0, $fields[0]->categories->count());
    }

    public function testHidesFieldsFromDeletedForms(): void
    {
        $deletedForm = $this->muffin(Form::class, ['deleted_at' => now()]);
        $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'form_id' => $deletedForm->id]);

        $fields = $this->search($this->columns, []);

        $this->assertEmpty($fields);
    }

    public function testCanViewUserFieldsApi(): void
    {
        $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS]);

        $fields = $this->search($this->columns, []);

        $this->assertCount(1, $fields);
        $this->assertEmpty((new FormColumn)->apiValue($fields->first()));
    }

    public function testApiCanViewFieldsInArchivedSeasons(): void
    {
        Consumer::set(new UserConsumer(current_account()->owner));
        Consumer::set(new ApiConsumer);

        current_account()->defineFeatures(new Features([new Feature('multiform', 'enabled')]));

        SeasonFilter::set($archivedSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]));

        $archivedForm1 = $this->muffin(Form::class, ['season_id' => $archivedSeason->id]);
        $archivedForm2 = $this->muffin(Form::class, ['season_id' => $archivedSeason->id]);

        $this->muffin(Field::class, ['season_id' => $archivedSeason->id, 'form_id' => $archivedForm2->id, 'resource' => Field::RESOURCE_FORMS]);

        $this->assertCount(1, $this->search($this->columns, ['season' => (string) $archivedSeason->slug]));
    }
}
