<?php

namespace AwardForce\Modules\Forms\Fields\Search\Columns;

use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use Platform\Search\Defaults;

class SearchField
{
    private static $fieldColumns = [
        Checkbox::TYPE => Checkbox::class,
        CheckboxList::TYPE => CheckboxList::class,
        Country::TYPE => Country::class,
        Currency::TYPE => Currency::class,
        Date::TYPE => Date::class,
        DateTime::TYPE => DateTime::class,
        DropDownList::TYPE => DropDownList::class,
        Email::TYPE => Email::class,
        File::TYPE => File::class,
        Formula::TYPE => Formula::class,
        Numeric::TYPE => Numeric::class,
        Phone::TYPE => Phone::class,
        Radio::TYPE => Radio::class,
        Table::TYPE => Table::class,
        Text::TYPE => Text::class,
        TextArea::TYPE => TextArea::class,
        Time::TYPE => Time::class,
        Url::TYPE => Url::class,
    ];

    /**
     * return available field column that matches the provided
     * $field configuration.
     *
     * @return mixed
     *
     * @throws InvalidFieldColumn
     */
    public static function fromField(Field $field, string $foreignId, ?Defaults $defaults = null)
    {
        if ($fieldColumn = self::$fieldColumns[$field->type] ?? false) {
            return app($fieldColumn, compact('field', 'foreignId', 'defaults'));
        }

        throw new InvalidFieldColumn($field->type);
    }
}
