<?php

namespace AwardForce\Modules\Forms\Fields\Services;

use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use AwardForce\Modules\Forms\Fields\Bus\GenerateAIFieldValue;
use AwardForce\Modules\Forms\Fields\Data\FieldTriggerData;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\FieldRepository;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;

class AIFieldValueGeneration
{
    public function __construct(
        private readonly FieldRepository $fields,
    ) {
    }

    public function triggerGeneration(FieldTriggerData $triggerData): void
    {
        $commands = $this->aiFields($triggerData->trigger, $triggerData->formId)
            ->map(fn(int $fieldId) => new GenerateAIFieldValue($triggerData->resource, $fieldId));

        if ($commands->isEmpty()) {
            return;
        }

        Bus::chain($commands)->dispatch();
    }

    private function aiFields(AIFieldTrigger $trigger, ?int $formId): Collection
    {
        return $this->fields
            ->type(Field::TYPE_AI)
            ->hasTrigger($trigger->value)
            ->form($formId)
            ->tabSort()
            ->pluck('fields.id');
    }
}
