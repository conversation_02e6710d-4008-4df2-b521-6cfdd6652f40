<?php

namespace AwardForce\Modules\Forms\Fields\Services;

use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Bus\GenerateAIFieldValue;
use AwardForce\Modules\Forms\Fields\Data\FieldTriggerData;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\Facades\Bus;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class AIFieldValueGenerationTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testDispatchesCommandsForMatchingAIFields(): void
    {
        $form = $this->muffin(Form::class);
        $tab = $this->muffin(Tab::class, ['form_id' => $form->id]);
        $field1 = $this->muffin(Field::class, [
            'type' => Field::TYPE_AI,
            'form_id' => $form->id,
            'tab_id' => $tab->id,
            'order' => 1,
            'configuration' => Json::encode(['triggers' => [AIFieldTrigger::EntrySubmitted->value]]),
        ]);
        $field2 = $this->muffin(Field::class, [
            'type' => Field::TYPE_AI,
            'form_id' => $form->id,
            'tab_id' => $tab->id,
            'order' => 2,
            'configuration' => Json::encode(['triggers' => [AIFieldTrigger::EntrySubmitted->value]]),
        ]);
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);

        Bus::fake();

        app(AIFieldValueGeneration::class)->triggerGeneration(new FieldTriggerData(
            trigger: AIFieldTrigger::EntrySubmitted,
            resource: new Resource(ResourceType::Entry, $entry->id),
            formId: $form->id,
        ));

        Bus::assertChained([
            function (GenerateAIFieldValue $job) use ($entry, $field1) {
                return $job->resource->type === ResourceType::Entry
                    && $job->resource->id === $entry->id
                    && $job->fieldId === $field1->id;
            },
            function (GenerateAIFieldValue $job) use ($entry, $field2) {
                return $job->resource->type === ResourceType::Entry
                    && $job->resource->id === $entry->id
                    && $job->fieldId === $field2->id;
            },
        ]);
    }

    public function testDoesNotDispatchWhenNoMatchingFields(): void
    {
        $form = $this->muffin(Form::class);
        $this->muffin(Field::class, [
            'type' => Field::TYPE_AI,
            'form_id' => $form->id,
            'configuration' => Json::encode(['triggers' => [AIFieldTrigger::ManualOnDemand->value]]),
        ]);
        $this->muffin(Field::class, [
            'type' => Field::TYPE_FORMULA,
            'form_id' => $form->id,
            'configuration' => Json::encode(['triggers' => [AIFieldTrigger::ManualOnDemand->value]]),
        ]);
        $this->muffin(Field::class, [
            'type' => Field::TYPE_AI,
            'form_id' => $this->muffin(Form::class)->id,
            'configuration' => Json::encode(['triggers' => [AIFieldTrigger::EntrySubmitted->value]]),
        ]);
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id]);

        Bus::fake();

        app(AIFieldValueGeneration::class)->triggerGeneration(new FieldTriggerData(
            trigger: AIFieldTrigger::EntrySubmitted,
            resource: new Resource(ResourceType::Entry, $entry->id),
            formId: $form->id,
        ));

        Bus::assertNothingDispatched();
    }
}
