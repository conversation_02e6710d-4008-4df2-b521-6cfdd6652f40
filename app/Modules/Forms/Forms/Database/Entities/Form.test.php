<?php

namespace AwardForce\Modules\Forms\Forms\Database\Entities;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Enums\ScopeOption;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Exceptions\InvalidFormType;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class FormTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testInAvailableTypes(): void
    {
        $this->assertTrue(Form::inAvailableTypes(Form::FORM_TYPE_ENTRY));

        Feature::spy()->shouldReceive('enabled')->with('grant_reports')->andReturnTrue();

        $this->assertTrue(Form::inAvailableTypes(Form::FORM_TYPE_REPORT));
    }

    public function testNotInAvailableTypes(): void
    {
        $this->assertFalse(Form::inAvailableTypes('invalid'));
        $this->assertFalse(Form::inAvailableTypes(Form::FORM_TYPE_REPORT));
    }

    public function testLimitApplies()
    {
        $this->assertTrue(Form::limitApplies(Form::FORM_TYPE_ENTRY));
        $this->assertFalse(Form::limitApplies(Form::FORM_TYPE_REPORT));
    }

    public function testRounds(): void
    {
        /** @var Form $form */
        $form = $this->muffin(Form::class);
        $form->rounds()->saveMany([
            $round1 = $this->getRound(-100, -50),
            $this->getRound(-100, -40),
            $this->getRound(-20, -5),
        ]);
        $this->assertEquals(3, $form->rounds->count());
        $this->assertEquals('ended', $form->nextRound->status);
        $this->assertEquals($round1->id, $form->nextRound->id);

        $form1 = $this->muffin(Form::class);
        $form1->rounds()->saveMany([
            $this->getRound(-100, -50),
            $this->getRound(-100, -40),
            $this->getRound(-20, -5),
            $round4 = $this->getRound(100, 150),
        ]);
        $this->assertEquals(4, $form1->rounds->count());
        $this->assertEquals('starting', $form1->nextRound->status);
        $this->assertEquals($round4->id, $form1->nextRound->id);

        $form2 = $this->muffin(Form::class);
        $form2->rounds()->saveMany([
            $this->getRound(-100, -50),
            $this->getRound(-100, -40),
            $this->getRound(-20, -5),
            $this->getRound(100, 150),
            $round5 = $this->getRound(-1, 2),
        ]);
        $this->assertEquals(5, $form2->rounds->count());
        $this->assertEquals('ending', $form2->nextRound->status);
        $this->assertEquals($round5->id, $form2->nextRound->id);

        $form3 = $this->muffin(Form::class);
        $form3->rounds()->saveMany([
            $this->getRound(-100, -50),
            $this->getRound(-100, -40),
            $this->getRound(-20, -5),
            $this->getRound(100, 150),
            $this->getRound(-1, 2),
            $round6 = $this->getRound(-1, 250),
        ]);
        $this->assertEquals(6, $form3->rounds->count());
        $this->assertEquals('active', $form3->nextRound->status);
        $this->assertEquals($round6->id, $form3->nextRound->id);
    }

    public function testPanels(): void
    {
        $form1 = $this->muffin(Form::class);
        $form2 = $this->muffin(Form::class);

        $scoreSet1 = $this->muffin(ScoreSet::class, ['form_id' => $form1->id]);
        $scoreSet2 = $this->muffin(ScoreSet::class, ['form_id' => $form2->id]);

        $panel1 = $this->muffin(Panel::class, ['score_set_id' => $scoreSet1->id]);
        $panel2 = $this->muffin(Panel::class, ['score_set_id' => $scoreSet1->id]);
        $panel3 = $this->muffin(Panel::class, ['score_set_id' => $scoreSet2->id]);

        $this->assertCount(2, $form1->panels);
        $this->assertCount(1, $form2->panels);
        $this->assertNotFalse(array_search($panel1->id, $form1->panels->pluck('id')->toArray()));
        $this->assertNotFalse(array_search($panel2->id, $form1->panels->pluck('id')->toArray()));
        $this->assertNotFalse(array_search($panel3->id, $form2->panels->pluck('id')->toArray()));
    }

    public function testTabTypeAllowed(): void
    {
        $form1 = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $form2 = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);

        $this->assertTrue($form1->isTabTypeAllowed(Tab::TYPE_CONTRIBUTORS));
        $this->assertTrue($form1->isTabTypeAllowed(Tab::TYPE_ATTACHMENTS));
        $this->assertTrue($form1->isTabTypeAllowed(Tab::TYPE_DETAILS));
        $this->assertTrue($form1->isTabTypeAllowed(Tab::TYPE_ELIGIBILITY));
        $this->assertTrue($form1->isTabTypeAllowed(Tab::TYPE_FIELDS));

        $this->assertTrue($form2->isTabTypeAllowed(Tab::TYPE_CONTRIBUTORS));
        $this->assertTrue($form2->isTabTypeAllowed(Tab::TYPE_ATTACHMENTS));
        $this->assertTrue($form2->isTabTypeAllowed(Tab::TYPE_DETAILS));
        $this->assertTrue($form2->isTabTypeAllowed(Tab::TYPE_FIELDS));
        $this->assertFalse($form2->isTabTypeAllowed(Tab::TYPE_ELIGIBILITY));
    }

    private function getRound(int $startsIn, int $endsIn): Round
    {
        $round = $this->muffin(Round::class, ['round_type' => Round::ROUND_TYPE_ENTRY]);
        $round->startAt(Carbon::now()->addDays($startsIn)->format('Y-m-d H:i'), 'UTC');
        $round->endAt(Carbon::now()->addDays($endsIn)->format('Y-m-d H:i'), 'UTC');

        return $round;
    }

    public function testTypeMutator(): void
    {
        $form = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $this->assertEquals(Form::FORM_TYPE_ENTRY, $form->type);

        $form->type = Form::FORM_TYPE_ENTRY;
        $this->assertEquals(Form::FORM_TYPE_ENTRY, $form->type);

        $form->type = Form::FORM_TYPE_REPORT;
        $this->assertEquals(Form::FORM_TYPE_REPORT, $form->type);

        $this->expectException(InvalidFormType::class);
        $form->type = 'invalid';
        $this->assertNotEquals('invalid', $form->type);
    }

    #[TestWith(['entry', true])]
    #[TestWith(['entry', false])]
    #[TestWith(['report', true])]
    #[TestWith(['report', false])]
    public function testEntryIsLockedWhenSubmitted(string $type, bool $lockWhenSubmitted): void
    {
        $entity = $type === 'entry' ? new Entry : new GrantReport;
        $entity->form = ($form = new Form);
        $form->type = $type;
        $form->settings = FormSettings::create(['lockWhenSubmitted' => $lockWhenSubmitted], FormType::from($type));

        $this->assertSame($lockWhenSubmitted, $entity->form->settings->lockWhenSubmitted);
    }

    public function testFormSettings()
    {
        $form = new Form;
        $form->settings = FormSettings::create([
            'lockWhenSubmitted' => true,
        ]);

        $form->save();

        $this->assertTrue($form->settings->lockWhenSubmitted);
    }

    public function testSupportsRealTimeUpdatesWhenCollaborative(): void
    {
        Feature::shouldReceive('enabled')->with('collaboration')->andReturnTrue();
        Feature::shouldReceive('enabled')->with('api_submission_updates')->andReturnTrue();

        $form = new Form;
        $form->settings = FormSettings::create([
            'allowApiUpdates' => false,
            'collaborative' => true,
        ]);

        $this->assertTrue($form->supportsRealTimeUpdates());
    }

    public function testSupportsRealTimeUpdatesWhenAllowsApiUpdates(): void
    {
        Feature::shouldReceive('enabled')->with('collaboration')->andReturnTrue();
        Feature::shouldReceive('enabled')->with('api_submission_updates')->andReturnTrue();

        $form = new Form;
        $form->settings = FormSettings::create([
            'allowApiUpdates' => true,
            'collaborative' => false,
        ]);

        $this->assertTrue($form->supportsRealTimeUpdates());
    }

    public function testDoesNotSupportsRealTimeUpdatesWhenNeitherCollaborativeNorAllowApiUpdates(): void
    {
        Feature::shouldReceive('enabled')->with('collaboration')->andReturnTrue();
        Feature::shouldReceive('enabled')->with('api_submission_updates')->andReturnTrue();

        $form = new Form;
        $form->settings = FormSettings::create([
            'allowApiUpdates' => false,
            'collaborative' => false,
        ]);

        $this->assertFalse($form->supportsRealTimeUpdates());
    }

    public function testDoesNotSupportsRealTimeUpdatesWhenBothFeaturesAreDisabled(): void
    {
        Feature::shouldReceive('enabled')->with('collaboration')->andReturnFalse();
        Feature::shouldReceive('enabled')->with('api_submission_updates')->andReturnFalse();

        $form = new Form;
        $form->settings = FormSettings::create(['collaborative' => true, 'allowApiUpdates' => true]);

        $this->assertFalse($form->supportsRealTimeUpdates());
    }

    public function testCastsSettingsDependingOnFormType()
    {
        $form1 = new Form;
        $form1->type = Form::FORM_TYPE_ENTRY;
        $form1->save();

        $form2 = new Form;
        $form2->type = Form::FORM_TYPE_REPORT;
        $form2->save();

        $this->assertInstanceOf(FormSettings::class, $form1->settings);
        $this->assertInstanceOf(FormSettings::class, $form2->settings);
    }

    public function testItSyncRoles()
    {
        $form = $this->muffin(Form::class);
        $roles = $this->muffins(2, Role::class);

        app(FormRepository::class)->syncRoles($form, $roles);

        $intersectedRoles = $form->refresh()->roles->intersect($roles);
        $this->assertTrue($intersectedRoles->isNotEmpty());
        $this->assertCount(count($roles), $intersectedRoles);
        $this->assertEqualsCanonicalizing($intersectedRoles->pluck('id')->toArray(), collect($roles)->pluck('id')->toArray());

        app(FormRepository::class)->syncRoles($form, []);
        $this->assertCount(0, $form->refresh()->roles);
    }

    public function testRoleEnabledShouldBeTrueIfFormAppliesToAllRoles()
    {
        $form = $this->muffin(Form::class);
        $form->settings = FormSettings::create(['roleVisibility' => ScopeOption::All->value]);

        $this->assertTrue($form->roleEnabled());
    }

    public function testRoleEnabledShouldBeFalseIfFormAppliesToSelectedRolesAndUserRoleIsNotAssigned()
    {
        $role = $this->muffin(Role::class);
        $form = $this->muffin(Form::class);
        $form->settings = FormSettings::create(['roleVisibility' => ScopeOption::Select->value]);
        app(FormRepository::class)->syncRoles($form, [$role->id]);

        $this->assertFalse($form->roleEnabled());
    }

    public function testRoleEnabledShouldBeTrueIfFormAppliesToSelectRolesAndUserRoleIsAssigned()
    {
        $role = $this->muffin(Role::class);
        Consumer::set(new UserConsumer($user = $this->muffin(User::class)));
        $user->roles()->sync([$role->id]);
        $form = $this->muffin(Form::class);
        $form->settings = FormSettings::create(['roleVisibility' => ScopeOption::Select->value]);
        app(FormRepository::class)->syncRoles($form, [$role->id]);

        $this->assertTrue($form->roleEnabled());
    }
}
