<?php

namespace AwardForce\Modules\Forms\Forms\Database\Repositories;

use AwardForce\Library\Database\Eloquent\Caching\WithRequestCaching;
use AwardForce\Modules\Chapters\Contracts\WithChapterBuilderRepository;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Seasons\Contracts\SeasonalBuilderRepository;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Repositories\SeasonalRepository;
use Illuminate\Support\Collection;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Repository;

interface FormRepository extends BuilderRepository, Repository, SeasonalBuilderRepository, SeasonalRepository, WithChapterBuilderRepository, WithRequestCaching
{
    /**
     * Returns the default form for the given season.
     *
     * @param  Season|int  $season
     */
    public function defaultForSeason($season, ?string $type = Form::FORM_TYPE_ENTRY): ?Form;

    /**
     * Contextualise the query by finding a form that matches the required slug.
     */
    public function slug(string $slug): static;

    /**
     * Return all forms in the season
     *
     * @param  string  $type
     */
    public function getAllForSeason(?Season $season = null, array|string $type = Form::FORM_TYPE_ENTRY, bool $includeInvitationOnly = true): Collection;

    public function countAllForSeason(?Season $season = null, array|string $type = Form::FORM_TYPE_ENTRY, bool $includeInvitationOnly = true): int;

    /**
     * Return all forms in the season except the archived once.
     */
    public function getForSeasonOrNonArchived(?Season $season = null, array|string $type = Form::FORM_TYPE_ENTRY): Collection;

    /**
     * Return all forms in the season, included soft-deleted
     */
    public function getAllWithTrashedForSeason(Season $season): Collection;

    /**
     * Return all forms in the season that apply to the given chapters
     */
    public function getAllForSeasonAndChapters(Season $season, array $chapters, string $type = Form::FORM_TYPE_ENTRY): Collection;

    /**
     * Sync chapters.
     *
     * @return void
     */
    public function syncChapters(Form $form, array $chapterIds);

    public function isAtLimit(int $seasonId): bool;

    public function isSeasonMultiform(int $seasonId): bool;

    /**
     * Returns all orphaned form ids.
     */
    public function orphanedIds(): array;

    /**
     * Force delete forms based on given ids
     */
    public function destroy(array $ids);

    public function promotedForSeason(?int $seasonId, bool $roundsActive = true): Collection;

    public function forType(string $type): static;

    public function allowApiUpdates(bool $allowed = true): self;

    public function getAllForEntriesInExistingGrantReports(?int $formId = null): Collection;

    public function setting(string $setting, bool $value): self;

    public function selectSettings(...$setting): self;

    public function activeRounds(): self;

    public function entries(array $entryIds): self;

    public function syncRoles(Form $form, array $rolesIds): array;

    public function roles(array $rolesIds = []): self;
}
