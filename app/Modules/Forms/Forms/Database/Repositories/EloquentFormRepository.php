<?php

namespace AwardForce\Modules\Forms\Forms\Database\Repositories;

use AwardForce\Library\Database\Eloquent\Caching\HasRequestCache;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Database\Repository\Builder\HasSlugBuilder;
use AwardForce\Library\Enums\ScopeOption;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Chapters\Repositories\HasChapterBuilder;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Repositories\EloquentSeasonalRepository;
use AwardForce\Modules\Seasons\Traits\HasSeasonalBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentFormRepository extends Repository implements FormRepository
{
    use EloquentSeasonalRepository;
    use HasCha<PERSON>erBuilder;
    use HasQueryBuilder;
    use HasRequestCache;
    use HasSeasonalBuilder;
    use HasSlugBuilder;

    public function __construct(Form $form)
    {
        $this->model = $form;
    }

    public function defaultForSeason($season, ?string $type = Form::FORM_TYPE_ENTRY): ?Form
    {
        $seasonId = $season instanceof Season ? $season->id : $season;

        return $this->getQuery()
            ->whereSeasonId($seasonId)
            ->when($type, function ($query, $type) {
                $query->whereType($type);
            })
            ->first();
    }

    private function allForSeasonId(?int $seasonId = null, array|string $type = Form::FORM_TYPE_ENTRY, bool $includeInvitationOnly = true): Builder
    {
        return $this->getQuery()
            ->when($seasonId, fn($query) => $query->whereSeasonId($seasonId))
            ->when(! $includeInvitationOnly, fn($query) => $query->where('forms.settings->invitationOnly', false))
            ->whereIn('type', (array) $type)
            ->orderByRaw('`order` is null')
            ->orderBy('order', 'asc');
    }

    public function getAllForSeasonId(?int $seasonId = null, array|string $type = Form::FORM_TYPE_ENTRY, bool $includeInvitationOnly = true): Collection
    {
        return $this->allForSeasonId($seasonId, $type, $includeInvitationOnly)->get();
    }

    public function getAllForSeason(?Season $season = null, array|string $type = Form::FORM_TYPE_ENTRY, bool $includeInvitationOnly = true): Collection
    {
        return $this->getAllForSeasonId($season?->id, $type, $includeInvitationOnly);
    }

    public function countAllForSeason(?Season $season = null, array|string $type = Form::FORM_TYPE_ENTRY, bool $includeInvitationOnly = true): int
    {
        return $this->allForSeasonId($season?->id, $type, $includeInvitationOnly)->withTrashed()->count();
    }

    public function getForSeasonOrNonArchived(?Season $season = null, array|string $type = Form::FORM_TYPE_ENTRY): Collection
    {
        return $this->getQuery()
            ->select('forms.*')
            ->when($season?->id,
                fn($query) => $query->where('forms.season_id', $season->id),
                fn($query) => $query->whereNot('seasons.status', Season::STATUS_ARCHIVED)
            )
            ->whereIn('forms.type', (array) $type)
            ->leftJoin('seasons', 'seasons.id', '=', 'forms.season_id')
            ->get();
    }

    /**
     * Return all forms in the season, included soft-deleted
     */
    public function getAllWithTrashedForSeason(Season $season): Collection
    {
        return $this->getQuery()
            ->withTrashed()
            ->whereSeasonId($season->id)
            ->get();
    }

    public function getAllForSeasonAndChapters(Season $season, array $chapters, string $type = Form::FORM_TYPE_ENTRY): Collection
    {
        return $this->getQuery()
            ->select(['forms.*'])
            ->addSelect(DB::raw('GROUP_CONCAT(chapter_form.chapter_id) as chapter_ids'))
            ->leftJoin('chapter_form', 'forms.id', '=', 'chapter_form.form_id')
            ->whereSeasonId($season->id)
            ->whereType($type)
            ->where(function ($query) use ($chapters) {
                $query->where('forms.chapter_option', 'all')->orWhereIn('chapter_form.chapter_id', $chapters);
            })
            ->groupBy('forms.id')
            ->get();
    }

    public function syncChapters(Form $form, array $chapterIds)
    {
        $form->chapters()->sync($chapterIds);
    }

    public function isAtLimit(int $seasonId): bool
    {
        $limit = CurrentAccount::attribute('formQuantityLimit');

        // If the limit is not set, it means that the limit does not apply,
        // and we can safely return false without querying the DB
        if ($limit === null) {
            return false;
        }

        $count = $this->getQuery()->whereSeasonId($seasonId)->whereIn('type', Form::TYPES_WITH_LIMIT)->count();

        return $count >= $limit;
    }

    public function delete($form, $permanent = false): void
    {
        if ($form->isEntry() && $this->getAllForSeason($form->season, $form->type)->count() === 1) {
            return;
        }

        parent::delete($form, $permanent);
    }

    public function isSeasonMultiform(int $seasonId): bool
    {
        if (feature_disabled('multiform')) {
            return false;
        }

        return $this->getAllForSeasonId($seasonId)->count() > 1;
    }

    public function orphanedIds(): array
    {
        return $this->getQuery()
            ->withTrashed()
            ->leftJoin('seasons', 'seasons.id', '=', 'forms.season_id')
            ->whereNull('seasons.id')->pluck('forms.id')->all();
    }

    public function destroy(array $ids)
    {
        return Form::whereIn('id', $ids)->withTrashed()->forceDelete();
    }

    public function promotedForSeason(?int $seasonId, bool $roundsActive = true): Collection
    {
        return $this->getQuery()
            ->select('forms.*')
            ->where('promoted', 1)
            ->when($seasonId, function ($query) use ($seasonId) {
                $query->whereSeasonId($seasonId);
            })
            ->when($roundsActive, function ($query) {
                $query->whereHas('rounds', function ($query) {
                    $query->where('rounds.round_type', Round::ROUND_TYPE_ENTRY)
                        ->whereNull('rounds.deleted_at')
                        ->where('rounds.enabled', true)
                        ->where(function ($query) {
                            $query->whereNull('rounds.starts_at');
                            $query->orWhere('rounds.starts_at', '<=', now()->toDateTimeString());
                        })
                        ->where(function ($query) {
                            $query->orWhereNull('rounds.ends_at');
                            $query->orWhere('rounds.ends_at', '>=', now()->toDateTimeString());
                        });
                });
            })
            ->get();
    }

    public function configurationExport(?int $seasonId = null, ?int $formId = null)
    {
        return $this->getQuery()
            ->forConfiguration()
            ->whereId($formId)
            ->first();
    }

    public function forType(string $type): static
    {
        $this->query()->where('type', $type);

        return $this;
    }

    public function allowApiUpdates(bool $allowed = true): self
    {
        return $this->setting('allowApiUpdates', $allowed);
    }

    /**
     * Returns all forms that have entries in existing grant reports.
     */
    public function getAllForEntriesInExistingGrantReports(?int $formId = null): Collection
    {
        return $this->getQuery()
            ->select('forms.id', 'forms.slug')
            ->whereIn('forms.id', function ($query) use ($formId) {
                $query->select('entries.form_id')
                    ->from('entries')
                    ->join('grant_reports', 'grant_reports.entry_id', 'entries.id')
                    ->when($formId, fn($q) => $q->where('grant_reports.form_id', $formId))
                    ->whereNull('entries.deleted_at')
                    ->whereNull('grant_reports.deleted_at');
            })
            ->get();
    }

    public function setting(string $setting, bool $value): self
    {
        $this->query()->where('forms.settings->'.$setting, $value);

        return $this;
    }

    public function selectSettings(...$setting): self
    {
        // in forms.settings, return only the given settings instead of the entire settings object
        $this->query()->addSelect(
            DB::raw("forms.settings->>'$.".implode("' as settings, forms.settings->>'$.", $setting)."' as settings")
        );

        return $this;
    }

    public function activeRounds(): self
    {
        $this->query()->whereHas('rounds', function ($query) {
            $query->where('rounds.round_type', Round::ROUND_TYPE_ENTRY)
                ->whereNull('rounds.deleted_at')
                ->where('rounds.enabled', true)
                ->where(function ($query) {
                    $query->whereNull('rounds.starts_at');
                    $query->orWhere('rounds.starts_at', '<=', now()->toDateTimeString());
                })
                ->where(function ($query) {
                    $query->orWhereNull('rounds.ends_at');
                    $query->orWhere('rounds.ends_at', '>=', now()->toDateTimeString());
                });
        });

        return $this;
    }

    public function entries(array $entryIds): self
    {
        $this->query()->whereIn('forms.id', function ($query) use ($entryIds) {
            $query->select('entries.form_id')
                ->from('entries')
                ->whereIn('entries.id', $entryIds);
        });

        return $this;
    }

    public function syncRoles(Form $form, array $rolesIds): array
    {
        return $form->roles()->sync($rolesIds);
    }

    public function roles(array $rolesIds = []): self
    {
        $this->query()->where(fn($query) => $query
            ->where('forms.settings->roleVisibility', ScopeOption::All)
            ->orWhereNull('forms.settings->roleVisibility')
            ->orWhereHas('roles', fn($subQuery) => $subQuery
                ->whereIn('roles.id', $rolesIds)
            )
        );

        return $this;
    }
}
