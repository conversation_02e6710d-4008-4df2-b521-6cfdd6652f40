<?php

namespace AwardForce\Modules\Forms\Forms\Database\Behaviours;

use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;

trait DeletedForms
{
    protected bool $includeDeletedForms = false;

    public function includeDeletedForms(): void
    {
        $this->includeDeletedForms = true;
    }

    protected function getQuery()
    {
        $query = parent::getQuery();

        if ($this->includeDeletedForms) {
            $this->includeDeletedForms = false;

            return $query;
        }

        if (property_exists($this, 'formId') && $this->formId) {
            return $query;
        }

        $query->where(function ($query) {
            $this->allowedForms($query);

            return $this->additionalFormConditions($query);
        });

        return $query;
    }

    public function formColumn(): string
    {
        return "{$this->model->getTable()}.form_id";
    }

    protected function allowedForms($query)
    {
        if ($this->restrictByAccount) {
            return $query->whereIn($this->formColumn(), FormSelector::formIds());
        }

        return $query->whereIn($this->formColumn(), function ($query) {
            $query->select('forms.id')->from('forms')->whereNull('forms.deleted_at');
        });
    }

    protected function additionalFormConditions($query)
    {
        return $query;
    }
}
