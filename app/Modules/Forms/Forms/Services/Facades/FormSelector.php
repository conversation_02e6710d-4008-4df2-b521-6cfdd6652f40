<?php

namespace AwardForce\Modules\Forms\Forms\Services\Facades;

use Illuminate\Support\Facades\Facade;
use Platform\Database\Eloquent\Enums\TrashedMode;

/**
 * @method static void set($form)
 * @method static \AwardForce\Modules\Forms\Forms\Database\Entities\Form|null get()
 * @method static \AwardForce\Modules\Forms\Forms\Database\Entities\Form|null selectedOrDefault()
 * @method static int getId()
 * @method static bool viewingAll()
 * @method static int|string|null getIdRaw()
 * @method static \AwardForce\Modules\Forms\Forms\Database\Entities\Form|null defaultForSeason(int $seasonId)
 * @method static \AwardForce\Modules\Forms\Forms\Database\Entities\Form|null setDefaultForSeason()
 * @method static \AwardForce\Modules\Forms\Forms\Database\Entities\Form getForEntry(?\AwardForce\Modules\Entries\Models\Entry $entry = null)
 * @method static \AwardForce\Modules\Forms\Forms\Database\Entities\Form|null getForSubmittable(?\AwardForce\Modules\Forms\Forms\Database\Entities\Submittable $submittable = null)
 * @method static \AwardForce\Modules\Forms\Forms\Database\Entities\Form|null fromRequest()
 * @method static string season()
 * @method static string sessionKey()
 * @method static \AwardForce\Modules\Forms\Forms\Database\Entities\Form|null fromContext()
 * @method static bool inSession()
 * @method static void setFormSessionUuid(string $uuid)
 * @method static string formSessionUuid()
 * @method static array formIds(TrashedMode $trashed = TrashedMode::None)
 */
class FormSelector extends Facade
{
    const FILTER_ALL = 'all';

    public static function getFacadeAccessor(): string
    {
        return 'form.selector';
    }
}
