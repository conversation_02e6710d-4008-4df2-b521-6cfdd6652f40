<?php

namespace AwardForce\Modules\Forms\Forms\Services;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Exceptions\FormNotFound;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Http\Request;
use Illuminate\Session\Store as SessionStore;
use Platform\Database\Eloquent\Enums\TrashedMode;

class FormSelectorService
{
    const FORM_SLUG = 'formSlug';

    const FORM_ID = 'formId';

    const FORM = 'form';

    /** @var SessionStore */
    protected $session;

    /** @var FormRepository */
    protected $forms;

    /** @var Request */
    protected $request;

    private ?string $formSessionUuid = null;

    public function __construct(SessionStore $session, FormRepository $forms, Request $request)
    {
        $this->session = $session;
        $this->forms = $forms;
        $this->request = $request;
    }

    public function set($form): void
    {
        if ($form instanceof Form) {
            $form = $form->id;
        }

        $this->session->put($this->sessionKey(), $form);
    }

    /**
     * Returns the currently selected form in the form filter. Returns null if 'All seasons' is selected.
     */
    public function get(): ?Form
    {
        if ($form = $this->fromRequest()) {
            return $form;
        }

        $inSession = $this->session->get($this->sessionKey());

        if ($inSession === FormSelector::FILTER_ALL) {
            return null;
        }

        if ($inSession && $form = $this->forms->requestCache()->getById($inSession)) {
            return $form;
        }

        return $this->defaultForSeason($this->season()->id);
    }

    public function selectedOrDefault(): ?Form
    {
        return $this->get() ?: $this->defaultForSeason($this->season()->id);
    }

    public function getId(): ?int
    {
        return $this->get()->id ?? null;
    }

    public function viewingAll(): bool
    {
        return is_null($this->get());
    }

    public function getIdRaw(): int|string|null
    {
        return $this->getId() ?: FormSelector::FILTER_ALL;
    }

    public function defaultForSeason(int $seasonId): ?Form
    {
        return $this->forms->requestCache()->defaultForSeason($seasonId);
    }

    public function setDefaultForSeason(): ?Form
    {
        $this->set($form = $this->defaultForSeason($this->season()->id));

        return $form;
    }

    /**
     * Determines the form for a given entry.
     *
     * @deprecated
     *
     * @throws FormNotFound
     */
    public function getForEntry(?Entry $entry = null): Form
    {
        if ($entry) {
            if (! $form = $entry->form) {
                throw new FormNotFound;
            }

            return $form;
        }

        if ($form = $this->fromRequest()) {
            return $form;
        }

        return $this->selectedOrDefault();
    }

    public function getForSubmittable(?Submittable $submittable = null): Form
    {
        if ($submittable) {
            if (! $form = $submittable->getForm()) {
                throw new FormNotFound;
            }

            return $form;
        }

        if ($form = $this->fromRequest()) {
            return $form;
        }

        return $this->selectedOrDefault();
    }

    private function fromRequest(): ?Form
    {
        if ($formSlug = $this->request->get(self::FORM_SLUG)) {
            return $this->forms->getBySlug($formSlug);
        }

        if ($formId = $this->request->get(self::FORM_ID)) {
            return $this->forms->getById($formId);
        }

        return null;
    }

    private function season(): Season
    {
        return SeasonFilter::selectedOrActive();
    }

    private function sessionKey(): string
    {
        return __CLASS__.'-'.((string) $this->season()->slug);
    }

    public function inSession(): bool
    {
        return $this->session->has($this->sessionKey());
    }

    public function setFormSessionUuid(string $uuid): void
    {
        $this->formSessionUuid = $uuid;
    }

    public function formSessionUuid(): ?string
    {
        return $this->formSessionUuid;
    }

    public function formIds(TrashedMode $trashed = TrashedMode::None): array
    {
        return $this->forms
            ->requestCache()
            ->fields(['id'])
            ->trashed($trashed)
            ->just('id');
    }
}
