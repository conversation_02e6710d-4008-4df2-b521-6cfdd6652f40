<?php

namespace AwardForce\Modules\Forms\Forms\Services;

use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Files\Models\File;

class FormFileMapper
{
    public function mapFile(?File $file, ?Attachment $attachment = null): array
    {
        if (! $file) {
            return [];
        }

        $fileArray = [
            'id' => $file->id,
            'slug' => (string) $file->slug,
            'token' => (string) $file->token,
            'file' => $file->file,
            'original' => $file->original,
            'size' => $file->size,
            'mime' => $file->mime,
            'status' => $file->status,
            'transcodingStatus' => $file->transcodingStatus,
            'url' => cloud_asset_url($file->file, true, filename: $file->original),
        ];

        if ($file->isVideo()) {
            $fileArray['source'] = cloud_asset_url($file->transcodePlaylist());
            $fileArray['transcodingErrors'] = $file->transcodingErrors();
            $fileArray['caption'] = $file->caption ? $this->mapFile($file->caption) : null;
            $fileArray['image'] = imgix($file->transcodeThumbnail(), $file->original, config('ui.images.gallery.small'));
            $fileArray['videoHeight'] = $file->videoHeight(current_account()->videoPlayerHeight);
        }

        if ($file->isCaption()) {
            $fileArray['source'] = cloud_asset_url($file->location('file.vtt'));
        }

        if ($file->isImage()) {
            $fileArray['image'] = imgix($file->file, $file->original, config('ui.images.gallery.small'));
        }

        if ($attachment) {
            $fileArray['order'] = $attachment->order;
        }

        return $fileArray;
    }
}
