<?php

namespace AwardForce\Modules\Forms\Forms\Services;

use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use Platform\Database\Eloquent\Enums\TrashedMode;
use Platform\Events\EventDispatcher;
use Tests\IntegratedTestCase;

final class FormSelectorTest extends IntegratedTestCase
{
    use EventDispatcher;

    public function testItReturnsSelectedFormOrDefault(): void
    {
        SeasonFilter::set($season = $this->muffin(Season::class));

        $this->assertCount(1, $season->forms);
        $this->assertEquals($season->forms->first()->id,
            FormSelector::selectedOrDefault()->id);

        FormSelector::set($secondForm = $this->muffin(Form::class,
            ['season_id' => $season->id]));

        $this->assertEquals($secondForm->id,
            FormSelector::selectedOrDefault()->id);
    }

    public function testItReturnsDefaultForSeason(): void
    {
        SeasonFilter::set($season = $this->muffin(Season::class));
        $defaultForm = $season->forms->first();

        FormSelector::set($this->muffin(Form::class,
            ['season_id' => $season->id]));

        $this->assertEquals($defaultForm->id,
            FormSelector::defaultForSeason($season->id)->id);
    }

    public function testItRemembersEachSeasonsSelection(): void
    {
        SeasonFilter::set($season1 = $this->muffin(Season::class));
        $this->muffin(Form::class, ['season_id' => $season1->id]);
        FormSelector::set($form1B = $this->muffin(Form::class,
            ['season_id' => $season1->id]));

        SeasonFilter::set($season2 = $this->muffin(Season::class));
        $this->muffin(Form::class, ['season_id' => $season2->id]);
        FormSelector::set($form2B = $this->muffin(Form::class,
            ['season_id' => $season2->id]));

        SeasonFilter::set($season1);

        $this->assertEquals($form1B->id, FormSelector::getId());

        SeasonFilter::set($season2);

        $this->assertEquals($form2B->id, FormSelector::getId());
    }

    public function testRequestOverridesSession(): void
    {
        SeasonFilter::set($season = $this->muffin(Season::class));
        FormSelector::set($defaultForm = $this->muffin(Form::class,
            ['season_id' => $season->id]));

        $secondForm = $this->muffin(Form::class, ['season_id' => $season->id]);

        $this->assertEquals($defaultForm->id, FormSelector::getId());

        request()->merge(['formSlug' => (string) $secondForm->slug]);

        $this->assertEquals($secondForm->id, FormSelector::getId());
    }

    public function testIgnoresDeletedFormsInSession(): void
    {
        SeasonFilter::set($season = $this->muffin(Season::class));
        $defaultForm = $season->forms->first();

        FormSelector::set($secondForm = $this->muffin(Form::class,
            ['season_id' => $season->id]));

        $this->assertEquals($secondForm->id, FormSelector::getId());

        app(FormRepository::class)->delete($secondForm);
        $this->dispatchAll($secondForm->releaseEvents());
        $this->assertEquals($defaultForm->id, FormSelector::getId());
    }

    public function testGetIdRaw(): void
    {
        SeasonFilter::set($season = $this->muffin(Season::class));
        FormSelector::set($defaultForm = $this->muffin(Form::class,
            ['season_id' => $season->id]));

        $this->assertEquals($defaultForm->id, FormSelector::getIdRaw());

        FormSelector::set($secondForm = $this->muffin(Form::class,
            ['season_id' => $season->id]));

        $this->assertEquals($secondForm->id, FormSelector::getIdRaw());

        FormSelector::set(FormSelector::FILTER_ALL);

        $this->assertEquals(FormSelector::FILTER_ALL, FormSelector::getIdRaw());
    }

    public function testGetReturnsNullWhenAllFormsIsSelected(): void
    {
        SeasonFilter::set($season = $this->muffin(Season::class));
        $defaultForm = $season->forms->first();

        $this->assertEquals($defaultForm->id, FormSelector::get()->id);

        FormSelector::set($secondForm = $this->muffin(Form::class,
            ['season_id' => $season->id]));

        $this->assertEquals($secondForm->id, FormSelector::get()->id);

        FormSelector::set(FormSelector::FILTER_ALL);

        $this->assertNull(FormSelector::get());
    }

    public function testFormIdsReturnsActive(): void
    {
        $forms = $this->muffins(3, Form::class);
        $forms[2]->delete();

        $activeFormIds = FormSelector::formIds();

        $this->assertContains($forms[0]->id, $activeFormIds);
        $this->assertContains($forms[1]->id, $activeFormIds);
        $this->assertNotContains($forms[2]->id, $activeFormIds);
    }

    public function testFormIdsReturnDeleted(): void
    {
        $forms = $this->muffins(3, Form::class);
        $forms[2]->delete();

        $activeFormIds = FormSelector::formIds(TrashedMode::Only);

        $this->assertNotContains($forms[0]->id, $activeFormIds);
        $this->assertNotContains($forms[1]->id, $activeFormIds);
        $this->assertContains($forms[2]->id, $activeFormIds);
    }

    public function testFormIdsReturnAll(): void
    {
        $forms = $this->muffins(3, Form::class);
        $forms[2]->delete();

        $activeFormIds = FormSelector::formIds(TrashedMode::All);

        $this->assertContains($forms[0]->id, $activeFormIds);
        $this->assertContains($forms[1]->id, $activeFormIds);
        $this->assertContains($forms[2]->id, $activeFormIds);
    }
}
