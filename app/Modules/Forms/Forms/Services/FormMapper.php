<?php

namespace AwardForce\Modules\Forms\Forms\Services;

use AwardForce\Library\Copier\Processor;
use AwardForce\Library\Search\Actions\FormInvitationAction;
use AwardForce\Library\View\FormatsDates;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\Uploadable;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Search\Columns\SubmittableCount;
use AwardForce\Modules\Forms\Forms\Services\UploadValidator as FormUploadValidator;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Rounds\Models\Round;

class FormMapper
{
    use FormatsDates;
    use Uploadable;

    protected FormInvitationAction $formInvitationAction;

    public function __construct(
        protected FormFileMapper $formFileMapper,
        protected FormUploadValidator $formUploadValidator,
        protected FileRepository $files,
        protected RoleRepository $roles,
    ) {
    }

    public function mapFormWithDefaults(Form $form): array
    {
        return $this->mapForm($form, config('ui.images.forms.cover_image'));
    }

    public function mapFormWithInvitation(Form $form): array
    {
        return $this->mapForm($form, config('ui.images.forms.cover_image'), true);
    }

    public function mapFormMyEntries(Form $form): array
    {
        return $this->mapForm($form, config('ui.images.home.forms.card'));
    }

    private function mapForm(Form $form, array $size, bool $withInvitationConfig = false): array
    {
        return [
            'id' => $form->id,
            'slug' => (string) $form->slug,
            'type' => $form->type,
            'order' => $form->order,
            'name' => lang($form, 'name'),
            'callToAction' => $form->callToAction,
            'chapterOption' => $form->chapterOption,
            'chapters' => $form->chapters->pluck('id')->toArray(),
            'translated' => $form->translated,
            'uploaderOptions' => $this->formUploaderOptions($form),
            'coverImage' => ($coverImage = $this->coverImage($form)) ? $this->formFileMapper->mapFile($coverImage) : null,
            'coverImageUrl' => $coverImage ? imgix($coverImage->file, params: $size) : null,
            'submittableCount' => $form->id ? (new SubmittableCount)->value($form) : 0,
            'round' => $this->formatRound($form->nextRound),
            'deletedAt' => $form->deletedAt ? (string) $form->deletedAt : null,
            'seasonId' => $form->seasonId,
            'settings' => $form->settings->toArray(),
            'invitationConfig' => $withInvitationConfig ? $this->invitationConfiguration($form) : [],
            'copierConfig' => $this->copierConfiguration($form),
            'contentBlock' => $form->contentblockId ? slug_from_id($form->contentblockId, app(ContentBlockRepository::class)) : null,
            'selectedRoles' => $form->id ? $this->roles->requestCache()->slugsAssociatedToForm($form->id) : [],
        ];
    }

    protected function formatRound(?Round $round)
    {
        return [
            'slug' => (string) $round?->slug,
            'name' => $round ? translate($round)->name : '',
            'status' => $round?->status,
            'startsAt' => $this->formatDate($round?->startsAt),
            'endsAt' => $this->formatDate($round?->endsAt),
        ];
    }

    private function coverImage(Form $form)
    {
        return $this->files->latestByResourceId(File::RESOURCE_FORM, $form->id);
    }

    private function formUploaderOptions(Form $form): array
    {
        return $this->formUploadValidator
            ->setupUploader($this->setupUploader(), $form->id)
            ->setTempPrefix($form->slug)
            ->options();
    }

    private function invitationConfiguration(Form $form)
    {
        $this->formInvitationAction ??= new FormInvitationAction('forms', 'Forms');

        return $this->formInvitationAction->viewData($form);
    }

    private function copierConfiguration(Form $form)
    {
        return [
            'route' => route('forms.copy', [$form->slug]),
            'options' => [
                'scoreSets' => Processor::SCORESETS,
                'scoringCriteria' => Processor::SCORING_CRITERIA,
                'rounds' => Processor::ROUNDS,
            ],
            'labels' => [
                'button' => trans_elliptic('form.buttons.copy'),
                'title' => trans('form.copier.title', ['name' => lang($form, 'name')]),
                'instructions' => trans('form.copier.instructions'),
                'submit' => trans('form.buttons.copy'),
                'cancel' => trans('buttons.cancel'),
                'settings' => trans('form.buttons.settings'),
                'categories' => trans(':category+'),
                'tabs' => trans('tabs.titles.main'),
                'fields' => trans('fields.titles.main'),
                'scoreSets' => trans('score-set.titles.main'),
                'scoringCriteria' => trans('scoring-criteria.titles.main'),
                'rounds' => trans('rounds.titles.main'),
            ],
        ];
    }
}
