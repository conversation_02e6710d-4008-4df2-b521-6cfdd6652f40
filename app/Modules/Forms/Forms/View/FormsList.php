<?php

namespace AwardForce\Modules\Forms\Forms\View;

use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Forms\Forms\Services\FormMapper;
use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Consumer;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;

class FormsList extends View
{
    private Collection $forms;
    private Collection $invitedNotificactions;

    public function __construct(
        private ChapterRepository $chapters,
        private FormMapper $formMapper,
        private ColumnatorFactory $columnators,
        private Request $request,
        private NotificationRepository $notifications,
        private SeasonFilterService $seasons,
    ) {
    }

    public function forms()
    {
        $search = new ColumnatorSearch($this->columnator());

        $this->forms = (translate($search->search(false))->map(function ($form) {
            return $this->formMapper->mapFormWithInvitation($form);
        }));

        return $this->forms;
    }

    public function formIds()
    {
        return $this->forms->pluck('id')->toArray();
    }

    public function area()
    {
        return 'forms.search';
    }

    public function columnator()
    {
        return $this->columnators->forArea($this->area(), $this->request->all());
    }

    public function routes()
    {
        return routes_for_vue([
            'forms.new',
            'forms.edit',
            'forms.settings',
            'forms.select',
            'forms.delete',
            'forms.undelete',
            'entry.manager.index',
            'grant_report.manager.index',
            'judging-dashboard.index',
        ]);
    }

    public function translations()
    {
        return translations_for_vue(Consumer::languageCode(), [
            'form.name.default',
            'form.units',
            'form.buttons',
            'form.type',
            'buttons.cancel',
            'buttons.delete',
            'buttons.ok',
            'buttons.undelete',
            'form.alerts.delete',
            'users.form.email.label',
            'validation.attributes.users-left',
            'validation.multiple_emails',
            'form.messages.multiple_emails',
            'miscellaneous.markdown_guide.label',
            'miscellaneous.datepicker',
            'notifications.missing',
            'notifications.triggers.entry_invited',
            'form.form.name.label',
            'content-block.keys.entry-view',
            'buttons.action_overflow',
        ]);
    }

    public function canCreate(): bool
    {
        return Consumer::can('create', 'Forms');
    }

    public function canUpdate(): bool
    {
        return Consumer::can('update', 'Forms');
    }

    public function canDelete(): bool
    {
        return Consumer::can('delete', 'Forms');
    }

    public function showGrantReportForms(): bool
    {
        return is_goodgrants();
    }

    public function newGrantReportFormLink(): string
    {
        return feature_enabled('grant_reports') ? route('forms.new', ['type' => 'report']) : 'feature-disabled/grant_reports';
    }
}
