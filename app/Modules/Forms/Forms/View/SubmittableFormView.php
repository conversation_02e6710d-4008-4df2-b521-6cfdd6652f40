<?php

namespace AwardForce\Modules\Forms\Forms\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Country\Countries;
use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Library\Facades\Vertical;
use AwardForce\Library\Html\Breadcrumbs;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Categories\Composers\CategoryComposable;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Categories\Services\SelectedSeasonIsActive;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Entries\Services\Firebase\FirebaseConfigData;
use AwardForce\Modules\Entries\Services\SubmittableFormService;
use AwardForce\Modules\Forms\Collaboration\Firebase\SubmittableClaim;
use AwardForce\Modules\Forms\Collaboration\Repositories\CollaboratorsRepository;
use AwardForce\Modules\Forms\Collaboration\Services\Collaborators;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Traits\TableFieldConfigurator;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Forms\Services\FormMapper;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Identity\Users\Services\UserPermissionsService;
use AwardForce\Modules\Integrations\Data\IntegrationRepository;
use AwardForce\Modules\Menu\Services\ContextService;
use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewStageRepository;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\Rounds\Services\RoundStatus;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Settings\Services\AttachmentTypes;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Platform\Authorisation\FeatureRoles\ChapterManager;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Facades\Translator;

abstract class SubmittableFormView extends View
{
    use CategoryComposable;
    use FirebaseConfigData;
    use SelectedSeasonIsActive;
    use TableFieldConfigurator;

    public function __construct(
        protected Request $request,
        protected RoundStatus $roundStatus,
        protected TabRepository $tabs,
        protected Manager $consumer,
        protected Countries $countries,
        protected CategoryRepository $categories,
        protected IntegrationRepository $integrations,
        protected ContentBlockRepository $contentBlocks,
        protected ScoreSetRepository $scoreSets,
        protected AttachmentTypes $attachmentTypes,
        protected UserPermissionsService $userPermissions,
        protected ChapterRepository $chapters,
        protected NotificationRepository $notifications,
        protected FormRepository $forms,
        protected FormMapper $formMapper,
        protected RoundRepository $rounds,
        protected ReviewStageRepository $reviewStages,
        protected Collaborators $collaborators,
        protected CollaboratorsRepository $collaboratorsRepository,
        protected Database $database,
    ) {
        $this->registerRealTimeUpdates();
        $this->registerRoutes();
        $this->registerFirebaseConfig();
        $this->registerTranslations();
    }

    abstract protected function season(): Season;

    abstract protected function submittable(): ?Submittable;

    abstract protected function submittableFormService(): SubmittableFormService;

    abstract public function mappedSubmittable(): ?array;

    abstract public function lockedCategory(): bool;

    abstract public function readOnly(): bool;

    abstract public function chapters(): array;

    abstract public function showSubmitOnFirstTab(): bool;

    abstract public function prepaymentRequired(): bool;

    abstract public function awaitingPayment(): bool;

    abstract public function submissionPossible(): bool;

    abstract public function paymentOnSubmit(): bool;

    abstract public function resubmissionPossible(): bool;

    abstract public function plagiarismDetection(): bool;

    abstract public function lockWhenSubmitted(): bool;

    abstract public function tabTypes(): array;

    abstract public function showCountdown(): bool;

    public function form(): Form
    {
        if ($this->submittable && $this->submittable->form) {
            return translate($this->submittable->form);
        }

        $formSlug = $this->request->get('formSlug');

        if ($formSlug && $form = $this->forms->getBySlug($formSlug)) {
            return translate($form);
        }

        return translate(FormSelector::selectedOrDefault());
    }

    public function mappedCategory(): ?array
    {
        $categorySlug = $this->request->get('categorySlug');
        if ($category = ($this->submittable()?->category ?: $this->categories->getBySlug($categorySlug))) {
            return [
                'id' => $category->id,
                'slug' => (string) $category->slug,
                'active' => $category->active,
                'chapters' => $category->chapters ? $category->chapters->pluck('id')->toArray() : [],
                'deletedAt' => $category->deletedAt ? (string) $category->deletedAt : null,
                'promoted' => $category->promoted,
            ];
        }

        return null;
    }

    public function mappedTab(): ?array
    {
        if ($tab = $this->tab()) {
            return [
                'id' => $tab->id,
                'slug' => (string) $tab->slug,
            ];
        }

        return null;
    }

    private function tab(): ?Tab
    {
        return $this->tabs->getBySlug($this->request->get('tabSlug'));
    }

    protected function mapSeason(Season $season): array
    {
        return [
            'id' => $season->id,
            'slug' => (string) $season->slug,
            'status' => $season->status,
            'name' => $season->formatName(),
        ];
    }

    public function mappedSeason(): array
    {
        return $this->mapSeason(translate($this->season()));
    }

    public function activeSeason(): Season
    {
        return current_account()->activeSeason();
    }

    public function fieldTemplate(): array
    {
        return $this->canConfigure() ?
            $this->submittableFormService()->fieldTemplate($this->submittable()) :
            [];
    }

    public function translations(): array
    {
        return translations_for_vue(Consumer::languageCode(), [
            'buttons',
            'category.configuration',
            'category.form',
            'category.alerts.delete',
            'chapters.configuration',
            'chapters.form',
            'chapters.messages.limit.message',
            'chapters.messages.limit.title',
            'collaboration',
            'entries.autosaver',
            'entries.form',
            'entries.messages',
            'entries.countdown',
            'features.not_available',
            'fields.alerts.delete',
            'fields.alerts.delete-option',
            'fields.configuration',
            'fields.form',
            'fields.auto_score',
            'fields.auto_tag',
            'fields.types',
            'fields.table.delete_row',
            'files.actions',
            'files.buttons',
            'files.captions',
            'files.download',
            'files.messages',
            'files.status',
            'files.transcoding',
            'files.types',
            'form.configuration',
            'form.form',
            'form.order.label',
            'miscellaneous.alerts.generic',
            'miscellaneous.alerts.no-connection',
            'miscellaneous.alerts.preview-mode',
            'miscellaneous.alerts.validator.short_message',
            'miscellaneous.alerts.validator.tabs',
            'miscellaneous.search.or',
            'miscellaneous.counters',
            'miscellaneous.datepicker.tooltips',
            'miscellaneous.markdown_guide.label',
            'miscellaneous.optional',
            'miscellaneous.outdated',
            'miscellaneous.searching',
            'miscellaneous.select_all',
            'miscellaneous.unsaved_changes',
            'multiselect.select_all',
            'setting.form.accept_attachment_links',
            'setting.form.max_attachments',
            'setting.form.max_filesize',
            'setting.form.min_attachments',
            'setting.form.min_filesize',
            'tabs.alerts',
            'tabs.configuration',
            'tabs.form',
            'tabs.types',
            'validation.email',
            'validation.numeric',
            'validation.phone',
            'validation.url',
            'validation.required',
            'validation.required_generic',
            'validation.required_this',
            'validation.markdown',
            'shared.description',
            'shared.learn_more',
            'grant_reports.form.submit',
            'grant_reports.messages.closed',
        ], [
            'entrant',
        ]);
    }

    public function configurationModeSwitchTranslations(): array
    {
        return translations_for_vue(Consumer::languageCode(), [
            'buttons.cancel',
            'buttons.continue',
            'buttons.off',
            'buttons.on',
            'entries.form.configuration',
            'miscellaneous.unsaved_changes',
        ]);
    }

    public function countries(): array
    {
        return $this->countries->list();
    }

    public function supportedLanguages(): array
    {
        return current_account()->supportedLanguages()->just('code');
    }

    public function user()
    {
        return [
            'id' => Consumer::get()->id(),
            'slug' => (string) Consumer::get()->user()->slug,
            'name' => Consumer::get()->user()->fullName(),
        ];
    }

    public function hasManagerRole(): bool
    {
        return Consumer::isManager();
    }

    public function canConfigure(): bool
    {
        return Consumer::isProgramManager()
            || (Consumer::can('create', 'Fields') && Consumer::can('update', 'Fields'));
    }

    public function canConfigureChapters(): bool
    {
        return feature_enabled('multi_chapter') &&
            Consumer::can('create', 'Chapters') &&
            Consumer::can('update', 'Chapters') &&
            $this->entryFormCanModify();
    }

    public function canEditForms(): bool
    {
        return Consumer::isProgramManager() || Consumer::can('update', 'Forms');
    }

    public function canConfigureCategories(): bool
    {
        return Consumer::can('create', 'Categories') && Consumer::can(
            'update',
            'Categories'
        ) && $this->entryFormCanModify();
    }

    public function categories()
    {
        return $this->canConfigure() ?
            $this->categoriesForMultiselectByForm($this->categories, $this->form->id) :
            [];
    }

    public function protectionTypes(): array
    {
        return Field::PROTECTION_TYPES;
    }

    public function fileTypes(): array
    {
        return array_map(function ($types) {
            return array_keys($types);
        }, config('filetypes'));
    }

    public function tabTemplate(): array
    {
        return $this->canConfigure() ?
            $this->submittableFormService()->tabTemplate($this->submittable()) :
            [];
    }

    public function contentBlocks()
    {
        $contentBlocks = $this->contentBlocks->getAllByKey('tab-info');

        return $contentBlocks->map(function ($contentBlock) {
            return SubmittableFormService::mapContentBlock($contentBlock);
        })->toArray();
    }

    public function formContentBlock(): ?ContentBlock
    {
        return $this->form->contentBlock;
    }

    public function categoryTemplate(): array
    {
        return $this->canConfigureCategories() ?
            $this->submittableFormService()->categoryTemplate() :
            [];
    }

    public function categoryMergeFields(): array
    {
        return $this->canConfigureCategories() ?
            Vertical::replaceArray(config('categories.merge_fields')) :
            [];
    }

    public function scoreSets(): Collection
    {
        if (! $this->canConfigureCategories()) {
            return collect();
        }

        $scoreSets = $this->scoreSets->getForSeason($this->season()->id)
            ->reject(function (ScoreSet $scoreSet) {
                return empty($scoreSet->attachmentTypeFieldId);
            });

        return translate($scoreSets);
    }

    public function attachmentTypes(): Collection
    {
        if (! $this->canConfigureCategories()) {
            return collect();
        }

        return $this->scoreSets()->keyBy('id')->map(function ($scoreSet) {
            return $this->attachmentTypes->getAttachmentTypes($scoreSet->attachmentTypeFieldId);
        });
    }

    public function chapterTemplate(): array
    {
        return $this->canConfigureChapters() ?
            $this->submittableFormService()->chapterTemplate() :
            [];
    }

    public function chapterManagers(): array
    {
        if (! $this->canConfigureChapters()) {
            return [];
        }

        return $this->userPermissions
            ->getUsersByPermissions(ChapterManager::permissions())
            ->sortBy('first_name')
            ->map(function ($manager) {
                return ['id' => $manager->id, 'name' => $manager->fullName()];
            })
            ->values()
            ->toArray();
    }

    public function isAtChapterLimit(): bool
    {
        return $this->chapters->isAtLimit($this->season());
    }

    protected function seasonId(): int
    {
        return $this->season()->id;
    }

    public function mappedForm(): array
    {
        return $this->formMapper->mapFormWithDefaults($this->form());
    }

    public function formTypes(): array
    {
        return [
            [
                'id' => Form::FORM_TYPE_ENTRY,
                'name' => trans('form.type.entry'),
            ],
            [
                'id' => Form::FORM_TYPE_REPORT,
                'name' => trans('form.type.report'),
            ],
        ];
    }

    public function locks(): object
    {
        return (object) [];
    }

    public function breadcrumbs(): string
    {
        return (new Breadcrumbs)->toJson();
    }

    public function visibleSelectors(): bool
    {
        return true;
    }

    public function visibleTitle(): bool
    {
        return true;
    }

    public function allowsDownloadBlankPdf(): bool
    {
        return $this->form->settings->allowBlankPdfDownload;
    }

    public function eligibleContentBlocks(): array
    {
        return [];
    }

    public function ineligibleContentBlocks(): array
    {
        return [];
    }

    public function eligibleNotifications(): array
    {
        return [];
    }

    public function ineligibleNotifications(): array
    {
        return [];
    }

    public function multiChapter(): bool
    {
        return false;
    }

    public function multiform(): bool
    {
        return false;
    }

    public function canPay(): bool
    {
        return $this->submittable()?->inCart() || $this->submittable()?->hasOrderAwaitingPayment();
    }

    public function request(): Request
    {
        return $this->request;
    }

    public function refereeReviewStages(): array
    {
        return Translator::shallow(
            $this->reviewStages
                ->season($this->season()->id)
                ->selectedForm($this->form()->id)
                ->reviewByReferee()
                ->fields(['id', 'slug'])
                ->get()
        )
            ->map(fn(ReviewStage $reviewStage) => ['id' => (string) $reviewStage->slug, 'title' => $reviewStage->name])
            ->prepend(['id' => '', 'title' => ''])
            ->toArray();
    }

    public function canCollaborate(): bool
    {
        return $this->submittable && $this->form()->supportsCollaboration();
    }

    private function registerRealTimeUpdates(): void
    {
        if (! ($this->submittable && $this->form()->supportsRealTimeUpdates())) {
            return;
        }

        VueData::registerForm($this->form());
        VueData::registerSubmittable($submittable = $this->submittable());
        VueData::registerFirebaseTTL($this->database->expireAt());
        VueData::registerSubmittableToken($submittable->getEncryptionToken());
        VueData::registerAuthToken($this->database->generateAuthToken(
            $this->consumer->user()->slug,
            new SubmittableClaim($submittable, $this->collaboratorsRepository, $this->consumer)
        ));
        VueData::registerSelectedContext(app(ContextService::class)->selectedContext());

        if ($this->canCollaborate()) {
            VueData::registerCollaborators($this->collaborators->getAllActiveCollaborators($submittable));
        }
    }

    private function registerRoutes()
    {
        VueData::registerRoutes([
            'collaborators.reinvite',
            'collaborators.owner',
            'collaborators.delete',
            'collaborators.invite',
            'collaborators.privilege.update',
        ]);
    }

    private function registerTranslations()
    {
        VueData::registerTranslations([
            'broadcasts.status.sent',
            'buttons.cancel',
            'buttons.continue',
            'buttons.delete',
            'category.form',
            'collaboration.dialogs.submitted',
            'collaboration.form.buttons.invite_more_collaborators',
            'collaboration.form.fields.is_editing',
            'collaboration',
            'content-block.keys.entry-view',
            'entries.countdown',
            'entries.form.add-referee',
            'entries.form.collaborators.label',
            'entries.form.no-referee-fields',
            'entries.form.referee.email',
            'entries.form.referee.name',
            'entries.form.referee.no-review-stage',
            'entries.form.referee.resend-request',
            'entries.form.referee.send-request',
            'entries.form.title.help',
            'entries.form.title.label',
            'entries.messages.ineligible',
            'entries.messages.referee-resent',
            'features.not_available',
            'fields.configuration',
            'files.actions.delete',
            'files.actions.replace',
            'files.download',
            'form.form.api_updateable.label',
            'form.form.collaborative.label',
            'form.form',
            'judging.table.columns.completed',
            'miscellaneous.drag_and_drop',
            'miscellaneous.optional',
            'review-flow.table.columns.review_stage',
            'shared.learn_more',
            'tabs.configuration.referees.label',
            'tabs.form.max_referees.label',
            'tabs.form.min_referees.label',
            'users.buttons.send_invite',
            'users.form.email',
            'validation.required_this',
        ],
            ['referee']);
    }
}
