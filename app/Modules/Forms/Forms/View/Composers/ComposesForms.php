<?php

namespace AwardForce\Modules\Forms\Forms\View\Composers;

use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Support\Collection;

class ComposesForms
{
    public function format(Collection $forms): Collection
    {
        return translate($forms)->flatMap(function (Form $form) {
            $slug = (string) $form->slug;

            return [$slug => lang($form, 'name')];
        })->sort();
    }

    public function season()
    {
        return SeasonFilter::viewingAll() ? null : SeasonFilter::get();
    }
}
