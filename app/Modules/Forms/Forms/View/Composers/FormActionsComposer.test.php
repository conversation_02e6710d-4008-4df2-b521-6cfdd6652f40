<?php

namespace AwardForce\Modules\Forms\Forms\View\Composers;

use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Mockery as m;
use Platform\View\View;
use Tests\IntegratedTestCase;

final class FormActionsComposerTest extends IntegratedTestCase
{
    private array $forms;
    private Form $reportForm;

    public function init()
    {
        $this->forms = $this->muffins(2, Form::class);
        $this->reportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
    }

    public function testOnlyEntryFormsAvailable(): void
    {
        FormSelector::set($this->forms[1]);
        $view = m::mock(View::class);
        $view->shouldReceive('route');
        $view->shouldReceive('with')->withArgs(function ($with) {
            foreach ($this->forms as $i => $form) {
                $this->assertArrayHasKey((string) $this->forms[$i]->slug, $with['availableForms']);
            }
            $this->assertArrayNotHasKey((string) $this->reportForm->slug, $with['availableForms']);

            return true;
        });

        app(FormActionsComposer::class)->compose($view);
    }

    public function testOnlyReportForms(): void
    {
        FormSelector::set($this->forms[1]);
        $view = m::mock(View::class);
        $view->allowedTypes = [Form::FORM_TYPE_REPORT];
        $view->shouldReceive('route');
        $view->shouldReceive('with')->withArgs(function ($with) {
            $this->assertArrayHasKey((string) $this->reportForm->slug, $with['availableForms']);

            return true;
        });

        app(FormActionsComposer::class)->compose($view);
    }

    public function testItDoesNotShowFormsOfArchivedSeasons(): void
    {
        SeasonFilter::set(SeasonFilterService::FILTER_ALL);
        $archivedForm = $this->muffin(Form::class, ['season_id' => $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED])->id]);
        $view = $this->mock(View::class);
        $view->allowedTypes = [Form::FORM_TYPE_ENTRY];
        $view->shouldReceive('route')->once();
        $view->shouldReceive('with')->once()->withArgs(function ($with) use ($archivedForm) {
            $this->assertArrayNotHasKey((string) $archivedForm->slug, $with['availableForms']);

            return true;
        });

        app(FormActionsComposer::class)->compose($view);
    }

    public function testItShowsFormsOfArchiveSeasonIfExplicitlySelected(): void
    {
        SeasonFilter::set($archivedSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]));
        $archivedForm = $this->muffin(Form::class, ['season_id' => $archivedSeason->id, 'type' => fn() => Form::FORM_TYPE_REPORT]);
        $view = $this->mock(View::class);
        $view->allowedTypes = [Form::FORM_TYPE_REPORT];
        $view->shouldReceive('route')->once();
        $view->shouldReceive('with')->once()->withArgs(function ($with) use ($archivedForm) {
            $this->assertCount(1, $with['availableForms']);
            $this->assertArrayHasKey((string) $archivedForm->slug, $with['availableForms']);

            return true;
        });

        app(FormActionsComposer::class)->compose($view);
    }
}
