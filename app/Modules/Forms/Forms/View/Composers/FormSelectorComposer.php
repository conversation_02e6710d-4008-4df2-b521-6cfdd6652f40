<?php

namespace AwardForce\Modules\Forms\Forms\View\Composers;

use AwardForce\Library\Composers\CacheableComposer;
use AwardForce\Library\Composers\ComposeCache;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use Illuminate\Support\Collection;

class FormSelectorComposer implements CacheableComposer
{
    use ComposeCache;

    public function __construct(protected FormRepository $forms, protected ComposesForms $composer)
    {
    }

    /**
     * Available forms for FormSelector displayed at the top of all list views.
     */
    public function compose($view)
    {
        $forms = $this->forms($allowedTypes = $view->allowedTypes ?? [Form::FORM_TYPE_ENTRY]);

        $view->with([
            'selectedForm' => (string) ($this->selectedForm($allowedTypes, $forms)?->slug),
            'allSelections' => $forms->prepend(trans('form.selector.all'), 'all')->toArray(),
            'isMultiform' => feature_enabled('multiform'),
        ]);
    }

    protected function forms(array $allowedTypes)
    {
        return $this->composer->format($this->forms->getAllForSeason($this->composer->season(), $allowedTypes));
    }

    protected function selectedForm(array $allowedTypes, Collection $forms): ?Form
    {
        if (($selectedForm = FormSelector::get()) && ! in_array($selectedForm->type, $allowedTypes)) {
            $selectedForm = FormSelector::setDefaultForSeason();
        }

        if ($selectedForm && ! $forms->has((string) $selectedForm->slug)) {
            FormSelector::set(FormSelector::FILTER_ALL);

            return null;
        }

        return $selectedForm;
    }
}
