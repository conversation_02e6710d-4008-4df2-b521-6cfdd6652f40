<?php

namespace AwardForce\Modules\Forms\Forms\View\Composers;

use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Platform\View\View;
use Tests\IntegratedTestCase;

final class FormSelectorComposerTest extends IntegratedTestCase
{
    public function testItShowsFormsOfArchivedSeasons(): void
    {
        SeasonFilter::set(SeasonFilterService::FILTER_ALL);

        $archivedForm = $this->muffin(Form::class,
            ['season_id' => $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED])->id]
        );

        $view = $this->mock(View::class);
        $view->allowedTypes = [Form::FORM_TYPE_ENTRY];
        $view->shouldReceive('with')->once()->withArgs(function ($with) use ($archivedForm) {
            $this->assertArrayHasKey((string) $archivedForm->slug, $with['allSelections']);

            return true;
        });

        app(FormSelectorComposer::class)->compose($view);
    }

    public function testItResetsFormToDefaultIfSelectedFormTypeNotAllowed(): void
    {
        $defaultForm = FormSelector::defaultForSeason($this->season->id);

        FormSelector::set($this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]));

        $view = $this->mock(View::class);
        $view->allowedTypes = [Form::FORM_TYPE_ENTRY];
        $view->shouldReceive('with')->once()->withArgs(function ($with) use ($defaultForm) {
            $this->assertEquals((string) $defaultForm->slug, $with['selectedForm']);
            $this->assertEquals((string) $defaultForm->slug, FormSelector::get()->slug);

            return true;
        });

        app(FormSelectorComposer::class)->compose($view);
    }

    public function testSelectsAllFormsForReportsIfEntryFormIsSelected(): void
    {
        $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);

        $view = $this->mock(View::class);
        $view->allowedTypes = [Form::FORM_TYPE_REPORT];
        $view->shouldReceive('with')->once()->withArgs(function ($with) {
            $this->assertEmpty($with['selectedForm']);
            $this->assertTrue(FormSelector::viewingAll());

            return true;
        });

        app(FormSelectorComposer::class)->compose($view);
    }
}
