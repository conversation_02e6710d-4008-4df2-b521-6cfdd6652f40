<?php

namespace AwardForce\Modules\Forms\Forms\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\FormMapper;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Eloquence\Behaviours\Slug;
use Illuminate\Http\Request;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Facades\Translator;

class NewForm extends View
{
    public function __construct(
        protected RoundRepository $rounds,
        protected ChapterRepository $chapters,
        protected Request $request,
        protected FormMapper $formMapper,
        protected SeasonFilterService $seasonFilter,
        protected ContentBlockRepository $contentBlocks,
        protected RoleRepository $roles,
    ) {
        $this->registerTranslations();
    }

    public function stage()
    {
        return 'new';
    }

    public function form()
    {
        $form = new Form;
        $form->slug = new Slug('');
        $form->type = feature_enabled('grant_reports') ? $this->request->get('type') : Form::FORM_TYPE_ENTRY;

        return $form;
    }

    public function mappedForm()
    {
        return $this->formMapper->mapFormWithDefaults(translate($this->form));
    }

    public function formTypes(): array
    {
        return [
            [
                'id' => Form::FORM_TYPE_ENTRY,
                'name' => trans('form.type.entry'),
            ],
            [
                'id' => Form::FORM_TYPE_REPORT,
                'name' => trans('form.type.report'),
            ],
        ];
    }

    public function activeRounds()
    {
        return [];
    }

    protected function season(): Season
    {
        return $this->seasonFilter->selectedOrActive();
    }

    public function allChapters()
    {
        return translate($this->chapters->getForSeason($this->season()->id))->map(function (Chapter $chapter) {
            return [
                'id' => $chapter->id,
                'name' => $chapter->name,
                'slug' => (string) $chapter->slug,
            ];
        });
    }

    public function translations()
    {
        return translations_for_vue(Consumer::languageCode(), [
            'form.form',
            'form.configuration',
            'files.buttons.single',
            'files',
            'miscellaneous.search.or',
            'buttons',
            'multiselect.select_all',
            'rounds.form',
            'rounds.titles',
            'entries.titles.start',
            'shared.learn_more',
            'form.messages.multiform_trial_alert',
            'form.order.label',
            'content-block.keys.entry-view',
        ]);
    }

    public function routes()
    {
        return routes_for_vue([
            'fast-start.round.validate',
            'forms.index',
            'forms.continue',
            'forms.save_continue',
        ]);
    }

    public function multiChapter()
    {
        return has_multiple_chapters();
    }

    public function multiform(): bool
    {
        return feature_enabled('multiform');
    }

    public function supportedLanguages(): array
    {
        return current_account()->supportedLanguages()->just('code');
    }

    public function formContentBlocks(): array
    {
        return Translator::shallow($this->contentBlocks->getAllByKey('entry-view'))
            ->map(fn($contentBlock) => ['slug' => (string) $contentBlock->slug, 'title' => $contentBlock->title])
            ->toArray();
    }

    public function formRoles(): array
    {
        return translate($this->roles->fields(['id', 'slug'])->guest(false)->get())
            ->map(fn(Role $role) => ['slug' => (string) $role->slug, 'name' => $role->name])
            ->toArray();
    }

    private function registerTranslations()
    {
        VueData::registerTranslations([
            'form.form.collaborative.label',
            'form.form.api_updateable.label',
            'shared.learn_more',
            'form.form',
            'notifications.form',
            'multiselect.select_all',
            'content-block.form.roles',
            'content-block.keys.entry-view',
        ]);
    }
}
