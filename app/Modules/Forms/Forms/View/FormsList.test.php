<?php

namespace AwardForce\Modules\Forms\Forms\View;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Features\Facades\Feature;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class FormsListTest extends BaseTestCase
{
    use Laravel;

    #[TestWith([Account::BRAND_AWARDFORCE, false])]
    #[TestWith([Account::BRAND_GOODGRANTS, true])]
    public function testItShowsGrantReportFormsOnBrand(string $brand, bool $has): void
    {
        $account = new Account;
        $account->brand = $brand;
        CurrentAccount::set($account);
        $formsList = app(FormsList::class);

        $this->assertEquals($has, $formsList->showGrantReportForms());
    }

    public function testItLinksToNewGrantReportForm(): void
    {
        $formsList = app(FormsList::class);
        Feature::shouldReceive('enabled')->with('grant_reports')->andReturnTrue();

        $this->assertEquals(route('forms.new', ['type' => 'report']), $formsList->newGrantReportFormLink());
    }

    public function testItLinksToFeatureDisabledGrantReports(): void
    {
        $formsList = app(FormsList::class);
        Feature::shouldReceive('enabled')->with('grant_reports')->andReturnFalse();

        $this->assertEquals('feature-disabled/grant_reports', $formsList->newGrantReportFormLink());
    }
}
