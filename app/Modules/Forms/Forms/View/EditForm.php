<?php

namespace AwardForce\Modules\Forms\Forms\View;

use AwardForce\Modules\Judging\Services\DefaultConfigurationGenerator;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Seasons\Models\Season;

class EditForm extends NewForm
{
    public function stage()
    {
        return 'settings';
    }

    public function form()
    {
        return $this->request->route('form');
    }

    protected function season(): Season
    {
        return $this->form->season;
    }

    public function activeRounds()
    {
        return translate($this->rounds->getForForm($this->form->id, $this->form->type))
            ->map(function (Round $round) {
                return [
                    'id' => $round->id,
                    'slug' => (string) $round->slug,
                    'name' => lang($round, 'name'),
                    'startsAt' => $this->date($round->startsAt, $round->startsTz),
                    'startsTz' => $round->startsTz,
                    'endsAt' => $this->date($round->endsAt, $round->endsTz),
                    'endsTz' => $round->endsTz,
                    'createdAt' => (string) $round->createdAt,
                    'selected' => true,
                ];
            });
    }

    private function date($date, $timezone): ?string
    {
        return $date ? (string) convert_date_to_timezone($date, $timezone)->format('Y-m-d H:i') : null;
    }

    public function roundTemplate(): array
    {
        return DefaultConfigurationGenerator::forRound(translate($this->form)->name ?: 'NTA');
    }
}
