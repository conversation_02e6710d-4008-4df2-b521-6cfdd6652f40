<?php

namespace AwardForce\Modules\Forms\Forms\Search;

use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Search\Columns\SubmittableCount;
use AwardForce\Modules\Forms\Forms\Search\Filters\ChapterManagerFilter;
use AwardForce\Modules\Forms\Forms\Search\Filters\SubmittableCountIncludeFilter;
use AwardForce\Modules\Forms\Forms\Search\Filters\TypeFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\TrashedFilter;

class FormColumnator extends Columnator
{
    /**
     * Base columns are those that are static and essentially not columns based off any custom
     * field configuration. For example, a user's first and last name are base columns.
     *
     * @return mixed
     */
    protected function baseColumns()
    {
        return new Columns([
            new SubmittableCount(),
        ]);
    }

    /**
     * Return the resource that this columnator represents (such as entries, or users).
     *
     * @return string|null
     */
    public function resource()
    {
        return 'Forms';
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;
        $columns = $this->columns($view);

        // Filters
        $dependencies->add((new ColumnFilter(...$columns))->with('forms.*'));
        $dependencies->add(new IncludeFilter(['chapters', 'season', 'rounds', 'nextRound']));
        $dependencies->add(new SubmittableCountIncludeFilter());
        $dependencies->add(new SeasonalFilter(
            $this->input['season'] ?? null,
            'forms.season_id'));
        $dependencies->add(new ChapterManagerFilter);
        $dependencies->add(new TypeFilter($this->input));
        $dependencies->add(new GroupingFilter('forms.id'));
        $dependencies->add(new TrashedFilter($this->input));

        return $dependencies;
    }

    /**
     * Each columnator must have a unique key that can be used to identify it when loading from settings.etc.
     */
    public static function key(): string
    {
        return 'forms.search';
    }

    /**
     * All columnators have an export view as well. The export key represents a value that can be used to search
     * for any saved exports for this particular columnator.
     */
    public static function exportKey(): string
    {
        return '';
    }

    /**
     * Return the repository to be used for future queries.
     */
    public function repository(): Repository
    {
        return app(FormRepository::class);
    }
}
