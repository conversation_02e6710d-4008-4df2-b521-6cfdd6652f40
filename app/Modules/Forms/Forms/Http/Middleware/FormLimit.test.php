<?php

namespace AwardForce\Modules\Forms\Forms\Http\Middleware;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Platform\Features\Feature;
use Platform\Features\Features;
use Tests\IntegratedTestCase;

final class FormLimitTest extends IntegratedTestCase
{
    /** @var FormLimit */
    protected $middleware;

    public function init(): void
    {
        $this->middleware = app(FormLimit::class);
    }

    public function testPassThroughWhenFormLimitNotReachedForTrialAccounts(): void
    {
        $account = current_account();
        $account->trialEndDate = now()->addDays(5);
        $account->save();

        $response = $this->middleware->handle(request()->merge(
            ['type' => Form::FORM_TYPE_ENTRY]),
            fn() => 'Trial account now can create multiple forms'
        );

        $this->assertEquals('Trial account now can create multiple forms', $response);
    }

    public function testPassThroughWhenFormLimitNotReachedForNonTrialAccounts(): void
    {
        $account = current_account();
        $account->trialEndDate = null;
        $account->formQuantityLimit = 2;
        $account->save();

        $response = $this->middleware->handle(
            request()->merge(['type' => 'entry']),
            fn() => 'Account can create multiple forms'
        );

        $this->assertEquals('Account can create multiple forms', $response);
    }

    public function testPassThroughWhenLimitReachedForTypeReport(): void
    {
        $account = current_account();
        $account->formQuantityLimit = 2;
        $account->save();

        // 1 muffin + 1 default form
        $this->muffin(Form::class);

        $response = $this->middleware->handle(
            request()->merge(['type' => Form::FORM_TYPE_REPORT]),
            fn() => 'Account can create multiple forms'
        );

        $this->assertEquals('Account can create multiple forms', $response);
    }

    public function testRedirectWhenLimitReachedForTypeEntry(): void
    {
        $account = current_account();
        $account->formQuantityLimit = 2;
        $account->save();

        // 2 muffins + 1 default form
        $this->muffins(1, Form::class);

        $response = $this->middleware->handle(
            request()->merge(['type' => Form::FORM_TYPE_ENTRY]),
            fn() => true
        );

        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('message'), $response->getTargetUrl());
    }

    public function testPassThroughWhenLimitReachedForFormReport(): void
    {
        $account = current_account();
        $account->formQuantityLimit = 2;
        $account->save();

        // 1 muffin + 1 default form
        $this->muffin(Form::class);

        $formReport = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);

        $request = request();
        $request->setRouteResolver(function () use ($formReport) {
            $router = $this->mock('router');
            $router->shouldReceive('parameter')->with('form')->andReturn($formReport);

            return $router;
        });

        $response = $this->middleware->handle(
            $request,
            fn() => 'Account can create multiple forms'
        );

        $this->assertEquals('Account can create multiple forms', $response);
    }

    public function testRedirectWhenLimitReachedForFormEntry(): void
    {
        $account = current_account();
        $account->formQuantityLimit = 2;
        $account->save();

        // 1 muffin + 1 default form
        $form = $this->muffin(Form::class);

        $request = request();
        $request->setRouteResolver(function () use ($form) {
            $router = $this->mock('router');
            $router->shouldReceive('parameter')->with('form')->andReturn($form);

            return $router;
        });

        $response = $this->middleware->handle(
            $request,
            fn() => true
        );

        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('message'), $response->getTargetUrl());
    }

    public function testRedirectWhenGoodGrantsAndQtyFormIsOne(): void
    {
        current_account()->defineFeatures(new Features([new Feature('multiform', 'enabled')]));
        current_account()->formQuantityLimit = 2;
        current_account()->brand = Account::BRAND_GOODGRANTS;

        $request = Request::create('/multiform');
        $middleware = app(FormLimit::class);

        $response = $middleware->handle(
            $request->merge(['type' => Form::FORM_TYPE_ENTRY]),
            fn() => true
        );
        $this->assertTrue($response);

        current_account()->formQuantityLimit = 1;
        $response = $middleware->handle($request, function () {
            return true;
        });
        $this->assertEquals(302, $response->getStatusCode());
        $this->assertEquals(route('message'), $response->getTargetUrl());
    }
}
