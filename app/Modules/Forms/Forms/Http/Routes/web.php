<?php

use AwardForce\Modules\Forms\Forms\Http\Controllers\FormsController;
use AwardForce\Modules\Forms\Forms\Http\Middleware\FormLimit;
use AwardForce\Modules\Forms\Forms\Http\Middleware\FormType;
use Illuminate\Support\Facades\Route;

Route::get('forms/{form}/settings')
    ->name('forms.settings')
    ->middleware(['auth.role', 'form.exceptions'])
    ->uses([FormsController::class, 'settings']);

Route::post('forms/{form}/settings')
    ->name('forms.save_settings')
    ->middleware(['auth.role', 'form.exceptions'])
    ->uses([FormsController::class, 'saveSettings']);

Route::group(['middleware' => ['auth.role', 'form.exceptions', 'feature:multiform']], function () {
    Route::get('forms')
        ->name('forms.index')
        ->middleware('remembrance')
        ->uses([FormsController::class, 'index']);

    Route::get('forms/select/{slug}/{route?}')
        ->name('forms.select')
        ->uses([FormsController::class, 'select']);

    Route::get('forms/{form}/edit')
        ->name('forms.edit')
        ->uses([FormsController::class, 'edit']);

    Route::get('forms/{form}/continue')
        ->name('forms.continue')
        ->uses([FormsController::class, 'continue']);

    Route::post('forms/{form}/continue')
        ->name('forms.save_continue')
        ->uses([FormsController::class, 'saveContinue']);

    Route::get('forms/new')
        ->name('forms.new')
        ->middleware([FormType::class, FormLimit::class])
        ->uses([FormsController::class, 'new']);

    Route::post('forms/new')
        ->name('forms.create')
        ->uses([FormsController::class, 'create']);

    Route::post('forms/{form}/copy')
        ->name('forms.copy')
        ->middleware(FormLimit::class)
        ->uses([FormsController::class, 'copy']);

    Route::delete('forms')
        ->name('forms.delete')
        ->uses([FormsController::class, 'delete']);

    Route::put('forms/undelete')
        ->name('forms.undelete')
        ->uses([FormsController::class, 'undelete']);

    Route::post('forms/invite')
        ->name('forms.invite')
        ->middleware('remembrance')
        ->uses([FormsController::class, 'invite'])
        ->withoutMiddleware(['feature:multiform']);
});
