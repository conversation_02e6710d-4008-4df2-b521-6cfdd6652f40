<?php

namespace AwardForce\Modules\Forms\Forms\Http\Controllers;

use AwardForce\Library\Copier\CopierOptions;
use AwardForce\Modules\Forms\Forms\Bus\CopyForm;
use AwardForce\Modules\Forms\Forms\Bus\CreateForm;
use AwardForce\Modules\Forms\Forms\Bus\FormInvite;
use AwardForce\Modules\Forms\Forms\Bus\UndeleteForms;
use AwardForce\Modules\Forms\Forms\Bus\UpdateForm;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Repositories\FormRepository;
use AwardForce\Modules\Forms\Forms\Http\Requests\CreateFormRequest;
use AwardForce\Modules\Forms\Forms\Http\Requests\DeleteFormRequest;
use AwardForce\Modules\Forms\Forms\Http\Requests\FilterRequest;
use AwardForce\Modules\Forms\Forms\Http\Requests\FormsListRequest;
use AwardForce\Modules\Forms\Forms\Http\Requests\InviteRequest;
use AwardForce\Modules\Forms\Forms\Http\Requests\UndeleteFormRequest;
use AwardForce\Modules\Forms\Forms\Http\Requests\UpdateFormRequest;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Forms\Services\FormSelectorService;
use AwardForce\Modules\Forms\Forms\Services\SubmittableService;
use AwardForce\Modules\Forms\Forms\View\ContinueNewForm;
use AwardForce\Modules\Forms\Forms\View\EditForm;
use AwardForce\Modules\Forms\Forms\View\FormsList;
use AwardForce\Modules\Forms\Forms\View\NewForm;
use AwardForce\Modules\Forms\Tabs\Bus\SetupDefaultDetailsTabCommand;
use AwardForce\Modules\Rounds\Commands\SeedDefaultRoundForFormCommand;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Platform\Bus\Commands\DeleteModelsCommand;
use Platform\Http\Controller;
use Platform\Http\Middleware\Remembrance;

class FormsController extends Controller
{
    use DispatchesJobs;

    public static $resource = 'Forms';

    /**
     * View all forms
     *
     * @return mixed
     */
    public function index(FormsListRequest $request, FormsList $view)
    {
        return $this->respond('form.list', $view);
    }

    /**
     * @return mixed
     */
    public function new(NewForm $view)
    {
        return $this->respond('form.form', $view);
    }

    /**
     * @return mixed
     */
    public function settings(Form $form, EditForm $view)
    {
        return $this->respond('form.form', $view);
    }

    public function saveSettings(Form $form, UpdateFormRequest $request)
    {
        $this->update($form, $request);

        return feature_enabled('multiform') ? redirect()->route('forms.index') : back();
    }

    /**
     * @param  EditForm  $view
     * @return mixed
     */
    public function continue(Form $form, ContinueNewForm $view)
    {
        return $this->respond('form.form', $view);
    }

    public function saveContinue(Form $form, UpdateFormRequest $request)
    {
        $this->update($form, $request);

        return $this->edit($form);
    }

    /**
     * @return mixed
     */
    public function edit(Form $form)
    {
        return redirect()->route(
            SubmittableService::getInstance($form->type)->formEditRoute(),
            ['configuration' => 'true', 'formSlug' => (string) $form->slug]
        );
    }

    private function update(Form $form, UpdateFormRequest $request)
    {
        $this->dispatch(new UpdateForm(
            $form,
            $request->array('settings'),
            $request->get('type'),
            $request->get('chapterOption'),
            $request->get('chapters', []),
            $request->get('translated', []),
            $request->collect('rounds'),
            $request->integer('order'),
            $request->string('contentBlocks'),
            $request->input('roles', [])
        ));
    }

    /**
     * @return mixed
     */
    public function create(CreateFormRequest $request)
    {
        $form = $this->dispatchSync(new CreateForm(
            $request->get('type'),
            $request->get('translated', []),
            $request->array('settings'),
            $request->integer('order'),
        ));

        $this->dispatch(new SetupDefaultDetailsTabCommand(
            $form->accountId,
            $form->seasonId,
            $form->id
        ));

        if ($request->get('createRound', false)) {
            $this->dispatch(new SeedDefaultRoundForFormCommand($form));
        }

        return $form->oneStepConfiguration() ?
            redirect()->route('forms.edit', ['form' => (string) $form->slug]) :
            redirect()->route('forms.continue', ['form' => $form, 'vtab' => 'cover']);
    }

    /**
     * Sets the session's FormSelector to the specified form.
     *
     * @return mixed
     */
    public function select(FilterRequest $request, FormSelectorService $formSelector, FormRepository $forms)
    {
        if (($formSlug = $request->route('slug')) == 'all') {
            $formSelector->set(FormSelector::FILTER_ALL);
        } else {
            $formSelector->set($forms->requireBySlug($formSlug));
        }

        if ($url = $request->input('redirect', false)) {
            return redirect()->to($url);
        }

        if ($request->route) {
            return redirect()->route($request->route);
        }

        return redirect()->to(Remembrance::removeFilters(url()->previous(), config('search.remove_filters.forms')));
    }

    /**
     * Delete form
     *
     * @return RedirectResponse
     */
    public function delete(DeleteFormRequest $request, FormRepository $forms)
    {
        $this->dispatch(new DeleteModelsCommand($request->get('selected', []), $forms));

        return redirect()->route('forms.index');
    }

    /**
     * Undelete form
     *
     * @return RedirectResponse
     */
    public function undelete(UndeleteFormRequest $request)
    {
        $this->dispatch(new UndeleteForms($request->get('selected', [])));

        return redirect()->route('forms.index');
    }

    public function invite(InviteRequest $request)
    {
        $form = $this->dispatchSync(
            new FormInvite(
                $request->get('formId'),
                $emails = $request->get('emails'),
                $request->get('role'),
                $request->get('chapter'),
                $request->get('category'),
                $request->get('message-markdown'),
                $request->get('deadlineDate'),
                $request->get('deadlineTimezone'),
                $request->input('notificationId') ?: null
            )
        );

        session()->put('invitation_form', $request->except('emails'));

        $invitesCount = count($emails);
        $message = trans_choice('users.form.invitation.success_without_members', $invitesCount, ['invites_sent_count' => $invitesCount]).
            ' '.trans('shared.for_resource', ['resource' => feature_enabled('multiform') ? $form->name : '']);

        return redirect()->back()
            ->with('message', $message)
            ->with('type', 'success');
    }

    public function copy(Form $form, Request $request)
    {
        $this->dispatch(new CopyForm(
            $form,
            new CopierOptions($request->get('options', []))
        ));

        return redirect()->route('forms.index')->with([
            'message' => trans('form.messages.form_copy_started'),
            'type' => 'info',
        ]);
    }
}
