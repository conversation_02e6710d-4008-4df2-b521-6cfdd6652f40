<?php

namespace AwardForce\Modules\Forms\Tabs\Search;

use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use Tests\IntegratedTestCase;
use Tests\Library\Search\ColumnatorTestHelper;

final class TabsColumnatorTest extends IntegratedTestCase
{
    use ColumnatorTestHelper;

    public function testCanSearchTabs(): void
    {
        $this->muffins(3, Tab::class);

        $items = $this->search();

        $this->assertCount(3, $items);
    }

    public function testCanSearchTabsByKeyword(): void
    {
        $fooTab = $this->muffin(Tab::class);
        $fooTab->saveTranslation(default_language_code(), 'name', 'foo', $fooTab->accountId);

        $barTab = $this->muffin(Tab::class);
        $barTab->saveTranslation(default_language_code(), 'name', 'bar', $barTab->accountId);

        $items = $this->search([], ['keywords' => 'foo']);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $fooTab->id);
    }

    protected function area()
    {
        return 'tabs.search';
    }

    public function testCanSearchTabsByType(): void
    {
        $this->muffin(Tab::class, ['type' => Tab::TYPE_DETAILS]);

        $tab = $this->muffin(Tab::class, ['type' => Tab::TYPE_FIELDS]);

        $items = $this->search([], ['tab_type' => Tab::TYPE_FIELDS]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $tab->id);
    }

    public function testCanSearchTabsByMultipleFilters(): void
    {
        $this->muffin(Tab::class, ['type' => Tab::TYPE_DETAILS]);

        $tab = $this->muffin(Tab::class, ['type' => Tab::TYPE_FIELDS]);
        $tab->saveTranslation(default_language_code(), 'name', 'foo', $tab->accountId);

        $items = $this->search([], [
            'tab_type' => Tab::TYPE_FIELDS,
            'keywords' => 'foo',
        ]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $tab->id);
    }
}
