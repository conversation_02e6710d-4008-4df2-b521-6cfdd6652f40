<?php

namespace AwardForce\Modules\Forms\Tabs\Database\Repositories;

use AwardForce\Library\Database\Eloquent\Caching\HasRequestCache;
use AwardForce\Library\Database\Eloquent\HardDeletesRepository;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Database\Repository\Builder\HasSlugBuilder;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Exports\Models\Exportable;
use AwardForce\Modules\Forms\Forms\Database\Behaviours\DeletedForms;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentTabRepository extends Repository implements TabRepository
{
    use DeletedForms, Exportable, HardDeletesRepository, Has<PERSON>ueryBuilder, HasRequestCache, HasSlugBuilder;

    public function __construct(Tab $model)
    {
        $this->model = $model;
    }

    /**
     * Returns a collection of tabs based on the specified season.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getFromSeason(Season $season)
    {
        return $this->getQuery()
            ->with('categories')
            ->with('contentBlock')
            ->whereSeasonId($season->id)
            ->get();
    }

    /**
     * @return mixed
     */
    public function getFromForm(?Form $form = null)
    {
        return $this->getQuery()
            ->with('categories')
            ->with('contentBlock')
            ->whereFormId($form->id)
            ->get();
    }

    /**
     * Returns a collection of tabs based on the specified form.
     *
     * @param  Form|null  $form
     * @return \Illuminate\Support\Collection
     */
    public function getAllForForm(Form $form)
    {
        return $this->getQuery()
            ->with('categories')
            ->with('contentBlock')
            ->whereFormId($form->id)
            ->get();
    }

    /**
     * Counts tabs based in the specified season.
     */
    public function countInSeason(Season $season): int
    {
        return $this->getQuery()
            ->whereSeasonId($season->id)
            ->count();
    }

    /**
     * Returns a collection of Tabs for the specified resource, optionally filtered by season.
     *
     * @param  string  $resource
     * @return \Illuminate\Support\Collection
     */
    public function forResource($resource, ?Season $season = null)
    {
        $query = $this->getQuery()
            ->with('contentBlock')
            ->whereResource($resource)
            ->orderBy('order', 'asc');

        if ($season) {
            $query = $query->whereSeasonId($season->id);
        }

        return $query->get();
    }

    public function forResourceAndForm($resource, Form $form)
    {
        return $this->getQuery()
            ->with('contentBlock')
            ->whereResource($resource)
            ->whereFormId($form->id)
            ->orderBy('order', 'asc')
            ->get();
    }

    /**
     * Returns a collection of Tabs for the specified resource and type, optionally filtered by season.
     *
     * @param  string  $resource
     * @param  string  $type
     * @return \Illuminate\Support\Collection
     */
    public function forResourceType($resource, $type, ?Season $season = null)
    {
        $query = $this->getQuery()
            ->with('contentBlock')
            ->whereResource($resource)
            ->whereType($type)
            ->orderBy('order', 'asc');

        if ($season) {
            $query = $query->whereSeasonId($season->id);
        }

        return $query->get();
    }

    /**
     * Returns a collection of tabs for the specified resource, type and form.
     *
     * @param  string  $resource
     * @param  array|string  $type
     * @param  int  $formId
     * @return \Illuminate\Support\Collection
     */
    public function forResourceTypeAndForm($resource, $type, $formId)
    {
        return $this->getQuery()
            ->whereResource($resource)
            ->whereIn('type', (array) $type)
            ->whereFormId($formId)
            ->orderBy('order', 'asc')
            ->get();
    }

    /**
     * Return all tabs that are of a deletable nature (aka, not locked).
     *
     * @return mixed
     */
    public function deletable()
    {
        return $this->getQuery()->whereLocked(false)->get();
    }

    /**
     * Search for a single tab by its tab type in a season or any season.
     *
     * @return mixed
     */
    public function tabExists(string $type, ?int $seasonId): bool
    {
        $query = $this->getQuery();

        if (isset($seasonId)) {
            $query = $query->whereSeasonId($seasonId);
        }

        return $query->whereType($type)->exists();
    }

    /**
     * Search for a single tab by its tab type, season and category.
     *
     * @return bool
     */
    public function tabExistsByFormAndCategory(int $formId, ?int $categoryId, string $type, $ignoreHidden = false)
    {
        return $this->getQuery()
            ->leftJoin('category_tab', 'category_tab.tab_id', '=', 'tabs.id')
            ->where('tabs.form_id', $formId)
            ->whereType($type)
            ->when($ignoreHidden, function ($query) {
                $query->where('tabs.visible_to_entrants', 1);
            })
            ->where(function ($query) use ($categoryId) {
                /**
                 * category_id will be in category_tab ONLY if `category_option` = 'select'
                 */
                return $query->where('category_option', '=', 'all')
                    ->when($categoryId, fn($q) => $q->orWhere('category_id', '=', $categoryId));
            })
            ->exists();
    }

    /**
     * Overrides the default behaviour so as to include some additional objects.
     *
     * @param  string  $slug
     * @return mixed
     */
    public function getBySlug($slug)
    {
        return $this->getByQuery('slug', $slug)
            ->with('season')
            ->first();
    }

    /**
     * Deletes all existing tabs from the database for the specified season.
     *
     * @param  int  $seasonId
     */
    public function deleteAllFromSeason($seasonId)
    {
        $this->getQuery()
            ->whereSeasonId($seasonId)
            ->delete();
    }

    /**
     * Synchronise the tab categories with the database.
     *
     * @return mixed
     */
    public function syncCategories(Tab $tab, array $categoryIds)
    {
        $tab->categories()->sync($categoryIds);
    }

    /**
     * Updates all tabs that have the content block and removes the association.
     */
    public function removeFromContentBlock(int $contentBlockId)
    {
        $this->getQuery()
            ->where('contentblock_id', '=', $contentBlockId)
            ->update(['contentblock_id' => null]);
    }

    /**
     * Returns all matching records filtered by season, with trashed.
     *
     * @param  int  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getForSeasonWithTrashed($seasonId = null)
    {
        $query = $this->getQuery();

        if ($seasonId) {
            $query->where('tabs.season_id', $seasonId);
        }

        return $query->withTrashed()->get();
    }

    public function getForFormWithTrashed($formId = null)
    {
        $query = $this->getQuery();

        if ($formId) {
            $query->where('tabs.form_id', $formId);
        }

        return $query->withTrashed()->get();
    }

    /**
     * Returns a collection of Tabs for the specified category, optionally filtered by resource and season.
     *
     * @param  string  $resource
     * @param  int  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function forCategory(Category $category, $resource = null, $seasonId = null)
    {
        return $this->getQuery()
            ->select('tabs.*')
            ->distinct()
            ->leftJoin('category_tab', 'tabs.id', '=', 'category_tab.tab_id')
            ->where(function ($query) use ($category) {
                $query->orWhere('tabs.category_option', 'all')
                    ->orWhere('category_tab.category_id', $category->id);
            })
            ->when($resource, function ($query, $resource) {
                return $query->whereResource($resource);
            })
            ->when($seasonId, function ($query, $seasonId) {
                return $query->whereSeasonId($seasonId);
            })
            ->whereFormId($category->formId)
            ->orderBy('order', 'asc')
            ->get();
    }

    /**
     * Returns maximum value of given field (as in SQL), filtered by season id.
     *
     * @return mixed
     */
    public function getMaxValue(string $fieldName, ?int $seasonId = null)
    {
        return $this->getQuery()
            ->when($seasonId, function ($query, $seasonId) {
                return $query->whereSeasonId($seasonId);
            })
            ->max($fieldName);
    }

    /**
     * Returns all visible tabs for the given category
     *
     * @return mixed
     */
    public function visibleForCategory(Category $category, bool $entrantVisibility = true)
    {
        return $this->getQuery()
            ->select('tabs.*')
            ->where('tabs.form_id', $category->formId)
            ->leftJoin('category_tab', 'tabs.id', '=', 'category_tab.tab_id')
            ->where(function ($query) use ($category) {
                $query->orWhere('tabs.category_option', 'all')
                    ->orWhere('category_tab.category_id', $category->id);
            })
            ->when($entrantVisibility, fn($query) => $query->where('tabs.visible_to_entrants', true))
            ->get();
    }

    public function contentBlockAssociatedWithAnEligibilityTab(string $contentBlockSlug): bool
    {
        return $this->getQuery()
            ->whereType(Tab::TYPE_ELIGIBILITY)
            ->where(function (Builder $query) use ($contentBlockSlug) {
                $query->whereJsonContains('settings->eligible-content-block', $contentBlockSlug)
                    ->orWhereJsonContains('settings->ineligible-content-block', $contentBlockSlug);
            })
            ->exists();
    }
}
