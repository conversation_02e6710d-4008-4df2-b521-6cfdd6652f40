<?php

namespace AwardForce\Modules\Forms\Collaboration\Firebase\Locator;

use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable as SubmittableModel;

final readonly class FlagsSubmittable extends BaseLocator
{
    public const COLLECTION = 'flags-submittable';

    public function __construct(private SubmittableModel $submittable)
    {
    }

    public function documentParts(): array
    {
        return array_merge(
            (new Submittable($this->submittable))->documentParts(),
        );
    }
}
