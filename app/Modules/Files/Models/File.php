<?php

namespace AwardForce\Modules\Files\Models;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\Filesystem\Metadata;
use AwardForce\Library\Filesystem\MetadataReader;
use AwardForce\Library\Filesystem\Storage;
use AwardForce\Modules\Accounts\Models\Relocation;
use AwardForce\Modules\Accounts\Traits\BelongsToAccount;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Files\Events\FileExpired;
use AwardForce\Modules\Files\Events\FileProcessed;
use AwardForce\Modules\Files\Events\FileTranscodingStatusWasUpdated;
use AwardForce\Modules\Files\Events\FileUploaded;
use AwardForce\Modules\Files\Events\FileWasCopied;
use AwardForce\Modules\Files\Events\UsageWasRefreshed;
use AwardForce\Modules\Files\Services\AWSMediaConvert;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFlatValues;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ValuesProvider;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use Exception;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Str;
use Platform\Filesystem\Paths\FileDirPath;

/**
 * AwardForce\Modules\Files\Models\File
 *
 * @property int $id
 * @property int $accountId
 * @property int $userId
 * @property string $resource
 * @property int|null $resourceId
 * @property string|null $token
 * @property string|null $file
 * @property string $original
 * @property int|null $size
 * @property int|null $duration
 * @property string|null $mime
 * @property string $status
 * @property string|null $statusMessage
 * @property int|null $usageSize
 * @property int|null $usageCount
 * @property string|null $transcodingStatus
 * @property mixed $transcodingOutputs
 * @property Metadata $metadata
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property int|null $foreignId
 * @property string|null $language
 * @property-read \AwardForce\Modules\Accounts\Models\Account|null $account
 * @property-read Attachment|null $attachment
 * @property-read File|null $caption
 * @property-read File|null $captionFor
 * @property-read \Platform\Database\Eloquent\Collection<int, Relocation> $relocations
 * @property-read int|null $relocationsCount
 * @property-read User|null $user
 * @property-read Organisation|null $organisation
 *
 * @method static \AwardForce\Modules\Files\Models\FilesCollection<int, static> all($columns = ['*'])
 * @method static \AwardForce\Modules\Files\Models\FilesCollection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|File newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|File newQuery()
 * @method static \Platform\Database\Eloquent\Builder|File preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|File query()
 * @method static \Platform\Database\Eloquent\Builder|File whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereDuration($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereFile($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereForeignId($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereLanguage($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereMime($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereOriginal($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereResource($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereResourceId($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereSize($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereStatus($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereStatusMessage($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereToken($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereTranscodingOutputs($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereTranscodingStatus($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereUpdatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereUsageCount($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereUsageSize($value)
 * @method static \Platform\Database\Eloquent\Builder|File whereUserId($value)
 *
 * @mixin \Eloquent
 */
class File extends Model implements ValuesProvider
{
    use BelongsToAccount;
    use HasFlatValues;

    const STATUS_OK = 'ok';

    const STATUS_PENDING = 'pending';

    const STATUS_MISSING = 'missing';

    const STATUS_EXPIRED = 'expired';

    const STATUS_REJECTED = 'rejected';

    const STATUS_REJECTED_NOT_FOUND = 'not-found';

    const STATUS_REJECTED_NO_VALIDATOR = 'no-validator';

    const STATUS_REJECTED_VALIDATION_FAILED = 'validation-failed';

    const STATUS_REJECTED_VIRUS_FOUND = 'virus-found';

    const STATUS_REJECTED_BASE64_CONVERSION_FAILED = 'base64-conversion-failed';

    const STATUS_BULK_DOWNLOAD_FAILED = 'failed-bulk-download';

    const RESOURCE_ATTACHMENTS = 'Attachments';

    const RESOURCE_AWARD = 'Award';

    const RESOURCE_ENTRIES = 'Entries';

    const RESOURCE_THEME = 'Theme';

    const RESOURCE_BULK_DOWNLOAD = 'Bulk download';

    const RESOURCE_CATEGORIES = 'Categories';

    const RESOURCE_CHAPTERS = 'Chapters';

    const RESOURCE_COMMENTS = 'Comments';

    const RESOURCE_EXPORT = 'Export';

    const RESOURCE_FILE_FIELD = 'File field';

    const RESOURCE_FORM = 'Form';

    const RESOURCE_IMPORT = 'Import';

    const RESOURCE_SETTING = 'Setting';

    const RESOURCE_SCORESETS = 'ScoreSets';

    const RESOURCE_PLAGIARISM_REPORT = 'Plagiarism';

    const RESOURCE_CAPTION = 'Caption';

    const RESOURCE_CONTRACTS = 'Contracts';

    const RESOURCE_DOCUMENTS = 'Documents';

    const RESOURCE_DOCUMENT_TEMPLATES = 'Document Templates';

    const RESOURCE_PROFILE_PHOTO = 'Avatar';

    const RESOURCE_ORGANISATION_LOGO = 'Organisation logo';

    const RESOURCES = [
        self::RESOURCE_ATTACHMENTS,
        self::RESOURCE_AWARD,
        self::RESOURCE_ENTRIES,
        self::RESOURCE_THEME,
        self::RESOURCE_BULK_DOWNLOAD,
        self::RESOURCE_CATEGORIES,
        self::RESOURCE_CHAPTERS,
        self::RESOURCE_COMMENTS,
        self::RESOURCE_EXPORT,
        self::RESOURCE_FILE_FIELD,
        self::RESOURCE_FORM,
        self::RESOURCE_IMPORT,
        self::RESOURCE_SETTING,
        self::RESOURCE_SCORESETS,
        self::RESOURCE_CAPTION,
        self::RESOURCE_CONTRACTS,
        self::RESOURCE_DOCUMENTS,
        self::RESOURCE_DOCUMENT_TEMPLATES,
        self::RESOURCE_PROFILE_PHOTO,
        self::RESOURCE_ORGANISATION_LOGO,
    ];

    // Transcode status constants
    const TRANSCODE_COMPLETED = 'completed';

    const TRANSCODE_ERROR = 'error';

    const TRANSCODE_PROGRESSING = 'progressing';

    const TRANSCODE_WAITING = 'waiting';

    const TRANSCODE_WARNING = 'warning';

    // Custom event for file copying
    protected $copiedEvent = FileWasCopied::class;

    /** @var array */
    protected $guarded = [];

    protected $casts = [
        'resource_id' => 'integer',
        'metadata' => Metadata::class,
    ];

    public static function prepare(
        string $original,
        string $file,
        string $resource,
        ?int $resourceId = null,
        ?int $foreignId = null,
        ?string $language = null,
        ?string $mime = null,
    ): File {
        return File::create([
            'accountId' => current_account_id(),
            'userId' => Consumer::id() ?? current_account()->userId,
            'original' => $original,
            'file' => $file,
            'resource' => $resource,
            'resourceId' => $resourceId,
            'foreignId' => $foreignId,
            'token' => Str::random(16),
            'language' => $language,
            'mime' => $mime,
        ]);
    }

    public static function addTranscription(Transcription $transcription, int $size)
    {
        return File::create([
            'accountId' => current_account_id(),
            'userId' => $transcription->userId,
            'original' => pathinfo($transcription->file->getAttribute('original'), PATHINFO_FILENAME).'.vtt',
            'file' => $transcription->file->location('file.vtt'),
            'resource' => self::RESOURCE_CAPTION,
            'foreignId' => $transcription->fileId,
            'size' => $size,
            'usageCount' => 1,
            'usageSize' => $size,
            'mime' => 'text/vtt',
            'status' => self::STATUS_OK,
        ]);
    }

    public function newCollection(array $models = []): FilesCollection
    {
        return new FilesCollection($models);
    }

    /**
     * Each file belongs to a specific user.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Each file may belong to an attachment.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function attachment()
    {
        return $this->belongsTo(Attachment::class, 'resource_id');
    }

    public function relocations(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(Relocation::class);
    }

    public function caption()
    {
        return $this->hasOne(File::class, 'foreign_id')->where('resource', self::RESOURCE_CAPTION);
    }

    // Inverse relationship for caption.
    public function captionFor()
    {
        return $this->hasOne(File::class, 'id', 'foreign_id');
    }

    /**
     * Returns the file's location. This is based on the file's path and represents a container or folder. Receives an
     * optional parameter used to represent a specific file in the location.
     *
     * @param  null  $location
     * @return string
     */
    public function location($location = null)
    {
        return dirname($this->file).'/'.$location;
    }

    /**
     * Set the transcoding status of the file record.
     *
     * @param  string  $status
     *
     * @throws Exception
     */
    public function setTranscodingStatusAttribute($status)
    {
        if (! is_null($status)) {
            $status = strtolower($status);
            $validStatuses = $this->validTranscodingStatuses();

            if (! in_array($status, $validStatuses)) {
                throw new Exception('Invalid transcoding status ['.$status.']. Valid options are: [waiting, progressing, completed, error, warning].');
            }
        }

        $this->attributes['transcoding_status'] = $status;
    }

    /**
     * Mutator to sanitise and encode the outputs array for storage.
     */
    public function setTranscodingOutputsAttribute(?array $outputs = null)
    {
        $this->attributes['transcoding_outputs'] = json_encode($outputs);
    }

    /**
     * Decode the JSON-encoded outputs from the database.
     *
     * @param  string  $outputs
     * @return mixed
     */
    public function getTranscodingOutputsAttribute($outputs)
    {
        return json_decode($outputs ?? '');
    }

    /**
     * Takes the array of outputs, and filters them to get the important information, such as
     * the file that transcoding tried to create, along with the error message. The resulting
     * array is a flat one-dimensional array consisting of only messages.
     *
     * @return array
     */
    public function transcodingErrors()
    {
        if ($this->transcodingStatus != self::TRANSCODE_ERROR) {
            return [];
        }

        if ($this->transcodedWithMediaConvert()) {
            return ["{$this->transcodingOutputs->errorCode}: {$this->transcodingOutputs->errorMessage}"];
        }

        return array_map(function ($output) {
            $status = $output->statusDetail ?? $output->status;

            return "[{$output->key}] {$status}";
        }, $this->transcodingOutputs ?: []);
    }

    /**
     * Sets the status for the file record, and saves it.
     *
     * @param  string  $status
     */
    public function updateTranscodingStatus($status, array $outputs)
    {
        $this->transcodingStatus = $this->transcodeStatus($status);
        $this->transcodingOutputs = $outputs;
        $this->duration = AWSMediaConvert::outputDuration((array) $this->transcodingOutputs);
        $this->save();
        $this->raise(new FileTranscodingStatusWasUpdated($this));
    }

    /**
     * Elastic Transcoder and MediaConvert have slightly different status codes. This method translates the status
     * codes from MediaConvert to the ones we already use in the application.
     *
     * @return string
     */
    private function transcodeStatus($status)
    {
        return match ($status) {
            AWSMediaConvert::STATUS_COMPLETED => self::TRANSCODE_COMPLETED,
            default => $status
        };
    }

    /**
     * We need to differentiate between files that were transcoded with Elastic Transcoder, and those that were
     * transcoded with MediaConvert.
     *
     * This method inspects the transcoding outputs looking for signs that the file was transcoded with MediaConvert.
     *
     * @return bool
     */
    private function transcodedWithMediaConvert()
    {
        return AWSMediaConvert::compatibleOutputs((array) $this->transcodingOutputs);
    }

    public function thumbnailable(): bool
    {
        if ($this->isImage()) {
            return $this->size <= config('ui.images.max_file_size');
        }

        return in_array($this->extension(), config('awardforce.extensions.thumbnails'));
    }

    public function thumbnail(): ?string
    {
        if ($this->isTranscodedVideo()) {
            return $this->transcodeThumbnail();
        }

        return ! $this->isVideo() ? $this->file : null;
    }

    public function transcodeThumbnail()
    {
        if ($this->transcodedWithMediaConvert()) {
            return $this->location(AWSMediaConvert::DEFAULT_THUMBNAIL);
        }

        return $this->location(AWSMediaConvert::LEGACY_THUMBNAIL);
    }

    public function transcodePlaylist()
    {
        if ($this->transcodedWithMediaConvert()) {
            return $this->location(AWSMediaConvert::DEFAULT_MANIFEST);
        }

        return $this->location(AWSMediaConvert::LEGACY_MANIFEST);
    }

    /**
     * When re-transcoding a file, the transcoding status should be reset back to null.
     */
    public function reTranscode()
    {
        $this->transcodingStatus = null;
        $this->transcodingOutputs = null;
        $this->duration = null;
        $this->save();
    }

    /**
     * Returns true if the file is a video.
     *
     * @return bool
     */
    public function isVideo()
    {
        return (bool) preg_match('#^video#', $this->mime);
    }

    /**
     * Returns true if the file is a caption.
     *
     * @return bool
     */
    public function isCaption()
    {
        return $this->resource === self::RESOURCE_CAPTION;
    }

    /**
     * Returns true if transcoding has finished.
     */
    public function transcodingFinished()
    {
        return in_array($this->transcodingStatus, [
            self::TRANSCODE_COMPLETED,
            self::TRANSCODE_WARNING,
        ]);
    }

    public function transcodable(): bool
    {
        return $this->isVideo() && $this->ready() && feature_enabled('transcoding');
    }

    /**
     * Returns true if the file is a video, transcoding feature is enabled and transcoding has finished.
     */
    public function isTranscodedVideo()
    {
        return $this->isVideo() && feature_enabled('transcoding') && $this->transcodingFinished();
    }

    /**
     * Returns true if the file is an audio file.
     *
     * @return bool
     */
    public function isAudio()
    {
        return (bool) preg_match('#^audio#', $this->mime);
    }

    /**
     * Returns true if the file is an image file.
     *
     * @return bool
     */
    public function isImage()
    {
        return (bool) preg_match('#^image#', $this->mime);
    }

    public function imgixUrl(): ?string
    {
        if (! $this->ready() || ! $this->isImage()) {
            return null;
        }

        return imgix($this->file);
    }

    public function imgixThumbnailUrl(): ?string
    {
        if (! $this->ready() || ! $this->isImage()) {
            return null;
        }

        return imgix($this->file, params: [
            'fit' => 'clip',
            'w' => 800,
            'h' => 800,
        ]);
    }

    /**
     * @return bool
     */
    public function isFileField()
    {
        return starts_with($this->resource, self::RESOURCE_FILE_FIELD.'-');
    }

    public function forAttachment(): bool
    {
        return $this->resource === self::RESOURCE_ATTACHMENTS;
    }

    /**
     * Returns the valid errors applicable to transcoding status updates.
     *
     * @return array
     */
    private function validTranscodingStatuses()
    {
        return [self::TRANSCODE_WAITING, self::TRANSCODE_PROGRESSING, self::TRANSCODE_COMPLETED, self::TRANSCODE_ERROR, self::TRANSCODE_WARNING];
    }

    /**
     * Ensures file is saved without leading slash.
     */
    public function setFileAttribute(string $value)
    {
        $this->attributes['file'] = ltrim($value, '/');
    }

    public function getMetadataUrl(): string
    {
        return app(MetadataReader::class)->getUrl($this->file);
    }

    /**
     * Replicate the file model, and set its new file path.
     *
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function replicate(?array $except = null)
    {
        $file = parent::replicate($except);

        $justFile = str_replace($this->location(), '', $file->file);
        $newPath = FileDirPath::fromHash();

        $file->file = $newPath.'/'.$justFile;
        $file->token = null; // Reset the token

        return $file;
    }

    /**
     * Refresh the file usage counters (file count and file size) on the file record.
     * Assumes any files within the storage directory are parts of this file and counts them appropriately.
     */
    public function refreshUsage()
    {
        $contents = app(Storage::class)->getDriver()
            ->listContents(dirname($this->file), true);

        $this->usageCount = count($contents);
        $this->usageSize = array_sum(array_pluck($contents, 'size'));

        $this->save();

        $this->raise(new UsageWasRefreshed($this));
    }

    /**
     * @return int|null
     */
    public function getDurationFromTranscodingOutputs()
    {
        return $this->transcodingOutputs[0]->duration ?? null;
    }

    /**
     * Log video viewing time.
     */
    public function watch(int $userId, int $watched): VideoLog
    {
        return VideoLog::logViewingTime($this, $userId, $watched);
    }

    public function ready(): bool
    {
        return $this->status === self::STATUS_OK;
    }

    public function expire()
    {
        $this->status = self::STATUS_EXPIRED;
        $this->save();

        $this->raise(new FileExpired($this));
    }

    public function reject(string $message): File
    {
        $this->update([
            'status' => self::STATUS_REJECTED,
            'statusMessage' => $message,
        ]);

        $this->raise(new FileProcessed($this));

        return $this;
    }

    public function finalise(string $filename, ?int $tabId = null)
    {
        $this->update([
            'file' => $filename,
            'statusMessage' => null,
        ]);

        $this->raise(new FileUploaded($this, $tabId));
        $this->raise(new FileProcessed($this, $tabId));
    }

    public function extension()
    {
        return pathinfo($this->file, PATHINFO_EXTENSION);
    }

    public function requiredAcl(): string
    {
        return $this->resource == self::RESOURCE_SETTING ? 'public' : 'private';
    }

    /**
     * Image width parameter from Imgix metadata request.
     */
    public function imageWidth(): ?int
    {
        if (empty($this->file)) {
            return null;
        }

        return $this->metadata->imageWidth;
    }

    /**
     * Image height parameter from Imgix metadata request.
     */
    public function imageHeight(): ?int
    {
        if (empty($this->file)) {
            return null;
        }

        return $this->metadata->imageHeight;
    }

    /**
     * Determines the video height as the minimum value between the video's actual height
     * and the account's video height setting. If no account setting is available, the default value is 450.
     */
    public function videoHeight(?int $fallbackVideoHeight): int
    {
        $defaultVideoHeight = $fallbackVideoHeight ?: 450;
        [$width, $height] = $this->getVideoDimensionsFromTranscodingOutputs('FILE_GROUP');

        // Filter out non-positive heights
        $validHeights = array_filter([$defaultVideoHeight, $height], fn($item) => $item > 0);

        return min($validHeights);
    }

    public function videoAspectRatio(): ?string
    {
        [$width, $height] = $this->getVideoDimensionsFromTranscodingOutputs('HLS_GROUP');

        if ($width <= 0 || $height <= 0) {
            return null;
        }

        $greatestCommonDivisor = function (int $width, int $height) use (&$greatestCommonDivisor): int {
            return ($height === 0) ? $width : $greatestCommonDivisor($height, $width % $height);
        };

        $divisor = $greatestCommonDivisor($width, $height);

        return ($width / $divisor).':'.($height / $divisor);
    }

    /**
     * Retrieves the video dimensions (width and height) from the transcoding outputs based on the specified output group type.
     *
     * @see https://docs.aws.amazon.com/mediaconvert/latest/ug/output-file-names-and-paths.html
     */
    private function getVideoDimensionsFromTranscodingOutputs(string $group): array
    {
        $data = json_decode($this, true) ?? [];

        if (empty($data) || ! data_get($data, 'transcodingOutputs', [])) {
            return [0, 0];
        }

        $fileGroupOutputs = $this->getTranscodingOutputDetails($data, $group);

        return [
            data_get($fileGroupOutputs, 'widthInPx', 0),
            data_get($fileGroupOutputs, 'heightInPx', 0),
        ];
    }

    private function getTranscodingOutputDetails(array $data, string $group): array
    {
        $outputs = array_filter(
            data_get($data, 'transcodingOutputs', []),
            fn($item) => data_get($item, 'type') === $group
        );

        return data_get(array_values($outputs), '0.outputDetails.0.videoDetails', []);
    }

    /**
     * check if a file is single file upload for a given field
     */
    public function isSingleUploadForField(int $field_id): bool
    {
        return $this->resource === "File field-$field_id";
    }

    public function unlinkResource(): void
    {
        $this->update(['resource_id' => null]);
    }

    /**
     * Return the size of a base64 encoded file in bytes
     */
    public static function sizeFromBase64String(string $data): float|int
    {
        return (strlen($data) * 3 / 4) - substr_count(substr($data, -2), '=');
    }

    public function getOriginalAttribute(?string $original): string
    {
        return preg_replace('#\p{C}+#u', '', $original ?? '');
    }

    public function belongsToSubmittable(): bool
    {
        return $this->foreignId &&
            Str::contains($this->resource, [self::RESOURCE_ENTRIES, self::RESOURCE_ATTACHMENTS, self::RESOURCE_FILE_FIELD]);
    }

    public function transcribable(): bool
    {
        return $this->transcodable() && feature_enabled('auto_caption');
    }

    public function transcriptionJobName()
    {
        return config('app.env').'-'.current_account_global_id().'-'.Consumer::user()->slug.'-'.$this->id;
    }

    public function isOrganisationLogo()
    {
        return $this->resource === self::RESOURCE_ORGANISATION_LOGO;
    }

    public function organisation(): HasOne
    {
        return $this->hasOne(Organisation::class, 'logo_id', 'id');
    }

    public function imgixUrlWithConfig(array $config): ?string
    {
        if (empty($this->file)) {
            return null;
        }

        return imgix($this->file, $this->getAttribute('original'), $config);
    }
}
