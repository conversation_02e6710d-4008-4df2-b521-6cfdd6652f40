<?php

namespace AwardForce\Modules\Files\Listeners;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Files\Commands\BroadcastTranscodingStatusUpdate;
use AwardForce\Modules\Files\Commands\CleanupTranscodingCommand;
use AwardForce\Modules\Files\Commands\QueueFileForTranscodingCommand;
use AwardForce\Modules\Files\Commands\TranscodeFilesCommand;
use AwardForce\Modules\Files\Events\FileProcessed;
use AwardForce\Modules\Files\Events\FileWasDeleted;
use AwardForce\Modules\Files\Events\TranscodeStatusWasUpdated;
use AwardForce\Modules\Files\Events\TranscodeWasLogged;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Services\TranscodingStatusMapper;
use AwardForce\Modules\Forms\Forms\Services\FormFileMapper;
use Illuminate\Contracts\Bus\Dispatcher as CommandDispatcher;
use Illuminate\Contracts\Events\Dispatcher as EventDispatcher;
use Illuminate\Translation\Translator;
use Illuminate\View\Factory as ViewFactory;
use Platform\Database\Eloquent\Collection;

class TranscodingListener
{
    /**
     * @var Dispatcher
     */
    private $events;

    /**
     * @var CommandDispatcher
     */
    private $commands;

    /**
     * @var Translator
     */
    private $translator;

    /**
     * @var ViewFactory
     */
    private $view;

    public function __construct(
        CommandDispatcher $commands,
        EventDispatcher $events,
        Translator $translator,
        ViewFactory $view
    ) {
        $this->events = $events;
        $this->commands = $commands;
        $this->translator = $translator;
        $this->view = $view;
    }

    /**
     * When the file is successfully stored (i.e. name updated), this is the point at which
     * we can now queue up any transcoding, should it be a video type file.
     */
    public function whenFileProcessed(FileProcessed $event)
    {
        $this->dispatchTranscoding($event->file);
    }

    /**
     * Whenever a new transcode is logged, we then need to notify our transcoding
     * service of each file that needs to be transcoded.
     */
    public function whenTranscodeWasLogged(TranscodeWasLogged $event)
    {
        foreach ($event->transcode->files as $file) {
            $this->commands->dispatch(new QueueFileForTranscodingCommand($event->transcode, $file));
        }

        // Notify the user
        $this->events->fire(
            new BroadcastTranscodingStatusUpdate($event->transcode->user, $this->translator->get('files.messages.transcoding_queued'), 'info')
        );
    }

    /**
     * Whenever the status is updated, we want to broadcast a message to the user that started the
     * transcode job, letting them know of the status.
     */
    public function whenTranscodeStatusWasUpdated(TranscodeStatusWasUpdated $event)
    {
        // Suppress warning messages until we handle them better, as now they are causing some confusion.
        if ($event->file->transcodingStatus == File::TRANSCODE_WARNING) {
            return;
        }

        CurrentAccount::set($event->file->account);

        $this->events->fire(new BroadcastTranscodingStatusUpdate(
            $event->transcode->user,
            $this->message($event),
            TranscodingStatusMapper::toasterClass($event->file->transcodingStatus),
            $this->data($event)
        ));

        if ($event->file->transcodingStatus == File::TRANSCODE_ERROR) {
            $this->commands->dispatch(new CleanupTranscodingCommand($event->file));
        }
    }

    public function whenFileWasDeleted(FileWasDeleted $event)
    {
        if (! $event->file->isVideo()) {
            return;
        }

        $this->commands->dispatch(new CleanupTranscodingCommand($event->file));
    }

    /**
     * Constructs the message to be sent to the user.
     *
     * @return string
     */
    private function message(TranscodeStatusWasUpdated $event)
    {
        return $this->translator->get(TranscodingStatusMapper::message($event->file->transcodingStatus), [
            'file' => $event->file->original,
            'position' => $event->transcode->position,
            'total' => $event->transcode->total,
        ]);
    }

    /**
     * Forms the data required for the broadcast message.
     *
     * @return array
     */
    private function data(TranscodeStatusWasUpdated $event)
    {
        return [
            'position' => $event->transcode->position,
            'total' => $event->transcode->total,
            'transcodedFiles' => $this->mapTranscodedFiles($event->transcode->files),
        ];
    }

    /**
     * Forms an array with information on transcoded files.
     *
     * @return array
     */
    private function mapTranscodedFiles(Collection $files)
    {
        return $files->take(100)->map(function (File $file) {
            $transcodingPreview = $this->renderTranscodingPreview($file);

            return [
                'id' => $file->id,
                'token' => $file->token,
                'transcodingStatus' => $file->transcodingStatus,
                'transcodingPreview' => $transcodingPreview,
                'transcodingErrors' => $file->transcodingErrors(),
                'image' => imgix($file->transcodeThumbnail(), params: config('ui.images.gallery.small')),
                'source' => cloud_asset_url($file->transcodePlaylist()),
                'videoHeight' => $file->videoHeight(current_account()->videoPlayerHeight),
                'caption' => $file->caption ? app(FormFileMapper::class)->mapFile($file->caption) : null,
            ];
        })->toArray();
    }

    /**
     * Renders a transcoding preview.
     *
     * @param  File  $file
     * @return string
     */
    private function renderTranscodingPreview($file)
    {
        return $this->view->make('entry.common.cards.previews.video', ['file' => $file, 'embeddedContent' => false])->render();
    }

    private function dispatchTranscoding(File $file)
    {
        if (! $file->transcodable()) {
            return;
        }

        $file->update(['transcodingStatus' => File::TRANSCODE_WAITING]);

        return $this->commands->dispatch(new TranscodeFilesCommand($file->user, [$file->id]));
    }
}
