<?php

namespace AwardForce\Modules\Exports\View;

use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\TestWith;
use Platform\Search\Columnator;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class NewExportLayoutTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testColumnators(): void
    {
        $columnators = app(NewExportLayout::class)->columnators();

        $this->assertInstanceOf(Collection::class, $columnators);

        collect($columnators)
            ->each(fn(Columnator $columnator) => $this->assertInstanceOf(Columnator::class, $columnator));
    }

    public function testColumns(): void
    {
        $area = 'manage_entries.export';

        $request = $this->mock(Request::class);
        $request->shouldReceive('query')->andReturn($area);
        $request->shouldReceive('get')->times(8)->andReturnNull();

        $columns = app(NewExportLayout::class)->columns();

        $this->assertIsArray($columns);

        collect($columns)
            ->each(function (array $column) use ($area) {
                $this->assertArrayHasKey('area', $column);
                $this->assertArrayHasKey('allColumns', $column);
                $this->assertArrayHasKey('defaultColumns', $column);

                $this->assertNotNull($currentArea = array_get($column, 'area'));
                $currentArea === $area ? $this->assertNotEmpty(array_get($column, 'allColumns')) : $this->assertEmpty(array_get($column, 'allColumns'));
                $currentArea === $area ? $this->assertNotEmpty(array_get($column, 'defaultColumns')) : $this->assertEmpty(array_get($column, 'defaultColumns'));
            });
    }

    #[TestWith([true, true])]
    #[TestWith([false, false])]
    #[TestWith([null, false])]
    public function testItRemembersIsShared(?bool $old, bool $expected): void
    {
        $request = $this->mock(Request::class);
        $request->shouldReceive('old')
            ->with('shared', false)
            ->once()
            ->andReturn($old);
        $exportLayout = app(NewExportLayout::class);

        $this->assertEquals($expected, $exportLayout->shared());
    }
}
