<?php

use AwardForce\Modules\Organisations\Organisations\Http\Controllers\MyOrganisationController;
use AwardForce\Modules\Organisations\Organisations\Http\Controllers\OrganisationController;
use AwardForce\Modules\Organisations\Organisations\Http\Middleware\IsMine;
use AwardForce\Modules\Organisations\Organisations\Http\Middleware\MyOrganisation;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix' => 'organisation',
    'middleware' => [
        'auth.role',
        'feature:organisations',
    ],
], function () {
    Route::get('/')
        ->name('organisation.index')
        ->uses([OrganisationController::class, 'index']);

    Route::get('/new')
        ->name('organisation.new')
        ->uses([OrganisationController::class, 'new']);

    Route::delete('/')
        ->name('organisation.delete')
        ->uses([OrganisationController::class, 'delete']);

    Route::get('{organisation}/edit')
        ->name('organisation.edit')
        ->uses([OrganisationController::class, 'edit']);

    Route::put('/')
        ->name('organisation.undelete')
        ->uses([OrganisationController::class, 'undelete']);

    Route::post('/')
        ->name('organisation.create')
        ->uses([OrganisationController::class, 'create']);

    Route::get('autocomplete')
        ->name('organisation.autocomplete')
        ->uses([OrganisationController::class, 'autocomplete']);

    Route::get('my')
        ->name('organisation.my')
        ->uses([OrganisationController::class, 'my']);

    Route::get('{organisation}')
        ->name('organisation.show')
        ->uses([OrganisationController::class, 'show']);

    Route::put('/{organisation}/logo')
        ->name('organisation.logo.update')
        ->uses([OrganisationController::class, 'updateLogo']);

    Route::delete('/{organisation}/logo')
        ->name('organisation.logo.delete')
        ->uses([OrganisationController::class, 'deleteLogo']);
});

Route::group([
    'prefix' => 'my-organisation',
    'middleware' => [
        'feature:organisations',
    ],
], function () {

    Route::get('/')
        ->name('my-organisation.index')
        ->middleware(MyOrganisation::class)
        ->uses([MyOrganisationController::class, 'index']);

    Route::get('/{organisation}')
        ->name('my-organisation.show')
        ->middleware(IsMine::class)
        ->uses([MyOrganisationController::class, 'show']);
});
