<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Controllers;

use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationDeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationUndeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Event;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class OrganisationControllerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanDeleteOrganisations(): void
    {
        $controller = app(OrganisationController::class);

        $organisations = Organisation::factory(3)->create();

        $request = new Request([
            'selected' => $organisations->take(2)->pluck('id')->map->toInteger()->toArray(),
        ]);
        Event::fake(OrganisationDeleted::class);
        $controller->delete($request);
        Event::assertDispatchedTimes(OrganisationDeleted::class, 2);

        $results = app(OrganisationProjectionRepository::class)->getAll();
        $this->assertCount(1, $results);
        $this->assertEquals($organisations->last()->id, $results->first()->id);
    }

    public function testItCanUndeleteOrganisations(): void
    {
        $controller = app(OrganisationController::class);

        $organisations = Organisation::factory(3)->create(['deleted_at' => now()]);

        $request = new Request([
            'selected' => $organisations->take(2)->pluck('id')->map->toInteger()->toArray(),
        ]);

        Event::fake(OrganisationUndeleted::class);
        $controller->undelete($request);
        Event::assertDispatchedTimes(OrganisationUndeleted::class, 2);

        $results = app(OrganisationProjectionRepository::class)->getAll();

        $this->assertCount(2, $results);
        $this->assertEqualsCanonicalizing($organisations->take(2)->just('id'), $results->just('id'));
    }

    public function testItCanSearchByName(): void
    {
        $controller = app(OrganisationController::class);

        $organisation1 = Organisation::factory()->create(['name' => 'Test Organisation']);
        $organisation2 = Organisation::factory()->create(['name' => 'Another Organisation']);
        $organisation3 = Organisation::factory()->create(['name' => 'Test Organisation 2']);

        $request = new Request([
            'keywords' => 'test',
        ]);

        $results = $controller->autocomplete($request)->getData();

        $this->assertCount(2, $results);
        $this->assertEquals($organisation1->id->toString(), $results[0]->id);
        $this->assertEquals($organisation1->name, $results[0]->name);
        $this->assertEquals($organisation3->id->toString(), $results[1]->id);
        $this->assertEquals($organisation3->name, $results[1]->name);
    }
}
