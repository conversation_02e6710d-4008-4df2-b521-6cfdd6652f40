<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Views;

use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Files\Services\Uploadable;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Services\OrganisationLogoUploadValidator;
use Illuminate\Http\Request;
use Platform\Html\Tabular\Tab;
use Platform\Html\Tabular\Tabular;
use Platform\View\View;

class ViewOrganisation extends View
{
    use Uploadable;

    public function __construct(
        private OrganisationProjectionRepository $repository,
        private Request $request,
        private OrganisationLogoUploadValidator $validator,
    ) {
        VueData::registerTranslations([
            'buttons.cancel',
            'buttons.done',
            'buttons.delete',
            'buttons.delete_permanently',
            'buttons.edit',
            'buttons.save',
            'files.buttons.drag_and_drop',
            'files.status',
            'files.buttons.single',
            'miscellaneous.alerts.delete.item',
            'miscellaneous.search.or',
            'organisations.form',
            'organisations.table.columns',
            'organisations.titles.organisation',
            'organisations.titles.logo',
            'search.columns.created',
            'search.columns.updated',
            'shared.overview',
        ]);

        VueData::registerRoutes([
            'organisation.delete',
            'organisation.logo.delete',
            'organisation.logo.update',
            'users.show',
            'file.own.delete',
        ]);

    }

    public function organisation(): Organisation
    {
        return translate($this->repository
            ->fields(['id', 'name', 'domains', 'administrator_id', 'season_id', 'created_by', 'created_by_user_id', 'created_at', 'updated_at', 'logo_id'])
            ->with(['administrator', 'season', 'logo'])
            ->primary($this->request->route('organisation'))
            ->require());
    }

    public function uploadOptions(): array
    {
        return $this->validator->setupUploader($this->setupUploader())->setMaxFileSize(current_account()->maxFileSize)->options();
    }

    public function organisationLogo(): array
    {
        if (! $this->organisation->logo) {
            return [];
        }

        return [
            'image' => imgix($this->organisation->logo->file, $this->organisation->logo->original, config('ui.images.organisations.small')),
            'imageLarge' => imgix($this->organisation->logo->file, $this->organisation->logo->original, config('ui.images.organisations.large')),
        ];
    }

    public function tabular(): Tabular
    {
        $tabular = new Tabular($this->request, 'normal');
        $tabular->addTab(new Tab(trans('shared.overview'), 'organisation.tabs.overview', Tab::STATUS_ACTIVE));

        return $tabular;
    }
}
