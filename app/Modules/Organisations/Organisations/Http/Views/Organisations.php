<?php

namespace AwardForce\Modules\Organisations\Organisations\Http\Views;

use AwardForce\Modules\Search\Services\ColumnatorFactory;
use Illuminate\Http\Request;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;

class Organisations extends View
{
    public function __construct(
        private Request $request,
        private ColumnatorFactory $columnators,
    ) {
    }

    public function organisations()
    {
        $search = new ColumnatorSearch($this->columnator);

        return translate($search->search());
    }

    public function columnator()
    {
        return $this->columnators->forArea($this->area(), $this->request->all());
    }

    public function area()
    {
        return 'organisations.search';
    }

    public function organisationsIds(): array
    {
        return $this->organisations->pluck('id')->map->toString()->toArray();
    }
}
