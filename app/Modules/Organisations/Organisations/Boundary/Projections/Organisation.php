<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Projections;

use AwardForce\Library\Database\Eloquent\Model;
use AwardForce\Library\EventSourcing\Metadata;
use AwardForce\Library\EventSourcing\ProjectionLifecycle;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Traits\BelongsToAccount;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\HasFlatValues;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ValuesProvider;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Members\Boundary\Projections\Member;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Created;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Deleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoAdded;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoDeleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoReplaced;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Undeleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Updated;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Platform\Search\HasValues;

/**
 * AwardForce\Modules\Organisations\Models\Organisation
 *
 * @property OrganisationId $id
 * @property int $accountId
 * @property int|null $administratorId
 * @property array $domains
 * @property string $name
 * @property CreatedBy $createdBy
 * @property int|null $createdByUserId
 * @property int|null $seasonId
 * @property \Carbon\Carbon $createdAt
 * @property \Carbon\Carbon $updatedAt
 * @property-read Account $account
 * @property-read User $administrator
 * @property-read User $createdByUser
 * @property-read Season $season
 * @property-read File $logo
 */
class Organisation extends Model implements HasValues, OrganisationProjection, ValuesProvider
{
    use BelongsToAccount;
    use HasFactory;
    use HasFlatValues;
    use ProjectionLifecycle;
    use SoftDeletes;

    public $incrementing = false;
    protected $fillable = [
        'id',
        'accountId',
        'administratorId',
        'domains',
        'name',
        'createdBy',
        'createdByUserId',
        'seasonId',
    ];
    protected $casts = [
        'id' => OrganisationId::class,
        'created_by' => CreatedBy::class,
        'values' => 'array',
        'hashes' => 'array',
        'protected' => 'array',
        'domains' => 'array',
    ];

    /**
     * Category belong to an Account.
     */
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function administrator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'administrator_id');
    }

    public function members(): HasMany
    {
        return $this->hasMany(Member::class, 'organisation_id');
    }

    public function createdByUser(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function season(): BelongsTo
    {
        return $this->belongsTo(Season::class);
    }

    public function logo(): BelongsTo
    {
        return $this->belongsTo(File::class, 'logo_id');
    }

    public function hasAdministrator(): bool
    {
        return $this->administrator()->exists();
    }

    public function applyCreated(Created $event, Metadata $metadata): void
    {
        $this->name = $event->name;
        $this->accountId = current_account_id();
        $this->administratorId = $event->administratorId;
        $this->domains = $event->domains;
        $this->version = $metadata->version;
        $this->createdBy = $event->createdBy;
        $this->createdByUserId = $event->createdByUserId;
        $this->seasonId = $event->seasonId;
    }

    public function applyUpdated(Updated $event, Metadata $metadata): void
    {
        $this->name = $event->name;
        $this->administratorId = $event->administratorId;
        $this->domains = $event->domains;
        $this->version = $metadata->version;
    }

    public static function repository(): OrganisationProjectionRepository
    {
        return app(OrganisationProjectionRepository::class);
    }

    public function newCollection(array $models = []): Organisations
    {
        return new Organisations($models);
    }

    public function applyDeleted(Deleted $event, Metadata $metadata): void
    {
        $this->deletedAt = now();
    }

    public function applyUndeleted(Undeleted $event, Metadata $metadata): void
    {
        $this->deletedAt = null;
    }

    public function applyLogoAdded(LogoAdded $event, Metadata $metadata): void
    {
        $this->version = $metadata->version;
        $this->logoId = $event->logoId;
    }

    public function applyLogoDeleted(LogoDeleted $event, Metadata $metadata): void
    {
        $this->version = $metadata->version;
        $this->logoId = null;
    }

    public function applyLogoReplaced(LogoReplaced $event, Metadata $metadata): void
    {
        $this->version = $metadata->version;
        $this->logoId = $event->logoId;
    }

    public function toVue(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'domains' => $this->domains ?? [],
            'administrator' => [
                'slug' => (string) $this->administrator?->slug,
                'fullName' => $this->administrator?->fullName(),
                'email' => $this->administrator?->email,
            ],
            'season' => [
                'slug' => (string) $this->season?->slug,
                'name' => $this->season?->name,
            ],
            'createdBy' => $this->createdBy?->value,
            'createdByUser' => [
                'slug' => (string) $this->createdByUser?->slug,
                'fullName' => $this->createdByUser?->fullName(),
            ],
            'createdAt' => $this->createdAt?->toIso8601String(),
            'updatedAt' => $this->updatedAt?->toIso8601String(),
            'logo' => array_filter([
                'id' => $this->logo?->id,
                'image' => $this->logo?->imgixUrlWithConfig(config('ui.images.organisations.small')),
                'imageLarge' => $this->logo?->imgixUrlWithConfig(config('ui.images.organisations.large')),
            ]),
        ];
    }

    public function mine(): bool
    {
        return $this->administratorId && $this->administratorId === consumer_id();
    }
}
