<?php

namespace AwardForce\Modules\Organisations\Organisations\Boundary\Projections;

use AwardForce\Library\EventSourcing\Metadata;
use AwardForce\Library\EventSourcing\Projection;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Created;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Deleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoAdded;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoDeleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoReplaced;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Undeleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Updated;

interface OrganisationProjection extends Projection
{
    public function applyCreated(Created $event, Metadata $metadata): void;

    public function applyUpdated(Updated $event, Metadata $metadata): void;

    public function applyDeleted(Deleted $event, Metadata $metadata): void;

    public function applyUndeleted(Undeleted $event, Metadata $metadata): void;

    public function applyLogoAdded(LogoAdded $event, Metadata $metadata): void;

    public function applyLogoDeleted(LogoDeleted $event, Metadata $metadata): void;

    public function applyLogoReplaced(LogoReplaced $event, Metadata $metadata): void;
}
