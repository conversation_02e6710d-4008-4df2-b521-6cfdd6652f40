<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain;

use AwardForce\Library\EventSourcing\HasSnapshots;
use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Forms\FieldValues\FieldValues;
use AwardForce\Modules\Forms\Formables\Boundary\FormableAggregate;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Created;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Deleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoAdded;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoDeleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoReplaced;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Undeleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Updated;
use EventSauce\EventSourcing\AggregateRootId;
use EventSauce\EventSourcing\AggregateRootWithAggregates;
use EventSauce\EventSourcing\Snapshotting\SnapshottingBehaviour;

class Organisation implements FormableAggregate
{
    use AggregateRootWithAggregates;
    use HasSnapshots;
    use SnapshottingBehaviour;

    private FieldValues $fieldValues;
    private string $name;
    private ?int $administratorId;
    private array $domains;
    private CreatedBy $createdBy;
    private ?int $createdByUserId;
    private ?int $logoId = null;

    public static function new(): static
    {
        return static::createNewInstance(OrganisationId::create());
    }

    public function create(string $name, ?int $administratorId, array $domains, CreatedBy $createdBy, ?int $createdByUserId = null): void
    {
        $this->initiateFieldValues();
        $createdByUserId = $createdBy === CreatedBy::User ? $createdByUserId : null;
        $seasonId = current_account()->activeSeason()->id;
        $this->recordThat(new Created($name, $administratorId, $domains, $seasonId, $createdBy, $createdByUserId));
    }

    public function update(string $name, ?int $administratorId, array $domains): void
    {
        if (! isset($this->fieldValues)) {
            $this->initiateFieldValues();
        }
        $this->recordThat(new Updated($name, $administratorId, $domains));
    }

    public function delete(): void
    {
        $this->recordThat(new Deleted);
    }

    public function undelete(): void
    {
        $this->recordThat(new Undeleted);
    }

    public function createFieldValues(FieldValuesCollection $fieldValues): void
    {
        $this->fieldValues->createFieldValues($fieldValues);
    }

    public function updateFieldValues(FieldValuesCollection $fieldValues): void
    {
        $this->fieldValues->updateFieldValues($fieldValues);
    }

    public function addLogo(int $logoId): void
    {
        $this->recordThat(new LogoAdded($logoId));
    }

    public function deleteLogo()
    {
        $this->recordThat(new LogoDeleted);
    }

    public function replaceLogo(int $logoId)
    {
        $this->recordThat(new LogoReplaced($logoId));
    }

    public function updateLogo(int $logoId): void
    {
        if ($this->logoId) {
            $this->replaceLogo($logoId);
        } else {
            $this->addLogo($logoId);
        }
    }

    public function logoId(): ?int
    {
        return $this->logoId;
    }

    protected function applyCreated(Created $event): void
    {
        $this->name = $event->name;
        $this->administratorId = $event->administratorId;
        $this->domains = $event->domains;
        $this->createdBy = $event->createdBy;
        $this->createdByUserId = $event->createdByUserId;
        $this->fieldValues = $this->fieldValues ?? new FieldValues($this->eventRecorder());
        $this->registerAggregate($this->fieldValues);
    }

    protected function applyUpdated(Updated $event): void
    {
        $this->name = $event->name;
        $this->administratorId = $event->administratorId;
        $this->domains = $event->domains;
    }

    protected function applyLogoAdded(LogoAdded $event): void
    {
        $this->logoId = $event->logoId;
    }

    protected function applyLogoDeleted(LogoDeleted $event): void
    {
        $this->logoId = null;
    }

    protected function applyLogoReplaced(LogoReplaced $event): void
    {
        $this->logoId = $event->logoId;
    }

    public function toArray(): array
    {
        return $this->createSnapshotState();
    }

    protected function createSnapshotState(): mixed
    {
        return [
            'name' => $this->name,
            'fieldValues' => $this->fieldValues->toArray(),
            'logoId' => $this->logoId,
        ];
    }

    protected static function reconstituteFromSnapshotState(AggregateRootId $id, $state): static
    {
        $aggregate = new self($id);
        $aggregate->name = $state['name'];

        $aggregate->fieldValues = new FieldValues($aggregate->eventRecorder());
        $aggregate->registerAggregate($aggregate->fieldValues);
        $aggregate->fieldValues->fieldValuesFromArray($state['fieldValues']);

        return $aggregate;
    }

    private function initiateFieldValues(): void
    {
        $this->fieldValues = new FieldValues($this->eventRecorder());
    }
}
