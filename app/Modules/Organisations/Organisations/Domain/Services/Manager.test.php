<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Services;

use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\AdministratorAssigned;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationCreated;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationLogoDeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationLogoUpdated;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Bus\CreateOrganisation;
use AwardForce\Modules\Organisations\Organisations\Domain\Organisation;
use Faker\Factory;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class ManagerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private OrganisationProjectionRepository $organisationRepository;
    private Manager $manager;

    public function init()
    {
        $this->organisationRepository = app(OrganisationProjectionRepository::class);
        $this->manager = app(Manager::class);
    }

    public function testCreateOrganisation(): void
    {
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $command = new CreateOrganisation(
            $organisationName,
            null,
            [],
            []
        );

        $this->manager->create($organisationName, null, [], new FieldValuesCollection, CreatedBy::System);

        $organisation = $this->organisationRepository->getByName($organisationName)->first();

        $this->assertEquals($organisationName, $organisation->name);
        $this->assertEmpty($organisation->values);
        $this->assertEmpty($organisation->domains);
        $this->assertNull($organisation->administratorId);
    }

    public function testCreateOrganisationWithAdministrator(): void
    {
        $administrator = $this->muffin(User::class);
        $organisationName = 'Test Organisation - '.Factory::create()->word;

        $this->manager->create($organisationName, $administrator->id, [], new FieldValuesCollection, CreatedBy::System);

        $organisation = $this->organisationRepository->getByName($organisationName)->first();

        $this->assertEquals($administrator->id, $organisation->administratorId);

        $this->assertEquals($administrator->fullName(), $organisation->administrator->fullName());

        $events = $this->manager->releaseEvents();
        $this->assertCount(2, $events);
        $this->assertInstanceOf(OrganisationCreated::class, $events[0]);
        $this->assertInstanceOf(AdministratorAssigned::class, $events[1]);
        $this->assertEquals($organisation->id, $events[1]->organisationId);
        $this->assertEquals($administrator->id, $events[1]->administratorId);
    }

    public function testCreateOrganisationWithDomains()
    {
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $allowedDomains = ['domainOne.com', 'domainTwo.com'];

        $this->manager->create($organisationName, null, $allowedDomains, new FieldValuesCollection, CreatedBy::System);

        $organisation = $this->organisationRepository->getByName($organisationName)->first();

        $this->assertEquals($allowedDomains, $organisation->domains);
    }

    public function testOrganisationCreatedRaised()
    {
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $this->manager->create($organisationName, null, [], new FieldValuesCollection, CreatedBy::System);
        $raisedEvents = $this->manager->releaseEvents();

        $this->assertCount(1, $raisedEvents);
        $this->assertInstanceOf(OrganisationCreated::class, $raisedEvents[0]);
    }

    public function testOrganisationLogoDeletedRaised()
    {
        $logo = $this->muffin(File::class, ['resource' => File::RESOURCE_ORGANISATION_LOGO]);
        $organisationAggregate = Organisation::new();
        $organisationAggregate->create('SomeName', null, [], CreatedBy::System);
        $organisationAggregate->addLogo($logo->id);
        app(OrganisationRepository::class)->save($organisationAggregate);

        $organisation = $this->organisationRepository->getByName('SomeName')->first();
        $this->manager->deleteLogo($organisation->id);
        $raisedEvents = $this->manager->releaseEvents();

        $this->assertCount(1, $raisedEvents);
        $this->assertInstanceOf(OrganisationLogoDeleted::class, $raisedEvents[0]);
        $this->assertEquals($logo->id, $raisedEvents[0]->logoId);
    }

    public function testOrgansiationLogoUpdatedRaisedWithFileWhenOrganisationAlreadyHasLogo()
    {
        $logo = $this->muffin(File::class, ['resource' => File::RESOURCE_ORGANISATION_LOGO]);
        $newLogo = $this->muffin(File::class, ['resource' => File::RESOURCE_ORGANISATION_LOGO]);
        $organisationAggregate = Organisation::new();
        $organisationAggregate->create('SomeName', null, [], CreatedBy::System);
        $organisationAggregate->addLogo($logo->id);
        app(OrganisationRepository::class)->save($organisationAggregate);
        $organisation = $this->organisationRepository->getByName('SomeName')->first();
        $this->assertNotNull($organisation->logoId);

        $this->manager->updateLogo($organisation->id, $newLogo->id);
        $raisedEvents = $this->manager->releaseEvents();
        $organisation = $this->organisationRepository->getByName('SomeName')->first();

        $this->assertEquals($newLogo->id, $organisation->logoId);
        $this->assertCount(1, $raisedEvents);
        $this->assertInstanceOf(OrganisationLogoUpdated::class, $raisedEvents[0]);
        $this->assertEquals($logo->id, $raisedEvents[0]->oldLogoId);
    }

    public function testOrgansiationLogoUpdatedRaisedWithNullFileWhenOrganisationDoesNotHaveLogo()
    {
        $logo = $this->muffin(File::class, ['resource' => File::RESOURCE_ORGANISATION_LOGO]);
        $organisationAggregate = Organisation::new();
        $organisationAggregate->create('SomeName', null, [], CreatedBy::System);
        app(OrganisationRepository::class)->save($organisationAggregate);

        $organisation = $this->organisationRepository->getByName('SomeName')->first();
        $this->assertNull($organisation->logoId);

        $this->manager->updateLogo($organisation->id, $logo->id);
        $raisedEvents = $this->manager->releaseEvents();
        $organisation = $this->organisationRepository->getByName('SomeName')->first();

        $this->assertEquals($logo->id, $organisation->logoId);

        $this->assertInstanceOf(OrganisationLogoUpdated::class, $raisedEvents[0]);
        $this->assertNull($raisedEvents[0]->oldLogoId);
    }
}
