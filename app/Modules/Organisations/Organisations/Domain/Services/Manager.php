<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Services;

use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\AdministratorAssigned;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationCreated;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationDeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationLogoDeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationLogoUpdated;
use AwardForce\Modules\Organisations\Organisations\Boundary\Events\OrganisationUndeleted;
use AwardForce\Modules\Organisations\Organisations\Boundary\OrganisationId;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;
use Platform\Events\Raiseable;

class Manager
{
    use Raiseable;

    public function __construct(private OrganisationRepository $organisations)
    {
    }

    public function create(string $name, ?int $administratorId, array $domains, FieldValuesCollection $fieldValues, CreatedBy $createdBy, ?int $createdByUserId = null): void
    {
        $aggregateRootId = $this->organisations->transaction(function (OrganisationRepository $repository) use ($name, $administratorId, $domains, $fieldValues, $createdBy, $createdByUserId) {
            // Create a formable aggregate
            $aggregate = $repository->new();
            $aggregate->create($name, $administratorId, $domains, $createdBy, $createdByUserId);
            // Create field values
            $aggregate->createFieldValues($fieldValues);
            // Persist and release sourced events
            $repository->save($aggregate);

            return $aggregate->aggregateRootId();
        });

        // Raise the domain event
        $this->raise(new OrganisationCreated(
            $aggregateRootId,
            $name,
            $administratorId,
            $domains,
            $fieldValues
        ));

        // Raise the administrator assigned event
        if ($administratorId) {
            $this->raise(new AdministratorAssigned(
                $aggregateRootId,
                $administratorId
            ));
        }
    }

    public function delete(OrganisationId $organisationId): void
    {
        $this->organisations->transaction(function (OrganisationRepository $repository) use ($organisationId) {
            $aggregate = $repository->retrieve($organisationId);
            $aggregate->delete();
            $repository->save($aggregate);
        });

        // Raise the domain event
        $this->raise(new OrganisationDeleted($organisationId));
    }

    public function undelete(OrganisationId $organisationId): void
    {
        $this->organisations->transaction(function (OrganisationRepository $repository) use ($organisationId) {
            $aggregate = $repository->retrieve($organisationId);
            $aggregate->undelete();
            $repository->save($aggregate);
        });

        // Raise the domain event
        $this->raise(new OrganisationUndeleted($organisationId));
    }

    public function deleteLogo(OrganisationId $organisationId)
    {
        $oldLogoId = $this->organisations->transaction(function (OrganisationRepository $repository) use ($organisationId) {
            $aggregate = $this->organisations->retrieve($organisationId);
            $oldLogoId = $aggregate->logoId();
            $aggregate->deleteLogo();

            $repository->save($aggregate);

            return $oldLogoId;
        });

        // Raise the domain event
        $this->raise(new OrganisationLogoDeleted($organisationId, $oldLogoId));
    }

    public function updateLogo(OrganisationId $organisationId, int $logoId): void
    {
        $oldLogoId = $this->organisations->transaction(function (OrganisationRepository $repository) use ($organisationId, $logoId) {
            $aggregate = $this->organisations->retrieve($organisationId);
            $oldLogoId = $aggregate->logoId();

            $aggregate->updateLogo($logoId);
            $repository->save($aggregate);

            return $oldLogoId;
        });

        $this->raise(new OrganisationLogoUpdated($organisationId, $logoId, $oldLogoId));
    }
}
