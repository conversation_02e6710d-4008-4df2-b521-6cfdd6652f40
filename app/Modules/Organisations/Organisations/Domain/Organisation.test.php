<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain;

use AwardForce\Modules\Forms\FieldValues\Boundary\FieldValuesCollection;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\CreatedBy;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Deleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoAdded;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoDeleted;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\LogoReplaced;
use AwardForce\Modules\Organisations\Organisations\Domain\Events\Undeleted;
use Eloquence\Behaviours\Slug;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class OrganisationTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private OrganisationRepository $repository;

    public function init(): void
    {
        $this->repository = app(OrganisationRepository::class);
    }

    public function testCreatesOrganisationAggregateSuccessfully()
    {
        $name = 'Test Organisation';
        $administratorId = 1;
        $organisation = Organisation::new();
        $organisation->create($name, $administratorId, [], CreatedBy::System);

        $this->assertInstanceOf(Organisation::class, $organisation);
        $this->assertEquals($name, $organisation->toArray()['name']);
    }

    public function testAppliesOrganisationCreatedEvent()
    {
        $name = 'Test Organisation';
        $administratorId = 1;
        $organisation = Organisation::new();
        $organisation->create($name, $administratorId, [], CreatedBy::System);

        $this->assertEquals($name, $organisation->toArray()['name']);
        $this->assertArrayHasKey('fieldValues', $organisation->toArray());
    }

    public function testCreatesFieldValuesSuccessfully()
    {
        $name = 'Test Organisation';
        $administratorId = 1;
        $organisation = Organisation::new();
        $organisation->create($name, $administratorId, [], CreatedBy::System);
        $fieldValues = FieldValuesCollection::fromArray([
            [
                'key' => (string) Slug::fromId(1),
                'value' => 'value1',
            ],
            [
                'key' => (string) Slug::fromId(2),
                'value' => 'value2',
            ],
        ]);
        $organisation->createFieldValues($fieldValues);

        $this->assertNotEmpty($organisation->toArray()['fieldValues']);
        $this->assertEqualsCanonicalizing(
            $fieldValues->toArray(),
            $organisation->toArray()['fieldValues']
        );
    }

    public function testReconstitutesFromSnapshotState()
    {
        $name = 'Test Organisation';
        $administratorId = $this->muffin(User::class)->id;
        $organisation = Organisation::new();
        $organisation->create($name, $administratorId, [], CreatedBy::System);
        $fieldValues = FieldValuesCollection::fromArray([
            [
                'key' => (string) Slug::fromId(1),
                'value' => 'value1',
            ],
            [
                'key' => (string) Slug::fromId(2),
                'value' => 'value2',
            ],
        ]);
        $organisation->createFieldValues($fieldValues);
        $this->repository->save($organisation);

        $organisation = $this->repository->retrieveFromSnapshot($organisation->aggregateRootId());

        $this->assertInstanceOf(Organisation::class, $organisation);
        $this->assertEquals('Test Organisation', $organisation->toArray()['name']);
        $this->assertEqualsCanonicalizing(
            $fieldValues->toArray(),
            $organisation->toArray()['fieldValues']
        );
    }

    public function testItRecordsDeletedEvent(): void
    {
        $name = 'Test Organisation';
        $administratorId = 1;
        $organisation = Organisation::new();
        $organisation->create($name, $administratorId, [], CreatedBy::System);
        $organisation->delete();

        $this->assertContainsInstanceOf(Deleted::class, $organisation->recordedEvents());
    }

    public function testItRecordsUndeletedEvent(): void
    {
        $name = 'Test Organisation';
        $administratorId = 1;
        $organisation = Organisation::new();
        $organisation->create($name, $administratorId, [], CreatedBy::System);
        $organisation->undelete();

        $this->assertContainsInstanceOf(Undeleted::class, $organisation->recordedEvents());
    }

    public function testItRecordsLogoAddedEvent()
    {
        $organisation = Organisation::new();

        $organisation->addLogo(1);

        $this->assertContainsInstanceOf(LogoAdded::class, $organisation->recordedEvents());
    }

    public function testItRecordsLogoDeletedEvent()
    {
        $organisation = Organisation::new();

        $organisation->deleteLogo();

        $this->assertContainsInstanceOf(LogoDeleted::class, $organisation->recordedEvents());
    }

    public function testItRecordsLogoReplacedEvent()
    {
        $organisation = Organisation::new();

        $organisation->replaceLogo(2);

        $this->assertContainsInstanceOf(LogoReplaced::class, $organisation->recordedEvents());
    }
}
