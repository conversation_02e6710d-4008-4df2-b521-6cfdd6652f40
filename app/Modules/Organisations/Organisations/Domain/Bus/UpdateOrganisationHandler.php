<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Forms\FieldValues\RequestToFieldValues;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationRepository;
use Platform\Events\EventDispatcher;

class UpdateOrganisationHandler
{
    use EventDispatcher;

    public function __construct(private OrganisationRepository $organisations, private RequestToFieldValues $requestToFieldValues)
    {
    }

    public function handle(UpdateOrganisation $command): void
    {
        $this->organisations->transaction(function (OrganisationRepository $repository) use ($command) {
            $aggregate = $this->organisations->retrieve($command->id);

            $aggregate->update($command->name, $command->administratorId, $command->domains);

            $aggregate->updateFieldValues($this->requestToFieldValues->fromRequest($command->values));

            $repository->save($aggregate);
        });

    }
}
