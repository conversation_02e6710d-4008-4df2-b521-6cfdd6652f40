<?php

namespace AwardForce\Modules\Organisations\Organisations\Domain\Bus;

use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use Faker\Factory;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class UpdateOrganisationTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private OrganisationProjectionRepository $organisationRepository;
    private UserRepository $userRepository;
    private ValuesService $valuesService;
    private UpdateOrganisationHandler $handler;

    public function init()
    {
        $this->organisationRepository = app(OrganisationProjectionRepository::class);
        $this->userRepository = app(UserRepository::class);
        $this->valuesService = app(ValuesService::class);
        $this->handler = app(UpdateOrganisationHandler::class);
    }

    public function testUpdateOrganisationName()
    {
        $oldName = 'Old Name';
        $newName = 'New Name';
        $organisation = Organisation::factory()->create(['name' => $oldName]);

        $command = new UpdateOrganisation($organisation->id, $newName, null, [], []);

        $this->handler->handle($command);

        $organisationUpdated = $this->organisationRepository->getById($organisation->id);

        $this->assertNotEquals($oldName, $organisationUpdated->name);
        $this->assertEquals($newName, $organisationUpdated->name);
    }

    public function testUpdateOrganisationAddAdministrator()
    {
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $organisation = Organisation::factory()->create(['name' => $organisationName, 'administrator_id' => null]);
        $administrator = $this->muffin(User::class);
        $command = new UpdateOrganisation($organisation->id, $organisationName, $administrator->id, [], []);

        $this->handler->handle($command);

        $organisation = $organisation->fresh();

        $this->assertEquals($organisationName, $organisation->name);
        $this->assertEquals($administrator->id, $organisation->administratorId);
        $this->assertEquals($administrator->fullName(), $organisation->administrator->fullName());
    }

    public function testUpdateOrganisationChangeDomains()
    {
        $organisationName = 'Test Organisation - '.Factory::create()->word;
        $oldDomainList = ['domain1.com', 'domain2.com'];
        $organisation = Organisation::factory()->create(['name' => $organisationName, 'administrator_id' => null, 'domains' => $oldDomainList]);
        $newDomainList = ['domain3.com', 'domain4.com'];
        $command = new UpdateOrganisation($organisation->id, $organisationName, null, $newDomainList, []);

        $this->handler->handle($command);

        $organisationUpdated = $this->organisationRepository->getById($organisation->id);

        $this->assertEquals($newDomainList, $organisationUpdated->domains);
    }

    public function testOrganisationWasUpdatedDispatched()
    {
        $organisation = Organisation::factory()->create();

        $command = new UpdateOrganisation($organisation->id, $organisation->name, null, [], []);

        $this->handler->handle($command);
    }

    //    public function testItUpdatesFieldValues(): void
    //    {
    //        $fields = $this->muffins(2, Field::class, ['resource' => Field::RESOURCE_ORGANISATIONS, 'type' => 'text']);
    //        $organisation = Organisation::factory()->create(['values' => [(string) $fields[0]->slug => 'valueOne', (string) $fields[1]->slug => 'valueTwo']]);
    //        $fieldValues = [
    //            (string) $fields[0]->slug => 'valueThree',
    //            (string) $fields[1]->slug => 'valueFour',
    //        ];
    //
    //        $command = new UpdateOrganisation(
    //            (string) $organisation->slug,
    //            $organisation->name,
    //            $organisation->administratorId,
    //            [],
    //            $fieldValues
    //        );
    //
    //        $this->handler->handle($command);
    //
    //        $this->assertEquals($fieldValues, $organisation->fresh()->values);
    //    }
}
