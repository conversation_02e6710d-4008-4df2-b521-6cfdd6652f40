<?php

namespace AwardForce\Modules\Organisations\Organisations\Search;

use AwardForce\Library\Search\Actions\DeleteAction;
use AwardForce\Library\Search\Actions\ViewAggregateAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\SnowflakeReactiveMarker;
use AwardForce\Modules\Organisations\Organisations\Database\Repositories\OrganisationProjectionRepository;
use AwardForce\Modules\Organisations\Organisations\Search\Columns\Administrator;
use AwardForce\Modules\Organisations\Organisations\Search\Columns\EntryCount;
use AwardForce\Modules\Organisations\Organisations\Search\Columns\Name;
use AwardForce\Modules\Organisations\Organisations\Search\Columns\Thumbnail;
use AwardForce\Modules\Organisations\Organisations\Search\Columns\UserCount;
use AwardForce\Modules\Organisations\Organisations\Search\Filters\AdministratorJoinFilter;
use AwardForce\Modules\Organisations\Organisations\Search\Filters\OrganisationKeywordFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Slug;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TrashedFilter;

class OrganisationColumnator extends Columnator
{
    public function resource()
    {
        return 'Organisations';
    }

    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;
        $columns = $this->columns($view);
        $dependencies->add((new ColumnFilter(...$columns))->with('organisations.id', 'logo_id'));

        $dependencies->add(new TrashedFilter($this->input));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new AdministratorJoinFilter);
        $dependencies->add(OrganisationKeywordFilter::fromKeywords($this->input['keywords'] ?? '', ['organisations.name', 'users.first_name', 'users.last_name']));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view), $this->input, 'organisations.updated')->uniqueColumn('organisations.id'));

        return $dependencies;
    }

    protected function baseColumns()
    {
        return new Columns([
            new SnowflakeReactiveMarker,
            $this->actionOverflow(),
            new Thumbnail('organisation.show'),
            new Name,
            new UserCount,
            //            new EntryCount,
            new Administrator,
            Slug::forResource('organisations'),
            Updated::forResource('organisations', consumer()->dateLocale()),
            Created::forResource('organisations', consumer()->dateLocale()),
        ]);
    }

    public static function key(): string
    {
        return 'organisations.search';
    }

    public static function exportKey(): string
    {
        return 'organisations.export';
    }

    public function repository(): Repository
    {
        return app(OrganisationProjectionRepository::class);
    }

    private function actionOverflow(): ActionOverflow
    {
        return (new ActionOverflow($this->key()))
            ->addAction(new ViewAggregateAction('organisation', $this->resource()))
            ->addAction(new DeleteAction('organisation', $this->resource(), 'id'));
    }
}
