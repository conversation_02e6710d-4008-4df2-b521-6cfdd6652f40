<?php

namespace AwardForce\Modules\AIAgents;

use AwardForce\Modules\AIAgents\Boundary\AIAgent as AIAgentInterface;
use AwardForce\Modules\AIAgents\Domain\Services\AIAgent;
use Illuminate\Contracts\Support\DeferrableProvider;
use Illuminate\Support\ServiceProvider;

class AIAgentServiceProvider extends ServiceProvider implements DeferrableProvider
{
    public function register(): void
    {
        $this->app->bind(AIAgentInterface::class, AIAgent::class);
    }

    public function provides(): array
    {
        return [AIAgentInterface::class];
    }
}
