<?php

namespace AwardForce\Modules\AIAgents\Boundary;

use AwardForce\Library\AIAgents\ValueObjects\Prompt;
use Platform\Database\Eloquent\Model;

interface AIAgent
{
    public function promptContext(Resource $resource, array $requiredContexts): PromptContext;

    public function generateText(Resource $resource, Prompt $prompt, array $metaData = []): string;

    public function model(Resource $resource): Model;

    /**
     * @return FieldTrigger[]
     */
    public function triggers(ResourceType $resourceType): array;

    /**
     * @return FieldContext[]
     */
    public function contexts(ResourceType $resourceType): array;
}
