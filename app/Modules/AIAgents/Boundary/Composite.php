<?php

namespace AwardForce\Modules\AIAgents\Boundary;

use Platform\Database\Eloquent\Model;

/**
 * Defines the contract for a Composite strategy.
 */
interface Composite
{
    /**
     * Generates a metadata array for a given resource ID.
     * This data is passed to `Context` handlers and should contain
     * any necessary identifiers (e.g., ['entry_id' => 123]).
     */
    public function metadata(int $resourceId): array;

    public function supports(ResourceType $resourceType): bool;

    /**
     * Finds and returns the underlying Eloquent model for the resource.
     * TODO: clean this up once the `ValueService` is refactored.
     */
    public function model(int $resourceId): Model;

    /**
     * @return FieldTrigger[]
     */
    public function triggers(): array;

    /**
     * @return FieldContext[]
     */
    public function contexts(): array;
}
