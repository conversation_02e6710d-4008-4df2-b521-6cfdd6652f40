<?php

namespace AwardForce\Modules\Agreements;

use AwardForce\Library\ServiceProvider;
use AwardForce\Modules\Agreements\Models\AgreementRepository;
use AwardForce\Modules\Agreements\Models\EloquentAgreementRepository;
use AwardForce\Modules\Agreements\Services\CookieConsent;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Blade;

class AgreementsServiceProvider extends ServiceProvider
{
    /**
     * @var bool
     */
    public $defer = true;

    /**
     * The repository bindings for the Agreements module.
     *
     * @var array
     */
    protected $repositories = [
        AgreementRepository::class => EloquentAgreementRepository::class,
    ];

    public function register()
    {
        parent::register();

        $this->registerCookieConsent();
    }

    public function boot()
    {
        parent::boot();

        $this->bootBladeExtensions();
    }

    private function registerCookieConsent()
    {
        $this->app->scoped('cookieconsent', function ($app) {
            return new CookieConsent($app->make(SettingRepository::class), $app->make(ContentBlockRepository::class), $app->make(Request::class));
        });
    }

    private function bootBladeExtensions()
    {
        Blade::if('cookieconsent', function (?string $cookieType) {
            if (! $cookieType) {
                $cookieType = 'necessary';
            }

            return $this->app['cookieconsent']->given($cookieType);
        });
    }
}
