<?php

namespace AwardForce\Modules\Billing\Gateways;

use AwardForce\Modules\Billing\Contracts\UsageBilling;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use RuntimeException;

class ChargebeeUsageBilling implements UsageBilling
{
    public function __construct(
        private readonly ChargebeeClientFactory $clientFactory,
    ) {
    }

    public function createPayload(UsageLog $usageLog): array
    {
        if (! ($subscriptionId = $usageLog->account?->subscriptionId)) {
            throw new RuntimeException("Account {$usageLog->accountId} does not have a subscription ID.");
        }

        return [
            'deduplication_id' => $usageLog->getID(),
            'subscription_id' => $subscriptionId,
            'usage_timestamp' => $usageLog->createdAt,
            'properties' => array_merge(
                [
                    'event_id' => $usageLog->getID(),
                    'event' => $usageLog->event,
                ],
                $this->toSnakeCase($usageLog->metrics),
                $this->toSnakeCase($usageLog->metadata),
            ),
        ];
    }

    private function toSnakeCase(array $data): array
    {
        return Arr::mapWithKeys($data, static fn($value, $key) => [Str::snake($key) => $value]);
    }

    public function ingestBatch(array $eventsPayload, string $subscriptionProvider): BatchIngestionResult
    {
        if (empty($eventsPayload)) {
            return new BatchIngestionResult([], []);
        }

        $result = $this->clientFactory->make($subscriptionProvider)
            ->usageEvent()
            ->batchIngest(['events' => $eventsPayload]);

        $allSentIds = array_column($eventsPayload, 'deduplication_id');
        $failedIds = array_map(static fn(array $event) => array_get($event, 'properties.event_id'), $result->failed_events ?? []);
        $succeededIds = array_values(array_diff($allSentIds, $failedIds));

        return new BatchIngestionResult($succeededIds, $failedIds);
    }

    public function maxEventsPerBatch(): int
    {
        return 500;
    }

    public function maxBatchSizeBytes(): int
    {
        return 490 * 1024; // A maximum of 500 KB of data is allowed. But we'll do 490 KB max to be safe.
    }
}
