<?php

namespace AwardForce\Modules\Billing\Gateways;

use Chargebee\ChargebeeClient;
use Exception;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class ChargebeeClientFactoryTest extends BaseTestCase
{
    use Laravel;

    public function testCreatesChargebeeClientWithValidConfiguration(): void
    {
        $subscriptionProvider = 'provider1';
        config()->set("payment-subscriptions.gateways.chargebee.$subscriptionProvider.site", 'test-site');
        config()->set("payment-subscriptions.gateways.chargebee.$subscriptionProvider.full-access-key", 'test-key');

        $factory = new ChargebeeClientFactory();
        $client = $factory->make($subscriptionProvider);

        $this->assertInstanceOf(ChargebeeClient::class, $client);
    }

    public function testThrowsExceptionWhenConfigurationIsMissing(): void
    {
        $subscriptionProvider = 'provider2';
        config()->set("payment-subscriptions.gateways.chargebee.$subscriptionProvider.site", null);
        config()->set("payment-subscriptions.gateways.chargebee.$subscriptionProvider.full-access-key", null);

        $factory = new ChargebeeClientFactory();

        $this->expectException(Exception::class);
        $factory->make($subscriptionProvider);
    }

    public function testHandlesNonExistentSubscriptionProviderGracefully(): void
    {
        $subscriptionProvider = 'nonexistent-provider';

        $factory = new ChargebeeClientFactory();

        $this->expectException(Exception::class);
        $factory->make($subscriptionProvider);
    }
}
