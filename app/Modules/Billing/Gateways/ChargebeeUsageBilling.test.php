<?php

namespace AwardForce\Modules\Billing\Gateways;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Billing\Data\UsageLog;
use Chargebee\ChargebeeClient;
use Chargebee\Responses\UsageEventResponse\BatchIngestUsageEventResponse;
use RuntimeException;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ChargebeeUsageBillingTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testCreateUsageEventPayloadCreatesPayloadSuccessfully(): void
    {
        $this->freezeTime();

        $usageLog = $this->muffin(UsageLog::class, [
            'account_id' => $this->muffin(Account::class, ['subscriptionId' => 'sub_123'])->id,
            'metrics' => ['promptTokens' => 100],
            'metadata' => ['fieldId' => 4],
            'event' => 'ai_tokens_consumed',
        ]);

        $billing = new ChargebeeUsageBilling($this->app->make(ChargebeeClientFactory::class));
        $result = $billing->createPayload($usageLog);

        $this->assertEqualsCanonicalizing([
            'deduplication_id' => $usageLog->getID(),
            'subscription_id' => 'sub_123',
            'usage_timestamp' => $usageLog->createdAt,
            'properties' => ['event_id' => $usageLog->getID(), 'event' => 'ai_tokens_consumed', 'prompt_tokens' => 100, 'field_id' => 4],
        ], $result);
    }

    public function testCreateUsageEventPayloadThrowsExceptionWhenSubscriptionIdIsMissing(): void
    {
        $usageLog = $this->muffin(UsageLog::class, [
            'account_id' => $this->muffin(Account::class, ['subscriptionId' => null])->id,
        ]);

        $billing = new ChargebeeUsageBilling($this->app->make(ChargebeeClientFactory::class));

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage("Account {$usageLog->accountId} does not have a subscription ID.");

        $billing->createPayload($usageLog);
    }

    public function testIngestBatchReturnsEmptyResultForEmptyPayload(): void
    {
        $billing = app(ChargebeeUsageBilling::class);

        $result = $billing->ingestBatch([], 'awardforce');

        $this->assertSame([], $result->succeededIds);
        $this->assertSame([], $result->failedIds);
    }

    public function testIngestBatchReturnsCorrectSucceededAndFailedIds(): void
    {
        $chargebeeMock = $this->mock(ChargebeeClient::class);
        $chargebeeMock->shouldReceive('usageEvent->batchIngest')
            ->once()
            ->andReturn(BatchIngestUsageEventResponse::from([
                'batch_id' => str_random(),
                'failed_events' => [
                    ['deduplication_id' => 'id2', 'properties' => ['event_id' => 'id2']],
                    ['deduplication_id' => 'id4', 'properties' => ['event_id' => 'id4']],
                ],
            ]));

        $factoryMock = $this->mock(ChargebeeClientFactory::class);
        $factoryMock->shouldReceive('make')
            ->with('awardforce')
            ->once()
            ->andReturn($chargebeeMock);

        $gateway = new ChargebeeUsageBilling($factoryMock);
        $result = $gateway->ingestBatch([
            ['deduplication_id' => 'id1', 'properties' => ['event_id' => 'id1']],
            ['deduplication_id' => 'id2', 'properties' => ['event_id' => 'id2']],
            ['deduplication_id' => 'id3', 'properties' => ['event_id' => 'id3']],
            ['deduplication_id' => 'id4', 'properties' => ['event_id' => 'id4']],
        ], 'awardforce');

        $this->assertEquals(['id1', 'id3'], $result->succeededIds);
        $this->assertEquals(['id2', 'id4'], $result->failedIds);
    }
}
