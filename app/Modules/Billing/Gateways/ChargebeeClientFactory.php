<?php

namespace AwardForce\Modules\Billing\Gateways;

use Chargebee\ChargebeeClient;

class ChargebeeClientFactory
{
    public function make(string $subscriptionProvider): ChargebeeClient
    {
        return new ChargebeeClient([
            'site' => config("payment-subscriptions.gateways.chargebee.$subscriptionProvider.site"),
            'apiKey' => config("payment-subscriptions.gateways.chargebee.$subscriptionProvider.full-access-key"),
        ]);
    }
}
