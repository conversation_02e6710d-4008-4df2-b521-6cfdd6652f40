<?php

namespace AwardForce\Modules\Billing\Services;

use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\ValueObjects\Loggable;
use Tests\BaseTestCase;

final class LoggableFactoryTest extends BaseTestCase
{
    public function testCreatesLoggableForEntryResourceType(): void
    {
        $resource = new Resource(ResourceType::Entry, '123');
        $factory = new LoggableFactory();

        $loggable = $factory->createFromAIResource($resource);

        $this->assertInstanceOf(Loggable::class, $loggable);
        $this->assertEquals(LoggableType::Entry, $loggable->type);
        $this->assertEquals('123', $loggable->id);
    }
}
