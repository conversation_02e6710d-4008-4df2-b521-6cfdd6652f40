<?php

namespace AwardForce\Modules\Billing\Services;

use AwardForce\Modules\Billing\Contracts\UsageBilling;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use RuntimeException;
use Throwable;

/**
 * Service responsible for synchronizing usage logs with the billing provider.
 *
 * This service handles the core business logic for:
 * - Claiming usage logs for processing
 * - Creating usage event payloads
 * - Ingesting batches to the billing gateway
 * - Updating usage log statuses based on results
 * - Error recovery and batch reset functionality
 */
class UsageSynchroniser
{
    public function __construct(

        private readonly UsageBilling $usageBilling,
        private readonly UsageLogRepository $usageLogs,
    ) {
    }

    /**
     * @param  int[]  $accountIds
     *
     * @throws Throwable
     */
    public function batch(string $subscriptionProvider, array $accountIds): void
    {
        $processingId = uniqid('sync-job-', true);
        $maxEvents = $this->usageBilling->maxEventsPerBatch();
        $eventsToProcess = $this->usageLogs->claimEventsForProcessing($processingId, $accountIds, $maxEvents);

        try {
            $this->processBatch($eventsToProcess, $subscriptionProvider);
        } catch (Throwable $e) {
            $this->usageLogs->resetBatchToReady($processingId);
            throw $e;
        }
    }

    private function processBatch(Collection $eventsToProcess, string $subscriptionProvider): void
    {
        if ($eventsToProcess->isEmpty()) {
            return;
        }

        $usageEvents = $eventsToProcess
            ->map(fn(UsageLog $usageLog) => $this->usageBilling->createPayload($usageLog))
            ->all();
        $this->validateBatchSize($usageEvents);
        $result = $this->usageBilling->ingestBatch($usageEvents, $subscriptionProvider);
        $this->updateUsageLogStatuses($result);
    }

    private function validateBatchSize(array $usageEvents): void
    {
        $batchSizeBytes = mb_strlen(Json::encode($usageEvents), '8bit');
        $maxBatchSizeBytes = $this->usageBilling->maxBatchSizeBytes();

        if ($batchSizeBytes > $maxBatchSizeBytes) {
            throw new RuntimeException('Batch size too large.');
        }
    }

    private function updateUsageLogStatuses(BatchIngestionResult $result): void
    {
        if (! empty($result->succeededIds)) {
            $this->usageLogs->updateStatusByIds($result->succeededIds, Status::Synced);
        }

        if (! empty($result->failedIds)) {
            $this->usageLogs->updateStatusByIds($result->failedIds, Status::Failed);
            Log::error('Failed to sync usage logs', $result->failedIds);
        }
    }
}
