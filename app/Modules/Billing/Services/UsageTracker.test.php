<?php

namespace AwardForce\Modules\Billing\Services;

use AwardForce\Modules\Billing\Data\Usage;
use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\Events\UsageLogged;
use AwardForce\Modules\Billing\ValueObjects\Loggable;
use Illuminate\Support\Facades\Event;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class UsageTrackerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testTrackWithTrackableCreatesUsageRecord(): void
    {
        $event = 'ai_tokens_consumed';
        $metrics = ['tokens' => 150];
        $resource = new Loggable(LoggableType::Entry, random_int(10000, 1000000));
        $metadata = ['source' => 'test'];
        Event::fake();

        $usageLog = app(UsageTracker::class)->log(Usage::withDefaults($event, $metrics, $resource, $metadata));

        $this->assertSame($event, $usageLog->event);
        $this->assertSame(Status::Ready, $usageLog->status);
        $this->assertSame($metrics, $usageLog->metrics);
        $this->assertSame($metadata, $usageLog->metadata);
        $this->assertNotEmpty($usageLog->createdAt);
        $this->assertNotEmpty($usageLog->updatedAt);
        $this->assertNotEmpty($usageLog->_id);

        $this->assertEquals($resource->id, $usageLog->loggableId);
        $this->assertEquals($resource->type, $usageLog->loggableType);

        Event::assertDispatched(UsageLogged::class, static fn(UsageLogged $event) => $event->usageLog->is($usageLog));
    }

    public function testTrackWithoutTrackableCreatesUsageRecord(): void
    {
        $event = 'ai_tokens_consumed';
        $metrics = ['tokens' => 200];

        $usageLog = app(UsageTracker::class)->log(Usage::withDefaults($event, $metrics));

        $this->assertSame($event, $usageLog->event);
        $this->assertSame(Status::Ready, $usageLog->status);
        $this->assertSame($metrics, $usageLog->metrics);
        $this->assertNotEmpty($usageLog->createdAt);
        $this->assertNotEmpty($usageLog->updatedAt);

        $this->assertEmpty($usageLog->metadata);
        $this->assertNull($usageLog->loggableId);
        $this->assertNull($usageLog->loggableType);
    }

    public function testTrackUsesCorrectAccountId(): void
    {
        $type = 'ai_tokens_consumed';
        $metrics = ['tokens' => 100];

        $usageLog = app(UsageTracker::class)->log(Usage::withDefaults($type, $metrics));

        $this->assertEquals(current_account_id(), $usageLog->accountId);
    }

    public function testTrackUsesCorrectUserId(): void
    {
        $type = 'ai_tokens_consumed';
        $metrics = ['tokens' => 100];

        $usageLog = app(UsageTracker::class)->log(Usage::withDefaults($type, $metrics));

        $this->assertEquals(consumer_id(), $usageLog->userId);
    }
}
