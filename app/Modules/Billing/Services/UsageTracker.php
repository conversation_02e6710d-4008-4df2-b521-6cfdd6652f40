<?php

namespace AwardForce\Modules\Billing\Services;

use AwardForce\Modules\Billing\Data\Usage;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Events\UsageLogged;

class UsageTracker
{
    public function __construct(private readonly UsageLogRepository $usageLogs)
    {
    }

    public function log(Usage $usage): UsageLog
    {
        $usageLog = UsageLog::fromUsage($usage);

        $this->usageLogs->save($usageLog);

        event(new UsageLogged($usageLog));

        return $usageLog;
    }
}
