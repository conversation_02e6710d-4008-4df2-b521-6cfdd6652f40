<?php

namespace AwardForce\Modules\Billing\Services;

use AwardForce\Modules\Billing\Contracts\UsageBilling;
use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;
use RuntimeException;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class UsageSynchroniserTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testSyncUsageLogsSuccessfully(): void
    {
        $usageLogs = $this->muffins(3, UsageLog::class, ['status' => Status::Ready]);
        $failingUsageLog = $this->muffin(UsageLog::class, ['status' => Status::Ready]);
        UsageLog::refreshIndex();

        $usageBilling = $this->mock(UsageBilling::class);
        $usageBilling->shouldReceive('createPayload')
            ->times(4)
            ->andReturn(['event' => 'test']);
        $usageBilling->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);
        $usageBilling->shouldReceive('maxBatchSizeBytes')->once()->andReturn(490 * 1024);
        $usageBilling->shouldReceive('ingestBatch')
            ->once()
            ->andReturn(new BatchIngestionResult(collect($usageLogs)->just('_id'), [$failingUsageLog->getID()]));

        $service = new UsageSynchroniser(
            $usageBilling,
            app(UsageLogRepository::class),
        );

        $service->batch('test-provider', [current_account_id()]);
    }

    public function testSyncUsageLogsWithNoEvents(): void
    {
        $usageBilling = $this->mock(UsageBilling::class);
        $usageBilling->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);

        $service = new UsageSynchroniser(
            $usageBilling,
            app(UsageLogRepository::class),
        );

        $service->batch('test-provider', [current_account_id()]);
    }

    public function testSyncUsageLogsResetsStatusOnError(): void
    {
        $this->muffin(UsageLog::class, ['status' => Status::Ready]);
        UsageLog::refreshIndex();

        $usageBilling = $this->mock(UsageBilling::class);
        $usageBilling->shouldReceive('createPayload')
            ->once()
            ->andReturn(['event' => 'test']);
        $usageBilling->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);
        $usageBilling->shouldReceive('maxBatchSizeBytes')->once()->andReturn(490 * 1024);
        $usageBilling->shouldReceive('ingestBatch')
            ->once()
            ->andThrow(new RuntimeException('Test error'));

        $service = new UsageSynchroniser(
            $usageBilling,
            app(UsageLogRepository::class),
        );

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Test error');

        $service->batch('test-provider', [current_account_id()]);
    }

    public function testSyncUsageLogsValidatesBatchSize(): void
    {
        $this->muffin(UsageLog::class, ['status' => Status::Ready]);
        UsageLog::refreshIndex();

        $usageBilling = $this->mock(UsageBilling::class);
        $usageBilling->shouldReceive('createPayload')
            ->once()
            ->andReturn(['event' => str_repeat('x', 1000000)]); // Large payload
        $usageBilling->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);
        $usageBilling->shouldReceive('maxBatchSizeBytes')->once()->andReturn(1024); // Small limit

        $service = new UsageSynchroniser(
            $usageBilling,
            app(UsageLogRepository::class),
        );

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Batch size too large.');

        $service->batch('awardforce', [current_account_id()]);
    }

    public function testItDoesNotDoAnyThingIfTheAccountIDsAreEmpty(): void
    {
        $usageBilling = $this->mock(UsageBilling::class);
        $usageBilling->shouldReceive('maxEventsPerBatch')->once()->andReturn(500);

        $service = new UsageSynchroniser(
            $usageBilling,
            app(UsageLogRepository::class),
        );

        $service->batch('test-provider', []);
    }
}
