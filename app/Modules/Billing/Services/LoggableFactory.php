<?php

namespace AwardForce\Modules\Billing\Services;

use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\ValueObjects\Loggable;

/**
 * Factory that acts as an anti-corruption layer to convert other Bounded Contexts
 * into a `Loggable` VO that the billing context can understand.
 */
class LoggableFactory
{
    public function createFromAIResource(Resource $resource): Loggable
    {
        $loggableType = match ($resource->type) {
            ResourceType::Entry => LoggableType::Entry,
        };

        return new Loggable($loggableType, $resource->id);
    }
}
