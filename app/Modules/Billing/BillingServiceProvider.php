<?php

namespace AwardForce\Modules\Billing;

use AwardForce\Library\Providers\ModuleServiceProvider;
use AwardForce\Modules\AIAgents\Domain\Events\AITokensConsumed;
use AwardForce\Modules\Billing\Contracts\UsageBilling;
use AwardForce\Modules\Billing\Data\ElasticSearchUsageLogRepository;
use AwardForce\Modules\Billing\Data\UsageLogRepository;
use AwardForce\Modules\Billing\Gateways\ChargebeeUsageBilling;
use AwardForce\Modules\Billing\Listeners\LogUsage;
use AwardForce\Modules\PaymentSubscriptions\Events\SubscriptionWasProcessed;
use Illuminate\Support\Facades\Route;

class BillingServiceProvider extends ModuleServiceProvider
{
    protected array $repositories = [
        UsageLogRepository::class => ElasticSearchUsageLogRepository::class,
    ];

    /**
     * Define the listeners for this module.
     */
    protected array $listeners = [
        SubscriptionWasProcessed::class => 'AwardForce\Modules\Billing\Listeners\BillingListener@whenSubscriptionWasProcessed',
        AITokensConsumed::class => LogUsage::class,
    ];

    public function register(): void
    {
        parent::register();

        $this->registerChargebee();
    }
    /**
     * This is being registered in `\Platform\Providers\ModuleServiceProvider::registerRoutes()`
     */
    protected function registerInternalApiRoutes(string $path): void
    {
        Route::prefix('api/internal/billing')
            ->group($path);
    }

    public function registerChargebee(): void
    {
        $this->app->bind(UsageBilling::class, ChargebeeUsageBilling::class);
    }
}
