<?php

namespace AwardForce\Modules\Billing\Commands;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Billing\Services\UsageSynchroniser;

readonly class SyncUsageLogsHandler
{
    public function __construct(
        private UsageSynchroniser $usageSynchroniser,
        private AccountRepository $accounts,
    ) {
    }

    public function handle(SyncUsageLogs $command): void
    {
        $accountIds = $this->accounts->subscriptionProvider($command->subscriptionProvider)->just('id');
        $this->usageSynchroniser->batch($command->subscriptionProvider, $accountIds);
    }
}
