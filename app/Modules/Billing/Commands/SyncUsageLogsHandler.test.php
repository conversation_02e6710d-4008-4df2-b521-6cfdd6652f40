<?php

namespace AwardForce\Modules\Billing\Commands;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Billing\Services\UsageSynchroniser;
use RuntimeException;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class SyncUsageLogsHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testHandleDelegatesToService(): void
    {
        $subscriptionProvider = 'test-provider';
        $command = new SyncUsageLogs($subscriptionProvider);

        $usageSynchroniser = $this->mock(UsageSynchroniser::class);
        $usageSynchroniser->shouldReceive('batch')
            ->with($subscriptionProvider, [])
            ->once();

        $handler = new SyncUsageLogsHandler($usageSynchroniser, app(AccountRepository::class));
        $handler->handle($command);
    }

    public function testHandlePassesThroughExceptions(): void
    {
        $subscriptionProvider = 'test-provider';
        $command = new SyncUsageLogs($subscriptionProvider);

        $usageSynchroniser = $this->mock(UsageSynchroniser::class);
        $usageSynchroniser->shouldReceive('batch')
            ->with($subscriptionProvider, [])
            ->once()
            ->andThrow(new RuntimeException('Service error'));

        $handler = new SyncUsageLogsHandler($usageSynchroniser, app(AccountRepository::class));

        $this->expectException(RuntimeException::class);
        $this->expectExceptionMessage('Service error');

        $handler->handle($command);
    }
}
