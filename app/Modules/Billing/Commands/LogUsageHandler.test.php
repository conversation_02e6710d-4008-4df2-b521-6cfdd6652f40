<?php

namespace AwardForce\Modules\Billing\Commands;

use AwardForce\Modules\Billing\Data\Usage;
use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\Services\UsageTracker;
use AwardForce\Modules\Billing\ValueObjects\Loggable;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class LogUsageHandlerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testHandleLogsUsage(): void
    {
        $usageData = Usage::withDefaults(
            event: 'ai_tokens_consumed',
            metrics: ['tokens' => 50],
            loggable: new Loggable(LoggableType::Entry, 12345),
            metadata: ['source' => 'handler_test'],
        );

        $usageTracker = $this->mock(UsageTracker::class);
        $usageTracker->shouldReceive('log')
            ->once()
            ->with($usageData);

        $handler = new LogUsageHandler($usageTracker);
        $command = new LogUsage($usageData);
        $handler->handle($command);
    }
}
