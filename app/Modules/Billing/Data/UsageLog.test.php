<?php

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\Loggable;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class UsageLogTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testFromUsageCreatesUsageLogWithValidData(): void
    {
        $event = 'ai_tokens_consumed';
        $metrics = ['tokens' => 150];
        $resource = new Loggable(LoggableType::Entry, random_int(10000, 1000000));
        $metadata = ['source' => 'test'];
        $usage = Usage::withDefaults($event, $metrics, $resource, $metadata);

        $usageLog = UsageLog::fromUsage($usage);

        $this->assertEquals($event, $usageLog->event);
        $this->assertEquals(Status::Ready, $usageLog->status);
        $this->assertEquals($metrics, $usageLog->metrics);
        $this->assertEquals($metadata, $usageLog->metadata);
        $this->assertEquals($usage->accountId, $usageLog->accountId);
        $this->assertEquals($usage->userId, $usageLog->userId);
        $this->assertEquals(LoggableType::Entry, $usageLog->loggableType);
        $this->assertEquals($resource->id, $usageLog->loggableId);
        $this->assertNotNull($usageLog->createdAt);
        $this->assertEquals($usageLog->createdAt, $usageLog->updatedAt);
    }
}
