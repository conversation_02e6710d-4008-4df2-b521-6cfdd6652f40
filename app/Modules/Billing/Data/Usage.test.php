<?php

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\Loggable;
use InvalidArgumentException;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class UsageTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItBeCanInitiatedWithDefaults(): void
    {
        $event = 'ai_tokens_consumed';
        $metrics = ['tokens' => 150];
        $loggable = new Loggable(LoggableType::Entry, random_int(10000, 1000000));
        $metadata = ['source' => 'test'];

        $usage = Usage::withDefaults($event, $metrics, $loggable, $metadata);

        $this->assertSame($event, $usage->event);
        $this->assertSame($metrics, $usage->metrics);
        $this->assertSame($loggable, $usage->loggable);
        $this->assertSame($metadata, $usage->metadata);
        $this->assertSame(Status::Ready, $usage->status);
    }

    public function testItCanBeInitiatedWithoutTrackableModelAndMetadataParameters(): void
    {
        $event = 'ai_tokens_consumed';
        $metrics = ['tokens' => 150];

        $usage = Usage::withDefaults(
            event: $event,
            metrics: $metrics
        );

        $this->assertSame($event, $usage->event);
        $this->assertSame($metrics, $usage->metrics);
        $this->assertNull($usage->loggable);
        $this->assertSame([], $usage->metadata);
        $this->assertSame(Status::Ready, $usage->status);
    }

    public function testItCanBeInitiatedForExternalService(): void
    {
        $event = 'external_api_call';
        $metrics = ['requests' => 5];
        $accountId = 12345;
        $metadata = ['source' => 'external_service'];

        $usage = Usage::fromExternalService($event, $metrics, $accountId, $metadata);

        $this->assertSame($event, $usage->event);
        $this->assertSame($metrics, $usage->metrics);
        $this->assertSame($accountId, $usage->accountId);
        $this->assertNull($usage->userId);
        $this->assertNull($usage->loggable);
        $this->assertSame($metadata, $usage->metadata);
        $this->assertSame(Status::Ready, $usage->status);
    }

    #[TestWith(['simple'])]
    #[TestWith(['ai_tokens_consumed'])]
    #[TestWith(['test_event_with_multiple_underscores'])]
    #[TestWith(['external_api_call'])]
    public function testItAcceptsValidEventFormats(string $event): void
    {
        $usage = Usage::withDefaults($event, ['count' => 1]);

        $this->assertSame($event, $usage->event);
    }

    #[TestWith(['Invalid-Event'])]
    #[TestWith(['UPPERCASE_EVENT'])]
    #[TestWith(['Mixed_Case_Event'])]
    #[TestWith(['event with spaces'])]
    #[TestWith(['event-with-dashes'])]
    #[TestWith(['event.with.dots'])]
    #[TestWith(['event123'])]
    #[TestWith(['event@special'])]
    #[TestWith([''])]
    public function testItRejectsInvalidEventFormats(string $event): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Event must start with a lowercase letter and contain only lowercase letters, and underscores');

        Usage::withDefaults($event, ['count' => 1]);
    }

    public function testItRejectsEventThatIsTooLong(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Event must not exceed 255 characters');

        $longEvent = str_repeat('a', 256);
        Usage::withDefaults($longEvent, ['count' => 1]);
    }

    #[TestWith([['cpuUsage' => 100]])]
    #[TestWith([['memoryTotal' => 1024]])]
    #[TestWith([['requestCount' => 50]])]
    #[TestWith([['apiCalls' => 25]])]
    #[TestWith([['simple' => 'value']])]
    #[TestWith([['a1b2c3' => 'test']])]
    #[TestWith([['test123Value' => 'data']])]
    public function testItAcceptsValidMetricsKeys(array $metrics): void
    {
        $usage = Usage::withDefaults('test_event', $metrics);

        $this->assertSame($metrics, $usage->metrics);
    }

    #[TestWith([['CPU_Usage' => 100]])]
    #[TestWith([['memory-total' => 1024]])]
    #[TestWith([['Request Count' => 50]])]
    #[TestWith([['1invalid' => 25]])]
    #[TestWith([['_invalid' => 'value']])]
    #[TestWith([['test.value' => 'test']])]
    #[TestWith([['test@value' => 'data']])]
    #[TestWith([['test-value' => 'info']])]
    #[TestWith([['Test_Value' => 'mixed']])]
    public function testItRejectsInvalidMetricsKeys(array $metrics): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessageMatches('/Metrics key .* must be camelCase, start with a lowercase letter, and contain only letters and numbers/');

        Usage::withDefaults('test_event', $metrics);
    }

    public function testItRejectsEmptyMetrics(): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessage('Metrics must contain at least one item');

        Usage::withDefaults('test_event', []);
    }

    #[TestWith([['sourceSystem' => 'external_api']])]
    #[TestWith([['versionNumber' => '1.2.3']])]
    #[TestWith([['userAgent' => 'test']])]
    #[TestWith([['requestId' => 'abc123']])]
    #[TestWith([['traceId' => 'def456']])]
    #[TestWith([['environment' => 'production']])]
    public function testItAcceptsValidMetadataKeys(array $metadata): void
    {
        $usage = Usage::withDefaults('test_event', ['count' => 1], null, $metadata);

        $this->assertSame($metadata, $usage->metadata);
    }

    #[TestWith([['Source-System' => 'external_api']])]
    #[TestWith([['VERSION_NUMBER' => '1.2.3']])]
    #[TestWith([['User Agent' => 'test']])]
    #[TestWith([['1request_id' => 'abc123']])]
    #[TestWith([['_trace_id' => 'def456']])]
    #[TestWith([['environment.name' => 'production']])]
    #[TestWith([['service@name' => 'api']])]
    public function testItRejectsInvalidMetadataKeys(array $metadata): void
    {
        $this->expectException(InvalidArgumentException::class);
        $this->expectExceptionMessageMatches('/Metadata key .* must be camelCase, start with a lowercase letter, and contain only letters and numbers/');

        Usage::withDefaults('test_event', ['count' => 1], null, $metadata);
    }
}
