<?php

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Billing\ValueObjects\Loggable;
use Webmozart\Assert\Assert;

final readonly class Usage
{
    private function __construct(
        public string $event,
        public array $metrics,
        public Status $status,
        public int $accountId,
        public ?int $userId,
        public ?Loggable $loggable = null,
        public array $metadata = [],
    ) {
        Assert::inArray($status, [Status::Pending, Status::Ready]);
        $this->validateEvent($event);
        $this->validateMetrics($metrics);
        $this->validateMetadata($metadata);
    }

    public static function withDefaults(
        string $event,
        array $metrics,
        ?Loggable $loggable = null,
        array $metadata = [],
    ): self {
        return new self(
            event: $event,
            metrics: $metrics,
            status: Status::Ready,
            accountId: current_account_id(),
            userId: consumer_id(),
            loggable: $loggable,
            metadata: $metadata,
        );
    }

    public static function fromExternalService(
        string $event,
        array $metrics,
        int $accountId,
        array $metadata = [],
    ): self {
        return new self(
            event: $event,
            metrics: $metrics,
            status: Status::Ready,
            accountId: $accountId,
            userId: null, // Always null for external services. Or is it? 🤔
            loggable: null, // Always null for external services
            metadata: $metadata,
        );
    }

    private function validateEvent(string $event): void
    {
        Assert::maxLength($event, 255, 'Event must not exceed 255 characters');
        Assert::regex($event, '/^[a-z][a-z_]*$/', 'Event must start with a lowercase letter and contain only lowercase letters, and underscores');
    }

    private function validateMetrics(array $metrics): void
    {
        Assert::notEmpty($metrics, 'Metrics must contain at least one item');
        $this->validateArrayKeys($metrics, 'Metrics');
    }

    private function validateMetadata(array $metadata): void
    {
        $this->validateArrayKeys($metadata, 'Metadata');
    }

    private function validateArrayKeys(array $data, string $fieldName): void
    {
        foreach (array_keys($data) as $key) {
            Assert::string($key, "{$fieldName} keys must be strings");
            Assert::regex($key, '/^[a-z][a-zA-Z0-9]*$/',
                "{$fieldName} key '{$key}' must be camelCase, start with a lowercase letter, and contain only letters and numbers"
            );
        }
    }
}
