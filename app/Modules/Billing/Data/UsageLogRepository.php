<?php

namespace AwardForce\Modules\Billing\Data;

use AwardForce\Modules\Billing\Enums\Status;
use Illuminate\Support\Collection;

interface UsageLogRepository
{
    public function updateStatusByIds(array $ids, Status $status): void;

    /**
     * Reset all usage logs with the given processing ID back to Ready status.
     */
    public function resetBatchToReady(string $processingId): void;

    /**
     * Atomically claim usage logs for processing.
     * This prevents race conditions where multiple processes grab the same logs.
     *
     * @return Collection<UsageLog>
     */
    public function claimEventsForProcessing(string $processingId, array $accountIds, int $maxEvents): Collection;
}
