<?php

namespace AwardForce\Modules\Billing\Http\Rules;

use Illuminate\Support\Facades\Validator;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class ValidArrayKeysTest extends BaseTestCase
{
    use Laravel;

    #[TestWith([['cpu_usage' => 100]])]
    #[TestWith([['memory_total' => 1024]])]
    #[TestWith([['request_count' => 50]])]
    #[TestWith([['api_calls' => 25]])]
    #[TestWith([['simple' => 'value']])]
    #[TestWith([['a1b2c3' => 'test']])]
    #[TestWith([['test_123_value' => 'data']])]
    #[TestWith([['lowercase_with_numbers_123' => 'info']])]
    public function testItPassesValidationForValidKeys(array $data): void
    {
        $validator = Validator::make(['data' => $data], ['data' => new ValidArrayKeys()]);

        $this->assertTrue($validator->passes());
    }

    #[TestWith([['CPU_Usage' => 100]])]
    #[TestWith([['memory-total' => 1024]])]
    #[TestWith([['Request Count' => 50]])]
    #[TestWith([['1invalid' => 25]])]
    #[TestWith([['_invalid' => 'value']])]
    #[TestWith([['test.value' => 'test']])]
    #[TestWith([['test@value' => 'data']])]
    #[TestWith([['test-value' => 'info']])]
    #[TestWith([['Test_Value' => 'mixed']])]
    #[TestWith([['UPPERCASE' => 'caps']])]
    #[TestWith([['mixed_Case' => 'case']])]
    #[TestWith([['special!char' => 'special']])]
    public function testItFailsValidationForInvalidKeys(array $data): void
    {
        $validator = Validator::make(['data' => $data], ['data' => new ValidArrayKeys()]);

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('data'));

        $errorMessage = $validator->errors()->first('data');
        $invalidKey = array_keys($data)[0];
        $this->assertStringContainsString($invalidKey, $errorMessage);
        $this->assertStringContainsString('invalid', $errorMessage);
    }

    public function testItPassesValidationForMultipleValidKeys(): void
    {
        $data = [
            'cpu_usage' => 75.5,
            'memory_total' => 1024,
            'request_count' => 100,
            'api_calls' => 50,
            'lowercase_key' => 'value',
            'key_with_123' => 'number',
        ];

        $validator = Validator::make(['data' => $data], ['data' => new ValidArrayKeys()]);

        $this->assertTrue($validator->passes());
    }

    public function testItFailsValidationForMixedValidAndInvalidKeys(): void
    {
        $data = [
            'cpu_usage' => 75.5,        // valid
            'Memory-Total' => 1024,     // invalid (uppercase and hyphen)
            'request_count' => 100,     // valid
            'Invalid Key' => 'value',   // invalid (space and uppercase)
        ];

        $validator = Validator::make(['data' => $data], ['data' => new ValidArrayKeys()]);

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('data'));

        $errorMessage = $validator->errors()->first('data');
        $this->assertStringContainsString('Memory-Total', $errorMessage);
        $this->assertStringContainsString('Invalid Key', $errorMessage);
    }

    public function testItPassesValidationForEmptyArray(): void
    {
        $validator = Validator::make(['data' => []], ['data' => new ValidArrayKeys()]);

        $this->assertTrue($validator->passes());
    }
}
