<?php

namespace AwardForce\Modules\Billing\Http\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Support\Collection;

class ValidArrayKeys implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! is_array($value)) {
            return;
        }

        collect(array_keys($value))
            ->filter(fn(string $key) => ! $this->isValidKey($key))
            ->whenNotEmpty(function (Collection $invalidKeys) use ($fail) {
                $fail("The following keys are invalid: {$invalidKeys->implode(', ')}. Keys must start with a lowercase letter and contain only lowercase letters, numbers, and underscores.");
            });
    }

    private function isValidKey(mixed $key): bool
    {
        return is_string($key) && preg_match('/^[a-z][a-z0-9_]*$/', $key);
    }
}
