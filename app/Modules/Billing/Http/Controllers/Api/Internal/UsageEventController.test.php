<?php

namespace AwardForce\Modules\Billing\Http\Controllers\Api\Internal;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Billing\Commands\LogUsage;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Symfony\Component\HttpFoundation\Response;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class UsageEventControllerTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItCanStoreUsageEventWithValidData(): void
    {
        Event::fake();
        config(['api.services' => ['prismatic' => 'test-secret']]);

        $account = $this->muffin(Account::class);

        $payload = [
            'event' => 'external_api_call',
            'metrics' => ['requests' => 5, 'duration' => 1200],
            'metadata' => ['source' => 'external_service', 'version' => '1.0'],
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer test-secret',
            'x-account-uuid' => $account->globalId,
        ])->postJson(route('api.internal.billing.usage-events.store'), $payload);

        $response->assertStatus(Response::HTTP_ACCEPTED);
        $response->assertJson([]);
    }

    public function testItDispatchesLogUsageJob(): void
    {
        Bus::fake();
        config(['api.services' => ['prismatic' => 'test-secret']]);

        $account = $this->muffin(Account::class);

        $payload = [
            'event' => 'test_event',
            'metrics' => ['count' => 1],
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer test-secret',
            'x-account-uuid' => $account->globalId,
        ])->postJson(route('api.internal.billing.usage-events.store'), $payload);

        $response->assertStatus(Response::HTTP_ACCEPTED);

        Bus::assertDispatched(LogUsage::class, static function ($job) use ($account) {
            return $job->usage->event === 'test_event'
                && $job->usage->metrics === ['count' => 1]
                && $job->usage->accountId === $account->id
                && $job->usage->userId === null;
        });
    }

    public function testItRejectsRequestWithInvalidToken(): void
    {
        Bus::fake();
        config(['api.services' => ['prismatic' => 'test-secret']]);

        $payload = [
            'event' => 'external_api_call',
            'metrics' => ['requests' => 5],
            'accountGlobalId' => '550e8400-e29b-41d4-a716-************',
        ];

        $response = $this->withHeaders([
            'Authorization' => 'Bearer invalid-secret',
        ])->postJson(route('api.internal.billing.usage-events.store'), $payload);

        $response->assertStatus(Response::HTTP_UNAUTHORIZED);
    }

    public function testItRejectsRequestWithoutToken(): void
    {
        Bus::fake();

        $payload = [
            'event' => 'external_api_call',
            'metrics' => ['requests' => 5],
            'accountGlobalId' => '550e8400-e29b-41d4-a716-************',
        ];

        $response = $this->postJson(route('api.internal.billing.usage-events.store'), $payload);

        $response->assertStatus(Response::HTTP_UNAUTHORIZED);
    }
}
