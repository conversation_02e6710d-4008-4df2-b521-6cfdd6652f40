<?php

namespace AwardForce\Modules\Billing\Http\Controllers\Api\Internal;

use AwardForce\Modules\Accounts\Contracts\AccountRepository;
use AwardForce\Modules\Billing\Commands\LogUsage;
use AwardForce\Modules\Billing\Data\Usage;
use AwardForce\Modules\Billing\Http\Requests\Api\Internal\StoreUsageRequest;
use Illuminate\Http\JsonResponse;
use Symfony\Component\HttpFoundation\Response;

readonly class UsageEventController
{
    public function __construct(
        private AccountRepository $accountRepository,
    ) {
    }

    public function store(StoreUsageRequest $request): JsonResponse
    {
        $accountId = $this->accountRepository->globalId($request->string('accountGlobalId'))
            ->fields(['id'])
            ->require()->id;

        dispatch(new LogUsage(Usage::fromExternalService(
            event: $request->string('event'),
            metrics: $request->array('metrics'),
            accountId: $accountId,
            metadata: $request->array('metadata'),
        )));

        return response()->json([], Response::HTTP_ACCEPTED);
    }
}
