<?php

namespace AwardForce\Modules\Billing\Http\Requests\Api\Internal;

use AwardForce\Modules\Accounts\Models\Account;
use Illuminate\Support\Facades\Validator;
use PHPUnit\Framework\Attributes\TestWith;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class StoreUsageRequestTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testItPassesValidationWithValidData(): void
    {
        $account = $this->muffin(Account::class);

        $data = [
            'event' => 'external_api_call',
            'metrics' => ['requests' => 5, 'duration' => 1200],
            'accountGlobalId' => (string) $account->globalId,
            'metadata' => ['source' => 'external_service', 'version' => '1.0'],
        ];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());
        $this->assertTrue($validator->passes());
    }

    public function testItFailsValidationForMissingRequiredFields(): void
    {
        $data = [];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('event'));
        $this->assertTrue($validator->errors()->has('metrics'));
        $this->assertTrue($validator->errors()->has('accountGlobalId'));
    }

    public function testItFailsValidationForInvalidAccountGlobalId(): void
    {
        $data = [
            'event' => 'test_event',
            'metrics' => ['count' => 1],
            'accountGlobalId' => 'invalid-uuid',
        ];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('accountGlobalId'));
    }

    public function testItFailsValidationForNonExistentAccount(): void
    {
        $data = [
            'event' => 'test_event',
            'metrics' => ['count' => 1],
            'accountGlobalId' => '550e8400-e29b-41d4-a716-************', // Valid UUID but non-existent
        ];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertFalse($validator->passes());
        $this->assertTrue($validator->errors()->has('accountGlobalId'));
        $this->assertStringContainsString('does not exist', $validator->errors()->first('accountGlobalId'));
    }

    #[TestWith(['simple'])]
    #[TestWith(['ai_tokens_consumed'])]
    #[TestWith(['test_event_with_multiple_underscores'])]
    public function testItPassesValidationForValidEventFormats(string $event): void
    {
        $account = $this->muffin(Account::class);
        $data = [
            'event' => $event,
            'metrics' => ['count' => 1],
            'accountGlobalId' => (string) $account->globalId,
        ];
        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());
        $this->assertTrue($validator->passes(), "Event '{$event}' should be valid");
    }

    #[TestWith(['Invalid-Event'])]
    #[TestWith(['UPPERCASE_EVENT'])]
    #[TestWith(['Mixed_Case_Event'])]
    #[TestWith(['event with spaces'])]
    #[TestWith(['event-with-dashes'])]
    #[TestWith(['event.with.dots'])]
    #[TestWith(['event123'])]
    #[TestWith(['event@special'])]
    public function testItFailsValidationForInvalidEventFormats(string $event): void
    {
        $account = $this->muffin(Account::class);
        $data = [
            'event' => $event,
            'metrics' => ['count' => 1],
            'accountGlobalId' => (string) $account->globalId,
        ];
        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());
        $this->assertFalse($validator->passes(), "Event '{$event}' should be invalid");
        $this->assertTrue($validator->errors()->has('event'));
    }

    #[TestWith(['cpu_usage'])]
    #[TestWith(['memory_total'])]
    #[TestWith(['request_count'])]
    #[TestWith(['api_calls'])]
    #[TestWith(['simple'])]
    #[TestWith(['a1b2c3'])]
    #[TestWith(['test_123_value'])]
    #[TestWith(['lowercase_with_numbers_123'])]
    public function testItPassesValidationForValidMetricsKeys(string $key): void
    {
        $account = $this->muffin(Account::class);
        $data = [
            'event' => 'test_event',
            'metrics' => [$key => 100],
            'accountGlobalId' => (string) $account->globalId,
        ];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertTrue($validator->passes(), "Metrics key '{$key}' should be valid");
    }

    #[TestWith(['CPU_Usage'])]
    #[TestWith(['memory-total'])]
    #[TestWith(['Request Count'])]
    #[TestWith(['1invalid'])]
    #[TestWith(['_invalid'])]
    #[TestWith(['test.value'])]
    #[TestWith(['test@value'])]
    #[TestWith(['test-value'])]
    #[TestWith(['Test_Value'])]
    #[TestWith(['UPPERCASE'])]
    #[TestWith(['mixed_Case'])]
    #[TestWith(['special!char'])]
    public function testItFailsValidationForInvalidMetricsKeys(string $key): void
    {
        $account = $this->muffin(Account::class);
        $data = [
            'event' => 'test_event',
            'metrics' => [$key => 100],
            'accountGlobalId' => (string) $account->globalId,
        ];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertFalse($validator->passes(), "Metrics key '{$key}' should be invalid");
        $this->assertTrue($validator->errors()->has('metrics'));
        $this->assertStringContainsString($key, $validator->errors()->first('metrics'));
    }

    #[TestWith(['source_system'])]
    #[TestWith(['version_number'])]
    #[TestWith(['user_agent'])]
    #[TestWith(['request_id'])]
    #[TestWith(['trace_id'])]
    #[TestWith(['environment'])]
    #[TestWith(['service_name'])]
    public function testItPassesValidationForValidMetadataKeys(string $key): void
    {
        $account = $this->muffin(Account::class);
        $data = [
            'event' => 'test_event',
            'metrics' => ['count' => 1],
            'accountGlobalId' => (string) $account->globalId,
            'metadata' => [$key => 'test_value'],
        ];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertTrue($validator->passes(), "Metadata key '{$key}' should be valid");
    }

    #[TestWith(['Source-System'])]
    #[TestWith(['VERSION_NUMBER'])]
    #[TestWith(['User Agent'])]
    #[TestWith(['1request_id'])]
    #[TestWith(['_trace_id'])]
    #[TestWith(['environment.name'])]
    #[TestWith(['service@name'])]
    #[TestWith(['Mixed_Case_Key'])]
    public function testItFailsValidationForInvalidMetadataKeys(string $key): void
    {
        $account = $this->muffin(Account::class);
        $data = [
            'event' => 'test_event',
            'metrics' => ['count' => 1],
            'accountGlobalId' => (string) $account->globalId,
            'metadata' => [$key => 'test_value'],
        ];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertFalse($validator->passes(), "Metadata key '{$key}' should be invalid");
        $this->assertTrue($validator->errors()->has('metadata'));
        $this->assertStringContainsString($key, $validator->errors()->first('metadata'));
    }

    public function testItPassesValidationWithEmptyMetadata(): void
    {
        $account = $this->muffin(Account::class);
        $data = [
            'event' => 'test_event',
            'metrics' => ['count' => 1],
            'accountGlobalId' => (string) $account->globalId,
            'metadata' => [],
        ];

        $request = new StoreUsageRequest();
        $validator = Validator::make($data, $request->rules(), $request->messages());

        $this->assertTrue($validator->passes());
    }

    public function testItMergesAccountGlobalIdFromHeader(): void
    {
        $account = $this->muffin(Account::class);

        $data = [
            'event' => 'external_api_call',
            'metrics' => ['requests' => 5, 'duration' => 1200],
            'metadata' => ['source' => 'external_service', 'version' => '1.0'],
        ];

        $headers = [
            'x-account-uuid' => (string) $account->globalId,
        ];

        $request = StoreUsageRequest::create(
            '/internal/usage',
            'POST',
            $data,
            [],
            [],
            [],
            [],
            null
        );
        $request->headers->add($headers);

        $reflection = new \ReflectionMethod($request, 'prepareForValidation');
        $reflection->setAccessible(true);
        $reflection->invoke($request);

        $this->assertEquals(
            (string) $account->globalId,
            $request->input('accountGlobalId')
        );
    }
}
