<?php

namespace AwardForce\Modules\Billing\Http\Requests\Api\Internal;

use AwardForce\Modules\Billing\Http\Rules\ValidArrayKeys;
use Illuminate\Foundation\Http\FormRequest;

class StoreUsageRequest extends FormRequest
{
    protected function prepareForValidation(): void
    {
        if ($this->hasHeader('x-account-uuid')) {
            $this->merge([
                'accountGlobalId' => $this->header('x-account-uuid'),
            ]);
        }
    }

    public function rules(): array
    {
        return [
            'event' => ['required', 'string', 'max:255', 'regex:/^[a-z_]+$/'],
            'metrics' => ['required', 'array', 'min:1', new ValidArrayKeys],
            'accountGlobalId' => ['required', 'uuid', 'exists:accounts,global_id'],
            'metadata' => ['sometimes', 'array', new ValidArrayKeys],
        ];
    }

    public function messages(): array
    {
        return [
            'event.regex' => 'The event must contain only lowercase letters and underscores.',
            'accountGlobalId.uuid' => 'The account global ID must be a valid UUID.',
            'accountGlobalId.exists' => 'The specified account does not exist.',
        ];
    }
}
