<?php

namespace AwardForce\Modules\Billing\Http\Middleware;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

final class VerifySharedSecretTest extends BaseTestCase
{
    use Laravel;

    public function test_it_allows_access_with_a_valid_secret(): void
    {
        config(['api.services' => ['prismatic' => 'test-secret']]);

        $request = Request::create('/test', 'POST');
        $request->headers->set('Authorization', 'Bearer test-secret');

        $middleware = new VerifySharedSecret();

        $response = $middleware->handle($request, function () {
            return new Response();
        });

        $this->assertEquals(200, $response->getStatusCode());
    }

    public function test_it_denies_access_with_an_invalid_secret(): void
    {
        $this->expectException(HttpException::class);

        config(['api.services' => ['prismatic' => 'test-secret']]);

        $request = Request::create('/test', 'POST');
        $request->headers->set('Authorization', 'Bearer invalid-secret');

        $middleware = new VerifySharedSecret();

        try {
            $middleware->handle($request, function () {
                return new Response();
            });
        } catch (HttpException $e) {
            $this->assertEquals(401, $e->getStatusCode());
            throw $e;
        }
    }

    public function test_it_denies_access_with_a_missing_secret(): void
    {
        $this->expectException(HttpException::class);

        config(['api.services' => ['prismatic' => 'test-secret']]);

        $request = Request::create('/test', 'POST');

        $middleware = new VerifySharedSecret();

        try {
            $middleware->handle($request, function () {
                return new Response();
            });
        } catch (HttpException $e) {
            $this->assertEquals(401, $e->getStatusCode());
            throw $e;
        }
    }

    public function test_it_allows_access_with_one_of_many_secrets(): void
    {
        config(['api.services' => ['prismatic' => 'secret1', 'service2' => 'secret2']]);

        $request = Request::create('/test', 'POST');
        $request->headers->set('Authorization', 'Bearer secret2');

        $middleware = new VerifySharedSecret();

        $response = $middleware->handle($request, function () {
            return new Response();
        });

        $this->assertEquals(200, $response->getStatusCode());
    }
}
