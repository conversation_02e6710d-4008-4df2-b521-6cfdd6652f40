<?php

namespace AwardForce\Modules\Billing\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class VerifySharedSecret
{
    public function handle(Request $request, Closure $next)
    {
        $token = $request->bearerToken();

        if (! $token) {
            abort(401, 'Unauthorized.');
        }

        $authorized = false;

        foreach (config('api.services') as $secret) {
            if (hash_equals($secret, $token)) {
                $authorized = true;
            }
        }

        if ($authorized) {
            return $next($request);
        }

        abort(401, 'Unauthorized.');
    }
}
