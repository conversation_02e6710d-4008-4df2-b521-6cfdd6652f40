<?php

namespace AwardForce\Modules\Billing\Listeners;

use AwardForce\Library\AIAgents\ValueObjects\TokenUsage;
use AwardForce\Modules\AIAgents\Boundary\Resource;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\AIAgents\Domain\Events\AITokensConsumed;
use AwardForce\Modules\Billing\Commands\LogUsage as LogUsageCommand;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Event;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class LogUsageTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testDispatchesLogUsageCommand(): void
    {
        $event = new AITokensConsumed(new Resource(ResourceType::Entry, '123'), new TokenUsage(100, 200), ['source' => 'test']);
        Bus::fake();

        app(LogUsage::class)->handle($event);

        Bus::assertDispatched(LogUsageCommand::class);
    }

    public function testItIsWiredUpToAITokensConsumedEvent(): void
    {
        Event::fake(AITokensConsumed::class);
        Event::assertListening(AITokensConsumed::class, LogUsage::class);
    }
}
