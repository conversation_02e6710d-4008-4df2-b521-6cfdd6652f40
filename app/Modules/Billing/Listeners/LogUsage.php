<?php

namespace AwardForce\Modules\Billing\Listeners;

use AwardForce\Modules\Billing\Commands\LogUsage as LogUsageCommand;
use AwardForce\Modules\Billing\Contracts\UsageTrackable;
use AwardForce\Modules\Billing\Services\LoggableFactory;
use Illuminate\Foundation\Bus\DispatchesJobs;

readonly class LogUsage
{
    use DispatchesJobs;

    public function __construct(
        private LoggableFactory $loggableFactory,
    ) {
    }

    public function handle(UsageTrackable $event): void
    {
        $this->dispatch(new LogUsageCommand($event->usage($this->loggableFactory)));
    }
}
