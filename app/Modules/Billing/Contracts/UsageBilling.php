<?php

namespace AwardForce\Modules\Billing\Contracts;

use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\ValueObjects\BatchIngestionResult;

interface UsageBilling
{
    public function createPayload(UsageLog $usageLog): array;

    public function ingestBatch(array $eventsPayload, string $subscriptionProvider): BatchIngestionResult;

    public function maxEventsPerBatch(): int;

    public function maxBatchSizeBytes(): int;
}
