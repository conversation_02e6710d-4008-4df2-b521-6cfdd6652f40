<?php

namespace AwardForce\Modules\Entries\Services\Copy;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Models\FieldPairing;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Referees\Models\Referee;
use Illuminate\Support\Collection;
use Str;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class PairingTest extends BaseTestCase
{
    use Database;
    use Laravel;

    private FieldPairing $fieldPairing;

    private array $pairings;

    private Pairing $pairing;

    private Collection $sourceReferees;

    private Collection $destinationTabs;

    public function init()
    {
        $destinationAccount = $this->muffin(Account::class);
        $valuesService = app(ValuesService::class);
        $entry = $this->muffin(Entry::class);
        $sourceTabs = $this->muffins(2, Tab::class, ['resource' => Tab::RESOURCE_ENTRIES, 'type' => Tab::TYPE_REFEREES]);
        $this->sourceReferees = collect();
        $this->pairings = [
            'tabs' => [],
            'referees' => [],
        ];
        $this->destinationTabs = collect([]);

        foreach ($sourceTabs as $tab) {
            $sourceReferees = $this->muffins(2, Referee::class, ['submittableId' => $entry->id, 'tabId' => $tab->id]);
            $sourceRefereeFields = $this->muffins(2, Field::class, [
                'resource' => Field::RESOURCE_REFEREES,
                'tab_id' => $tab->id,
            ]);
            foreach ($sourceReferees as $referee) {
                $valuesService->setValuesForObject([
                    ((string) $sourceRefereeFields[0]->slug) => Str::random(),
                    ((string) $sourceRefereeFields[1]->slug) => Str::random(),
                ], $referee);
                $this->sourceReferees->push($referee);
            }

            $destinationTab = $this->muffin(Tab::class, [
                'resource' => Tab::RESOURCE_ENTRIES,
                'type' => Tab::TYPE_REFEREES,
                'account_id' => $destinationAccount->id,
            ]);
            $destinationRefereeFields = $this->muffins(2, Field::class, [
                'resource' => Field::RESOURCE_REFEREES,
                'tab_id' => $destinationTab->id,
                'account_id' => $destinationAccount->id,
            ]);

            $this->destinationTabs->push($destinationTab);
            $this->pairings['tabs'][(string) $destinationTab->slug] = (string) $tab->slug;
            $this->pairings['referees'][(string) $destinationTab->slug] =
                collect($destinationRefereeFields)->mapWithKeys(function (Field $destinationField, $index) use ($sourceRefereeFields) {
                    $sourceFieldSlug = (string) $sourceRefereeFields[$index]->slug;

                    return [(string) $destinationField->slug => [
                        'type' => 'text',
                        'pairing' => '{'.$sourceFieldSlug.'}',
                    ]];
                })->toArray();
        }

        $this->fieldPairing = $this->muffin(FieldPairing::class, ['pairings' => $this->pairings]);
        $this->pairing = new Pairing(new SourceEntry($entry), $this->fieldPairing, 0);
    }

    public function testItFormatsRefereesArray()
    {
        $result = $this->pairing->referees();

        $destinationTabsSlugs = $this->destinationTabs->map(fn($tab) => (string) $tab->slug)->toArray();

        $this->assertCount(count($destinationTabsSlugs), $result);
        $this->assertEquals(array_keys($result), $destinationTabsSlugs);

        foreach ($result as $refereesArray) {
            $this->assertIsArray($refereesArray);
            $this->assertCount(2, $refereesArray);
            foreach ($refereesArray as $destinationReferee) {
                $this->assertInstanceOf(Referee::class, $destinationReferee);
            }
        }
    }

    public function testItMatchesRefereesToTheProperTab()
    {
        $result = $this->pairing->referees();

        foreach ($result as $destinationTabSlug => $refereesArray) {
            $sourceTabSlug = $this->fieldPairing->tab($destinationTabSlug);

            foreach ($refereesArray as $destinationReferee) {
                $this->assertInstanceOf(Referee::class, $destinationReferee);
                $this->assertEquals((string) $sourceTabSlug, (string) $destinationReferee->tab->slug);
            }
        }
    }

    public function testItSetsPairingsOnTheReferees()
    {
        $result = $this->pairing->referees();

        foreach ($result as $destinationTabSlug => $refereesArray) {

            foreach ($refereesArray as $destinationReferee) {
                $sourceTabReferee = $this->sourceReferees->firstWhere('id', $destinationReferee->id);
                $destinationTabPairings = collect($this->pairings['referees'][$destinationTabSlug]);
                $refereePairings = [];

                // manually build pairings array
                foreach ($sourceTabReferee->values as $sourceSlug => $value) {
                    $destinationFieldSlug = $destinationTabPairings->reduce(function ($slug, $pairing, $destinationFieldSlug) use ($sourceSlug) {
                        if (Str::contains($pairing['pairing'], $sourceSlug)) {
                            $slug = $destinationFieldSlug;
                        }

                        return $slug;
                    });

                    $refereePairings[$destinationFieldSlug] = $value;
                }

                $this->assertInstanceOf(Referee::class, $destinationReferee);
                $this->assertEquals($destinationReferee->name, $sourceTabReferee->name);
                $this->assertEquals($destinationReferee->email, $sourceTabReferee->email);
                $this->assertIsArray($destinationReferee->pairings);
                $this->assertEquals($refereePairings, $destinationReferee->pairings);
            }
        }
    }
}
