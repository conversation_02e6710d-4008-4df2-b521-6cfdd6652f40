<?php

namespace AwardForce\Modules\Entries\Services\AIAgent;

use AwardForce\Modules\AIAgents\Boundary\Composite;
use AwardForce\Modules\AIAgents\Boundary\FieldTrigger;
use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use Platform\Database\Eloquent\Model;

readonly class EntryComposite implements Composite
{
    public function __construct(
        private EntryRepository $entries,
    ) {
    }

    public function metadata(int $resourceId): array
    {
        return ['entry_id' => $resourceId];
    }

    public function supports(ResourceType $resourceType): bool
    {
        return $resourceType === ResourceType::Entry;
    }

    public function model(int $resourceId): Model
    {
        return $this->entries->fields(['id'])
            ->primary($resourceId)
            ->require();
    }

    /**
     * @return FieldTrigger[]
     */
    public function triggers(): array
    {
        return AIFieldTrigger::cases();
    }

    public function contexts(): array
    {
        return AIFieldContext::cases();
    }
}
