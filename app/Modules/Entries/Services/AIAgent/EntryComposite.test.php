<?php

namespace AwardForce\Modules\Entries\Services\AIAgent;

use AwardForce\Modules\AIAgents\Boundary\ResourceType;
use AwardForce\Modules\Entries\Enums\AIFieldContext;
use AwardForce\Modules\Entries\Enums\AIFieldTrigger;
use AwardForce\Modules\Entries\Models\Entry;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class EntryCompositeTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testMetadataReturnsCorrectStructure(): void
    {
        $composite = app(EntryComposite::class);
        $resourceId = 123;

        $result = $composite->metadata($resourceId);

        $this->assertIsArray($result);
        $this->assertArrayHasKey('entry_id', $result);
        $this->assertEquals($resourceId, $result['entry_id']);
    }

    public function testSupportsReturnsTrueForEntryResource(): void
    {
        $composite = app(EntryComposite::class);

        $result = $composite->supports(ResourceType::Entry);

        $this->assertTrue($result);
    }

    public function testSupportsReturnsFalseForNonEntryResources(): void
    {
        $composite = app(EntryComposite::class);
        $nonEntryResources = array_filter(
            ResourceType::cases(),
            static fn($resource) => $resource !== ResourceType::Entry
        );

        foreach ($nonEntryResources as $resource) {
            $this->assertFalse($composite->supports($resource));
        }
    }

    public function testItReturnsCorrectModel(): void
    {
        $entry = $this->muffin(Entry::class);
        $composite = app(EntryComposite::class);
        $model = $composite->model($entry->id);

        $this->assertInstanceOf(Entry::class, $model);
        $this->assertEquals($entry->id, $model->id);
    }

    public function testTriggersReturnsAllEnumCases(): void
    {
        $result = app(EntryComposite::class)->triggers();

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertContainsOnlyInstancesOf(AIFieldTrigger::class, $result);
    }

    public function testContextsReturnsAllEnumCases(): void
    {
        $result = app(EntryComposite::class)->contexts();

        $this->assertIsArray($result);
        $this->assertNotEmpty($result);
        $this->assertContainsOnlyInstancesOf(AIFieldContext::class, $result);
    }
}
