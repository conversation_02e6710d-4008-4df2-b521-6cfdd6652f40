<?php

namespace AwardForce\Modules\Entries\Commands;

use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use Platform\Events\EventDispatcher;

class MarkAsDuplicateAndArchiveHandler
{
    use EventDispatcher;

    /**
     * @var DuplicateRepository
     */
    protected $duplicateRepository;

    /**
     * @var EntryRepository
     */
    protected $entryRepository;

    public function __construct(DuplicateRepository $duplicateRepository, EntryRepository $entryRepository)
    {
        $this->duplicateRepository = $duplicateRepository;
        $this->entryRepository = $entryRepository;
    }

    public function handle(MarkAsDuplicateAndArchive $command)
    {
        $entries = $this->entryRepository->getByIds($command->entryIds);
        $primary = Duplicate::getForEntry($this->entryRepository->requireById($command->primary));

        foreach ($entries as $entry) {
            $child = Duplicate::getForEntry($entry);
            $child->markAsDuplicateAndArchive($primary);

            $this->dispatch($child->releaseEvents());
        }
    }
}
