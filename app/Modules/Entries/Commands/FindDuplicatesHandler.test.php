<?php

namespace AwardForce\Modules\Entries\Commands;

use AwardForce\Modules\Entries\Events\DuplicatesFound;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use Illuminate\Support\Facades\Event;
use Platform\Test\EventAssertions;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class FindDuplicatesHandlerTest extends BaseTestCase
{
    use Database;
    use EventAssertions;
    use Laravel;

    public function testItLoadsFormSettingsCorrectly()
    {
        Event::fake(DuplicatesFound::class);

        $form = FormSelector::get();
        $form->settings = FormSettings::create(['minimumSimilarityPercentage' => 90]);
        $form->save();

        app(FindDuplicatesHandler::class)->handle(new FindDuplicates($form->seasonId));

        Event::assertDispatched(DuplicatesFound::class);
    }
}
