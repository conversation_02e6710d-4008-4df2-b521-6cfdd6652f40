<?php

namespace AwardForce\Modules\Entries\View;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\View\PreviewSubmittable;
use Illuminate\Support\Str;

class PreviewEntry extends PreviewSubmittable
{
    public function model(): Entry
    {
        return $this->translator->translate($this->request->entry);
    }

    public function backButtonUrl()
    {
        $editRouteParam['entry'] = $this->model;
        $editRouteParam['tabSlug'] = $this->request->get('tabSlug');
        if ($this->request->routeIs(['entry.manager.*', 'entry-form.manager.*', 'entry.entrant.preview'])) {
            $previousUrl = $this->request->headers->get('referer', '');

            if (Str::contains($previousUrl, route('entry-form.manager.edit', $editRouteParam))) {
                return route('entry-form.manager.edit', $editRouteParam);
            }
            if (Str::contains($previousUrl, route('entry.manager.view', ['entry' => $this->model]))) {
                return route('entry.manager.view', ['entry' => $this->model]);
            }

            if (Str::contains($previousUrl, route('entry.manager.index'))) {
                return route('entry.manager.index');
            }

            if (Str::contains($previousUrl, route('grant-report.entrant.index'))) {
                return route('grant-report.entrant.index');
            }
        }

        return $this->model->isArchived() ? route('entry.entrant.index') : route('entry-form.entrant.edit', $editRouteParam);
    }

    public function plagiarismScans(): array
    {
        return $this->model->completedPlagiarismScans->forView();
    }

    public function showHiddenFields(): bool
    {
        return true;
    }

    public function displayMetadata(): bool
    {
        return true;
    }

    public function title(): string
    {
        return $this->model->title;
    }

    public function submittableTitle(): string
    {
        return trans('entries.titles.preview-entry');
    }
}
