<?php

namespace AwardForce\Modules\Entries\View\Manager;

use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Services\Duplicates\LocksFindDuplicates;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Http\Request;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;

class DuplicatesList extends View
{
    /** @var ColumnatorFactory */
    private $columnators;

    /** @var Request */
    private $request;

    /** @var EntryRepository */
    private $entries;

    /** @var DuplicateRepository */
    private $duplicates;

    /** @var LocksFindDuplicates */
    private $locker;

    public function __construct(
        ColumnatorFactory $columnators,
        Request $request,
        EntryRepository $entries,
        DuplicateRepository $duplicates,
        LocksFindDuplicates $locker
    ) {
        $this->columnators = $columnators;
        $this->request = $request;
        $this->entries = $entries;
        $this->duplicates = $duplicates;
        $this->locker = $locker;
    }

    public function primaries()
    {
        $search = new ColumnatorSearch($this->columnator);

        return $search->search();
    }

    public function columnator()
    {
        return $this->columnators->forArea($this->area(), $this->request->all());
    }

    public function area()
    {
        return 'manage_duplicates.search';
    }

    public function lastCompleted()
    {
        return $this->locker->completedAt();
    }

    public function unscanned()
    {
        return $this->entries->countUnscannedPotentialDuplicates(SeasonFilter::getId());
    }

    public function outOfDate()
    {
        return $this->duplicates->countOutOfDate(SeasonFilter::getId());
    }
}
