<?php

namespace AwardForce\Modules\Entries\View\Manager;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Entries\Services\Firebase\FirebaseConfigData;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Search\Services\SavedViews;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Contracts\Translation\Translator;
use Illuminate\Http\Request;
use Platform\Search\ColumnatorSearch;
use Platform\View\View;

class EntryList extends View
{
    use FirebaseConfigData;
    use SavedViews;

    public function __construct(private ColumnatorFactory $columnators, private Request $request)
    {
        $this->registerTranslations();
        $this->registerRoutes();
        $this->registerFirebaseConfig();
        $this->registerFirebaseTTL();
    }

    public function entries()
    {
        $search = new ColumnatorSearch($this->columnator);

        return $search->search();
    }

    public function columnator()
    {
        return $this->columnators->forArea($this->area(), $this->request->all());
    }

    public function area(): string
    {
        return 'manage_entries.search';
    }

    public function tooltipText(): Translator|string|array|null
    {
        return trans('entries.search.tooltip.keywords');
    }

    public function entryIds(): array
    {
        return $this->entries->pluck('id')->toArray();
    }

    public function canScheduleGrantReports(): bool
    {
        $season = SeasonFilter::get();

        return $season && ! $season->isArchived() && feature_enabled('grant_reports') && Consumer::can('create', 'Grants');
    }

    public function translations(): array
    {
        return translations_for_vue(Consumer::languageCode(), [
            'form.form.name.label',
            'miscellaneous.datepicker.tooltips',
        ]);
    }

    private function registerTranslations()
    {
        VueData::registerTranslations([
            'buttons.cancel',
            'buttons.continue',
            'collaboration.form.buttons.invite_more_collaborators',
            'collaboration',
            'entries.countdown',
            'entries.form.collaborators.label',
            'fields.configuration',
            'miscellaneous.optional',
            'users.buttons.send_invite',
            'users.form.email',
        ]);
    }

    private function registerRoutes()
    {
        VueData::registerRoutes([
            'collaborators.reinvite',
            'collaborators.owner',
            'collaborators.delete',
            'collaborators.invite',
            'collaborators.privilege.update',
        ]);
    }
}
