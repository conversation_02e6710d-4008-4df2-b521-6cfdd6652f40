<?php

namespace AwardForce\Modules\Entries\View\Manager;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Library\Search\Actions\FormInvitationAction;
use AwardForce\Modules\AllocationPayments\Traits\HasAllocationPayments;
use AwardForce\Modules\Audit\Data\EventLogRepository;
use AwardForce\Modules\Comments\Services\EntryManagerComments;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Documents\Search\Enhancers\Files;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Search\Columns\PaymentStatus;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\View\PreviewSubmittable;
use AwardForce\Modules\Forms\Forms\View\PreviewSubmittableFactory;
use AwardForce\Modules\Forms\Forms\View\SubmittableView;
use AwardForce\Modules\Funding\View\EntryAllocation;
use AwardForce\Modules\GrantReports\Composers\GrantReportLabels;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use Illuminate\Http\Request;
use Illuminate\Support\Collection;
use Illuminate\Support\HtmlString;
use Platform\Html\Tabular\Tab;
use Platform\Html\Tabular\Tabular;
use Route;

class ViewEntry extends SubmittableView
{
    use GrantReportLabels, HasAllocationPayments;

    protected function setSubmittable(Request $request): void
    {
        $this->submittable = $request->entry;
    }

    public function entry(): Entry
    {
        return $this->translator->translate($this->submittable);
    }

    public function entrant(): array
    {
        if ($entrant = $this->entry->entrant) {
            return [
                'id' => $entrant->id,
                'slug' => (string) $entrant->slug,
                'name' => $entrant->fullName(),
                'isDeleted' => is_null($entrant->currentMembership),
                'preferredContact' => $entrant->preferredContact(),
            ];
        }

        return [];
    }

    public function collaboratorLinks(): string
    {
        if (feature_disabled('collaboration')) {
            return '';
        }

        return $this->submittable->collaborators()->active()->with('user')->get()
            ->reject(fn(Collaborator $collaborator) => $this->submittable->ownedBy($collaborator->user))
            ->sortBy(fn(Collaborator $collaborator) => $collaborator->user->fullName())
            ->map(fn(Collaborator $collaborator) => (string) link_to_route('users.show', $collaborator->user->fullName(), ['user' => $collaborator->user]))
            ->implode(', ');
    }

    public function recusals()
    {
        return $this->recusals->getWildcards($this->submittable->id);
    }

    public function scoreableFields(): Collection|\Platform\Database\Eloquent\Collection
    {
        return $this->fields
            ->getAllScoreableWithTitleTranslatedByForm($this->submittable->getFormId(), categoryId: $this->submittable->getCategoryId())
            ->map(function (Field $field) {
                return (object) [
                    'title' => $field['title'],
                    'score' => $this->entry->scores[(string) $field->slug] ?? 0,
                    'maxScore' => $field->options->maxScore(),
                ];
            });
    }

    public function canManageDuplicates()
    {
        return feature_enabled('manage_duplicates');
    }

    public function canRequestGrantReports()
    {
        return feature_enabled('grant_reports') && Consumer::can('create', 'Grants');
    }

    public function canScheduleGrantReports(): bool
    {
        return $this->canRequestGrantReports() && ! $this->entry->season->isArchived();
    }

    public function orders(): Collection
    {
        $prices = $this->translator->translate($this->prices->getForSeason($this->entry()->seasonId))->keyBy('id');

        return $this->entry()->orders->map(function (Order $order) use ($prices) {
            $item = $order->orderItems->where('entry_id', $this->entry()->id)->first();

            return [
                'slug' => $order->slug,
                'invoice' => invoice_no($order),
                'amount' => format_amount($order->getTotal()),
                'status' => $order->paymentStatus ?: 'in_progress',
                'price' => $prices->get($item->priceId)->title ?? null,
            ];
        });
    }

    public function plagiarismDetection()
    {
        return $this->integrations->activePlagiarismDetection($this->entry->seasonId);
    }

    public function plagiarismScans()
    {
        if (! is_null($this->plagiarismDetection)) {
            return $this->plagiarismScans->getValidForEntry($this->plagiarismDetection->id, $this->entry()->id);
        }
    }

    public function status()
    {
        $orders = PaymentStatus::orders($this->entry())->all();

        return new HtmlString(view('entry.manager.search.entry-status', ['entry' => $this->entry, 'orders' => $orders]));
    }

    public function duplicates()
    {
        if ($primary = $this->primaryEntry) {
            return $this->duplicates->duplicatesForEntry($primary->duplicate);
        }

        return [];
    }

    public function primaryEntry()
    {
        return $this->entry->duplicate ? $this->entry->duplicate->primaryEntry() : null;
    }

    public function canArchive(): bool
    {
        if ($primary = $this->primaryEntry) {
            return ! $primary->inProgress();
        }

        return false;
    }

    public function contracts(): array
    {
        return translate($this->contracts->getForEntries($this->entry->id))->contracts();
    }

    public function reportForms(): array
    {
        return translate($this->forms->getAllForSeason($this->entry->season, Form::FORM_TYPE_REPORT))
            ->map(function (Form $reportForm) {
                return [
                    'id' => $reportForm->id,
                    'name' => lang($reportForm, 'name'),
                ];
            })
            ->sortBy('name')
            ->values()
            ->all();
    }

    public function grantReports(): array
    {
        return $this->grantReports->getAllByEntry($this->entry->id)
            ->map(function (GrantReport $grantReport) {
                return $grantReport->forVue();
            })
            ->values()
            ->all();
    }

    public function contentBlocks(): array
    {
        return translate($this->contentBlocks->getAllByKey('contract'))
            ->map(function (ContentBlock $contentBlock) {
                return [
                    'id' => $contentBlock->id,
                    'slug' => (string) $contentBlock->slug,
                    'title' => lang($contentBlock, 'title'),
                ];
            })
            ->sortBy('title')
            ->values()
            ->all();
    }

    public function contractLabels(): array
    {
        return [
            'contract' => trans('entries.contracts.table.columns.contract'),
            'signed' => trans('entries.contracts.table.columns.signed'),
            'unsigned' => trans('entries.contracts.unsigned'),
            'confirmation' => trans('entries.contracts.confirmation'),
            'saveError' => trans('entries.contracts.errors.save'),
            'deleteError' => trans('entries.contracts.errors.delete'),
            'add' => trans('buttons.add_contract'),
            'save' => trans('buttons.save'),
            'cancel' => trans('buttons.cancel'),
            'continue' => trans('buttons.continue'),
            'delete' => trans('buttons.delete'),
        ];
    }

    public function contractRoutes(): array
    {
        return routes_for_vue([
            'entry.manager.add-contract',
            'entry.manager.delete-contract',
            'entry.manager.contract-pdf',
        ]);
    }

    public function reportLabels(): array
    {
        return array_merge([
            'grant_report' => trans('grant_reports.entry_manager.title'),
            'due_date' => trans('grant_reports.entry_manager.due_date'),
            'confirmation' => trans('grant_reports.entry_manager.confirmation'),
            'add' => trans('grant_reports.entry_manager.add'),
            'save' => trans('buttons.save'),
            'cancel' => trans('buttons.cancel'),
            'continue' => trans('buttons.continue'),
            'delete' => trans('buttons.delete'),
            'status' => trans('grant_reports.table.status'),
            'preview' => trans('grant_reports.entry_manager.preview'),
            'edit' => trans('grant_reports.entry_manager.edit'),
            'updateDueDate' => trans('grant_reports.entry_manager.update_due_date'),
            'delete_confirmation' => trans('grant_reports.entry_manager.delete_confirmation'),
            'in_progress' => trans('grant_reports.status.in_progress'),
            'overdue' => trans('grant_reports.status.overdue'),
            'submitted' => trans('grant_reports.status.submitted'),
            'scheduled' => trans('grant_reports.status.scheduled'),
        ], $this->labels());
    }

    public function reportRoutes(): array
    {
        return routes_for_vue([
            'grant-report.update.due_date',
            'grant-report.create',
            'grant-report.delete',
            'grant-report.manager.preview',
            'grant-report.entrant.preview',
            'grant-report.manager.edit',
        ]);
    }

    public function managerComments()
    {
        return (new EntryManagerComments)->forEntryView($this->entry);
    }

    public function fundingTranslations(): array
    {
        return translations_for_vue(Consumer::languageCode(), [
            'documents.actions.create',
            'funding.titles.allocation.entry',
            'funding.table.columns.fund',
            'allocation-payments.table.columns.allocated',
            'allocation-payments.table.columns.paid',
            'funding.actions.add-allocation',
            'buttons.edit',
            'buttons.save',
            'buttons.cancel',
            'funding.table.total',
            'buttons.edit',
            'buttons.delete',
            'buttons.comment',
            'buttons.continue',
            'funding.actions.add-allocation',
            'allocation-payments.buttons.payment_schedule',
            'miscellaneous.alerts.delete.comment',
            'miscellaneous.perform_actions',
        ]);
    }

    public function canDisplayAllocations(): bool
    {
        return ! (feature_disabled('fund_management') || $this->entryAllocation()->notAllowed || $this->entryAllocation()->funds->isEmpty());
    }

    public function allocationPermissions(): array
    {
        return [
            'canViewPayments' => \Consumer::can('view', 'Payments'),
            'canCreateDocument' => feature_enabled('documents') && \Consumer::can('create', 'Documents'),
            'canUpdateOrDelete' => $this->entryAllocation()->canUpdateOrDelete(),
            'canCreate' => $this->entryAllocation()->canCreate(),
            'canUpdate' => $this->entryAllocation()->canUpdate(),
            'canDelete' => $this->entryAllocation()->canDelete(),
        ];
    }

    public function existingFunds(): array
    {
        return $this->entryAllocation()->fundSummaryList();
    }

    public function funding(): array
    {
        $funding = $this->entryAllocation()->toArray();

        $funding['messages'] = [
            'error' => trans('miscellaneous.alerts.generic'),
        ];
        $funding['routes'] = [
            'index' => route('funding.entry-allocations.index', $this->entry()),
            'add' => route('funding.allocation.add', $this->entry()),
            'update' => route('funding.allocation.update', $this->entry()),
            'show' => str_replace('{entry}', (string) $this->entry()->slug, Route::getRoutes()->getByName('funding.allocation.show')->uri()),
            'delete' => route('funding.allocation.delete', $this->entry()),
        ];

        return $funding;
    }

    public function entryAllocation(): EntryAllocation
    {
        return app(EntryAllocation::class);
    }

    public function tabular()
    {
        $tabs = [
            'overview' => new Tab(trans('entries.tabs.manage.overview'), config('tabs.entry.manage.overview')),
            'entry' => new Tab(trans('entries.tabs.manage.entry'), config('tabs.entry.manage.entry')),
            'review-flow' => new Tab(trans('entries.tabs.manage.review-flow'), config('tabs.entry.manage.review-flow')),
            'review' => new Tab(trans('entries.tabs.manage.review'), config('tabs.entry.manage.review')),
            'grant' => new Tab(trans('entries.tabs.manage.grant'), config('tabs.entry.manage.grant')),
            'documents' => new Tab(trans('documents.titles.main'), config('tabs.entry.manage.documents')),
        ];

        $tabs[$this->request->get('tab', 'overview')]->setActive();

        $tabular = new Tabular($this->request, 'normal');
        $tabular->addTab($tabs['overview']);
        $tabular->addTab($tabs['entry']);
        $tabular->addTab($tabs['review-flow']);
        $tabular->addTab($tabs['review']);

        if (has_active_grant_features()) {
            $tabular->addTab($tabs['grant']);
        }

        if (feature_enabled('documents') && Consumer::can('view', 'Documents')) {
            $tabular->addTab($tabs['documents']);
        }

        if (Consumer::can('view', 'Audit')) {
            $tabular->addTab(new Tab(trans('entries.tabs.manage.audit'), 'entry.manager.tabs.audit'));
        }

        return $tabular;
    }

    public function logs()
    {
        return app(EventLogRepository::class)->forResource('entry', $this->entry->id);
    }

    public function preview(): ?PreviewSubmittable
    {
        return PreviewSubmittableFactory::createFromRequest($this->request);
    }

    public function reviewTasksList()
    {
        return app(EntryReviewTasksList::class);
    }

    public function documents(): Collection
    {
        $documents = translate($this->documents->forEntry($this->entry()));

        app(Files::class)->enhance($documents);

        return $documents;
    }

    public function editDocumentLabels(): array
    {
        return [
            'button' => trans_elliptic('documents.actions.edit'),
            'cancel' => trans('buttons.cancel'),
            'title' => trans('buttons.save'),
            'documentName' => trans('documents.form.document_name.label'),
            'shared' => trans('documents.form.shared.label'),
        ];
    }

    public function documentDeleteLabels(): array
    {
        return [
            'button' => trans('buttons.delete'),
            'ok' => trans('buttons.ok'),
            'cancel' => trans('buttons.cancel'),
        ];
    }

    public function translations(): void
    {
        VueData::registerTranslations([
            'miscellaneous.copied_clipboard',
            'files.metadata.title',
            'files.metadata.labels.size',
            'files.metadata.not_found',
            'form.form.name.label',
        ]);
    }

    public function routes(): void
    {
        VueData::registerRoutes([
            'file.metadata',
        ]);
    }

    public function invitationConfig(): array
    {
        return (new FormInvitationAction('forms', 'Forms', true))->viewData($this->entry->user);
    }
}
