<?php

namespace AwardForce\Modules\Entries\Repositories;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Database\Eloquent\Caching\HasFlexibleCache;
use AwardForce\Library\Database\Eloquent\NonRestrictedRepository;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Database\Repository\Builder\HasSlugBuilder;
use AwardForce\Modules\Assignments\Services\SyncFilter;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Query\UserOrCollaborator;
use AwardForce\Modules\Exports\Models\Exportable;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ImplementsSearchByField;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\WithValues;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Database\Repositories\RepositoryWithValues;
use AwardForce\Modules\Forms\Forms\Database\Behaviours\DeletedForms;
use AwardForce\Modules\Forms\Forms\Traits\HasFormBuilder;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Panels\Models\Panel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Platform\Database\Eloquent\Archivable;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Eloquent\Collection;
use Platform\Database\Eloquent\HasQueryBuilder;
use Platform\Search\SearchFilterCollection;

class EloquentEntryRepository extends Repository implements BuilderRepository, EntryRepository, RepositoryWithValues
{
    use Archivable;
    use DeletedForms;
    use Exportable;
    use HasFlexibleCache;
    use HasFormBuilder;
    use HasQueryBuilder;
    use HasSlugBuilder;
    use ImplementsSearchByField;
    use WithValues;

    /**
     * Make sure we assign the required model.
     */
    public function __construct(Entry $model)
    {
        $this->model = $model;
    }

    /**
     * Returns entries for the specified user.
     *
     * @param  int  $userId
     * @return Collection
     */
    public function getForUser($userId)
    {
        return $this->getQuery()->whereUserId($userId)->get();
    }

    public function user(int $userId): self
    {
        $this->query()->where('entries.user_id', $userId);

        return $this;
    }

    public function userOrCollaborator(int $userId): self
    {
        $this->query()->tap(new UserOrCollaborator($userId));

        return $this;
    }

    /**
     * Retrieve a collection of entries for a given user id, loading the given relations
     *
     * @return mixed
     */
    public function getForUserWithRelations(int $userId, array $relations)
    {
        return $this->getQuery()
            ->whereUserId($userId)
            ->with($relations)
            ->get();
    }

    /**
     * Fetch an entry and its related constituents by its slug.
     *
     * @param  string  $slug
     * @return mixed
     *
     * @throws ModelNotFoundException
     */
    public function requireBySlug($slug)
    {
        $entry = $this->getQuery()
            ->with('category', 'chapter')
            ->whereSlug($slug)
            ->first();

        if (! $entry) {
            $exception = with(new ModelNotFoundException)->setModel(get_class($this->model));

            throw $exception;
        }

        return $entry;
    }

    /**
     * Returns entries for the specified user.
     *
     * @param  int  $userId
     * @return Collection
     */
    public function getTrashedForUser($userId)
    {
        return $this->getQuery()->onlyTrashed()->whereUserId($userId)->get();
    }

    /**
     * Returns the trashed entries count for the specified user.
     *
     * @param  int  $userId
     */
    public function countTrashedForUser($userId): int
    {
        return $this->getQuery()->onlyTrashed()->whereUserId($userId)->count();
    }

    /**
     * Returns the number of entries owned by the user.
     *
     * @param  int  $seasonId
     * @return int
     */
    public function countUserEntries(User $user, $seasonId)
    {
        return $this->getQuery()->whereUserId($user->id)->whereSeasonId($seasonId)->count();
    }

    /**
     * Returns the number of entries owned by the user for a particular account.
     *
     * @param  int  $seasonId
     * @return int
     */
    public function countUserEntriesForAccount(User $user, $accountId)
    {
        return $this->getQuery()->whereUserId($user->id)->whereAccountId($accountId)->count();
    }

    /**
     * Returns the entries in the specified category, optionally filtered by one or more divisions.
     *
     *     ->getForCategory($category->id); // all divisions
     *     ->getForCategory($category->id, 2); // division 2
     *     ->getForCategory($category->id, [3, 4]); // divisions 3 & 4
     *
     * @param  int  $categoryId
     * @param  bool  $submitted
     * @param  int|array  $division
     * @return \Illuminate\Support\Collection
     */
    public function getForCategory($categoryId, $submitted = false, $division = null)
    {
        $query = $this->getQuery()
            ->whereCategoryId($categoryId);

        if ($submitted) {
            $query->whereNotNull('submitted_at');
        }

        if ($division && is_array($division)) {
            $query->whereIn('division', $division);
        } elseif ($division) {
            $query->whereDivision($division);
        }

        return $query->get();
    }

    /**
     * Returns the volume of submitted entries per period, up to the limit.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @param  int  $limit
     * @param  string  $period
     * @return mixed
     */
    public function getSubmittedEntryVolume($seasonId, $formId, $limit, $period)
    {
        [$convertOffset, $startingOffset] = $this->getTimezoneOffsets();

        $query = $this->getQuery()
            ->select(DB::raw('COUNT(submitted_at) AS total'), DB::raw('DATE(CONVERT_TZ(submitted_at,"'.$startingOffset.'","'.$convertOffset.'")) AS date'))
            ->whereNotNull('submitted_at')
            ->where('submitted_at', '>=', $this->volumeLimitFromDate($limit, $period))
            ->whereNull('deleted_at')
            ->whereHas('category', function ($query) {
                $query
                    ->onlyLeaves()
                    ->whereNull('deleted_at');
            })
            ->whereHas('chapter', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->groupBy('date')
            ->orderBy('submitted_at');

        if ($seasonId) {
            $query->whereSeasonId($seasonId);
        }

        if ($formId) {
            $query->whereFormId($formId);
        }

        return $query->get();
    }

    /**
     * Returns the volume of created entries per period, up to the limit.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @param  int  $limit
     * @param  string  $period
     * @return mixed
     */
    public function getCreatedEntryVolume($seasonId, $formId, $limit, $period)
    {
        [$convertOffset, $startingOffset] = $this->getTimezoneOffsets();

        $query = $this->getQuery()
            ->select(DB::raw('COUNT(created_at) AS total'), DB::raw('DATE(CONVERT_TZ(created_at,"'.$startingOffset.'","'.$convertOffset.'")) AS date'))
            ->where('created_at', '>=', $this->volumeLimitFromDate($limit, $period))
            ->whereNull('deleted_at')
            ->whereHas('category', function ($query) {
                $query
                    ->onlyLeaves()
                    ->whereNull('deleted_at');
            })
            ->whereHas('chapter', function ($query) {
                $query->whereNull('deleted_at');
            })
            ->groupBy('date')
            ->orderBy('created_at');

        if ($seasonId) {
            $query->whereSeasonId($seasonId);
        }

        if ($formId) {
            $query->whereFormId($formId);
        }

        return $query->get();
    }

    /**
     * Returns the total number of entries in progress for the season.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @return int
     */
    public function totalInProgress($seasonId, $formId)
    {
        return $this->getQuery()
            ->whereNull('submitted_at')
            ->whereNull('deleted_at')
            ->whereHas('category', fn($query) => $query->whereNull('deleted_at')
                ->onlyLeaves()
                ->when($seasonId, fn(Builder $query) => $query->where('season_id', $seasonId))
                ->when($formId, fn(Builder $query) => $query->where('form_id', $formId))
            )
            ->whereHas('chapter', fn($query) => $query->whereNull('deleted_at')
                ->when($seasonId, fn(Builder $query) => $query->where('entries.season_id', $seasonId))
            )
            ->when($seasonId, fn(Builder $query) => $query->where('season_id', $seasonId))
            ->when($formId, fn(Builder $query) => $query->where('form_id', $formId))
            ->count();
    }

    /**
     * Returns the total number of entries that have been submitted for the season.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @return int
     */
    public function totalSubmitted($seasonId, $formId)
    {
        $query = $this->getQuery()
            ->whereNotNull('submitted_at')
            ->whereNull('deleted_at')
            ->whereHas('category', function ($query) use ($seasonId, $formId) {
                $query
                    ->onlyLeaves()
                    ->whereNull('deleted_at');

                if ($seasonId) {
                    $query->where('season_id', $seasonId);
                }

                if ($formId) {
                    $query->where('form_id', $formId);
                }
            })
            ->whereHas('chapter', function ($query) use ($seasonId, $formId) {
                $query->whereNull('deleted_at');

                if ($seasonId) {
                    $query->where('season_id', $seasonId);
                }

                if ($formId) {
                    $query->where('form_id', $formId);
                }
            });

        if ($seasonId) {
            $query->whereSeasonId($seasonId);
        }

        if ($formId) {
            $query->whereFormId($formId);
        }

        return $query->count();
    }

    /**
     * @return mixed
     */
    private function volumeLimitFromDate($limit, $period)
    {
        if (! in_array($period, ['days', 'weeks', 'months'])) {
            $period = 'days';
        }

        $method = 'sub'.ucfirst($period);
        $from = Carbon::now()->$method($limit)->format('Y-m-d h:i:s');

        return $from;
    }

    /** ToDo: check this
     * Return the entry that matches the provided slug.
     *
     * @return mixed
     */
    public function viewFromApi(string $slug)
    {
        return $this->getQuery()
            ->with('attachments', 'attachments.file')
            ->whereSlug($slug)
            ->first();
    }

    /**
     * Return all entries based on the ids provided, and relationships for attachments.
     *
     * @param  array  $with
     * @return mixed
     */
    public function forThumbnails(array $entryIds)
    {
        return $this->getByIds($entryIds)->load('attachments');
    }

    /**
     * Returns all the entries a user has in the given season.
     *
     * @return mixed
     */
    public function getForUserInSeason(int $userId, int $seasonId)
    {
        return $this->getQuery()
            ->whereUserId($userId)
            ->whereSeasonId($seasonId)
            ->get();
    }

    /**
     * Returns all the entries a user has in the given category.
     *
     * @return mixed
     */
    public function getForUserInCategory(int $userId, int $categoryId)
    {
        return $this->getQuery()
            ->whereUserId($userId)
            ->whereCategoryId($categoryId)
            ->get();
    }

    /**
     * Returns an iterator over a chunked collection of entries for the specified IDs,
     * with the related recorded loaded and translated, ready for an export.
     *
     * @param  array  $ids
     * @return \Iterator  containing \Illuminate\Support\Collection
     */
    public function yieldForExport($ids): \Iterator
    {
        $query = $this->getQuery()
            ->whereIn('id', $ids)
            ->with('category', 'tags', 'chapter');

        $results = $query->forPage($page = 1, $count = 10)->get();

        while (count($results) > 0) {
            yield translate($results);

            $results = $query->forPage($page += 1, $count)->get();
        }
    }

    /**
     * Returns a collection of entries with categories given an array of IDs.
     *
     * @return mixed
     */
    public function getByIdsWithCategories(array $ids)
    {
        return $this->getQuery()
            ->with('category', 'chapter')
            ->whereIn('id', $ids)
            ->get();
    }

    /**
     * Returns a collection of entries with categories given an array of IDs.
     *
     * @return mixed
     */
    public function getIdsWithCategoriesExcludingInvited(array $ids): Collection
    {
        return $this->getQuery()
            ->with('category', 'chapter')
            ->whereIn('id', $ids)
            ->whereNull('invited_at')
            ->get();
    }

    /**
     * Returns an entry with categories given an ID.
     *
     * @param  int  $id
     * @return mixed
     */
    public function getOneByIdWithCategories($id)
    {
        return $this->getQuery()
            ->with('category', 'chapter')
            ->where('id', $id)
            ->first();
    }

    /**
     * Returns a raw collection of results from the search filters
     */
    public function getRawByFilters(SearchFilterCollection $filters): \Illuminate\Support\Collection
    {
        return collect($this->applyFilters($filters)->get());
    }

    /**
     * Returns all entries that match the Panel.
     */
    public function forPanel(Panel $panel, SyncFilter $filter): Collection
    {
        return $this->forPanelQuery($panel, $filter)->get();
    }

    public function countForPanel(Panel $panel): int
    {
        return $this->forPanelQuery($panel, new SyncFilter($panel->scoreSet))->count();
    }

    public function forPanelQuery(Panel $panel, SyncFilter $filter): Builder
    {
        $chapters = $panel->chapters->just('id');
        $categories = $panel->categories->pluck('pivot.divisions', 'id');

        if (! count($chapters) || ! count($categories)) {
            return $this->getQuery()->whereRaw('0=1');
        }

        $query = $this->getQuery()
            ->whereNotNull('submitted_at')
            ->whereIn('chapter_id', $chapters);

        // Categories
        $query->where(function ($query) use ($categories) {
            // non-division categories
            if ($ids = $categories->where(null, null)->keys()->all()) {
                $query->whereIn('category_id', $ids);
            }

            // OR division categories
            $categories->filter()->each(function ($divisions, $id) use ($query) {
                $query->orWhere(function ($query) use ($divisions, $id) {
                    $query->where('category_id', $id)
                        ->whereIn('division', array_filter(explode(',', $divisions)));
                });
            });
        });

        // Moderation status
        if ($panel->isModerated()) {
            $query->where('moderation_status', Entry::MODERATION_STATUS_APPROVED);
        } else {
            $query->where('moderation_status', '!=', Entry::MODERATION_STATUS_REJECTED);
        }

        // Archived entries
        if (! $panel->includesArchivedEntries()) {
            $query->whereNull('archived_at');
        }

        // Tags
        $this->applyTagFilterLogic($query, $panel);

        // Filter
        if ($whitelist = $filter->entries()) {
            $query->whereIn('id', $whitelist);
        }

        return $query;
    }

    /**
     * Apply complex tag logic (any of/all of/none of tags) to the query.
     */
    private function applyTagFilterLogic($query, Panel $panel)
    {
        $tagIds = $panel->tags->just('id');

        if ($panel->tagMatch === Panel::TAGS_ANY && $tagIds) {
            $query->whereHas('tags', function ($query) use ($tagIds) {
                $query->whereIn('tag_id', $tagIds);
            });
        } elseif ($panel->tagMatch === Panel::TAGS_ALL && $tagIds) {
            $query->withCount(['tags' => function ($query) use ($tagIds) {
                $query->whereIn('tag_id', $tagIds);
            }])->having('tags_count', count($tagIds));
        } elseif ($panel->tagMatch === Panel::TAGS_NONE && $tagIds) {
            $query->whereDoesntHave('tags', function ($query) use ($tagIds) {
                $query->whereIn('tag_id', $tagIds);
            });
        }
    }

    /**
     * Returns an Entry with matching slug, loading essential relations.
     *
     * @param  string  $slug
     * @return Entry
     */
    public function requireBySlugWithRelations($slug, array $relations = [])
    {
        return $this->getByQuery('slug', $slug)
            ->with($relations)
            ->first();
    }

    /**
     * @return mixed
     */
    public function inProgressEntryFees(int $seasonId, string $defaultCurrency)
    {
        return $this->getQuery()
            ->join('categories', 'categories.id', '=', 'entries.category_id')
            ->select(DB::raw(
                "SUM(JSON_EXTRACT((
                    SELECT price_amounts.amounts
                    FROM price_amounts
                    JOIN prices ON prices.id = price_amounts.price_id AND prices.default = 1
                    WHERE prices.season_id = entries.season_id AND
                        (
                            (
                                price_amounts.category_id IN (categories.id, categories.parent_id) AND
                                price_amounts.chapter_id = entries.chapter_id
                            ) OR
                            (
                                price_amounts.chapter_id = entries.chapter_id AND
                                price_amounts.category_id IS NULL
                            ) OR
                            (
                                price_amounts.category_id IN (categories.id, categories.parent_id) AND
                                price_amounts.chapter_id IS NULL
                            ) OR
                            (
                                price_amounts.chapter_id IS NULL AND
                                price_amounts.category_id IS NULL
                            )
                        )
                    LIMIT 1
                ), '$.{$defaultCurrency}')) AS total"
            ))
            ->where('entries.season_id', $seasonId)
            ->whereNull('entries.submitted_at')
            ->value('total');
    }

    /**
     * @return mixed
     */
    public function getAllEntryIdsForUser(int $userId)
    {
        return $this->getQuery()
            ->withTrashed()
            ->select('entries.id')
            ->where('entries.user_id', $userId)
            ->pluck('id')
            ->toArray();
    }

    /**
     * @return mixed
     */
    public function getAllEntryIdsForSeason(int $seasonId)
    {
        return $this->getQuery()
            ->withTrashed()
            ->select('entries.id')
            ->where('entries.season_id', $seasonId)
            ->pluck('id')
            ->toArray();
    }

    /**
     * Retrieves all entries (irrespective of account) that has any of the associated tags.
     *
     * @return mixed
     *
     * @throws NonRestrictedRepository
     */
    public function getEntriesWithAnyTags(array $tagIds)
    {
        return $this->relaxed(
            fn() => $this->getQuery()
                ->select('entries.id', 'entries.season_id')
                ->joinTaggables()
                ->whereIn('taggables.tag_id', $tagIds)
                ->groupBy('entries.id')
        );
    }

    /**
     * Retrieves entries that match all given tags.
     *
     * @return mixed
     */
    public function getEntriesWithAllTags(array $tagIds)
    {
        return $this->getQuery()
            ->select('entries.id', 'entries.season_id', \DB::raw('COUNT(taggables.taggable_id) AS num_tags'))
            ->joinTaggables()
            ->whereIn('taggables.tag_id', $tagIds)
            ->groupBy('entries.id')
            ->having('num_tags', count($tagIds))
            ->get();
    }

    /** ToDo: check this
     * Finds entries in the given season that should be scanned for plagiarism and runs the given callback on each.
     *
     * @return mixed
     */
    public function findForPlagiarismScan(int $seasonId, callable $callback)
    {
        return $this->getQuery()
            ->with('attachments.file')
            ->whereSeasonId($seasonId)
            ->whereNull('entries.deleted_at')
            ->whereNull('entries.archived_at')
            ->where('entries.moderation_status', '<>', Entry::MODERATION_STATUS_REJECTED)
            ->whereNotNull('entries.submitted_at')
            ->chunk(100, function ($entries) use ($callback) {
                $entries->each(function ($entry) use ($callback) {
                    $callback($entry);
                });
            });
    }

    protected function getQueryForSearchByField(Field $field): Builder
    {
        return $this->getQuery()
            ->whereSeasonId($field->seasonId);
    }

    public function getSeasonedQuery(Field $field): Builder
    {
        return $field->seasonal ? $this->getQuery()->whereSeasonId($field->seasonId) : $this->getQuery();
    }

    /**
     * Return cursor with entries in the given season that should be scanned as potential duplicates.
     *
     * @return mixed
     */
    public function potentialDuplicates(int $seasonId, int $primaryCount = 0)
    {
        return $this->getQuery()
            ->select(['entries.id', 'entries.account_id', 'entries.season_id', 'entries.category_id', 'entries.title'])
            ->leftJoin('duplicate_entries', 'duplicate_entries.entry_id', '=', 'entries.id')
            ->where('entries.season_id', '=', $seasonId)
            ->whereNull('entries.deleted_at')
            ->whereNull('entries.archived_at')
            ->where(function ($query) {
                return $query->whereNull('duplicate_entries.id')->orWhereNull('duplicate_entries.confirmed_at');
            })->orderBy('entries.id', 'asc')
            ->limit(2000 + $primaryCount)
            ->cursor();
    }

    /**
     * Count how many potential duplicate entries have not been scanned in the given season.
     *
     * @return mixed
     */
    public function countUnscannedPotentialDuplicates(int $seasonId)
    {
        return $this->getQuery()
            ->leftJoin('duplicate_entries', 'duplicate_entries.entry_id', '=', 'entries.id')
            ->where('entries.season_id', '=', $seasonId)
            ->whereNull('entries.deleted_at')
            ->whereNull('entries.archived_at')
            ->whereNull('duplicate_entries.id')
            ->count();
    }

    /**
     * Get primary non archived, non deleted entries for active season based on title.
     *
     *
     * @return mixed
     */
    public function getPrimaryEntriesForSeason(int $seasonId, string $title)
    {
        $primaryLabel = trans('entries.duplicates.columns.primary');
        $localIdExpression = $this->model::localIdShortcodeConcatExpression('entries.local_id', 'translations.value');

        return $this->getQuery()
            ->select([
                'entries.id AS id',
                DB::raw("CONCAT(
                    entries.title,
                    ' (ID: ', IF(translations.value = '' OR translations.value IS NULL, entries.local_id, CONCAT($localIdExpression)) , ')',
                    IF(duplicate_entries.id IS NOT NULL AND duplicate_entries.parent_id IS NULL, ' ({$primaryLabel})', '')
                ) AS value"),
            ])->leftJoin('duplicate_entries', 'duplicate_entries.entry_id', '=', 'entries.id')
            ->join('categories', 'categories.id', '=', 'entries.category_id')
            ->join('translations', 'translations.foreign_id', '=', 'categories.id')
            ->where('entries.season_id', $seasonId)
            ->where('entries.title', 'LIKE', '%'.$title.'%')
            ->whereNull('entries.archived_at')
            ->whereNull('entries.deleted_at')
            ->where('translations.resource', '=', 'Category')
            ->where('translations.language', '=', consumer()->languageCode())
            ->where('translations.field', '=', 'shortcode')
            ->get();
    }

    public function allWithoutLocalId()
    {
        return $this->getQuery()
            ->whereNull('local_id')
            ->orderBy('created_at', 'asc')
            ->get();
    }

    private function getTimezoneOffsets()
    {
        return [
            substr(now()->timezone(Consumer::timezone() ?? 'UTC')->toAtomString(), -6),
            substr(now()->timezone(config('app.timezone'))->toAtomString(), -6),
        ];
    }

    /**
     * Get entries for which we can set resubmission_required
     */
    public function getForResubmissionByIds(array $ids = []): Collection
    {
        return $this->getQuery()
            ->whereIn('id', $ids)
            ->whereNotNull('submitted_at')
            ->get();
    }

    /**
     * Search for a list of users based on name.
     *
     * @return mixed
     */
    public function filterByTitle(string $title = '', int $limit = 8)
    {
        return $this->getQuery()
            ->select('entries.id', 'entries.slug', 'entries.title')
            ->where('title', 'LIKE', '%'.$title.'%')
            ->take($limit)
            ->get();
    }

    public function getBySlugs(array $slugs)
    {
        return $this->getQuery()
            ->whereIn('entries.slug', $slugs)
            ->get();
    }

    /**
     * {@inheritDoc}
     */
    public function getForFundAllocationByIds(array $ids = []): Collection
    {
        return $this->getQuery()
            ->select('entries.*')
            ->leftJoin('grant_statuses', 'grant_statuses.id', '=', 'entries.grant_status_id')
            ->whereRaw('!(grant_statuses.lock_fund_allocation <=> 1)')
            ->whereIn('entries.id', $ids)
            ->get();
    }

    public function eligibilityExistsForEntrant(int $entrantId, ?int $seasonId = null): bool
    {
        return $this->getQuery()
            ->whereSeasonId($seasonId ?: $this->activeSeasonId())
            ->whereUserId($entrantId)
            ->where(function ($query) {
                $query->whereNotNull('eligible_at')
                    ->orWhereNotNull('ineligible_at');
            })
            ->exists();
    }

    /**
     * Get the entries for the specified form.
     */
    public function filterIdsByForm(array|int $ids, int $formId): array
    {
        return $this->getQuery()
            ->whereIn('id', (array) $ids)
            ->where('form_id', $formId)
            ->pluck('id')
            ->toArray();
    }

    /**
     * Returns the entry linked to the specified Attachment.
     *
     * @return Entry
     */
    public function getFromAttachment(Attachment $attachment)
    {
        return $attachment->submittable;
    }

    public function existsForField(Field $field): bool
    {
        return $this->getQuery()->where("values->{$field->slug}", '!=', '')->whereNotNull("values->{$field->slug}")->exists();
    }

    public function getForField(Field $field): Collection
    {
        return $this->getQuery()->where("values->{$field->slug}", '!=', '')->whereNotNull("values->{$field->slug}")->get();
    }
}
