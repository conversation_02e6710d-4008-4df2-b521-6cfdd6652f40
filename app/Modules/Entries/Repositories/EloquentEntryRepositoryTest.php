<?php

namespace AwardForce\Modules\Entries\Repositories;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Assignments\Services\SyncFilter;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\Models\PriceAmount;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Settings\Enums\LocalIdShortcodeFormat;
use AwardForce\Modules\Settings\Services\Settings;
use AwardForce\Modules\Tags\Models\Tag;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Concerns\Passport;
use Tests\Library\Values\FindByField;

final class EloquentEntryRepositoryTest extends BaseTestCase
{
    use Database;
    use FindByField;
    use Laravel;
    use Passport;

    /**
     * @var EloquentEntryRepository
     */
    protected $repository;

    /**
     * Initiate the unit tests
     */
    public function init()
    {
        $this->repository = app(EloquentEntryRepository::class);
    }

    public function testGetForCategory(): void
    {
        $categoryOne = $this->muffin(Category::class);
        $categoryTwo = $this->muffin(Category::class, ['divisions' => 3]);

        // 2x Cat1
        $this->muffins(2, Entry::class, ['category_id' => $categoryOne->id]);

        // 1x Cat2 div 1
        $this->muffin(Entry::class, ['category_id' => $categoryTwo->id, 'division' => 1]);
        // 1x Cat2 div 2
        $this->muffin(Entry::class, ['category_id' => $categoryTwo->id, 'division' => 2]);
        // 2x Cat2 div 3
        $this->muffins(2, Entry::class, ['category_id' => $categoryTwo->id, 'division' => 3]);

        $this->assertCount(2, $this->repository->getForCategory($categoryOne->id));
        $this->assertCount(4, $this->repository->getForCategory($categoryTwo->id));
        $this->assertCount(1, $this->repository->getForCategory($categoryTwo->id, false, 1));
        $this->assertCount(1, $this->repository->getForCategory($categoryTwo->id, false, 2));
        $this->assertCount(2, $this->repository->getForCategory($categoryTwo->id, false, 3));
        $this->assertCount(3, $this->repository->getForCategory($categoryTwo->id, false, [1, 3]));
    }

    public function testGetForCategorySubmitted(): void
    {
        $category = $this->muffin(Category::class);

        $this->muffins(2, Entry::class, [
            'category_id' => $category->id,
            'submitted_at' => Carbon::now(),
        ]);
        $this->muffins(2, Entry::class, [
            'category_id' => $category->id,
        ]);

        $this->assertCount(4, $this->repository->getForCategory($category->id));
        $this->assertCount(2, $this->repository->getForCategory($category->id, true));
    }

    public function testTotalInProgress(): void
    {
        $null = function () {
            return null;
        }; // FactoryMuffin stupidity...
        $form = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $this->muffins(2, Entry::class, ['form_id' => $form->id, 'submitted_at' => $null]);         // 2x in progress
        $this->muffin(Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);     // 1x submitted
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id, 'submitted_at' => $null]);    // 1x deleted category
        $entry->category->delete();
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id, 'submitted_at' => $null]);    // 1x deleted chapter
        $entry->chapter->delete();
        $this->muffin(Entry::class, ['submitted_at' => $null]);                                     // 1x associated with a different form

        $this->assertEquals(0, $this->repository->totalInProgress($entry->seasonId, $form->id));
    }

    public function testTotalSubmitted(): void
    {
        $form = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $this->muffins(2, Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);         // 2x submitted
        $this->muffin(Entry::class, ['form_id' => $form->id, 'submitted_at' => function () {
            return null;
        }]);  // 1x in progress
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);    // 1x deleted category
        $entry->category->delete();
        $entry = $this->muffin(Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);    // 1x deleted chapter
        $entry->chapter->delete();
        $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);                                     // 1x associated with a different form

        $this->assertEquals(0, $this->repository->totalSubmitted($entry->seasonId, $form->id));
    }

    public function testTotalInProgressCountDoesNotIncludeEntriesWithDifferentFormIdThanCategory(): void
    {
        $form1 = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $form2 = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $category1 = $this->muffin(Category::class, ['form_id' => $form1->id]);
        $category2 = $this->muffin(Category::class, ['form_id' => $form2->id]);
        $chapter1 = $category1->chapters->first();

        $this->muffins(2, Entry::class, ['form_id' => $form1->id, 'category_id' => $category1->id,  'submitted_at' => null, 'chapter_id' => $chapter1->id]);
        $this->muffins(5, Entry::class, ['form_id' => $form1->id, 'category_id' => $category2->id,  'submitted_at' => null, 'chapter_id' => $chapter1->id]);

        $inProgress = $this->repository->totalInProgress($this->season->id, $form1->id);

        $this->assertEquals(2, $inProgress);
    }

    public function testTotalSubmittedCountDoesNotIncludeEntriesWithDifferentFormIdThanCategory(): void
    {
        $form1 = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $form2 = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $category1 = $this->muffin(Category::class, ['form_id' => $form1->id]);
        $category2 = $this->muffin(Category::class, ['form_id' => $form2->id]);
        $chapter1 = $category1->chapters->first();

        $this->muffins(2, Entry::class, ['form_id' => $form1->id, 'category_id' => $category1->id,  'submitted_at' => now(), 'chapter_id' => $chapter1->id]);
        $this->muffins(5, Entry::class, ['form_id' => $form1->id, 'category_id' => $category2->id,  'submitted_at' => now(), 'chapter_id' => $chapter1->id]);

        $submitted = $this->repository->totalSubmitted($this->season->id, $form1->id);

        $this->assertEquals(2, $submitted);
    }

    public function testEntryCompletionCountsDoesNotExcludeEntriesWithParentCategories(): void
    {
        $form = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $category = $this->muffin(Category::class, ['form_id' => $form->id]);
        $category2 = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);
        $category2->makeChildOf($category);
        $chapter = $category->chapters->first();

        $this->muffins(2, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $this->muffins(3, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => now(),
        ]);

        $this->muffins(2, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category2->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $this->muffins(4, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category2->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => now(),
        ]);

        $inProgress = $this->repository->totalInProgress($this->season->id, $category->formId);
        $submitted = $this->repository->totalSubmitted($this->season->id, $category->formId);

        $this->assertEquals(2, $inProgress);
        $this->assertEquals(4, $submitted);

        $this->assertNotEquals(4, $inProgress);
        $this->assertNotEquals(7, $submitted);
    }

    public function testGetSubmittedEntryVolume(): void
    {
        $form = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $this->setupUserWithRole('ProgramManager', true);

        // set a time that can result to three different dates through out the timezones
        Carbon::setTestNow(now()->setTime(10, 30));

        $entries = $this->muffins(2, Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);         // 1x submitted
        $entries[] = $entry = $this->muffin(Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);    // 1x deleted category
        $entry->category->delete();
        $entries[] = $entry = $this->muffin(Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);  // 1x deleted chapter
        $entry->chapter->delete();
        $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);                                                // 1x associated with a different form

        $result = $this->repository->getSubmittedEntryVolume($entry->seasonId, $form->id, 1, 'day');
        $utcDate = $result->first()->date;

        $this->assertSame(2, $result->first()->total);
        $this->assertSame(Carbon::now()->toDateString(), $utcDate);

        // -11:00
        app(Settings::class)->updateGeneralSettings(['timezone' => 'Pacific/Niue']);
        $result = $this->repository->getSubmittedEntryVolume($entry->seasonId, $form->id, 1, 'day');
        $niueDate = $result->first()->date;

        $this->assertSame(2, $result->first()->total);
        $this->assertNotSame($utcDate, $niueDate);
        $this->assertEquals(-1, Carbon::parse($utcDate)->diffInDays(Carbon::parse($niueDate), false));
        $this->assertSame(Carbon::parse($utcDate)->timezone(setting('timezone'))->toDateString(), $niueDate);

        foreach ($entries as $entry) {
            $entry->delete();
        }
        Carbon::setTestNow(now()->setTime(12, 30));

        $this->muffins(2, Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);

        // +12:00
        app(Settings::class)->updateSingleGeneralSetting('timezone', 'Pacific/Fiji');

        $result = $this->repository->getSubmittedEntryVolume($entry->seasonId, $form->id, 1, 'day');
        $kiriDate = $result->first()->date;

        $this->assertSame(2, $result->first()->total);
        $this->assertNotSame($utcDate, $kiriDate);
        $this->assertEquals(1, Carbon::parse($utcDate)->diffInDays(Carbon::parse($kiriDate), false));

        // set utc time
        $this->assertSame(Carbon::parse($utcDate.' 12:30')->timezone(setting('timezone'))->toDateString(), $kiriDate);

        // Reset testNow() and timezone
        Carbon::setTestNow();
        app(Settings::class)->updateSingleGeneralSetting('timezone', 'UTC');
    }

    public function testGetCreatedEntryVolume(): void
    {
        $form = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $this->setupUserWithRole('ProgramManager', true);

        // set a time that can result to three different dates through out the timezones
        Carbon::setTestNow(now()->setTime(10, 30));

        $entries = $this->muffins(2, Entry::class, ['form_id' => $form->id, 'created_at' => Carbon::now()]);         // today
        $entries[] = $entry = $this->muffin(Entry::class, ['form_id' => $form->id, 'created_at' => Carbon::now()->subDay(2)]);    // this week
        $entries[] = $this->muffin(Entry::class, ['form_id' => $form->id, 'created_at' => Carbon::now()->subDays(20)]); // 20 days ago
        $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]); // today, associated with a different form

        $result = $this->repository->getCreatedEntryVolume($entry->seasonId, $form->id, 1, 'day');
        $utcDate = $result->first()->date;

        $this->assertSame(2, $result->first()->total);
        $this->assertSame(Carbon::now()->toDateString(), $utcDate);

        // -11:00
        app(Settings::class)->updateSingleGeneralSetting('timezone', 'Pacific/Niue');

        $result = $this->repository->getCreatedEntryVolume($entry->seasonId, $form->id, 1, 'day');
        $niuDate = $result->first()->date;

        $this->assertSame(2, $result->first()->total);
        $this->assertNotSame($utcDate, $niuDate);
        $this->assertEquals(-1, Carbon::parse($utcDate)->diffInDays(Carbon::parse($niuDate), false));
        $this->assertSame(Carbon::parse($utcDate)->timezone(setting('timezone'))->toDateString(), $niuDate);

        foreach ($entries as $entry) {
            $entry->delete();
        }
        Carbon::setTestNow(now()->setTime(12, 30));

        $this->muffins(2, Entry::class, ['form_id' => $form->id, 'submitted_at' => Carbon::now()]);
        // +14:00
        app(Settings::class)->updateSingleGeneralSetting('timezone', 'Pacific/Fiji');

        $result = $this->repository->getCreatedEntryVolume($entry->seasonId, $form->id, 1, 'day');
        $fijiDate = $result->first()->date;

        $this->assertSame(2, $result->first()->total);
        $this->assertNotSame($utcDate, $fijiDate);
        $this->assertEquals(1, Carbon::parse($utcDate)->diffInDays(Carbon::parse($fijiDate), false));

        // set utc time
        $this->assertSame(Carbon::parse($utcDate.' 12:30')->timezone(setting('timezone'))->toDateString(), $fijiDate);

        // Reset testNow() and timezone
        Carbon::setTestNow();
        app(Settings::class)->updateSingleGeneralSetting('timezone', 'UTC');
    }

    public function testGetEntryVolumeWidgetDoNotIncludeEntriesWithParentCategories(): void
    {
        $form = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $category = $this->muffin(Category::class, ['form_id' => $form->id]);
        $category2 = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);
        $category2->makeChildOf($category);
        $chapter = $category->chapters->first();

        $this->muffins(2, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $this->muffins(3, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => now(),
        ]);

        $this->muffins(2, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category2->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $this->muffins(4, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category2->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => now(),
        ]);

        $createdEntryVolume = $this->repository->getCreatedEntryVolume($this->season->id, $form->id, 7, 'days');
        $submittedEntryVolume = $this->repository->getSubmittedEntryVolume($this->season->id, $form->id, 7, 'days');

        $totalSubmitted = Arr::get($submittedEntryVolume->first(), 'total');
        $totalCreated = Arr::get($createdEntryVolume->first(), 'total');

        $this->assertNotEquals(7, $totalSubmitted);
        $this->assertNotEquals(11, $totalCreated);
        $this->assertEquals(4, $totalSubmitted);
        $this->assertEquals(6, $totalCreated);
    }

    public function testGetForUserInSeason(): void
    {
        $this->muffin(Entry::class, ['user_id' => 1, 'season_id' => 1]);
        $this->muffin(Entry::class, ['user_id' => 1, 'season_id' => 1, 'deleted_at' => Carbon::now()]);
        $this->muffin(Entry::class, ['user_id' => 1, 'season_id' => 2]);

        $this->muffin(Entry::class, ['user_id' => 2, 'season_id' => 1]);

        $result = $this->repository->getForUserInSeason(1, 1);

        $this->assertCount(1, $result->all());
    }

    public function testGetForUserInCategory(): void
    {
        $this->muffin(Entry::class, ['user_id' => 1, 'category_id' => 1]);
        $this->muffin(Entry::class, ['user_id' => 1, 'category_id' => 1, 'deleted_at' => Carbon::now()]);

        $this->muffin(Entry::class, ['user_id' => 1, 'category_id' => 2]);

        $result = $this->repository->getForUserInCategory(1, 1);

        $this->assertCount(1, $result->all());
    }

    public function testCountUserEntriesForAccount(): void
    {
        $user = $this->muffin(User::class);
        $this->muffin(Entry::class, ['user_id' => $user->id, 'account_id' => 1]);
        $this->muffin(Entry::class, ['user_id' => $user->id, 'account_id' => 1]);
        $this->muffin(Entry::class, ['user_id' => $user->id, 'account_id' => 2]);
        $this->muffin(Entry::class, ['user_id' => $user->id + 1, 'account_id' => 1]);

        $result = $this->repository->relaxed(fn() => $this->repository->countUserEntriesForAccount($user, 1));

        $this->assertEquals(2, $result);
    }

    public function testForPanelWithChaptersOrCategories(): void
    {
        $panel = new Panel;

        $panel->setRelation('chapters', collect());
        $panel->setRelation('categories', collect());
        $this->assertEmpty($this->repository->forPanel($panel, new SyncFilter(new ScoreSet)));

        $panel->setRelation('chapters', collect([['id' => 1]]));
        $this->assertEmpty($this->repository->forPanel($panel, new SyncFilter(new ScoreSet)));

        $panel->setRelation('chapters', collect());
        $panel->setRelation('categories', collect([['id' => 1]]));
        $this->assertEmpty($this->repository->forPanel($panel, new SyncFilter(new ScoreSet)));
    }

    public function testForPanelSubmittedUnRejected(): void
    {
        $panel = $this->muffin(Panel::class, ['moderation_entries' => false]);
        $chapterId = $panel->chapters->first()->id;
        $categoryId = $panel->categories->first()->id;

        $this->muffin(Entry::class, ['chapter_id' => $chapterId, 'category_id' => $categoryId]);
        $this->newSubmittedEntry($chapterId, $categoryId);
        $this->newSubmittedEntry($chapterId, $categoryId, ['moderation_status' => Entry::MODERATION_STATUS_REJECTED]);

        $entries = $this->repository->forPanel($panel, new SyncFilter(new ScoreSet));

        $this->assertCount(1, $entries);
        $this->assertTrue($entries->first()->submitted());
    }

    protected function newSubmittedEntry(int $chapterId, int $categoryId, array $params = []): Entry
    {
        return $this->muffin(Entry::class, array_merge(
            ['submitted_at' => Carbon::now(), 'chapter_id' => $chapterId, 'category_id' => $categoryId],
            $params
        ));
    }

    public function testForPanelWithDivisions(): void
    {
        $panel = $this->muffin(Panel::class, ['moderation_entries' => false]);
        $chapterId = $panel->chapters->first()->id;
        $category = $panel->categories->first();
        $panel->categories()->sync([$category->id => ['divisions' => ',1,']]);

        $this->newSubmittedEntry($chapterId, $category->id, ['division' => 1]);
        $this->newSubmittedEntry($chapterId, $category->id, ['division' => 2]);

        $entries = $this->repository->forPanel($panel->fresh(), new SyncFilter(new ScoreSet));

        $this->assertCount(1, $entries);
        $this->assertEquals(1, $entries->first()->division);
    }

    public function testForPanelModerated(): void
    {
        $panel = $this->muffin(Panel::class, ['moderation_entries' => true]);
        $chapterId = $panel->chapters->first()->id;
        $categoryId = $panel->categories->first()->id;

        $this->newSubmittedEntry($chapterId, $categoryId, ['moderation_status' => Entry::MODERATION_STATUS_APPROVED]);
        $this->newSubmittedEntry($chapterId, $categoryId, ['moderation_status' => Entry::MODERATION_STATUS_UNDECIDED]);

        $entries = $this->repository->forPanel($panel, new SyncFilter(new ScoreSet));

        $this->assertCount(1, $entries);
        $this->assertTrue($entries->first()->isApproved());
    }

    public function testForPanelArchived(): void
    {
        $panel = $this->muffin(Panel::class, ['moderation_entries' => false, 'archived_entries' => false]);
        $chapterId = $panel->chapters->first()->id;
        $categoryId = $panel->categories->first()->id;

        $this->newSubmittedEntry($chapterId, $categoryId, ['archived_at' => Carbon::now()]);
        $this->newSubmittedEntry($chapterId, $categoryId);

        $entries = $this->repository->forPanel($panel, new SyncFilter(new ScoreSet));

        $this->assertCount(1, $entries);
        $this->assertFalse($entries->first()->isArchived());
    }

    public function testForPanelTagged(): void
    {
        $tag = $this->muffin(Tag::class);

        $panel = $this->muffin(Panel::class, ['moderation_entries' => false]);
        $panel->tags()->attach($tag);
        $chapterId = $panel->chapters->first()->id;
        $categoryId = $panel->categories->first()->id;

        $this->newSubmittedEntry($chapterId, $categoryId);
        $tagged = $this->newSubmittedEntry($chapterId, $categoryId);
        $tagged->tags()->attach($tag);

        $entries = $this->repository->forPanel($panel, new SyncFilter(new ScoreSet));

        $this->assertCount(1, $entries);
        $this->assertEquals($tagged->id, $entries->first()->id);
    }

    public function testForPanelWithAnyTags(): void
    {
        $tagA = $this->muffin(Tag::class, ['tag' => 'A']);
        $tagB = $this->muffin(Tag::class, ['tag' => 'B']);
        $tagC = $this->muffin(Tag::class, ['tag' => 'C']);

        $panel = $this->muffin(Panel::class, ['moderation_entries' => false, 'tag_match' => Panel::TAGS_ANY]);
        $chapterId = $panel->chapters->first()->id;
        $categoryId = $panel->categories->first()->id;
        $scoreSet = $panel->scoreSet;

        $panel->tags()->attach($tagA);
        $panel->tags()->attach($tagB);

        $entry1 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry2 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry3 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry4 = $this->newSubmittedEntry($chapterId, $categoryId);

        $entries = $this->repository->forPanel($panel, new SyncFilter($scoreSet));

        $this->assertEquals(0, $entries->count());

        $entry1->addTag($tagA);
        $entry2->addTag($tagA);
        $entry2->addTag($tagB);
        $entry2->addTag($tagC);
        $entry3->addTag($tagC);

        $entries = $this->repository->forPanel($panel, new SyncFilter($scoreSet));

        $this->assertEquals(2, $entries->count());
        $this->assertTrue($entries->pluck('id')->contains($entry1->id));
        $this->assertTrue($entries->pluck('id')->contains($entry2->id));
    }

    public function testForPanelWithAllTags(): void
    {
        $tagA = $this->muffin(Tag::class, ['tag' => 'A']);
        $tagB = $this->muffin(Tag::class, ['tag' => 'B']);
        $tagC = $this->muffin(Tag::class, ['tag' => 'C']);

        $panel = $this->muffin(Panel::class, ['moderation_entries' => false, 'tag_match' => Panel::TAGS_ALL]);
        $chapterId = $panel->chapters->first()->id;
        $categoryId = $panel->categories->first()->id;
        $scoreSet = $panel->scoreSet;

        $panel->tags()->attach($tagA);
        $panel->tags()->attach($tagB);

        $entry1 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry2 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry3 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry4 = $this->newSubmittedEntry($chapterId, $categoryId);

        $entries = $this->repository->forPanel($panel, new SyncFilter($scoreSet));

        $this->assertEquals(0, $entries->count());

        $entry1->addTag($tagA);
        $entry2->addTag($tagA);
        $entry2->addTag($tagB);
        $entry3->addTag($tagA);
        $entry3->addTag($tagB);
        $entry3->addTag($tagC);

        $entries = $this->repository->forPanel($panel, new SyncFilter($scoreSet));

        $this->assertEquals(2, $entries->count());
        $this->assertTrue($entries->pluck('id')->contains($entry2->id));
        $this->assertTrue($entries->pluck('id')->contains($entry3->id));
    }

    public function testForPanelWithNoneOfTags(): void
    {
        $tagA = $this->muffin(Tag::class, ['tag' => 'A']);
        $tagB = $this->muffin(Tag::class, ['tag' => 'B']);
        $tagC = $this->muffin(Tag::class, ['tag' => 'C']);

        $panel = $this->muffin(Panel::class, ['moderation_entries' => false, 'tag_match' => Panel::TAGS_NONE]);
        $chapterId = $panel->chapters->first()->id;
        $categoryId = $panel->categories->first()->id;
        $scoreSet = $panel->scoreSet;

        $panel->tags()->attach($tagA);
        $panel->tags()->attach($tagB);

        $entry1 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry2 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry3 = $this->newSubmittedEntry($chapterId, $categoryId);
        $entry4 = $this->newSubmittedEntry($chapterId, $categoryId);

        $entries = $this->repository->forPanel($panel, new SyncFilter($scoreSet));

        $this->assertEquals(4, $entries->count());

        $entry1->addTag($tagA);
        $entry2->addTag($tagA);
        $entry2->addTag($tagB);
        $entry2->addTag($tagC);
        $entry3->addTag($tagC);

        $entries = $this->repository->forPanel($panel, new SyncFilter($scoreSet));

        $this->assertEquals(2, $entries->count());
        $this->assertTrue($entries->pluck('id')->contains($entry3->id));
        $this->assertTrue($entries->pluck('id')->contains($entry4->id));
    }

    public function testForPanelFiltered(): void
    {
        $panel = $this->muffin(Panel::class, ['moderation_entries' => false]);
        $chapterId = $panel->chapters->first()->id;
        $categoryId = $panel->categories->first()->id;

        $entry = $this->newSubmittedEntry($chapterId, $categoryId);
        $this->newSubmittedEntry($chapterId, $categoryId);

        $entries = $this->repository->forPanel($panel, new SyncFilter($panel->scoreSet, [$entry->id]));

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
    }

    public function testMatchingEntriesWithAnyTags(): void
    {
        $tag1 = $this->muffin(Tag::class);
        $tag2 = $this->muffin(Tag::class);

        $entry1 = $this->muffin(Entry::class);
        $entry1->addTag($tag1);

        $entry2 = $this->muffin(Entry::class);
        $entry2->addTag($tag2);

        $this->muffin(Entry::class);

        $entries = $this->repository->getEntriesWithAnyTags([$tag1->id, $tag2->id])->get();

        $this->assertCount(2, $entries->toArray());
    }

    public function testMatchingEntriesWithAllTags(): void
    {
        $tag1 = $this->muffin(Tag::class);
        $tag2 = $this->muffin(Tag::class);

        $entry1 = $this->muffin(Entry::class);
        $entry1->addTag($tag1);

        $entry2 = $this->muffin(Entry::class);
        $entry2->addTag($tag2);

        $entry3 = $this->muffin(Entry::class);
        $entry3->addTag($tag1);
        $entry3->addTag($tag2);

        $entries = $this->repository->getEntriesWithAllTags([$tag1->id, $tag2->id]);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry3->id, $entries->first()->id);
    }

    public function testPossiblePrimariesIncludeLocalId(): void
    {
        $primary = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $duplicate = $this->muffin(Entry::class, ['title' => 'aaaaabbbbb']);

        Duplicate::getForEntry($primary)->markAsPrimary();

        $entries = $this->repository->getPrimaryEntriesForSeason($this->season->id, 'aaa')->pluck('value', 'id');

        $primaryLocalId = local_id(translate($primary));
        $duplicateLocalId = local_id(translate($duplicate));

        $this->assertEquals("{$primary->title} (ID: {$primaryLocalId}) (Primary)", $entries[$primary->id]);
        $this->assertEquals("{$duplicate->title} (ID: {$duplicateLocalId})", $entries[$duplicate->id]);
    }

    public function testPossiblePrimariesIncludeLocalIdWithShortcodePrefix(): void
    {
        app(Settings::class)->saveSettings(['local-id-shortcode-format' => LocalIdShortcodeFormat::Before->value], current_account());
        $primary = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $duplicate = $this->muffin(Entry::class, ['title' => 'aaaaabbbbb']);

        Duplicate::getForEntry($primary)->markAsPrimary();

        $entries = $this->repository->getPrimaryEntriesForSeason($this->season->id, 'aaa')->pluck('value', 'id');

        $primaryLocalId = local_id(translate($primary));
        $duplicateLocalId = local_id(translate($duplicate));

        $this->assertEquals("{$primary->title} (ID: {$primaryLocalId}) (Primary)", $entries[$primary->id]);
        $this->assertEquals("{$duplicate->title} (ID: {$duplicateLocalId})", $entries[$duplicate->id]);
    }

    public function testGetForResubmissionByIds(): void
    {
        $submitted = $this->muffins(3, Entry::class, ['submitted_at' => now()]);
        $inProgress = $this->muffins(4, Entry::class);

        $entries = $this->repository->getForResubmissionByIds(collect($submitted, $inProgress)->pluck('id')->toArray());

        $this->assertEquals(3, $entries->count());
    }

    public function testInProgressEntryFees(): void
    {
        Price::unguard();

        $category = $this->muffin(Category::class);
        $parent = $this->muffin(Category::class);
        $child = $this->muffin(Category::class, ['parent_id' => $parent->id]);
        $this->muffin(Entry::class, ['category_id' => $category->id]);
        $this->muffin(Entry::class, ['category_id' => $child->id]);
        $price = $this->muffin(Price::class, ['type' => Price::TYPE_ENTRY, 'default' => true, 'season_id' => $this->season->id]);
        $price->amounts()->save(new PriceAmount(['amounts' => ['AUD' => 100], 'category_id' => $parent->id, 'chapter_id' => null]));
        $price->amounts()->save(new PriceAmount(['amounts' => ['AUD' => 5]]));

        $total = $this->repository->inProgressEntryFees($this->season->id, 'AUD');

        $this->assertSame((float) 105, $total);
    }

    public function testGetForFundAllocationByIds(): void
    {
        $locked = $this->muffin(GrantStatus::class, ['lock_fund_allocation' => 1]);
        $unlocked = $this->muffin(GrantStatus::class);

        $lockedEntry = $this->muffin(Entry::class, ['grant_status_id' => $locked->id]);
        $unlockedEntries = collect($this->muffins(3, Entry::class, ['grant_status_id' => $unlocked->id]));

        $results = $this->repository->getForFundAllocationByIds($unlockedEntries->push($lockedEntry)->pluck('id')->toArray());

        $this->assertCount(3, $results);
        $this->assertFalse(in_array($lockedEntry->id, $results->pluck('id')->toArray()));
    }

    public function testEligibilityExistsForEntrant(): void
    {
        $entry = $this->muffin(Entry::class);

        $this->assertFalse($this->repository->eligibilityExistsForEntrant($entry->userId));

        $entry->ineligibility();
        $this->repository->save($entry);

        $this->assertTrue($this->repository->eligibilityExistsForEntrant($entry->userId));
    }

    public function testUser(): void
    {
        $this->muffins(2, Entry::class, ['user_id' => ($user = $this->muffin(User::class))->id]);
        $this->muffin(Entry::class, ['user_id' => $this->muffin(User::class)->id]);

        $this->assertEquals(2, $this->repository->user($user->id)->count());
    }

    public function testCountTrashedForUser(): void
    {
        $this->muffins(2, Entry::class, ['user_id' => 1]);
        $this->muffin(Entry::class, ['user_id' => 1, 'deleted_at' => Carbon::now()]);
        $this->muffin(Entry::class, ['user_id' => 2]);

        $this->assertEquals(1, $this->repository->countTrashedForUser(1));
    }

    public function testItFetchesEntriesCollectionWithCategoryAndWithoutInvitedEntries(): void
    {
        $entry1 = $this->muffin(Entry::class, [
            'invited_at' => null,
        ]);

        $entry2 = $this->muffin(Entry::class, [
            'invited_at' => now(),
        ]);

        $ids = [$entry1->id, $entry2->id];

        $results = $this->repository->getIdsWithCategoriesExcludingInvited($ids);

        $this->assertCount(1, $results);
        $this->assertNotNull($results->first()->category);
    }

    public function testExistsForFieldWhenNoEntryHasTheField()
    {
        $field = $this->muffin(Field::class);

        $entryEmptyField = $this->muffin(Entry::class, ['values' => [(string) $field->slug => '']]);
        $resultEmptyField = $this->repository->existsForField($field);

        $entryNotField = $this->muffin(Entry::class, ['values' => []]);
        $resultNotField = $this->repository->existsForField($field);

        $entryWithField = $this->muffin(Entry::class, ['values' => [(string) $field->slug => ['someValue']]]);
        $resultField = $this->repository->existsForField($field);

        $this->assertFalse($resultEmptyField);
        $this->assertFalse($resultNotField);
        $this->assertTrue($resultField);
    }

    public function testGetForField()
    {
        $entryTextField = $this->muffin(Entry::class);
        $entryPhoneField = $this->muffin(Entry::class);
        $entryTableField = $this->muffin(Entry::class);
        $entryWithNoTextField = $this->muffin(Entry::class);
        $entryWithNoPhoneField = $this->muffin(Entry::class);
        $entryWithNoTableField = $this->muffin(Entry::class);
        $entryWithAllFieldsOnlyPhoneWithValue = $this->muffin(Entry::class);
        $textField = $this->muffin(Field::class, ['type' => 'text']);
        $phoneField = $this->muffin(Field::class, ['type' => 'phone']);
        $tableField = $this->muffin(Field::class, ['type' => 'table']);

        app(ValuesService::class)->setValuesForObject([(string) $textField->slug => 'Text'], $entryTextField);
        app(ValuesService::class)->setValuesForObject([(string) $textField->slug => ''], $entryWithNoTextField);
        app(ValuesService::class)->setValuesForObject([(string) $phoneField->slug => '123456789'], $entryPhoneField);
        app(ValuesService::class)->setValuesForObject([(string) $phoneField->slug => ''], $entryWithNoPhoneField);
        app(ValuesService::class)->setValuesForObject([(string) $tableField->slug => '1'], $entryTableField);
        app(ValuesService::class)->setValuesForObject([(string) $tableField->slug => ''], $entryWithNoTableField);
        app(ValuesService::class)->setValuesForObject(
            [
                (string) $tableField->slug => '',
                (string) $phoneField->slug => '123456789',
                (string) $textField->slug => '',
            ], $entryWithAllFieldsOnlyPhoneWithValue);

        $this->assertCount(1, $textResults = $this->repository->getForField($textField));
        $this->assertCount(2, $phoneResults = $this->repository->getForField($phoneField));
        $this->assertCount(1, $tableResults = $this->repository->getForField($tableField));
        $this->assertEquals($entryTextField->id, $textResults->first()->id);
        $this->assertEqualsCanonicalizing([$entryPhoneField->id, $entryWithAllFieldsOnlyPhoneWithValue->id], $phoneResults->pluck('id')->values()->toArray());
        $this->assertEquals($entryTableField->id, $tableResults->first()->id);
    }

    public function testGetEntryBySlugSuccessfully()
    {
        $entry = $this->muffin(Entry::class);

        $found = $this->repository->slug($entry->slug)->fields(['id', 'slug'])->first();

        $this->assertEquals($entry->slug, $found->slug);
    }

    public function testItCanFilterByUserOrCollaborator(): void
    {
        Feature::shouldReceive('enabled')->once()->with('collaboration')->andReturnTrue();
        $consumer = $this->muffin(User::class);
        Consumer::set(new UserConsumer($consumer));
        $this->muffin(Entry::class, ['user_id' => $consumer->id]);
        $this->muffin(Entry::class, ['user_id' => $this->muffin(User::class)->id]);
        $entry = $this->muffin(Entry::class, ['user_id' => $this->muffin(User::class)->id]);
        $entry->form->settings = FormSettings::create(['collaborative' => true]);
        $entry->form->save();
        $this->muffin(Collaborator::class, ['user_id' => $consumer->id, 'submittable_id' => $entry->id, 'submittable_type' => Entry::class]);

        $results = $this->repository->userOrCollaborator($consumer->id)->count();

        $this->assertSame(2, $results);
    }

    public function testFilterByForm()
    {
        $form1 = $this->muffin(Form::class);
        $form2 = $this->muffin(Form::class);
        $entry1 = $this->muffin(Entry::class, ['form_id' => $form1->id]);
        $entry2 = $this->muffin(Entry::class, ['form_id' => $form2->id]);

        $results = $this->repository->form($form1->id)->fields(['id'])->get();

        $this->assertCount(1, $results);
        $this->assertEquals($entry1->id, $results->first()->id);

        $results = $this->repository->form($form2->id)->fields(['id'])->get();

        $this->assertCount(1, $results);
        $this->assertEquals($entry2->id, $results->first()->id);
    }
}
