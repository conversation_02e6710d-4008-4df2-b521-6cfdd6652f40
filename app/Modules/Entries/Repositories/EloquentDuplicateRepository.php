<?php

namespace AwardForce\Modules\Entries\Repositories;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use Illuminate\Support\Facades\DB;

class EloquentDuplicateRepository extends Repository implements DuplicateRepository
{
    /** @var bool */
    protected $restrictByAccount = true;

    public function __construct(Duplicate $model)
    {
        $this->model = $model;
    }

    /**
     * Get all duplicates in the given season, sorted by confirmed and entry_id.
     *
     * @return mixed
     */
    public function forDuplicateFinder(int $seasonId)
    {
        return $this->getQuery()
            ->select([
                'duplicate_entries.id',
                'duplicate_entries.entry_id',
                'duplicate_entries.confirmed_at',
                DB::raw('IF(duplicate_entries.confirmed_at IS NOT NULL AND duplicate_entries.parent_id IS NULL, 1, 0) as confirmed_primary'),
            ])->join('entries', 'entries.id', '=', 'duplicate_entries.entry_id')
            ->where('duplicate_entries.season_id', $seasonId)
            ->whereNull('entries.deleted_at')
            ->whereNull('entries.archived_at')
            ->where(function ($query) {
                $query->whereNull('duplicate_entries.confirmed_at')->orWhereNull('duplicate_entries.parent_id');
            })->orderByRaw('confirmed_primary DESC, entry_id ASC')
            ->with('entry')
            ->get();
    }

    /**
     * The number of out of date entries in the given season.
     *
     * @return mixed
     */
    public function countOutOfDate(int $seasonId)
    {
        return $this->getQuery()
            ->where('duplicate_entries.season_id', $seasonId)
            ->where('duplicate_entries.out_of_date', true)
            ->whereNull('duplicate_entries.confirmed_at')
            ->count();
    }

    /**
     * The number primaries in the given season.
     *
     * @return mixed
     */
    public function countPrimaries(int $seasonId)
    {
        return $this->getQuery()
            ->where('duplicate_entries.season_id', $seasonId)
            ->whereNull('duplicate_entries.parent_id')
            ->count();
    }

    /**
     * Get all unconfirmed duplicates found for the given entry
     *
     * @return mixed
     */
    public function unconfirmedDuplicates(Entry $entry)
    {
        return $this->getQuery()
            ->select(['duplicate_entries.id', 'duplicate_entries.entry_id', 'duplicate_entries.parent_id'])
            ->join('entries', 'entries.id', 'duplicate_entries.entry_id')
            ->where('duplicate_entries.season_id', '=', $entry->seasonId)
            ->whereRaw("duplicate_entries.lft >= {$entry->duplicate->lft}")
            ->whereRaw("duplicate_entries.rgt < {$entry->duplicate->rgt}")
            ->whereNotNull('duplicate_entries.parent_id')
            ->whereNull('duplicate_entries.confirmed_at')
            ->where('duplicate_entries.out_of_date', '=', false)
            ->whereNull('entries.deleted_at')
            ->whereNull('entries.archived_at')
            ->with(['entry.entrant', 'entry.tags'])
            ->get();
    }

    /** Get all duplicates (confirmed and not) found for the given entry root
     *
     * @return mixed
     */
    public function duplicatesForEntry(Duplicate $root)
    {
        return $this->getQuery()
            ->select('duplicate_entries.*')
            ->join('entries', 'entries.id', 'duplicate_entries.entry_id')
            ->where('duplicate_entries.season_id', '=', $root->seasonId)
            ->whereRaw("duplicate_entries.lft >= {$root->lft}")
            ->whereRaw("duplicate_entries.rgt < {$root->rgt}")
            ->whereNotNull('duplicate_entries.parent_id')
            ->whereNull('entries.deleted_at')
            ->with('entry.entrant')
            ->orderBy('entries.id', 'asc')
            ->get();
    }

    /**
     * Get all submitted duplicates found for the given root
     *
     * @return mixed
     */
    public function submittedDuplicatesForEntry(Duplicate $root)
    {
        return $this->getQuery()
            ->join('entries', 'entries.id', 'duplicate_entries.entry_id')
            ->where('duplicate_entries.season_id', '=', $root->seasonId)
            ->whereRaw("duplicate_entries.lft >= {$root->lft}")
            ->whereRaw("duplicate_entries.rgt < {$root->rgt}")
            ->whereNotNull('duplicate_entries.parent_id')
            ->whereNull('entries.deleted_at')
            ->whereNotNull('entries.submitted_at')
            ->with('entry.entrant')
            ->get();
    }

    /**
     * Permanently delete all Duplicate records in the given season.
     *
     * @return mixed
     */
    public function resetSeason(int $seasonId)
    {
        return $this->getQuery()
            ->where('season_id', '=', $seasonId)
            ->delete();
    }
}
