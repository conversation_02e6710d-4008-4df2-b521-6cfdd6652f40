<?php

namespace AwardForce\Modules\Entries\Search;

use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Accounts\Models\SupportedLanguage;
use AwardForce\Modules\Accounts\Models\SupportedLanguages;
use AwardForce\Modules\Accounts\Services\MembershipService;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Comments\Models\Comment;
use AwardForce\Modules\Comments\Models\CommentCollection;
use AwardForce\Modules\Comments\Tags\CommentableTag;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Ecommerce\Orders\Data\OrderItem;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Contributor;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Search\Columns\Language;
use AwardForce\Modules\Entries\Search\Columns\LocalId;
use AwardForce\Modules\Features\Services\Feature as FeatureService;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Collaboration\ValueObjects\Privilege;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Events\Listeners\RecalculateFormula;
use AwardForce\Modules\Forms\Fields\Search\Columns\Text;
use AwardForce\Modules\Forms\Fields\Search\Enhancers\FieldValuesEnhancer;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Services\UserGateway;
use AwardForce\Modules\ReviewFlow\Data\ReviewStageAction;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use AwardForce\Modules\Settings\Enums\LocalIdShortcodeFormat;
use AwardForce\Modules\Settings\Services\Settings;
use AwardForce\Modules\Tags\Contracts\TagRepository;
use AwardForce\Modules\Tags\Services\TagManager;
use Carbon\Carbon;
use Mockery as m;
use Platform\Features\Feature;
use Platform\Features\Features;
use Platform\Search\Columns;
use Platform\Search\Defaults;
use Tectonic\LaravelLocalisation\Database\TranslationService;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Library\Search\ColumnatorTestHelper;

final class ManageEntriesColumnatorTest extends BaseTestCase
{
    use ColumnatorTestHelper;
    use Database;
    use Laravel;

    private $terms;

    public function init()
    {
        $this->gateway = m::mock(UserGateway::class);
        app()->instance(UserGateway::class, $this->gateway);

        $this->values = app(ValuesService::class);
        $this->memberships = app(MembershipService::class);
    }

    protected function area()
    {
        return 'manage_entries.search';
    }

    public function testCanSearchEntries(): void
    {
        $this->muffins(2, Entry::class);

        $entries = $this->search();

        $this->assertCount(2, $entries);
    }

    public function testCanFilterDeletedEntries(): void
    {
        $existing = $this->muffin(Entry::class);
        $deleted = $this->muffin(Entry::class, ['deleted_at' => Carbon::now()]);

        // UI filter
        $entries = $this->search([], ['trashed' => 'none']);
        $this->assertCount(1, $entries);
        $this->assertEquals($existing->id, $entries->first()->id);

        // API filter
        $entries = $this->search([], ['deleted' => 'none']);
        $this->assertCount(1, $entries);
        $this->assertEquals($existing->id, $entries->first()->id);

        // UI filter
        $entries = $this->search([], ['trashed' => 'only']);
        $this->assertCount(1, $entries);
        $this->assertEquals($deleted->id, $entries->first()->id);

        // API filter
        $entries = $this->search([], ['deleted' => 'only']);
        $this->assertCount(1, $entries);
        $this->assertEquals($deleted->id, $entries->first()->id);
    }

    public function testCanFilterBySeason(): void
    {
        $this->muffin(Entry::class);
        $pastSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $pastSeasonEntry = $this->muffin(Entry::class, ['season_id' => $pastSeason->id, 'form_id' => FormSelector::defaultForSeason($pastSeason->id)->id]);

        SeasonFilter::set(SeasonFilterService::FILTER_ALL);
        $entries = $this->search();
        $this->assertCount(2, $entries);

        SeasonFilter::set($pastSeason);
        $entries = $this->search();
        $this->assertCount(1, $entries);
        $this->assertEquals($pastSeasonEntry->id, $entries->first()->id);
    }

    public function testCanFilterByEntryFieldValues(): void
    {
        $entry = $this->muffin(Entry::class);
        $this->muffin(Entry::class); // Additional entry

        $field = $this->muffin(Field::class, ['searchable' => true, 'resource' => Field::RESOURCE_FORMS]);
        $this->values->syncValuesForObject([(string) $field->slug => 'fieldvalue'], $entry);

        $entries = $this->search([], [(string) $field->slug => 'fieldvalue']);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
    }

    public function testCanFilterByUserFieldValues(): void
    {
        $user = $this->muffin(User::class);

        $this->gateway->shouldReceive('addMembership')->with((string) $user->globalId, (string) current_account()->globalId);

        $this->memberships->registerMembership($user);
        $user->load('currentMembership');

        $entry = $this->muffin(Entry::class, ['user_id' => $user->id]);
        $this->muffin(Entry::class); // Additional entry

        $field = $this->muffin(Field::class, ['searchable' => true, 'resource' => Field::RESOURCE_USERS]);
        $this->values->syncValuesForObject([(string) $field->slug => 'fieldvalue'], $user->currentMembership);

        $entries = $this->search([], [(string) $field->slug => 'fieldvalue']);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
        $this->assertSame('fieldvalue', $entries->first()->{strtolower($field->slug)});
    }

    public function testCanFilterByContributorFieldValues(): void
    {
        $entry = $this->muffin(Entry::class);
        $contributor = $this->muffin(Contributor::class, ['submittable_id' => $entry->id]);

        /** @var Entry $entry2 */
        $entry2 = $this->muffin(Entry::class); // Additional entry

        $field = $this->muffin(Field::class, ['searchable' => true, 'resource' => Field::RESOURCE_CONTRIBUTORS]);
        $this->values->syncValuesForObject([(string) $field->slug => 'fieldvalue'], $contributor);

        $entries = $this->search([], [(string) $field->slug => 'fieldvalue']);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
    }

    public function testCanFilterByAttachmentFieldValues(): void
    {
        $entry = $this->muffin(Entry::class);
        $contributor = $this->muffin(Attachment::class, ['submittable_id' => $entry->id]);

        $this->muffin(Entry::class); // Additional entry

        $field = $this->muffin(Field::class, ['searchable' => true, 'resource' => Field::RESOURCE_ATTACHMENTS]);
        $this->values->syncValuesForObject([(string) $field->slug => 'fieldvalue'], $contributor);

        $entries = $this->search([], [(string) $field->slug => 'fieldvalue']);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
    }

    public function testChapterManagersOnlySeeEntriesFromTheirChapter(): void
    {
        $chapterManager = $this->setupUserWithRole('ChapterManager', true);

        $entry = $this->muffin(Entry::class);
        $this->muffin(Entry::class); // Entry in different chapter
        app(ChapterRepository::class)->syncManagers($entry->chapter, [$chapterManager->id]);

        $entries = $this->search();

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
    }

    public function testAuditorCanSeeAllEntries(): void
    {
        $auditor = $this->setupUserWithRole('Auditor', true);

        $this->muffins(2, Entry::class);

        $this->travel(5)->minutes();
        $entry = $this->muffin(Entry::class, ['user_id' => $auditor->id]); // Own entry

        $entries = $this->search();

        $this->assertCount(3, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
        $this->travelBack();
    }

    public function testCanFilterByCategory(): void
    {
        $category = $this->muffin(Category::class);
        $entry = $this->muffin(Entry::class, ['category_id' => $category->id]);
        $this->muffin(Entry::class); // Entry in different category

        // Using id
        $entries = $this->search([], ['category' => $category->id]);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);

        // Using slug
        $entries = $this->search([], ['category' => (string) $category->slug]);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
    }

    public function testCanFilterByParentCategory(): void
    {
        $parent = $this->muffin(Category::class);
        $childA = $this->muffin(Category::class, ['parent_id' => $parent->id]);
        $childB = $this->muffin(Category::class, ['parent_id' => $parent->id]);

        $this->muffin(Entry::class, ['category_id' => $childA->id]);
        $this->muffin(Entry::class, ['category_id' => $childB->id]);
        $other = $this->muffin(Entry::class); // Entry in different category

        $entries = $this->search([], ['category' => $parent->id]);

        $this->assertCount(2, $entries);
        $this->assertNotContains($other->id, $entries->just('id'));
    }

    public function testCanFilterByChapter(): void
    {
        $chapter = $this->muffin(Chapter::class);
        $entry = $this->muffin(Entry::class, ['chapter_id' => $chapter->id]);
        $this->muffin(Entry::class); // Entry in different chapter

        $entries = $this->search([], ['chapter' => $chapter->id]);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);

        $entries = $this->search([], ['chapter' => (string) $chapter->slug]);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
    }

    public function testCanFilterBySubmissionStatus(): void
    {
        $inProgress = $this->muffin(Entry::class);
        $submitted = $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);

        $entries = $this->search([], ['status' => 'in_progress']);
        $this->assertCount(1, $entries);
        $this->assertEquals($inProgress->id, $entries->first()->id);

        $entries = $this->search([], ['status' => 'submitted']);
        $this->assertCount(1, $entries);
        $this->assertEquals($submitted->id, $entries->first()->id);

        // Can recognize in_progress entries from entries with resubmission statuses without prior submission

        $resubmitted = $this->muffin(Entry::class, ['resubmitted_at' => Carbon::now()]);
        $resubmissionRequired = $this->muffin(Entry::class, ['resubmission_required_at' => Carbon::now()]);

        $entries = $this->search([], ['status' => 'in_progress']);
        $this->assertCount(1, $entries);
        $this->assertEquals($inProgress->id, $entries->first()->id);

        $entries = $this->search([], ['status' => 'resubmission_required']);
        $this->assertCount(1, $entries);
        $this->assertEquals($resubmissionRequired->id, $entries->first()->id);

        $entries = $this->search([], ['status' => 'resubmitted']);
        $this->assertCount(1, $entries);
        $this->assertEquals($resubmitted->id, $entries->first()->id);
    }

    public function testCanFilterByModerationStatus(): void
    {
        $approved = $this->muffin(Entry::class, ['moderation_status' => Entry::MODERATION_STATUS_APPROVED]);
        $rejected = $this->muffin(Entry::class, ['moderation_status' => Entry::MODERATION_STATUS_REJECTED]);
        $undecided = $this->muffin(Entry::class, ['moderation_status' => Entry::MODERATION_STATUS_UNDECIDED]);

        $entries = $this->search([], ['moderation' => 'approved']);
        $this->assertCount(1, $entries);
        $this->assertEquals($approved->id, $entries->first()->id);

        $entries = $this->search([], ['moderation' => 'rejected']);
        $this->assertCount(1, $entries);
        $this->assertEquals($rejected->id, $entries->first()->id);

        $entries = $this->search([], ['moderation' => 'undecided']);
        $this->assertCount(1, $entries);
        $this->assertEquals($undecided->id, $entries->first()->id);
    }

    public function testCanFilterByTags(): void
    {
        $tagged = $this->muffin(Entry::class);
        $this->muffin(Entry::class); // Untagged entry

        app(TagManager::class)->tag($tagged, ['test tag']);
        $tag = app(TagRepository::class)->getAllUsedInSeason(SeasonFilter::getId())->first();

        // UI filter
        $entries = $this->search([], ['tag' => $tag->id]);

        $this->assertCount(1, $entries);
        $this->assertEquals($tagged->id, $entries->first()->id);

        // API filter
        $entries = $this->search([], ['tag' => (string) $tag->tag]);

        $this->assertCount(1, $entries);
        $this->assertEquals($tagged->id, $entries->first()->id);
    }

    public function testCanFilterByReviewStatus(): void
    {
        $entry = $this->muffin(Entry::class); // Un-reviewed entry
        $reviewTask = $this->muffin(ReviewTask::class, ['entry_id' => $entry->id]);
        $reviewAction = new ReviewStageAction('proceed');

        $entry->reviewStatusAction = $reviewAction;
        $entry->save();

        $reviewTask->entry->saveTranslation(default_language_code(), 'reviewStatus', 'go go go', current_account_id());

        $entries = $this->search(['manage_entries.review_status'], ['review_status' => 'go go go']);

        $this->assertCount(1, $entries);
        $this->assertEquals($reviewTask->entry->id, $entries->first()->id);
    }

    public function testCanFilterByPaymentStatus(): void
    {
        $paidOrder = $this->muffin(Order::class, ['payment_status' => Order::PAYMENT_STATUS_PAID]);
        $unpaidOrder = $this->muffin(Order::class, ['payment_status' => Order::PAYMENT_STATUS_AWAITING_PAYMENT]);
        $deletedOrder = $this->muffin(Order::class, ['payment_status' => Order::PAYMENT_STATUS_AWAITING_PAYMENT]);

        $paidItem = $this->muffin(OrderItem::class, ['order_id' => $paidOrder->id]);
        $unpaidItem = $this->muffin(OrderItem::class, ['order_id' => $unpaidOrder->id]);

        $this->muffin(OrderItem::class, ['order_id' => $deletedOrder->id]);
        $deletedOrder->delete();

        $this->muffin(Entry::class); // Unpaid entry

        $entries = $this->search([], ['payment_status' => Order::PAYMENT_STATUS_PAID]);
        $this->assertCount(1, $entries);
        $this->assertEquals($paidItem->entry->id, $entries->first()->id);

        $entries = $this->search([], ['payment_status' => Order::PAYMENT_STATUS_AWAITING_PAYMENT]);
        $this->assertCount(1, $entries);
        $this->assertEquals($unpaidItem->entry->id, $entries->first()->id);
    }

    public function testCanFilterByPrice(): void
    {
        $item = $this->muffin(OrderItem::class);
        $this->muffin(Entry::class); // Unpaid entry

        $entries = $this->search([], ['price' => $item->price->id]);

        $this->assertCount(1, $entries);
        $this->assertEquals($item->entry->id, $entries->first()->id);
    }

    public function testMultipleLanguagesDoNotAddDuplicates(): void
    {
        $chapter = $this->muffin(Chapter::class);
        $chapter->saveTranslation('es_LA', 'name', 'Region', current_account_id());

        $category = $this->muffin(Category::class);
        $category->saveTranslation('es_LA', 'name', 'Categoria', current_account_id());
        $category->saveTranslation('es_LA', 'shortcode', 'Categoria', current_account_id());

        $this->muffin(Entry::class, ['chapter_id' => $chapter->id, 'category_id' => $category->id]);

        $entries = $this->search([], []);

        $this->assertCount(1, $entries);
    }

    public function testOrdersByUpdatedTimestampByDefault(): void
    {
        $entry1 = $this->muffin(Entry::class, ['updatedAt' => now()->subDays(3), 'local_id' => 1]);
        $entry2 = $this->muffin(Entry::class, ['updatedAt' => now()->subDays(2), 'local_id' => 2]);
        $entry3 = $this->muffin(Entry::class, ['updatedAt' => now()->subDays(1), 'local_id' => 3]);

        $entry2->updatedAt = now();
        $entry2->save();

        $entries = $this->search([], []);

        $this->assertCount(3, $entries);
        $this->assertEquals($entry2->id, $entries->first()->id);
    }

    public function testCanFilterByEntrant(): void
    {
        $user1 = $this->muffin(User::class);
        $user2 = $this->muffin(User::class);
        /** @var Entry $entry */
        $entry1 = $this->muffin(Entry::class, ['user_id' => $user1->id]);
        $entry2 = $this->muffin(Entry::class, ['user_id' => $user2->id]);

        // UI filter
        $entries = $this->search([], ['entrant' => $user1->id]);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry1->id, $entries->first()->id);

        // API filter
        $entries = $this->search([], ['entrant' => (string) $user2->slug]);

        $this->assertCount(1, $entries);
        $this->assertEquals($entry2->id, $entries->first()->id);

        // Does not apply when empty
        $entries = $this->search([], ['entrant' => '']);
        $this->assertCount(2, $entries);
    }

    public function testDownloadLinkApiRequestFileField(): void
    {
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return Field::TYPE_FILE;
        }]);
        $file = $this->muffin(File::class, ['resource' => 'File field-'.$field->id, 'resource_id' => $field->id, 'token' => $token = 'tokenA']);

        $entry = $this->muffin(Entry::class);
        app(ValuesService::class)->setValuesForObject(
            [
                (string) $field->slug => (string) $file->token,
            ],
            $entry
        )->refresh();

        $entryResult = $this->apiSearch([], ['slug' => (string) $entry->slug])->items()[0];
        $this->assertArrayHasKey('entry_fields', $entryResult);
        $entryField = $entryResult['entry_fields'][0];

        $this->assertSame((string) $field->slug, $entryField['slug']);
        $this->assertStringContainsString($token, $entryField['value']);
        $this->assertSame(cloud_asset_url($file->file), $entryField['download']);
    }

    public function testDownloadLinkApiRequestAttachment(): void
    {
        $entry = $this->muffin(Entry::class);
        $tabAttachments = $this->muffin(
            Tab::class,
            ['resource' => Tab::RESOURCE_ENTRIES, 'type' => Tab::TYPE_ATTACHMENTS, 'category_option' => 'select',
                'season_id' => $entry->seasonId]
        );
        $tabAttachments->categories()->attach($entry->categoryId);
        $fileAttachment = $this->muffin(File::class, [
            'user_id' => $entry->userId,
            'resource_id' => $tabAttachments->id,
            'resource' => File::RESOURCE_ATTACHMENTS,
        ]);
        $this->muffin(Attachment::class, [
            'tab_id' => $tabAttachments->id,
            'submittable_id' => $entry->id,
            'file_id' => $fileAttachment->id,
        ]);

        $entryResult = $this->apiSearch([], ['slug' => (string) $entry->slug])->items()[0];

        $this->assertArrayHasKey('attachments', $entryResult);
        $attachments = $entryResult['attachments'][0];
        $this->assertSame(cloud_asset_url($fileAttachment->file), $attachments['download']);
    }

    public function testOrderByNumeric(): void
    {
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => function () {
            return 'currency';
        }]);

        $entry1 = $this->muffin(Entry::class);
        $entry2 = $this->muffin(Entry::class);
        $entry3 = $this->muffin(Entry::class);
        $this->setValues($entry1, [(string) $field->slug => '1.3']);
        $this->setValues($entry2, [(string) $field->slug => '2.2']);
        $this->setValues($entry3, [(string) $field->slug => '19.1']);

        $entries = $this->search([], ['order' => (string) $field->slug, 'dir' => 'asc']);

        $this->assertCount(3, $entries);

        $this->assertEquals($entry1->id, $entries[0]->id);
        $this->assertEquals($entry2->id, $entries[1]->id);
        $this->assertEquals($entry3->id, $entries[2]->id);

        $entries = $this->search([], ['order' => (string) $field->slug, 'dir' => 'desc']);

        $this->assertCount(3, $entries);
        $this->assertEquals($entry3->id, $entries[0]->id);
        $this->assertEquals($entry2->id, $entries[1]->id);
        $this->assertEquals($entry1->id, $entries[2]->id);
    }

    public function testGetEntryBySlugArchivedSeason(): void
    {
        $season = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $entry = $this->muffin(Entry::class, ['season_id' => $season->id]);

        $entries = $this->search([], ['slug' => (string) $entry->slug]);
        $this->assertEquals($entry->id, $entries[0]->id);
    }

    public function testDoesNotExcludeGrantsWhenConsumerCanViewGrants(): void
    {
        $this->muffin(Entry::class);
        $this->muffin(Entry::class, ['grant_status_id' => $this->muffin(GrantStatus::class)->id]);

        $entries = $this->search();
        $this->assertCount(2, $entries);
    }

    public function testDoesExcludesGrantsWhenConsumerCanViewGrants(): void
    {
        $entry = $this->muffin(Entry::class);
        $this->muffin(Entry::class, ['grant_status_id' => $this->muffin(GrantStatus::class)->id]);
        current_account()->defineFeatures(new Features([new Feature('grants', 'enabled')]));
        $this->setupUserWithRole('Program manager', true);

        $entries = $this->search();
        $this->assertCount(1, $entries);
        $this->assertEquals($entry->id, $entries->first()->id);
    }

    public function testIncludesGrantsWhenGrantsStateSelected(): void
    {
        $this->muffin(Entry::class);
        $grant = $this->muffin(Entry::class, ['grant_status_id' => $this->muffin(GrantStatus::class)->id]);
        current_account()->defineFeatures(new Features([new Feature('grants', 'enabled')]));
        $this->setupUserWithRole('Program manager', true);

        $entries = $this->search([], ['showGrants' => 1]);
        $this->assertCount(1, $entries);
        $this->assertEquals($grant->id, $entries->first()->id);
    }

    public function testReturnsFallbackLanguageValuesOnTranslatedColumns(): void
    {
        $account = current_account();
        $account->setRelation('languages', new SupportedLanguages([
            new SupportedLanguage(['code' => 'el_GR', 'default' => 1]),
        ]));

        $entry = $this->muffin(Entry::class);
        $this->setupUserWithRole('Program manager', true);

        $this->user->currentMembership->language = 'el_GR';

        $category = translate($entry->category);
        $chapter = $entry->chapter;
        $chapter->saveTranslation('el_GR', 'name', 'The greek name', current_account_id());

        $entries = $this->search([]);
        $this->assertCount(1, $entries);

        // Current language
        $this->assertEquals('The greek name', $entries->first()->tf_manage_entrieschapter_table_name);

        // Fallback language
        $this->assertEquals(lang($category, 'name', 'en_GB'), $entries->first()->tf_manage_entriescategory_table_name);
    }

    public function testCategoryOrderingNotDuplicateResults(): void
    {
        $parent = $this->muffin(Category::class);
        $cat1 = $this->muffin(Category::class, ['parent_id' => $parent->id]);
        $cat2 = $this->muffin(Category::class, ['parent_id' => $parent->id]);
        $this->muffin(Category::class, ['parent_id' => $parent->id]);
        $this->muffins(10, Entry::class, ['category_id' => $cat1->id]);
        $this->muffins(10, Entry::class, ['category_id' => $cat2->id]);

        $results = collect();
        for ($i = 1; $i < 5; $i++) {
            $result = $this->search([], [
                'category' => (string) $parent->slug,
                'order' => 'manage_entries.category',
                'dir' => 'asc',
                'per_page' => 5,
                'page' => $i,
            ]);

            $results->push($result->values());
        }

        $results = $results->flatten();

        $this->assertCount(20, $results);

        // If the translated column ordering isn't combined with a unique column ordering, the result here will be less than 20
        $this->assertCount(20, $results->unique('slug'));
    }

    public function testCanOrderByTotalScore(): void
    {
        $this->muffin(Field::class, ['type' => 'radio', 'auto_scoring' => true]);
        $entry1 = $this->muffin(Entry::class, ['total_score' => 10]);
        $entry2 = $this->muffin(Entry::class, ['total_score' => 5]);

        $entries = $this->search([], ['order' => 'auto_scoring_total', 'dir' => 'asc']);
        $this->assertCount(2, $entries);
        $this->assertEquals($entry2->id, $entries->first()->id);

        $entries = $this->search([], ['order' => 'auto_scoring_total', 'dir' => 'desc']);
        $this->assertCount(2, $entries);
        $this->assertEquals($entry1->id, $entries->first()->id);
    }

    public function testCanFilterWithEligibilityStatus(): void
    {
        $eligible = $this->muffins(3, Entry::class, ['eligible_at' => now()]);
        $inEligible = $this->muffins(2, Entry::class, ['ineligible_at' => now()]);

        app(FeatureService::class)->disableCache();
        current_account()->defineFeatures(new Features([new Feature('eligibility', 'enabled')]));
        $this->muffin(
            Tab::class,
            ['resource' => Tab::RESOURCE_ENTRIES, 'type' => Tab::TYPE_ELIGIBILITY, 'category_option' => 'select',
                'season_id' => $eligible[0]->seasonId]
        );

        $this->muffins(5, Entry::class);

        $entries = $this->search([], ['eligibility_status' => 'eligible']);
        $this->assertCount(3, $entries);
        $this->assertArrayHasKey($eligible[0]->id, $ids = $entries->pluck('id', 'id')->toArray());
        $this->assertArrayHasKey($eligible[1]->id, $ids);
        $this->assertArrayHasKey($eligible[2]->id, $ids);

        $entries = $this->search([], ['eligibility_status' => 'ineligible']);
        $this->assertCount(2, $entries);
        $this->assertArrayHasKey($inEligible[0]->id, $ids = $entries->pluck('id', 'id')->toArray());
        $this->assertArrayHasKey($inEligible[1]->id, $ids);
    }

    public function testItDoesNotReturnMixedValuesWhenEntryAndGrantReportShareId(): void
    {
        $entry = $this->muffin(Entry::class);
        $grantReport = $this->muffin(GrantReport::class, ['id' => $entry->id, 'entry_id' => $entry->id]);

        $attachmentTabEntry = $this->muffin(Tab::class, ['type' => Tab::TYPE_ATTACHMENTS, 'form_id' => $entry->formId]);
        $fileEntry = $this->muffin(File::class, ['resource_id' => $entry->id]);
        $attachmentEntry = $this->muffin(Attachment::class, ['submittable_id' => $entry->id, 'tab_id' => $attachmentTabEntry->id, 'file_id' => $fileEntry->id]);
        $contributorsTabEntry = $this->muffin(Tab::class, ['type' => Tab::TYPE_CONTRIBUTORS, 'form_id' => $entry->formId]);
        $contributorEntry = $this->muffin(Contributor::class, ['submittable_id' => $entry->id, 'tab_id' => $contributorsTabEntry->id]);

        $attachmentTabGR = $this->muffin(Tab::class, ['type' => Tab::TYPE_ATTACHMENTS, 'form_id' => $grantReport->formId]);
        $fileGR = $this->muffin(File::class, ['resource_id' => $grantReport->id]);
        $attachmentGR = $this->muffin(Attachment::class, ['submittable_id' => $grantReport->id, 'tab_id' => $attachmentTabGR->id, 'file_id' => $fileGR->id]);
        $contributorsTabGR = $this->muffin(Tab::class, ['type' => Tab::TYPE_CONTRIBUTORS, 'form_id' => $grantReport->formId]);
        $contributorGR = $this->muffin(Contributor::class, ['submittable_id' => $grantReport->id, 'tab_id' => $contributorsTabGR->id]);

        $entries = $this->search([], []);

        $this->assertCount(1, $entries);
    }

    public function testCanFilterByGrantStatus(): void
    {
        current_account()->defineFeatures(new Features([new Feature('grants', 'enabled')]));
        $this->setupUserWithRole('Program manager', true);

        $grantStatuses = collect($this->muffins(3, GrantStatus::class));
        foreach ($grantStatuses as $i => $grantStatus) {
            app(TranslationService::class)->sync($grantStatus, ['name' => ['en_GB' => $i.'TestStatus']]);
            translate($grantStatus);
        }

        // No grants
        $this->muffins(4, Entry::class);

        // Grants
        $this->muffins(1, Entry::class, ['grant_status_id' => $grantStatuses[0]->id]);
        $this->muffins(3, Entry::class, ['grant_status_id' => $grantStatuses[1]->id]);
        $this->muffins(5, Entry::class, ['grant_status_id' => $grantStatuses[2]->id]);

        // No grants state selector the filter isn't applied and no grants shown in the list
        $results = $this->search([], ['grant-status' => (string) $grantStatuses[0]->slug]);
        $this->assertCount(4, $results);

        $results = $this->search([], ['showGrants' => 1, 'grant_status' => (string) $grantStatuses[0]->slug]);
        $this->assertCount(1, $results);

        $results = $this->search([], ['showGrants' => 1, 'grant_status' => (string) $grantStatuses[1]->slug]);
        $this->assertCount(3, $results);

        $results = $this->search([], ['showGrants' => 1, 'grant_status' => (string) $grantStatuses[2]->slug]);
        $this->assertCount(5, $results);
    }

    public function testOrderByFormulaField()
    {
        $this->setupUserWithRole('Program manager', true);

        $field = $this->muffin(Field::class, ['type' => 'formula', 'resource' => Field::RESOURCE_FORMS]);
        $entries = $this->muffins(7, Entry::class, ['form_id' => $field->formId]);

        app()->instance(RecalculateFormula::class, m::spy(RecalculateFormula::class));

        $this->setValues($entries[0], [(string) $field->slug => '1']);
        $this->setValues($entries[1], [(string) $field->slug => '15']);
        $this->setValues($entries[2], [(string) $field->slug => 'abc']);
        $this->setValues($entries[3], [(string) $field->slug => 'zxy']);
        $this->setValues($entries[4], [(string) $field->slug => '-7']);
        $this->setValues($entries[5], [(string) $field->slug => '7']);
        $this->setValues($entries[6], [(string) $field->slug => 'BCDE']);

        $results = $this->search([], ['order' => (string) $field->slug, 'dir' => 'asc']);

        // Expected order is: -7, 1, 7, 15, abc, BCDE, zxy
        $this->assertCount(7, $results);

        $this->assertEquals($entries[4]->id, $results[0]->id); // -7
        $this->assertEquals($entries[0]->id, $results[1]->id); // 1
        $this->assertEquals($entries[5]->id, $results[2]->id); // 7
        $this->assertEquals($entries[1]->id, $results[3]->id); // 15
        $this->assertEquals($entries[2]->id, $results[4]->id); // abc
        $this->assertEquals($entries[6]->id, $results[5]->id); // BCDE
        $this->assertEquals($entries[3]->id, $results[6]->id); // zxy
    }

    public function testDoesNotIncludeCollaboratorCountByDefault()
    {
        current_account()->defineFeatures(new Features([new Feature('collaboration', 'enabled')]));

        Collaborator::forSubmittable(
            $this->muffin(Entry::class),
            $this->setupUserWithRole('Entrant'),
            Privilege::submitter()
        );

        $entries = $this->search(
            $this->columnator()->columns(new Defaults('search'))->map(fn($column) => $column->name())->all()
        );

        $this->assertNull($entries->first()->collaboratorsCount);
    }

    public function testIncludesCollaboratorCountIfColumnEnabled()
    {
        current_account()->defineFeatures(new Features([new Feature('collaboration', 'enabled')]));

        Collaborator::forSubmittable(
            $this->muffin(Entry::class),
            $this->setupUserWithRole('Entrant'),
            Privilege::submitter()
        );

        $entries = $this->search(['collaborators.count']);

        $this->assertEquals(1, $entries->first()->collaboratorsCount);
    }

    private function setValues($entry, $values)
    {
        app(ValuesService::class)->setValuesForObject($values, $entry);
    }

    public function testDoesNotLoadThumbnailsWhenImageOptimisationIsDisabled()
    {
        $featureSpy = \AwardForce\Modules\Features\Facades\Feature::spy();
        $featureSpy->shouldReceive('enabled')
            ->andReturnTrue()
            ->byDefault()
            ->shouldReceive('enabled')
            ->with('image_optimisation')
            ->andReturnFalse();

        $this->instance(EntryListViewThumbnailRetriever::class, $spy = m::spy(EntryListViewThumbnailRetriever::class));
        $spy->shouldNotReceive('getThumbnailsForEntries');

        $this->search();
    }

    public function testHiddenConditionalFieldsShouldShowEmptyValue(): void
    {
        $field1 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $field2 = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $field1->setConditionalField(true, 'show', $field2->id, 'is', 'value1')->save();

        app(ValuesService::class)->setValuesForObject([
            ((string) $field1->slug) => 'value1',
            ((string) $field2->slug) => 'value2',
        ], $entry = $this->muffin(Entry::class));

        $column1 = new Text($field1, 'entries.id');
        $column2 = new Text($field2, 'entries.id');

        (FieldValuesEnhancer::fromColumns(new Columns([$column1, $column2])))->enhance(collect([$entry]));

        $this->assertNull($entry->{strtolower($field1->slug)});
    }

    public function testManagerCanSeeUserComments()
    {
        $this->muffin(Membership::class, ['user_id' => ($user = $this->muffin(User::class))->id]);
        $tagString = implode(' ', array_map(fn(CommentableTag $tag) => $tag->toString(), $user->commentsTags()));

        $comments[] = $this->muffin(Comment::class, ['tags' => $tagString, 'comment' => $firstComment = 'User Comment']);
        $comments[] = $this->muffin(Comment::class, ['tags' => $tagString, 'comment' => $secondComment = 'Other User Comment']);

        $this->muffins(2, Entry::class, ['user_id' => $user->id]);

        $entries = $this->search();

        /** @var CommentCollection $userComments */
        $this->assertNotEmpty($userComments = $entries->first()->userComments);
        $this->assertCount(count($comments), $userComments);

        $this->assertTrue($userComments->some(fn(Comment $comment) => $comment->comment === $firstComment));
        $this->assertTrue($userComments->some(fn(Comment $comment) => $comment->comment === $secondComment));
    }

    public function testItOrderAutoScoreColumnForIndividualField(): void
    {
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => Field::TYPE_CHECKBOXLIST, 'auto_scoring' => true, 'options' => ['1' => 1, '10' => 10, '100' => 100, '500' => 500]]);

        $this->muffin(Entry::class, ['scores' => [(string) $field->slug => 500]]);
        $this->muffin(Entry::class, ['scores' => [(string) $field->slug => 1]]);
        $this->muffin(Entry::class, ['scores' => [(string) $field->slug => 100]]);
        $this->muffin(Entry::class, ['scores' => [(string) $field->slug => 10]]);

        $entries = $this->search([], ['order' => $field->slug.'_auto_score', 'dir' => 'asc']);

        $this->assertCount(4, $entries);
        $this->assertEquals(1, $entries[0]->scores[$field->slug.'_auto_score']);
        $this->assertEquals(10, $entries[1]->scores[$field->slug.'_auto_score']);
        $this->assertEquals(100, $entries[2]->scores[$field->slug.'_auto_score']);
        $this->assertEquals(500, $entries[3]->scores[$field->slug.'_auto_score']);

        $entries = $this->search([], ['order' => $field->slug.'_auto_score', 'dir' => 'desc']);

        $this->assertCount(4, $entries);
        $this->assertEquals(500, $entries[0]->scores[$field->slug.'_auto_score']);
        $this->assertEquals(100, $entries[1]->scores[$field->slug.'_auto_score']);
        $this->assertEquals(10, $entries[2]->scores[$field->slug.'_auto_score']);
        $this->assertEquals(1, $entries[3]->scores[$field->slug.'_auto_score']);
    }

    public function testItCanOrderByLocalIdDefaultFormat(): void
    {
        $this->muffin(Entry::class, ['local_id' => 1]);
        $this->muffin(Entry::class, ['local_id' => 2]);
        $this->muffin(Entry::class, ['local_id' => 3]);

        $entries = $this->search([], ['order' => 'manage_entries.local_id', 'dir' => 'desc']);
        $column = new LocalId;

        $this->assertCount(3, $entries);
        $this->stringStartsWith('3-', $entries[0]->{$column->columnName()});
        $this->stringStartsWith('2-', $entries[1]->{$column->columnName()});
        $this->stringStartsWith('-', $entries[2]->{$column->columnName()});
    }

    public function testItCanOrderByLocalIdShortcodePrefixFormat(): void
    {
        app(Settings::class)->saveSettings(['local-id-shortcode-format' => LocalIdShortcodeFormat::Before->value], current_account());
        $category1 = $this->muffin(Category::class);
        app(TranslationService::class)->update($category1, 'en_GB', 'shortcode', 'Cat1');
        $category2 = $this->muffin(Category::class);
        app(TranslationService::class)->update($category2, 'en_GB', 'shortcode', 'Cat2');
        $category3 = $this->muffin(Category::class);
        app(TranslationService::class)->update($category3, 'en_GB', 'shortcode', 'Cat3');

        $this->muffin(Entry::class, ['local_id' => 1, 'category_id' => $category3->id]);
        $this->muffin(Entry::class, ['local_id' => 2, 'category_id' => $category2->id]);
        $this->muffin(Entry::class, ['local_id' => 3, 'category_id' => $category1->id]);

        $entries = $this->search([], ['order' => 'manage_entries.local_id', 'dir' => 'desc']);
        $column = new LocalId;

        $this->assertCount(3, $entries);
        $this->assertEquals('Cat3-1', $entries[0]->{$column->columnName()});
        $this->assertEquals('Cat2-2', $entries[1]->{$column->columnName()});
        $this->assertEquals('Cat1-3', $entries[2]->{$column->columnName()});
    }

    public function testItContainsLanguageColumn()
    {
        $columns = $this->columnator()->columns(new Defaults('all'))->map(fn($column) => $column->name())->all();

        $this->assertContains('user.language', $columns);
    }

    public function testItShowsUserLanguageColumn()
    {
        $entry = $this->muffin(Entry::class);
        $user = $entry->user;

        $entries = $this->search(['user.language']);

        $this->assertEquals($user->preferredLanguage()->code(), $entries[0]->language);
    }

    public function testHtmlValueShowsLanguageString()
    {
        $entry = $this->muffin(Entry::class);
        $user = $entry->user;

        $entries = $this->search(['user.language']);

        $this->assertEquals($user->preferredLanguage()->language(), (new Language)->html($entries->first()));
    }

    public function testItShowsMultipleLanguages()
    {
        $this->muffin(Membership::class, ['user_id' => ($userLA = $this->muffin(User::class))->id, 'language' => 'es_LA']);
        $this->muffin(Membership::class, ['user_id' => ($userGB = $this->muffin(User::class))->id, 'language' => 'en_GB']);
        $entryLA = $this->muffin(Entry::class, ['user_id' => $userLA->id]);
        $entryGB = $this->muffin(Entry::class, ['user_id' => $userGB->id]);

        $entries = $this->search(['user.language']);
        $this->assertCount(2, $entries);
        $this->assertContains('es_LA', $entries->pluck('language'));
        $this->assertContains('en_GB', $entries->pluck('language'));
    }

    public function testItCanFilterByLanguage()
    {
        $this->muffin(Membership::class, ['user_id' => ($userLA = $this->muffin(User::class))->id, 'language' => 'es_LA']);
        $this->muffin(Membership::class, ['user_id' => ($userGB = $this->muffin(User::class))->id, 'language' => 'en_GB']);
        $entryLA = $this->muffin(Entry::class, ['user_id' => $userLA->id]);
        $entryGB = $this->muffin(Entry::class, ['user_id' => $userGB->id]);

        $entriesLA = $this->search(['user.language'], ['language' => 'es_LA']);
        $entriesGB = $this->search(['user.language'], ['language' => 'en_GB']);
        $entriesAll = $this->search(['user.language'], ['language' => '']);
        $entriesNone = $this->search(['user.language'], ['language' => 'fr_FR']);

        $this->assertCount(1, $entriesLA);
        $this->assertEquals($entryLA->id, $entriesLA->first()->id);
        $this->assertCount(1, $entriesGB);
        $this->assertEquals($entryGB->id, $entriesGB->first()->id);
        $this->assertCount(2, $entriesAll);
        $this->assertCount(0, $entriesNone);
    }
}
