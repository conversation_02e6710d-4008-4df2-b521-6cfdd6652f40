<?php

namespace AwardForce\Modules\Entries\Search\Enhancers;

use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Search\Columns\Duplicates\Category;
use Platform\Search\Enhancers\SearchEnhancer;

class UnconfirmedDuplicates implements SearchEnhancer
{
    /** @var DuplicateRepository */
    protected $duplicates;

    public function __construct(DuplicateRepository $duplicates)
    {
        $this->duplicates = $duplicates;
    }

    public function enhance($results): void
    {
        $column = new Category;

        foreach ($results as $primary) {
            $duplicates = $this->duplicates->unconfirmedDuplicates($entry = $primary->entry);
            $canArchive = ! $entry->inProgress();
            $groupId = $primary->id;

            foreach ($duplicates as $duplicate) {
                $duplicate->{$column->key()} = $column->value($primary);
                $duplicate->canArchive = $canArchive;
                $duplicate->groupId = $groupId;
            }

            $primary->duplicates = $duplicates;
            $primary->canArchive = $canArchive;
            $primary->groupId = $groupId;
        }
    }

    public function applies(): bool
    {
        return true;
    }
}
