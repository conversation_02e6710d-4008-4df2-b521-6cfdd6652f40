<?php

namespace AwardForce\Modules\Menu\Settings;

use AwardForce\Modules\Menu\Settings\Links\General\Account;
use AwardForce\Modules\Menu\Settings\Links\General\CustomExportLayout;
use AwardForce\Modules\Menu\Settings\Links\General\Documents;
use AwardForce\Modules\Menu\Settings\Links\General\DocumentTemplates;
use AwardForce\Modules\Menu\Settings\Links\General\Languages;
use AwardForce\Modules\Menu\Settings\Links\General\Notifications;
use AwardForce\Modules\Menu\Settings\Links\General\ReviewFlow;
use AwardForce\Modules\Menu\Settings\Links\General\Seasons;
use AwardForce\Modules\Menu\Settings\Links\General\Tags;
use AwardForce\Modules\Menu\Settings\Links\General\Theme;
use Platform\Menu\Context\Menu;

class General extends Menu
{
    public function name(): string
    {
        return 'general-settings';
    }

    public function text(): string
    {
        return trans('setting.titles.menu');
    }

    public function menuItems(): array
    {
        return [
            new Account,
            new CustomExportLayout,
            new Documents,
            new DocumentTemplates,
            new Languages,
            new Notifications,
            new ReviewFlow,
            new Seasons,
            new Tags,
            new Theme,
        ];
    }

    public function icon(): string
    {
        return '';
    }

    public function extras(): array
    {
        return [];
    }

    public function position(): int
    {
        return 0;
    }
}
