<?php

namespace AwardForce\Modules\Categories\Models;

use AwardForce\Library\Database\Eloquent\ConfigurationExporter;
use AwardForce\Library\Database\Eloquent\ExportsConfiguration;
use AwardForce\Library\Database\Eloquent\HtmlTranslatable;
use AwardForce\Library\Database\Eloquent\TranslationRetrieval;
use AwardForce\Library\Database\Node\HasScopedNestedSet;
use AwardForce\Library\Database\Node\Node;
use AwardForce\Library\Localisation\InjectTranslations;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Categories\Events\CategoryDivisionsWereChanged;
use AwardForce\Modules\Categories\Events\CategoryWasActivated;
use AwardForce\Modules\Categories\Events\CategoryWasCopied;
use AwardForce\Modules\Categories\Events\CategoryWasCreated;
use AwardForce\Modules\Categories\Events\CategoryWasDeactivated;
use AwardForce\Modules\Categories\Events\CategoryWasDeleted;
use AwardForce\Modules\Categories\Events\CategoryWasUpdated;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Models\HasFiles;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Forms\Database\Relations\BelongsToForm;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\Payments\Models\Discount;
use AwardForce\Modules\Seasons\Models\Season;
use Carbon\Carbon;
use Eloquence\Behaviours\HasCamelCasing;
use Eloquence\Behaviours\HasSlugs;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Platform\Authorisation\Consumer;
use Platform\Database\Eloquent\TranslatableModel;
use Platform\Events\Raiseable;
use Tectonic\Localisation\Contracts\Translatable;
use Tectonic\Localisation\Translator\Translations;

/**
 * AwardForce\Modules\Categories\Models\Category
 *
 * @property int $id
 * @property int|null $parentId
 * @property int|null $lft
 * @property int|null $rgt
 * @property int|null $depth
 * @property int $accountId
 * @property int $formId
 * @property int $seasonId
 * @property string|null $slug
 * @property bool $active
 * @property bool $locked
 * @property int|null $entrantMaxEntries
 * @property int $fillEntryName
 * @property int $maxImageWidth
 * @property int $divisions
 * @property array|null $attachmentTypes
 * @property string|null $attachmentsVip
 * @property string|null $attachmentsVoting
 * @property string|null $attachmentsTopPick
 * @property string|null $attachmentsGallery
 * @property string|null $attachmentsQualifying
 * @property bool $packingSlip
 * @property int|null $chapterCount
 * @property \Illuminate\Support\Carbon|null $createdAt
 * @property \Illuminate\Support\Carbon|null $updatedAt
 * @property \Illuminate\Support\Carbon|null $deletedAt
 * @property int|null $promoted
 * @property-read Account|null $account
 * @property-read \Platform\Database\Eloquent\Collection<int, Chapter> $chapters
 * @property-read int|null $chaptersCount
 * @property-read \Baum\Extensions\Eloquent\Collection<int, Category> $children
 * @property-read int|null $childrenCount
 * @property-read \Platform\Database\Eloquent\Collection<int, Discount> $discounts
 * @property-read int|null $discountsCount
 * @property-read \AwardForce\Modules\Forms\Fields\Database\DataAccess\Fields<int, Field> $fields
 * @property-read int|null $fieldsCount
 * @property-read \AwardForce\Modules\Files\Models\FilesCollection<int, File> $files
 * @property-read int|null $filesCount
 * @property-read \AwardForce\Modules\Forms\Forms\Database\Entities\Form|null $form
 * @property-read \Platform\Database\Eloquent\Collection<int, Panel> $panels
 * @property-read int|null $panelsCount
 * @property-read Category|null $parent
 * @property-read Season|null $season
 * @property-read \Platform\Database\Eloquent\Collection<int, Tab> $tabs
 * @property-read int|null $tabsCount
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Tectonic\LaravelLocalisation\Database\Translation> $translations
 * @property-read int|null $translationsCount
 *
 * @method static \Baum\Extensions\Eloquent\Collection<int, static> all($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Category dCallback(callable $callback)
 * @method static \Platform\Database\Eloquent\Builder|Category forConfiguration()
 * @method static \Baum\Extensions\Eloquent\Collection<int, static> get($columns = ['*'])
 * @method static \Platform\Database\Eloquent\Builder|Category limitDepth($limit)
 * @method static \Platform\Database\Eloquent\Builder|Category newModelQuery()
 * @method static \Platform\Database\Eloquent\Builder|Category newQuery()
 * @method static \Platform\Database\Eloquent\Builder|Category onlyLeaves()
 * @method static Builder|Category onlyTrashed()
 * @method static \Platform\Database\Eloquent\Builder|Category preventLazyLoadingInColumnator(bool $prevent = true)
 * @method static \Platform\Database\Eloquent\Builder|Category query()
 * @method static \Platform\Database\Eloquent\Builder|Category whereAccountId($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereActive($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereAttachmentTypes($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereAttachmentsGallery($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereAttachmentsQualifying($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereAttachmentsTopPick($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereAttachmentsVip($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereAttachmentsVoting($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereChapterCount($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereCreatedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereDeletedAt($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereDepth($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereDivisions($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereEntrantMaxEntries($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereFillEntryName($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereFormId($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereId($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereLft($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereLocked($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereMaxImageWidth($value)
 * @method static \Platform\Database\Eloquent\Builder|Category wherePackingSlip($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereParentId($value)
 * @method static \Platform\Database\Eloquent\Builder|Category wherePromoted($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereRgt($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereSeasonId($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereSlug($value)
 * @method static \Platform\Database\Eloquent\Builder|Category whereUpdatedAt($value)
 * @method static Builder|Category withTrashed()
 * @method static \Platform\Database\Eloquent\Builder|Category withoutNode($node)
 * @method static \Platform\Database\Eloquent\Builder|Category withoutRoot()
 * @method static \Platform\Database\Eloquent\Builder|Category withoutSelf()
 * @method static Builder|Category withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Category extends Node implements ConfigurationExporter, HtmlTranslatable, Translatable
{
    use BelongsToForm;
    use ExportsConfiguration;
    use HasCamelCasing, TranslatableModel {
        HasCamelCasing::attributesToArray as camelAttributesToArray;
        HasCamelCasing::__isset as camelIsset;
        TranslatableModel::__isset as translationIsset;
    }
    use HasFiles;
    use HasScopedNestedSet;
    use HasSlugs;
    use InjectTranslations;
    use Raiseable;
    use SoftDeletes;
    use TranslationRetrieval;
    use Translations;

    const DEFAULT_MAX_IMAGE_WIDTH = 300;

    /**
     * Return a consumer object.
     */
    protected function consumer(): Consumer
    {
        return \AwardForce\Library\Authorization\Consumer::get();
    }

    /**
     * @var string
     */
    protected $table = 'categories';

    /**
     * Column name which stores reference to parent's node.
     *
     * @var string
     */
    protected $parentColumn = 'parent_id';

    /**
     * Column name for the left index.
     *
     * @var string
     */
    protected $leftColumnName = 'lft';

    /**
     * Column name for the right index.
     *
     * @var string
     */
    protected $rightColumnName = 'rgt';

    /**
     * Column name for the depth field.
     *
     * @var string
     */
    protected $depthColumn = 'depth';

    /**
     * Columns which restrict what we consider our Nested Set list
     *
     * @var string[]
     */
    protected $scoped = ['season_id'];

    /**
     * Column to perform the default sorting
     *
     * @var string
     */
    protected $orderColumn = null;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'active' => 'bool',
        'locked' => 'boolean',
        'attachment_types' => 'array',
        'packingSlip' => 'boolean',
    ];

    /**
     * @var string
     */
    protected $deletedEvent = CategoryWasDeleted::class;

    /**
     * @return array
     */
    public function getAttributes()
    {
        return $this->attributes;
    }

    /**
     * @param  null  $key
     * @param  null  $default
     * @return mixed
     */
    public function getOriginal($key = null, $default = null)
    {
        return Arr::get($this->original, $key, $default);
    }

    /**
     * Creates a new instance of the Category model
     *
     * @return Category
     *
     * @throws \Exception
     */
    public static function add(
        $seasonId,
        $formId,
        $status,
        $chapterIds = null,
        $parent = null,
        $divisions = null,
        $entrantMaxEntries = null,
        $fillEntryName = null,
        $maxImageWidth = null,
        $pdfPackingSlip = null,
        $promoted = null,
        $locked = false
    ) {
        $category = new Category;

        $category->accountId = current_account_id();
        $category->seasonId = $seasonId;
        $category->formId = $formId;
        $category->active = $status;
        $category->divisions = $divisions ?: 1;
        $category->entrantMaxEntries = $entrantMaxEntries;
        $category->fillEntryName = $fillEntryName ?? false;
        $category->maxImageWidth = $maxImageWidth ?: Category::DEFAULT_MAX_IMAGE_WIDTH;
        $category->packingSlip = $pdfPackingSlip ?? false;
        $category->promoted = $promoted;
        $category->createdAt = Carbon::now();
        $category->updatedAt = Carbon::now();
        $category->locked = $locked;

        $category->save();

        // Add parent association
        if (! empty($parent)) {
            $category->makeChildOf($parent);
        }

        // Add chapter associations
        if (! empty($chapterIds)) {
            $category->chapters()->sync($chapterIds);
        }

        return $category;
    }

    /**
     * Updates an existing instance of a Category
     *
     * @param  array  $attachmentTypes
     * @param  array  $chapterIds
     * @return bool|void
     */
    public function edit(
        $attachmentTypes,
        $chapterIds,
        $divisions,
        $entrantMaxEntries,
        $fillEntryName,
        $maxImageWidth,
        $packingSlip,
        $parent,
        $reassign,
        $status,
        $locked,
        $promoted
    ) {
        // Update category settings
        $this->entrantMaxEntries = $entrantMaxEntries;
        $this->fillEntryName = $fillEntryName;
        $this->maxImageWidth = $maxImageWidth ?: Category::DEFAULT_MAX_IMAGE_WIDTH;
        $this->updatedAt = Carbon::now();

        // Attachment type override
        $this->attachmentTypes = array_map(function ($item) {
            return implode("\n", $item);
        }, $attachmentTypes);

        // Update divisions
        if ($reassign || $this->divisions > $divisions) {
            $this->raise(new CategoryDivisionsWereChanged($this));
        }

        $this->divisions = $divisions ?: 1;

        // Packing slip
        $this->packingSlip = $packingSlip;

        // Lock category on submitted entries
        $this->locked = $locked;

        // Save modified category
        $this->save();

        // Toggle status
        $status ? $this->activate() : $this->deactivate();

        // Manipulate tree
        if (! empty($parent)) {
            $this->makeChildOf($parent);
        } else {
            // This category is now a root (has no parent)
            $this->makeRoot();
        }

        // Add chapter associations
        $this->chapters()->sync($chapterIds);

        $this->promoted = $promoted;
    }

    /**
     * Set the array of model attributes. No checking is done.
     *
     * @param  bool  $sync
     * @return void
     */
    public function setRawAttributes(array $attributes, $sync = false)
    {
        $convertedAttributes = [];

        foreach ($attributes as $key => $value) {
            $convertedAttributes[snake_case($key)] = $value;
        }

        parent::setRawAttributes($convertedAttributes, $sync);
    }

    /**
     * The required translatable fields for this model.
     *
     * @return array
     */
    public function getTranslatableFields()
    {
        return ['name', 'description', 'shortcode', 'packingSlipInstructions', 'image_heading', 'entryNameLabel'];
    }

    public function htmlFields(): array
    {
        return ['description', 'packingSlipInstructions'];
    }

    /**
     * The fields that need something appended to them when copied.
     *
     * @return array
     */
    public function getTranslatableCopyAppendFields()
    {
        return [
            'name' => 'category.actions.copy.append',
        ];
    }

    /**
     * Category belong to an Account.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function account()
    {
        return $this->belongsTo(Account::class);
    }

    /**
     * Categories must belong to one season.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function season()
    {
        return $this->belongsTo(Season::class);
    }

    /**
     * Category belongs to many Chapters
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function chapters()
    {
        return $this->belongsToMany(Chapter::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function fields()
    {
        return $this->belongsToMany(Field::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function discounts()
    {
        return $this->belongsToMany(Discount::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function panels()
    {
        return $this->belongsToMany(Panel::class)->withPivot('divisions');
    }

    public function tabs()
    {
        return $this->belongsToMany(Tab::class);
    }

    /**
     * Save category
     *
     *
     * @return bool
     */
    public function save(array $options = [])
    {
        if ($this->exists) {
            $this->raiseUnique(new CategoryWasUpdated($this));
        } else {
            $this->raiseUnique(new CategoryWasCreated($this));
        }

        return parent::save($options);
    }

    /**
     * Return the cache key for the model. Method taken from AwardForce
     * base model as we're not extending it here.
     *
     * @return string
     */
    public function cacheKey()
    {
        if ($this->exists) {
            return class_basename($this).'-'.$this->id.'-'.$this->updatedAt;
        }

        return class_basename($this).'-new';
    }

    /**
     * Copy a category, ensuring to update the lft/rgt values so that these can be populated by the
     * nested set node system. Also make sure we raise a new event.
     *
     * @return \Illuminate\Database\Eloquent\Model
     */
    public function copy(?array $except = null)
    {
        $except = $except ?: [
            $this->getKeyName(),
            'depth',
            'lft',
            'rgt',
            'slug',
            $this->getCreatedAtColumn(),
            $this->getUpdatedAtColumn(),
        ];

        $category = $this->replicate($except);
        $category->raise(new CategoryWasCopied($this, $category));

        return $category;
    }

    /**
     * Overload the delete method so that we can throw more specific event objects.
     *
     * @return bool|null
     *
     * @throws \Exception
     */
    public function delete()
    {
        $this->refresh()->makeRoot();

        $result = parent::delete();

        if ($result) {
            $this->raise(new CategoryWasDeleted($this));
        }

        return $result;
    }

    /**
     * @return Category
     */
    public function deletedParent()
    {
        return $this->parent()->withTrashed()->first();
    }

    /**
     * Returns true if the Category has children.
     *
     * @return bool
     */
    public function hasChildren()
    {
        return ! $this->isLeaf();
    }

    /**
     * Returns true if the Category has a parent Category.
     *
     * @return bool
     */
    public function hasParentCategory()
    {
        return $this->{$this->parentColumn} !== null;
    }

    /**
     * Ensure falsy values (such as an empty string) are stored as null.
     */
    public function setEntrantMaxEntriesAttribute($value)
    {
        $this->attributes['entrant_max_entries'] = $value ?: null;
    }

    public function deactivate()
    {
        if (! $this->active) {
            return $this;
        }

        $this->active = false;
        $this->save();

        $this->raise(new CategoryWasDeactivated($this));

        return $this;
    }

    public function activate()
    {
        if ($this->active) {
            return $this;
        }

        $this->active = true;
        $this->save();

        $this->raise(new CategoryWasActivated($this));

        return $this;
    }

    /**
     * Disable timestamps while manipulating category tree.
     *
     * @param  \Baum\Baum\Node|int  $target
     * @param  string  $position
     * @return Node
     */
    protected function moveTo($target, $position)
    {
        $this->timestamps = false;

        $result = parent::moveTo($target, $position);

        $this->timestamps = true;

        return $result;
    }

    /**
     * Setting up isset to work across 2 separate traits.
     *
     * @param  string  $key
     * @return bool|mixed
     */
    public function __isset($key)
    {
        if ($this->translationIsset($key)) {
            return true;
        }

        return $this->camelIsset($key);
    }

    /**
     * Converts objects with __toString methods to their string representations when
     * fetching all attributes from a model.
     *
     * @return array
     */
    public function attributesToArray()
    {
        return array_map(function ($attribute) {
            if (is_object($attribute) && method_exists($attribute, '__toString')) {
                return $attribute->__toString();
            }

            return $attribute;
        }, self::camelAttributesToArray());
    }

    public function setPromotedAttribute($value)
    {
        $this->attributes['promoted'] = ! is_null($value) && $value !== '' ? (bool) $value : null;
    }

    public function isPromoted(): bool
    {
        if ($this->promoted !== null || ! $this->hasParentCategory()) {
            return (bool) $this->promoted;
        }

        return $this->parent->isPromoted();
    }

    public function promotedValue(): string
    {
        $value = ! $this->hasParentCategory() ? (bool) $this->promoted : $this->promoted;

        return match ($value) {
            false => 'no',
            true => 'yes',
            default => 'inherit'
        };
    }

    public function packingSlipsEnabled(): bool
    {
        return $this->packingSlip && feature_enabled('packing_slips');
    }

    public function resourceLabel()
    {
        return $this->name ?? '';
    }

    public function configurationRelations(): array
    {
        return [
            'chapters',
            'files',
        ];
    }

    protected function fileResource(): string
    {
        return File::RESOURCE_CATEGORIES;
    }

    public function scopeOnlyLeaves(Builder $query): Builder
    {
        $grammar = $this->getConnection()->getQueryGrammar();

        $rgtCol = $grammar->wrap($this->getQualifiedRightColumnName());
        $lftCol = $grammar->wrap($this->getQualifiedLeftColumnName());

        return $query->whereRaw($rgtCol.' - '.$lftCol.' = 1');
    }
}
