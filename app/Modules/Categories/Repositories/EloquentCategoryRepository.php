<?php

namespace AwardForce\Modules\Categories\Repositories;

use AwardForce\Library\Database\Eloquent\Caching\HasFlexibleCache;
use AwardForce\Library\Database\Eloquent\Caching\HasRequestCache;
use AwardForce\Library\Database\Eloquent\HardDeletesRepository;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Chapters\Repositories\HasChapterBuilder;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Exports\Models\Exportable;
use AwardForce\Modules\Forms\Forms\Database\Behaviours\DeletedForms;
use AwardForce\Modules\Forms\Forms\Traits\HasFormBuilder;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Repositories\EloquentSeasonalRepository;
use AwardForce\Modules\Seasons\Traits\HasSeasonalBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Support\Facades\DB;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentCategoryRepository extends Repository implements CategoryRepository
{
    use DeletedForms;
    use EloquentSeasonalRepository;
    use Exportable;
    use HardDeletesRepository;
    use HasChapterBuilder;
    use HasFlexibleCache;
    use HasFormBuilder;
    use HasQueryBuilder;
    use HasRequestCache;
    use HasSeasonalBuilder;

    public function __construct(Category $category)
    {
        $this->setModel($category);
    }

    /**
     * Return a collection of all categories with parent
     *
     * @param  ?int  $seasonId
     * @return mixed
     */
    public function getAllCategories(?int $seasonId = null)
    {
        $query = $this->getQuery()->with('parent');

        if ($seasonId) {
            $query->whereSeasonId($seasonId);
        }

        return $query->get();
    }

    /**
     * Return a count of all categories for the account and/or season.
     *
     * @param  int|null  $seasonId
     */
    public function countAllCategories($seasonId = null): int
    {
        $query = $this->getQuery();

        if ($seasonId) {
            $query->whereSeasonId($seasonId);
        }

        return $query->count();
    }

    /**
     * Return a collection of all categories with parent
     */
    public function getAllCategoriesForForm(?int $formId = null): Collection
    {
        $query = $this->getQuery()->with('parent');

        if ($formId) {
            $query->whereFormId($formId);
        }

        return $query->get();
    }

    public function getAllCategoriesForFormTypeAndSeason(string $formType, ?Season $season = null): Collection
    {
        return $this->getQuery()->with('parent')
            ->select('categories.id', 'categories.slug', 'categories.parent_id')
            ->join('forms', function ($join) use ($formType) {
                $join->on('categories.form_id', '=', 'forms.id')->where('forms.type', $formType);
            })
            ->when($season, function ($query) use ($season) {
                $query->where('categories.season_id', $season->id);
            })
            ->get();
    }

    /**
     * Return a count of all categories for the account and/or season.
     */
    public function countAllCategoriesForForm(?int $formId = null): int
    {
        $query = $this->getQuery();

        if ($formId) {
            $query->whereFormId($formId);
        }

        return $query->count();
    }

    public function countAllCategoriesForFormTypeAndSeason(string $formType, ?Season $season = null): int
    {
        return $this->getQuery()->with('parent')
            ->join('forms', 'categories.form_id', 'forms.id')
            ->where('forms.type', $formType)
            ->when($season, function ($query) use ($season) {
                $query->where('categories.season_id', $season->id);
            })
            ->count();
    }

    /**
     * Returns the active Categories assigned to the specified Chapter.
     *
     * @return Collection
     */
    public function getActiveFromChapter(Chapter $chapter, ?Season $season = null)
    {
        $query = $chapter->categories()->whereActive(true)->with('parent');

        if ($season) {
            $query->whereSeasonId($season->id);
        }

        return $query->get();
    }

    /**
     * Returns all Categories assigned to the specified Chapter.
     *
     * @return Collection
     */
    public function getForChapter(Chapter $chapter)
    {
        return $chapter->categories()->get();
    }

    /**
     * Retrieves all categories for the selected chapter ids.
     *
     * @return mixed
     */
    public function getActiveForChapters(array $chapterIds)
    {
        return $this->getQuery()->whereHas('chapters', function ($query) use ($chapterIds) {
            $query->whereIn('chapters.id', $chapterIds);
        })->get();
    }

    /**
     * Return all categories for the required season.
     *
     * @param  int  $seasonId
     * @return Collection
     */
    public function getAllForSeason($seasonId)
    {
        return $this->getQuery()->whereSeasonId($seasonId)->get();
    }

    /**
     * Return all categories for the required season including trashed categories.
     *
     * @param  int  $seasonId
     * @return Collection
     */
    public function getAllForSeasonWithTrashed($seasonId)
    {
        return $this->getQuery()->withTrashed()->whereSeasonId($seasonId)->get();
    }

    /**
     * Return all categories for the provided form.
     *
     * @return Collection
     */
    public function getAllForForm(int $formId)
    {
        return $this->getQuery()->whereFormId($formId)->get();
    }

    /**
     * Return all child categories for the required season
     *
     * @param  int  $seasonId
     * @return collection
     */
    public function getAllChildCategoriesForSeason($seasonId)
    {
        return $this->getQuery()
            ->whereSeasonId($seasonId)
            ->onlyLeaves()
            ->groupBy('categories.id')
            ->get();
    }

    /**
     * Syncs the fields on the category.
     */
    public function syncFields(Category $category, array $fieldIds)
    {
        $category->fields()->sync($fieldIds);
    }

    /**
     * Syncs the chapters on the category.
     */
    public function syncChapters(Category $category, array $chapterIds)
    {
        $category->chapters()->sync($chapterIds);
    }

    /**
     * Syncs the discounts on the category.
     */
    public function syncDiscounts(Category $category, array $discountIds)
    {
        $category->discounts()->sync($discountIds);
    }

    /**
     * Returns the active categories, optionally limited by season.
     *
     * @param  int  $seasonId
     * @return Collection
     */
    public function getActive($seasonId = null)
    {
        $query = $this->getQuery()->whereActive(true);

        if ($seasonId) {
            $query->whereSeasonId($seasonId);
        }

        return $query->get();
    }

    /**
     * Returns the category ids grouped into each chapter that they belong to.
     *
     * @param  int  $seasonId
     * @param  bool  $active
     * @return array
     */
    public function getChapterGroupedList($seasonId = null, $active = true)
    {
        $query = $this->getQuery();

        if ($active) {
            $query->whereActive(true);
        }

        if ($seasonId) {
            $query->whereSeasonId($seasonId);
        }

        $query->join('category_chapter', 'categories.id', '=', 'category_chapter.category_id')
            ->select(['category_chapter.category_id', 'category_chapter.chapter_id']);

        $results = $query->get()->toArray();

        $grouped = [];

        foreach ($results as $result) {
            if (! isset($grouped[$result['chapterId']])) {
                $grouped[$result['chapterId']] = [];
            }

            $grouped[$result['chapterId']][] = $result['categoryId'];
        }

        return $grouped;
    }

    /**
     * Returns the category given the slug, with the essential relations.
     *
     * @param  string  $slug
     * @return Category
     */
    public function getBySlugWithRelations($slug)
    {
        return $this->getByQuery('slug', $slug)->with(['parent', 'season', 'chapters'])->first();
    }

    /**
     * Returns the category given the slug, with the essential relations.
     *
     * @param  string  $slug
     * @return Category
     *
     * @throws
     */
    public function requireBySlugWithRelations($slug)
    {
        $result = $this->getByQuery('slug', $slug)->with(['parent', 'season', 'chapters'])->first();

        if (! $result) {
            $exception = with(new ModelNotFoundException)->setModel(get_class($this->model));

            throw $exception;
        }

        return $result;
    }

    /**
     * Returns the array of category IDs, with divisions, for the specified panel.
     *
     * @param  int  $panelId
     * @return array
     */
    public function getDivisionIdsForPanel($panelId)
    {
        $query = $this->getQuery()
            ->join('category_panel', 'category_panel.category_id', '=', 'categories.id')
            ->where('category_panel.panel_id', $panelId)
            ->select('categories.id', 'category_panel.divisions')
            ->get();

        return $query->reduce(function ($return, $record) {
            if (is_null($record->divisions)) {
                $return[] = $record->id;
            } else {
                foreach (explode(',', $record->divisions) as $division) {
                    if ($division) {
                        $return[] = $record->id.'.'.$division;
                    }
                }
            }

            return $return;
        }, []);
    }

    /**
     * Return the categories along with the entry counts based on the season.
     *
     * @param  int  $seasonId
     * @param  int  $formId
     * @return mixed
     */
    public function getEntrySummariesByCategory($seasonId, $formId)
    {
        return $this->getQuery()
            ->selectRaw('categories.id,
            categories.slug,
            count(distinct case when entries.submitted_at is null then entries.id end) AS in_progress,
            count(distinct case when entries.submitted_at is not null then entries.id end) AS submitted,
            translations.value AS name')
            ->join('translations', function ($join) {
                $join->on('translations.foreign_id', '=', 'categories.id');
                $join->where('translations.resource', '=', 'Category');
            })
            ->join('entries', function ($join) {
                $join->on('entries.category_id', '=', 'categories.id')
                    ->whereNull('entries.deleted_at')
                    ->whereExists(function ($query) {
                        $query->select(DB::raw(1))
                            ->from('chapters')
                            ->whereRaw('entries.chapter_id = chapters.id')
                            ->whereNull('chapters.deleted_at');
                    });
            })
            ->when($seasonId, fn(Builder $query) => $query->where('categories.season_id', $seasonId))
            ->when($formId, fn(Builder $query) => $query->where('categories.form_id', $formId))
            ->onlyLeaves()
            ->orderBy('submitted', 'DESC')
            ->orderBy('in_progress', 'DESC')
            ->orderBy('name', 'ASC')
            ->groupBy('categories.id')
            ->limit(50)
            ->get();
    }

    /**
     * Update chapter count on category.
     *
     * @param  int  $categoryId
     */
    public function updateChapterCount($categoryId)
    {
        DB::table('categories')
            ->where('id', $categoryId)
            ->update(['chapter_count' => DB::raw('(SELECT count(*)
                FROM chapters
                LEFT JOIN category_chapter ON category_chapter.chapter_id = chapters.id
                WHERE chapters.deleted_at IS NULL
                  AND chapters.season_id = categories.season_id
                  AND chapters.account_id = categories.account_id
                  AND category_chapter.category_id = categories.id)')]);
    }

    /**
     * Return all categories that match the title provided.
     *
     * @param  int|null  $seasonId
     * @return mixed
     */
    public function getAllByTitle($seasonId, string $title, bool $includeParents = false)
    {
        $query = $this->getQuery()
            ->select('categories.slug', DB::raw('translations.value AS title'))
            ->join('translations', function ($join) {
                $join->on('translations.foreign_id', '=', 'categories.id');
                $join->where('translations.resource', '=', 'Category');
                $join->where('translations.field', '=', 'name');
            });

        if (is_numeric($seasonId)) {
            $query->where('categories.season_id', '=', $seasonId);
        }

        $query->where('translations.value', 'LIKE', '%'.$title.'%');

        return $query->get()->map(function ($q) {
            return ['id' => (string) $q->slug, 'title' => $q->title];
        })->unique();
    }

    /**
     * Returns the root categories assigned to the specified chapter.
     *
     * @param  int  $seasonId
     * @param  bool  $active
     * @param  bool  $trashed
     * @return Collection
     */
    public function getRootFromChapter(Chapter $chapter, $seasonId, $active = true, $trashed = false, $formId = 0)
    {
        return $this->chapterQuery($chapter, $seasonId, $active)
            ->when($trashed, function ($query) {
                $query->withTrashed();
            })
            ->when($formId, function ($query, $formId) {
                $query->whereFormId($formId);
            })
            ->whereNull('parent_id')
            ->get();
    }

    /**
     * Returns the child categories of the specified category.
     *
     * @param  int  $seasonId
     * @param  bool  $active
     * @return Collection
     */
    public function getChildren(Chapter $chapter, Category $category, $seasonId, $active = true)
    {
        return $this->chapterQuery($chapter, $seasonId, $active)
            ->whereParentId($category->id)
            ->get();
    }

    /**
     * Return all sibling ids of the given category, including self by default
     *
     * @return int[]
     */
    public function siblingIds(int $category, bool $includeSelf = true): array
    {
        $parent = $this->getQuery()->whereId($category)->value('parent_id');

        return $this->getQuery()
            ->when($parent, function ($query, $parent) {
                $query->whereParentId($parent);
            }, function ($query) {
                $query->whereNull('parent_id');
            })
            ->pluck('id')
            ->all();
    }

    private function chapterQuery(Chapter $chapter, int $seasonId, bool $active)
    {
        return $chapter->categories()
            ->with('children')
            ->whereSeasonId($seasonId)
            ->when($active, function ($query, $active) {
                $query->whereActive($active);
            })
            ->where(function ($query) use ($chapter, $active) {
                $query->whereRaw('(rgt - lft) = 1')
                    ->orWhereExists(function ($query) use ($chapter, $active) {
                        $query->select(DB::raw(1))
                            ->from('categories AS child')
                            ->join('category_chapter', 'category_chapter.category_id', '=', 'child.id')
                            ->where('category_chapter.chapter_id', $chapter->id)
                            ->whereRaw('child.lft > categories.lft')
                            ->whereRaw('child.rgt < categories.rgt')
                            ->whereRaw('(child.rgt - child.lft) = 1')
                            ->when($active, function ($query, $active) {
                                $query->where('child.active', $active);
                            });
                    });
            });
    }

    public function getWithAllChildren(Category $category, int $chapterId, int $seasonId, bool $active, bool $trashed, int $formId = 0): Collection
    {
        return $this->getQuery()
            ->select([
                'categories.id',
                'categories.slug',
                'categories.parent_id',
                'categories.lft',
                'categories.rgt',
                'categories.depth',
                'categories.active',
                'categories.locked',
                'categories.max_image_width',
                'categories.fill_entry_name',
                'categories.packing_slip',
                'categories.entrant_max_entries',
                'categories.divisions',
                'categories.attachment_types',
                'categories.created_at',
                'categories.promoted',
            ])
            ->join('category_chapter', 'category_chapter.category_id', '=', 'categories.id')
            ->whereSeasonId($seasonId)
            ->whereChapterId($chapterId)
            ->when($active, function ($query, $active) {
                $query->whereActive($active);
            })
            ->when($formId, function ($query, $formId) {
                $query->whereFormId($formId);
            })
            ->when($trashed, function ($query) {
                $query->withTrashed();
            })
            ->whereRaw("lft BETWEEN {$category->lft} AND {$category->rgt}")
            ->where(function ($query) use ($category) {
                $query->where('categories.id', '=', $category->id)->orWhereNotNull('parent_id');
            })
            ->orderBy('depth', 'ASC')
            ->get();
    }

    public function getByFormId(int $formId)
    {
        return $this->getQuery()->whereFormId($formId)->get();
    }

    public function getAllForSeasonWithChapterIds($seasonId)
    {
        return $this->getQuery()
            ->select('categories.*', \DB::raw('json_arrayagg(category_chapter.chapter_id) as chapterIds'))
            ->leftJoin('category_chapter', 'category_chapter.category_id', '=', 'categories.id')
            ->whereSeasonId($seasonId)
            ->groupBy('categories.id')
            ->get();
    }

    public function getAllForEntriesInExistingGrantReports(?int $userId = null): Collection
    {
        return $this->getQuery()
            ->select('categories.*')
            ->join('entries', 'entries.category_id', 'categories.id')
            ->join('grant_reports', 'grant_reports.entry_id', 'entries.id')
            ->when($userId, fn($query) => $query->where('entries.user_id', $userId))
            ->whereNull('grant_reports.deleted_at')
            ->get();
    }

    public function promotedForSeason(int $seasonId): Collection
    {
        return $this->getActive($seasonId)
            ->filter(fn(Category $category) => $category->isPromoted())
            ->values();
    }

    public function active(bool $active = true): self
    {
        $this->query()->whereActive($active);

        return $this;
    }

    public function leaves(): self
    {
        $this->query()->onlyLeaves();

        return $this;
    }
}
