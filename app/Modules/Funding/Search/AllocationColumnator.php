<?php

namespace AwardForce\Modules\Funding\Search;

use AwardForce\Library\Search\Actions\DeleteAction;
use AwardForce\Library\Search\Actions\PaymentScheduleAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Library\Search\Filters\TagSearchFilter;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use AwardForce\Modules\Funding\Search\Actions\CreateDocumentAction;
use AwardForce\Modules\Funding\Search\Allocations\Columns\Allocated;
use AwardForce\Modules\Funding\Search\Allocations\Columns\AmountDue;
use AwardForce\Modules\Funding\Search\Allocations\Columns\Category;
use AwardForce\Modules\Funding\Search\Allocations\Columns\CategoryShortcode;
use AwardForce\Modules\Funding\Search\Allocations\Columns\Chapter;
use AwardForce\Modules\Funding\Search\Allocations\Columns\Currency;
use AwardForce\Modules\Funding\Search\Allocations\Columns\EntrantContact;
use AwardForce\Modules\Funding\Search\Allocations\Columns\EntrantName;
use AwardForce\Modules\Funding\Search\Allocations\Columns\Entry;
use AwardForce\Modules\Funding\Search\Allocations\Columns\EntryID;
use AwardForce\Modules\Funding\Search\Allocations\Columns\EntryRawLocalId;
use AwardForce\Modules\Funding\Search\Allocations\Columns\EntrySlug;
use AwardForce\Modules\Funding\Search\Allocations\Columns\EntryTitle;
use AwardForce\Modules\Funding\Search\Allocations\Columns\Fund;
use AwardForce\Modules\Funding\Search\Allocations\Columns\Paid;
use AwardForce\Modules\Funding\Search\Allocations\Columns\PaymentsScheduled;
use AwardForce\Modules\Funding\Search\Allocations\Columns\UnscheduledBalance;
use AwardForce\Modules\Funding\Search\Allocations\Filters\CategoryFilter;
use AwardForce\Modules\Funding\Search\Allocations\Filters\ChapterFilter;
use AwardForce\Modules\Funding\Search\Allocations\Filters\EntriesJoinFilter;
use AwardForce\Modules\Funding\Search\Allocations\Filters\EntriesUserJoinFilter;
use AwardForce\Modules\Funding\Search\Allocations\Filters\EntryCategoryFilter;
use AwardForce\Modules\Funding\Search\Allocations\Filters\EntryChapterFilter;
use AwardForce\Modules\Funding\Search\Allocations\Filters\FundFilter;
use AwardForce\Modules\Funding\Search\Allocations\Filters\FundsJoinFilter;
use AwardForce\Modules\Seasons\Search\Columns\LinkedSeason;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Slug;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\KeywordFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class AllocationColumnator extends Columnator implements ApiColumnator
{
    use ApiColumns;

    public static function exportKey(): string
    {
        return 'allocations.export';
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $columns = $this->columns($view);
        $dependencies = new Dependencies;

        $dependencies->add((new ColumnFilter(...$columns))->with(
            'fund_allocations.id',
            'fund_allocations.fund_id',
            'fund_allocations.entry_id',
            'fund_allocations.season_id',
            'fund_allocations.deleted_at',
            'fund_allocations.created_at',
            'fund_allocations.slug',
            'fund_allocations.amount',
        ));
        $dependencies->add(new IncludeFilter('entry.user'));
        $dependencies->add(new SeasonalFilter($this->input['season'] ?? null, 'fund_allocations.season_id'));
        $dependencies->add(new EntriesJoinFilter);
        $dependencies->add(new EntryCategoryFilter);
        $dependencies->add(new EntryChapterFilter);
        $dependencies->add(new EntriesUserJoinFilter);
        $dependencies->add(new FundsJoinFilter);
        $dependencies->add(new GroupingFilter('fund_allocations.id'));
        $dependencies->add(new FundFilter($this->input));
        $dependencies->add(new TagSearchFilter($this->input, $this->repository(), [\AwardForce\Modules\Entries\Models\Entry::class, Allocation::class], [Allocation::class => 'entry_id']));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));
        $dependencies->add(new CategoryFilter($this->input));
        $dependencies->add(new ChapterFilter($this->input));
        $dependencies->add(KeywordFilter::fromKeywords($this->input['keywords'] ?? '', ['entries.title']));
        $dependencies->add(OrderFilter::fromColumns($columns, $this->input, 'fund_allocations.updated')->uniqueColumn('fund_allocations.id'));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('allocation.chapter'), 'Chapter', current_account_id()))->setJoinTable('chapters')->restrictLanguage($language = consumer()->languageCode()));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('allocation.category'), 'Category', current_account_id()))->setJoinTable('categories')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('allocation.season'), 'Season', current_account_id()))->setJoinTable('seasons')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('allocation.fund'), 'Fund', current_account_id()))->setJoinTable('Funds')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('allocation.entrant-name'), 'Entry', current_account_id()))->setJoinTable('Entries')->restrictLanguage($language));

        return $dependencies;
    }

    public function repository(): Repository
    {
        return app(AllocationRepository::class);
    }

    /**
     * @return mixed
     */
    protected function baseColumns(): Columns
    {
        return new Columns([
            new ReactiveMarker,
            (new ActionOverflow($this->key()))
                ->addAction(new CreateDocumentAction('allocation', $this->resource()))
                ->addAction(new DeleteAction('funding.allocation', $this->resource()))
                ->addAction(new PaymentScheduleAction('view', $this->resource())),
            Slug::forResource('fund_allocations'),
            new EntryID,
            new EntrySlug,
            new EntryTitle,
            new EntryRawLocalId,
            new CategoryShortcode,
            new LinkedSeason(trans('funding.table.columns.season'), 'allocation.season'),
            new EntrantName,
            new EntrantContact,
            new Fund,
            new Allocated,
            new Paid(),
            new Category,
            new Chapter,
            new Currency,
            new PaymentsScheduled(),
            new UnscheduledBalance(),
            new AmountDue(),
            Created::forResource('fund_allocations', consumer()->dateLocale()),
            Updated::forResource('fund_allocations', consumer()->dateLocale()),
            new Entry,
        ]);
    }

    public static function key(): string
    {
        return 'allocations.search';
    }

    /**
     * @return string|null
     */
    public function resource()
    {
        return 'Funding';
    }
}
