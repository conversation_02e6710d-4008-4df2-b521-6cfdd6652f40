<?php

namespace AwardForce\Modules\Assignments\Search;

use AwardForce\Library\Cache\Cacher;
use AwardForce\Library\Search\Actions\RecuseAction;
use AwardForce\Library\Search\Actions\UnlockAssignmentAction;
use AwardForce\Library\Search\Actions\UnrecuseAction;
use AwardForce\Library\Search\Columns\ActionOverflow;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Library\Search\Filters\EntryFormFilter;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Library\Search\Filters\TagSearchFilter;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Assignments\Search\Columns\AbstentionComments;
use AwardForce\Modules\Assignments\Search\Columns\AssignmentId;
use AwardForce\Modules\Assignments\Search\Columns\AssignmentSlug;
use AwardForce\Modules\Assignments\Search\Columns\Category;
use AwardForce\Modules\Assignments\Search\Columns\CategoryShortcode;
use AwardForce\Modules\Assignments\Search\Columns\CategorySlug;
use AwardForce\Modules\Assignments\Search\Columns\Chapter;
use AwardForce\Modules\Assignments\Search\Columns\ChapterSlug;
use AwardForce\Modules\Assignments\Search\Columns\Comments;
use AwardForce\Modules\Assignments\Search\Columns\Entry as EntryColumn;
use AwardForce\Modules\Assignments\Search\Columns\EntryId;
use AwardForce\Modules\Assignments\Search\Columns\EntryLocalId;
use AwardForce\Modules\Assignments\Search\Columns\EntrySlug;
use AwardForce\Modules\Assignments\Search\Columns\Form;
use AwardForce\Modules\Assignments\Search\Columns\Judge;
use AwardForce\Modules\Assignments\Search\Columns\JudgeFirstName;
use AwardForce\Modules\Assignments\Search\Columns\JudgeLastName;
use AwardForce\Modules\Assignments\Search\Columns\JudgeSlug;
use AwardForce\Modules\Assignments\Search\Columns\JudgingComments;
use AwardForce\Modules\Assignments\Search\Columns\Method;
use AwardForce\Modules\Assignments\Search\Columns\Panels;
use AwardForce\Modules\Assignments\Search\Columns\ParentCategory;
use AwardForce\Modules\Assignments\Search\Columns\Role;
use AwardForce\Modules\Assignments\Search\Columns\Round;
use AwardForce\Modules\Assignments\Search\Columns\ScoreSet;
use AwardForce\Modules\Assignments\Search\Columns\ScoringCriterionComments;
use AwardForce\Modules\Assignments\Search\Columns\Status;
use AwardForce\Modules\Assignments\Search\Columns\Tags;
use AwardForce\Modules\Assignments\Search\Enhancers\CommentsEnhancer;
use AwardForce\Modules\Assignments\Search\Enhancers\MethodEnhancer;
use AwardForce\Modules\Assignments\Search\Enhancers\ScoreSetEnhancer;
use AwardForce\Modules\Assignments\Search\Filters\JudgeSearchFilter;
use AwardForce\Modules\Assignments\Search\Filters\MethodSearchFilter;
use AwardForce\Modules\Assignments\Search\Filters\SlugSearchFilter;
use AwardForce\Modules\Assignments\Search\Filters\StatusSearchFilter;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Search\Filters\CategorySearchFilter;
use AwardForce\Modules\Entries\Search\Filters\ChapterSearchFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryKeywordFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryStateFilter;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Judging\Search\Filters\AssignmentPanelsFilter;
use AwardForce\Modules\Judging\Search\Filters\ChapterManagerRoleFilter;
use AwardForce\Modules\Judging\Search\Filters\ScoreSetSearchFilter;
use AwardForce\Modules\Panels\Models\PanelRepository;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\ScoringCriteria\Repositories\ScoringCriterionRepository;
use AwardForce\Modules\Search\FormRelated;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class AssignmentsColumnator extends Columnator implements ApiColumnator, FormRelated
{
    use ApiColumns;
    use Cacher;

    /**
     * @return mixed
     */
    protected function baseColumns(): Columns
    {
        $columns = new Columns([
            new AssignmentId,
            new ReactiveMarker,
            $this->actionOverflow(),
            new JudgeSlug,
            new Judge,
            new JudgeFirstName,
            new JudgeLastName,
            new Role,
            new EntryId,
            new CategoryShortcode,
            new EntryLocalId,
            new EntrySlug,
            new EntryColumn,
            new Tags,
            new ChapterSlug,
            new Chapter,
            new CategorySlug,
            new Category,
            new ParentCategory,
            new ScoreSet,
            new Round,
            new Comments,
            new Status,
            new Method,
            new Panels,
            new JudgingComments,
            new AbstentionComments,
            new Form,
            new AssignmentSlug,
            Updated::forResource('assignments', consumer()->dateLocale()),
            Created::forResource('assignments', consumer()->dateLocale()),
        ]);

        foreach ($this->scoringCriteria() as $criterion) {
            $columns->push(new ScoringCriterionComments($criterion));
        }

        return $columns;
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;
        $dependencies->add((new ColumnFilter(...$this->columns($view)))->with(
            'assignments.id',
            'assignments.entry_id',
            'assignments.judge_id',
            'assignments.role_id',
            'assignments.score_set_id',
            'assignments.slug',
            'assignments.locked',
            'assignments.method',
            'assignments.conflict_of_interest',
        ));
        $dependencies->add(new IncludeFilter(
            'season',
            'commentLinks',
            'entry',
            'entry.form',
            'entry.category',
            'entry.category.parent',
            'entry.chapter',
            'entry.entrant.currentMembership',
            'entry.tags',
            'entry.allocations',
            'entry.allocations.tags',
            'scoreSet',
            'scoreSetWithTrashed',
            'rounds',
            'judge',
            'judge.currentMembership',
            'role',
            'panels',
        ));
        $dependencies->add(new SeasonalFilter($this->input['season'] ?? null, 'assignments.season_id'));
        $dependencies->add(new EntryFilter('assignments.entry_id', $deleted = true));
        $dependencies->add(app(ChapterManagerRoleFilter::class));
        $dependencies->add(new SlugSearchFilter($this->input, app(AssignmentRepository::class)));
        $dependencies->add(new ChapterSearchFilter($this->input));
        $dependencies->add(new CategorySearchFilter($this->input));
        $dependencies->add(new TagSearchFilter($this->input, app(EntryRepository::class), [Entry::class, Allocation::class], [Allocation::class => 'entry_id']));
        $dependencies->add(new ScoreSetSearchFilter($this->input, 'assignments', app(ScoreSetRepository::class)));
        $dependencies->add(new EntryFormFilter($this->input, 'score_sets.form_id'));
        $dependencies->add(new JudgeSearchFilter($this->input));
        $dependencies->add(new MethodSearchFilter($this->input));
        $dependencies->add(new StatusSearchFilter($this->input));
        $dependencies->add(new EntryStateFilter($this->input));
        $dependencies->add(new AssignmentPanelsFilter($this->input, app(PanelRepository::class)));
        $dependencies->add(new GroupingFilter('assignments.id'));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view), $this->input, 'assignments.updated')->uniqueColumn('assignments.id'));
        $dependencies->add((new TranslatedColumnSearchFilter($this->columns($view)->translatedColumns(['form']), 'Form', current_account_id()))->setJoinTable('entries'));

        if ($keywords = Arr::get($this->input, 'keywords')) {
            $dependencies->add(new EntryKeywordFilter($keywords, ['entries.title', 'entries.slug', 'entries.local_id']));
        }

        // Enhancers
        $dependencies->add(app(CommentsEnhancer::class));
        $dependencies->add(app(ScoreSetEnhancer::class));
        $dependencies->add(app(MethodEnhancer::class));

        return $dependencies;
    }

    /**
     * @return string|null
     */
    public function resource()
    {
        return 'EntriesAll';
    }

    /**
     * @return Collection
     */
    protected function fieldColumns()
    {
        return collect([]);
    }

    public static function key(): string
    {
        return 'assignments.search';
    }

    public static function exportKey(): string
    {
        return 'assignments.export';
    }

    public function repository(): Repository
    {
        return app(AssignmentRepository::class);
    }

    /**
     * @return \Platform\Database\Eloquent\Collection
     */
    public function scoringCriteria()
    {
        $accountId = current_account_id();
        $seasonId = $this->input['season'] ?? SeasonFilter::getId();

        return $this->requestCache("scoringCriteria:{$accountId}-{$seasonId}", function () use ($seasonId) {
            return translate(app(ScoringCriterionRepository::class)->getAllBySeason($seasonId));
        });
    }

    private function actionOverflow(): ActionOverflow
    {
        return (new ActionOverflow($this->key()))
            ->addAction(new RecuseAction('assignment', $this->resource()))
            ->addAction(new UnrecuseAction('assignment', $this->resource()))
            ->addAction(new UnlockAssignmentAction('assignment', $this->resource()));
    }
}
