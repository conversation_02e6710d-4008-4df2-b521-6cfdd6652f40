<?php

namespace AwardForce\Modules\GrantReports\Models;

use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Exports\Models\Exportable;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\ImplementsSearchByField;
use AwardForce\Modules\Forms\Fields\Database\Behaviours\WithValues;
use AwardForce\Modules\Forms\Fields\Database\Repositories\RepositoryWithValues;
use AwardForce\Modules\Forms\Forms\Traits\HasFormBuilder;
use AwardForce\Modules\GrantReports\Query\UserOrCollaborator;
use Illuminate\Database\Query\JoinClause;
use Platform\Database\Eloquent\Archivable;
use Platform\Database\Eloquent\BuilderRepository;
use Platform\Database\Eloquent\Collection;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentGrantRepository extends Repository implements BuilderRepository, GrantReportRepository, RepositoryWithValues
{
    use Archivable;
    use Exportable;
    use HasFormBuilder;
    use Has<PERSON><PERSON>yBuilder;
    use ImplementsSearchByField;
    use WithValues;

    /**
     * Make sure we assign the required model.
     */
    public function __construct(GrantReport $model)
    {
        $this->model = $model;
    }

    protected function getJoinEntriesQuery()
    {
        return $this->getQuery()->join('entries', 'grant_reports.entry_id', 'entries.id');
    }

    public function getAllByEntry(int $entryId)
    {
        return $this->getQuery()->whereEntryId($entryId)->get();
    }

    public function getAllByEntries(array $entryIds)
    {
        return $this->getQuery()->whereIn('grant_reports.entry_id', $entryIds)->get();
    }

    public function getIdsByEntries(array $entryIds): array
    {
        return $this->getQuery()->select('grant_reports.id')->whereIn('grant_reports.entry_id', $entryIds)->get()->pluck('id')->toArray();
    }

    public function getForUser(int $userId)
    {
        return $this->getJoinEntriesQuery()->select('grant_reports.*')->where('user_id', $userId)->get();
    }

    public function getTrashedForUser(?int $userId = null)
    {
        return $this->getJoinEntriesQuery()
            ->select('grant_reports.*')
            ->onlyTrashed()
            ->when($userId, fn($query) => $query->whereUserId($userId))
            ->get();
    }

    public function getByIdsWithCategories(array $ids)
    {
        return $this->getJoinEntriesQuery()
            ->select('grant_reports.*')
            ->whereIn('grant_reports.id', $ids)
            ->get();
    }

    public function getIdsWithCategoriesExcludingInvited(array $ids): Collection
    {
        return $this->getJoinEntriesQuery()
            ->with('entry.category')
            ->select('grant_reports.*')
            ->whereIn('grant_reports.id', $ids)
            ->whereNull('entries.invited_at')
            ->get();
    }

    public function user(int $userId): self
    {
        $this->query()->join('entries', function (JoinClause $join) use ($userId) {
            $join->on('entries.id', 'grant_reports.entry_id')
                ->where('entries.user_id', $userId);
        });

        return $this;
    }

    public function userOrCollaborator(int $userId): self
    {
        $this->query()->tap(new UserOrCollaborator($userId));

        return $this;
    }

    public function entry(int|array $entryId): self
    {
        $this->query()->whereIn('grant_reports.entry_id', (array) $entryId);

        return $this;
    }
}
