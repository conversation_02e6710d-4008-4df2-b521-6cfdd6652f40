<?php

namespace AwardForce\Modules\Judging\Middleware;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Assignments\Services\AssignmentUser;
use AwardForce\Modules\Assignments\Services\CurrentAssignments;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use Closure;
use Illuminate\Support\Collection;

class AllowedPreview
{
    /** @var CurrentAssignments */
    private $assignments;

    public function __construct(CurrentAssignments $assignments)
    {
        $this->assignments = $assignments;
    }

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     *
     * @throws \AwardForce\Modules\Assignments\Exceptions\UnknownAssignmentUserTypeException
     */
    public function handle($request, Closure $next)
    {
        $this->rejectEntriesAreNotDuplicates($request);
        $this->rejectNoAssignments($request);

        return $next($request);
    }

    protected function rejectEntriesAreNotDuplicates($request)
    {
        if (! Duplicate::getForEntry($request->entry)->allDuplicateEntryIds()->contains($request->duplicateEntry->id)) {
            abort(404);
        }
    }

    /**
     * @return void
     *
     * @throws \AwardForce\Modules\Assignments\Exceptions\UnknownAssignmentUserTypeException
     */
    protected function rejectNoAssignments($request)
    {
        $user = new AssignmentUser(Consumer::get());

        if ($this->isModeGallery($request)) {
            $scoreSet = $request->scoreSet;
            abort_unless($this->hasAssignments($scoreSet, $user) && $scoreSet->galleryIsOpen(), 404);

            return;
        }

        abort_unless($this->assignments->judgeHasEntry(
            $this->scoreSetOrMode($request),
            $user,
            $request->duplicateEntry->id
        ), 404);
    }

    protected function scoreSetOrMode($request)
    {
        if ($scoreSet = $request->scoreSet) {
            return $scoreSet->id;
        }

        return ScoreSet::MODE_VIP;
    }

    protected function isModeGallery($request)
    {
        return ($scoreSet = $request->scoreSet) && $scoreSet->isGallery();
    }

    protected function hasAssignments(ScoreSet $scoreSet, AssignmentUser $user)
    {
        $allAssignments = app(AssignmentRepository::class)->getForJudge(
            $scoreSet->id,
            $user->id(),
            $user->roleIds(),
            new Collection()
        );

        return ! $allAssignments->isEmpty();
    }
}
