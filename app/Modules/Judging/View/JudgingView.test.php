<?php

namespace AwardForce\Modules\Judging\View;

use AwardForce\Library\Values\Services\ValueTransformer;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Assignments\Models\EloquentAssignmentRepository;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use AwardForce\Modules\Entries\Services\EntryDestroyer;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetSettings;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\FileBag;
use Symfony\Component\HttpFoundation\HeaderBag;
use Symfony\Component\HttpFoundation\InputBag;
use Symfony\Component\HttpFoundation\ServerBag;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class JudgingViewTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function init()
    {
        $this->assignments = app(EloquentAssignmentRepository::class);
        $this->user = $this->setupUserWithRole('Judge', true);

        $this->request = $this->newRequest();
        $this->app->instance('request', $this->request);
    }

    public function testDuplicates(): void
    {
        $assignments = $this->setupAssignments();
        $oldPrimary = Duplicate::getForEntry($assignments[0]->entry);
        $newPrimary = Duplicate::getForEntry($assignments[1]->entry);
        $newPrimary->markAsDuplicate($oldPrimary);
        $newPrimary->forcePrimary();

        $view = $this->getViewStub($assignments[0]->entry);
        $this->assertCount(1, $view->duplicates());
    }

    public function testDuplicatesDeletedRoot(): void
    {
        $assignments = $this->setupAssignments();
        $oldPrimary = Duplicate::getForEntry($assignments[0]->entry);
        $newPrimary = Duplicate::getForEntry($assignments[1]->entry);
        $newPrimary->markAsDuplicate($oldPrimary);
        $newPrimary->forcePrimary();

        app(EntryDestroyer::class)->destroyEntry($assignments[1]->entry->id);

        $view = $this->getViewStub($assignments[0]->entry);
        $this->assertEmpty($view->duplicates());
    }

    public function testShouldReturnRefereeFieldIfTheyAreNotConfigured()
    {
        $view = $this->getViewStub(new Entry);
        $view->setScoreSet(new ScoreSet());

        $this->assertEquals(['name', 'email'], $view->refereeFields());
    }

    public function testShouldReturnRefereeFieldBasedOnItsConfiguration()
    {
        $view = $this->getViewStub(new Entry);
        $scoreSet = new ScoreSet();
        $scoreSet->settings = (new ValueTransformer(ScoreSetSettings::class, ['referee' => ['hideRefereeName' => true, 'hideRefereeEmail' => false]]))->transform(); // only email should be visible
        $view->setScoreSet($scoreSet);
        $this->assertCount(1, $fields = $view->refereeFields());
        $this->assertEquals(['email'], array_values($fields));

        $scoreSet->settings = (new ValueTransformer(ScoreSetSettings::class, ['referee' => ['hideRefereeName' => true, 'hideRefereeEmail' => true]]))->transform(); // no fields should be visible
        $view->setScoreSet($scoreSet);

        $this->assertEmpty($view->refereeFields());
    }

    private function setupAssignments()
    {
        $categories = $this->muffins(2, Category::class);
        $scoreSet = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_VIP,
        ]);
        $round = $this->muffin(Round::class, ['round_type' => Round::ROUND_TYPE_JUDGE, 'enabled' => true]);
        $this->request->request->set('score-set', (string) $scoreSet->slug);

        $entries = [
            $this->muffin(Entry::class, ['category_id' => $categories[0]->id]),
            $this->muffin(Entry::class, ['category_id' => $categories[0]->id]),
            $this->muffin(Entry::class, ['category_id' => $categories[0]->id]),
            $this->muffin(Entry::class, ['category_id' => $categories[1]->id]),
        ];

        $a1 = $this->newAssignment($entries[0], $scoreSet);
        $a2 = $this->newAssignment($entries[1], $scoreSet);
        $a3 = $this->newAssignment($entries[2], $scoreSet);
        $a4 = $this->newAssignment($entries[3], $scoreSet);
        $this->assignments->ensureNonAutoRoundLinksExist(
            $scoreSet->id,
            collect($entries)->pluck('id')->toArray(),
            [$this->user->id],
            [$round->id]
        );

        return [$a1, $a2, $a3, $a4];
    }

    private function newAssignment(Entry $entry, ScoreSet $scoreSet): Assignment
    {
        return $this->muffin(Assignment::class, [
            'entry_id' => $entry->id,
            'judge_id' => $this->user->id,
            'score_set_id' => $scoreSet->id,
        ]);
    }

    private function newRequest(): Request
    {
        $request = new Request;

        $request->query = $parameterBag = app(InputBag::class);
        $request->request = $parameterBag;
        $request->attributes = $parameterBag;
        $request->cookies = $parameterBag;
        $request->files = app(FileBag::class);
        $request->server = app(ServerBag::class);
        $request->headers = app(HeaderBag::class);

        return $request;
    }

    private function getViewStub(Entry $entry)
    {
        return new class($entry)
        {
            use JudgingView;

            public function __construct(protected Entry $entry)
            {
            }

            public function setScoreSet(ScoreSet $scoreSet)
            {
                $this->scoreSet = $scoreSet;
            }

            public function entry()
            {
                return $this->entry();
            }
        };
    }
}
