<?php

namespace AwardForce\Modules\Authentication\Tokens;

use Firebase\JWT\JWT as JWTGenerator;
use Illuminate\Support\Arr;
use Platform\Tokens\Token;

class JWT implements Token
{
    const EXPIRE = 60 * 4; // 4 hours

    public function __construct(private string $key, private array $data = [])
    {

    }

    public function expires(): int
    {
        return self::EXPIRE;
    }

    public function accountId()
    {
        return current_account_id();
    }

    public function generate(): string
    {
        $now = now();

        return JWTGenerator::encode(array_merge([
            'expires' => Arr::get($this->data, 'expires', ($now->addMinutes($this->expires()))->timestamp),
            'iat' => $now->timestamp,
        ], $this->data), openssl_get_privatekey($this->key), 'RS256');
    }
}
