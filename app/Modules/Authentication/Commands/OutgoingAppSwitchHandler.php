<?php

namespace AwardForce\Modules\Authentication\Commands;

use AwardForce\Library\Values\App;
use AwardForce\Modules\Authentication\Events\OutgoingAppWasSwitch;
use AwardForce\Modules\Authentication\Exceptions\UnknownApp;
use AwardForce\Modules\Authentication\Middleware\Authenticator;
use AwardForce\Modules\Identity\Users\Services\UserGateway;
use Platform\Events\EventDispatcher;

class OutgoingAppSwitchHandler
{
    use EventDispatcher;

    public function __construct(private UserGateway $gateway)
    {
    }

    /**
     * @throws UnknownApp
     */
    public function handle(OutgoingAppSwitch $command)
    {
        $this->dispatch(new OutgoingAppWasSwitch(new App($command->app)));

        return $this->gateway->generateSwitchAppToken(
            $command->app,
            $command->user->globalId,
            Authenticator::bypassChallenge(request())
        )['url'];
    }
}
