<?php

namespace AwardForce\Modules\Identity\Users\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Accounts\Contracts\MembershipRepository;
use AwardForce\Modules\Agreements\Models\AgreementRepository;
use AwardForce\Modules\Audit\Data\EventLogRepository;
use AwardForce\Modules\Content\Blocks\Contracts\ContentBlockRepository;
use AwardForce\Modules\Entries\Contracts\ContractRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use AwardForce\Modules\Settings\Services\RegistrationSettings;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Platform\Html\Tabular\Tab;
use Platform\Html\Tabular\Tabular;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

abstract class UserView extends View
{
    public function __construct(
        protected ColumnatorFactory $columnators,
        protected Request $request,
        protected UserRepository $users,
        protected AgreementRepository $agreements,
        protected SeasonRepository $seasons,
        protected RoleRepository $roles,
        protected Engine $translator,
        protected EventLogRepository $eventLogs,
        protected MembershipRepository $memberships,
        protected ContractRepository $contracts,
        protected RegistrationSettings $registrationSettings,
    ) {
        VueData::registerTranslations($this->translationKeys());
    }

    public function tabular()
    {
        $tabs = [
            'profile' => new Tab(trans('users.tabs.profile'), 'users.tabs.profile'),
            'account_fields' => new Tab(trans('users.tabs.account_fields'), 'users.tabs.account-fields', $this->user->id ? '' : 'disabled'),
            'entries' => new Tab(trans('users.tabs.entries'), 'users.tabs.entries', $this->user->id ? '' : 'disabled'),
            'documents' => new Tab(trans('documents.titles.main'), 'users.tabs.documents', $this->user->id ? '' : 'disabled'),
            'preferences' => new Tab(trans('users.tabs.preferences'), 'users.tabs.preferences', $this->user->id ? '' : 'disabled'),
            'comments' => new Tab(trans('entries.comments.title'), 'users.tabs.comments'),
            'grants' => new Tab(trans('grants.titles.grants'), 'users.tabs.grants', $this->user->id ? '' : 'disabled'),
        ];

        $tabs[$this->request->get('tab', 'profile')]->setActive();

        $tabular = new Tabular($this->request, 'normal');
        $tabular->addTab($tabs['profile']);
        $tabular->addTab($tabs['account_fields']);
        $tabular->addTab($tabs['entries']);
        $tabular->addTab($tabs['comments']);

        if (feature_enabled('documents') && Consumer::can('view', 'Documents')) {
            $tabular->addTab($tabs['documents']);
        }

        if (feature_enabled('grants') && Consumer::can('view', 'Grants')) {
            $tabular->addTab($tabs['grants']);
        }

        $tabular->addTab($tabs['preferences']);

        return $tabular;
    }

    public function owner()
    {
        return current_account()->owner;
    }

    public function roles()
    {
        $owner = Auth::user()->ownerOf(current_account());
        $userLevel = $this->manager->highestRolePermissionLevel();

        return $this->translator->shallow($this->roles->getAllWithPermissions())
            ->each(function (Role $role) use ($owner, $userLevel) {
                $roleLevel = $role->permissionLevel();
                $role->name = lang($role, 'name');
                $role->checked = (bool) $this->assignedRoles->where('id', $role->id)->count();
                $role->disabled = ! $owner && (($roleLevel > $userLevel) || ($roleLevel == $userLevel && $role->checked));
                $role->judgeLocked = $role->checked && $this->hasAssignments && $role->isJudge();
            })
            ->sortBy('name', SORT_NATURAL | SORT_FLAG_CASE);
    }

    public function assignedRoles()
    {
        return $this->roles->getRolesForUser($this->user->id);
    }

    public function agreements()
    {
        return $this->user->id ?
            $this->translator->translate($this->agreements->getAllForUser($this->user->id)) : [];
    }

    public function entries()
    {
        return collect();
    }

    public function entryIds(): array
    {
        return $this->entries->pluck('id')->toArray();
    }

    public function entriesColumnator()
    {
        $input = $this->request->all();
        $input['entrant'] = $this->user->id;

        return $this->columnators->forArea('users.entries', $input);
    }

    public function documents()
    {
        return collect();
    }

    public function documentIds(): array
    {
        return $this->documents->pluck('id')->toArray();
    }

    public function documentsColumnator()
    {
        $input = $this->request->all();
        $input['entrant'] = $this->user->id;

        return $this->columnators->forArea('document.user.search', $input);
    }

    public function manager()
    {
        return Auth::user();
    }

    public function activeSeasonId(): int
    {
        return $this->seasons->getActive()->id;
    }

    public function preferencesContentBlock()
    {
        return app(ContentBlockRepository::class)->getByKey('profile-preferences-info-box');
    }

    public function informationIsChangeable()
    {
        $user = $this->request->user;

        return ! $user ||
            ! ($user->ownerOf(current_account()) &&
                $user->ownedAccounts->count() === 1 &&
                $user->id !== \Auth::user()->id);
    }

    public function trialLimitReached()
    {
        return isTrialAccount() && $this->memberships->getTotal() >= config('awardforce.trial.user-limit');
    }

    public function canSave(): bool
    {
        return ! $this->trialLimitReached;
    }

    abstract public function hasAssignments(): bool;

    abstract public function passwordIsChangeable();

    public function showMobile(): bool
    {
        return $this->user->mobile || $this->canResendToken;
    }

    public function canResendToken(): bool
    {
        return $this->mobileRegistrationsEnabled;
    }

    public function mobileRegistrationsEnabled(): bool
    {
        return $this->registrationSettings->mobileRegistrationsEnabled();
    }

    public function translationKeys(): array
    {
        return ['validation', 'miscellaneous.optional', 'miscellaneous.search', 'files'];
    }
}
