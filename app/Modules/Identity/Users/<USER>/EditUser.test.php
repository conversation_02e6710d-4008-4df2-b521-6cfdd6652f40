<?php

namespace AwardForce\Modules\Identity\Users\View;

use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Comments\Services\CommentableComments;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Grants\Search\UserGrantsColumnator;
use AwardForce\Modules\Identity\Users\Models\User;
use Consumer;
use Illuminate\Http\Request;
use PHPUnit\Framework\Attributes\TestWith;
use Platform\Html\Tabular\Tab;
use Symfony\Component\HttpFoundation\ParameterBag;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

class EditUserTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testCanSaveExistingUsersAfterTrialUserLimitExceeded()
    {
        config()->set('awardforce.trial.user-limit', 1);

        Consumer::set(new UserConsumer($user = $this->muffin(User::class)));
        CurrentAccount::set(
            $this->muffin(Account::class, ['user_id' => $user->id, 'trial_end_date' => now()->subDay()])
        );

        $this->muffin(Membership::class, [
            'account_id' => current_account()->id,
            'user_id' => ($newUser = $this->muffin(User::class))->id,
        ]);

        $request = $this->mock(Request::class)->makePartial();
        $request->user = $newUser;

        $editUser = app(EditUser::class);

        $this->assertTrue($editUser->trialLimitReached());
        $this->assertTrue($editUser->canSave());
    }

    public function testViewShouldContainCommentForUser()
    {
        $user = $this->muffin(User::class);
        $commentable = $this->mock(CommentableComments::class);
        $commentable->shouldReceive('forCommentable')->andReturn(['comments']);
        $request = $this->mock(Request::class)->makePartial();
        $request->user = $user;

        $editUser = app(EditUser::class, ['request' => $request]);

        $this->assertCount(1, $editUser->comments());
        $this->assertEquals(['comments'], $editUser->comments());
    }

    public function testViewShouldContainsTabForComments()
    {
        $user = $this->muffin(User::class);
        $request = $this->mock(Request::class)->makePartial();
        $request->shouldReceive('get')->andReturn('comments'); // active tab
        $request->shouldReceive('filled')->andReturn(false);
        $request->user = $user;
        $request->attributes = new ParameterBag();
        $editUser = app(EditUser::class, ['request' => $request]);

        $this->assertEquals('users.tabs.comments', $editUser->tabular()->getActive()->view());
        $this->assertEquals(trans('entries.comments.title'), $editUser->tabular()->getActive()->name());
    }

    #[TestWith([true])]
    #[TestWith([false])]
    public function testGrantsTabFeatureBasedVisibility(bool $enabled): void
    {
        $user = $this->setupUserWithRole('Program manager');
        Feature::shouldReceive('all')->andReturn(collect());
        Feature::shouldReceive('enabled')->with('documents')->andReturnFalse();
        Feature::shouldReceive('enabled')->with('grants')->andReturn($enabled);

        $request = $this->mock(Request::class)->makePartial();
        $request->shouldReceive('get')->with('tab', 'profile')->andReturn('profile');
        $request->shouldReceive('filled')->with('tab')->andReturnFalse();
        $request->user = $user;

        $editUser = app(EditUser::class);

        $grantsTab = array_filter($editUser->tabular()->tabs(), fn(Tab $tab) => $tab->view() === 'users.tabs.grants');

        $this->assertEquals($enabled, ! empty($grantsTab));
    }

    #[TestWith([true])]
    #[TestWith([false])]
    public function testGrantsTabConsumerBasedVisibility(bool $can): void
    {
        $user = $this->setupUserWithRole('Entrant');
        Consumer::spy()->makePartial();
        Consumer::shouldReceive('can')->andReturn($can);

        Feature::shouldReceive('all')->andReturn(collect());
        Feature::shouldReceive('enabled')->andReturnTrue();
        $request = $this->mock(Request::class)->makePartial();
        $request->shouldReceive('get')->with('tab', 'profile')->andReturn('profile');
        $request->shouldReceive('filled')->with('tab')->andReturnFalse();
        $request->user = $user;

        $editUser = app(EditUser::class);

        $grantsTab = array_filter($editUser->tabular()->tabs(), fn(Tab $tab) => $tab->view() === 'users.tabs.grants');

        $this->assertEquals($can, ! empty($grantsTab));
    }

    public function testItCanLoadGrants(): void
    {
        $user = $this->setupUserWithRole('Entrant');
        $grantStatus = $this->muffin(GrantStatus::class);
        $grants = $this->muffins(2, Entry::class, ['grant_status_id' => $grantStatus->id, 'user_id' => $user->id]);

        $this->setupUserWithRole('Program manager', true);

        $request = $this->mock(Request::class)->makePartial();
        $request->user = $user;
        $request->shouldReceive('all')->andReturn([]);

        $result = app(EditUser::class)->grants();

        $this->assertCount(2, $result);
        $this->assertEqualsCanonicalizing(array_map(fn($grant) => $grant->id, $grants), $result->just('id'));
        $this->assertEqualsCanonicalizing(array_map(fn($grant) => $grant->id, $grants), app(EditUser::class)->grantIds());
    }

    public function testItCanLoadGrantsColumnator(): void
    {
        $request = $this->mock(Request::class)->makePartial();
        $request->user = $this->setupUserWithRole('Program manager');
        $request->shouldReceive('all')->andReturn([]);

        $this->assertInstanceOf(UserGrantsColumnator::class, app(EditUser::class)->grantsColumnator());
    }

    public function testEntriesColumnatorDoesNotIncludeAutoScoringFieldColumns(): void
    {
        $user = $this->setupUserWithRole('Program manager', true);
        $entry = $this->muffin(Entry::class, ['user_id' => $user->id]);
        $autoScoringField = $this->muffin(Field::class, [
            'auto_scoring' => true,
            'options' => '{"one": 5, "two": 10}',
            'type' => 'drop-down-list',
            'resource' => Field::RESOURCE_FORMS,
        ]);

        app(ValuesService::class)->syncValuesForObject([
            (string) $autoScoringField->slug => 'one',
        ], $entry);

        $request = $this->mock(Request::class)->makePartial();
        $request->user = $user;
        $request->shouldReceive('all')->andReturn([]);
        $result = app(EditUser::class)->entries()->first()->toArray();

        $this->assertArrayNotHasKey('scores', $result);
    }
}
