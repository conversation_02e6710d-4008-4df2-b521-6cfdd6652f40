<?php

namespace AwardForce\Modules\Broadcasts\View;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Mail\Values\Recipients;
use AwardForce\Library\View\FormatsDates;
use AwardForce\Modules\Broadcasts\Exceptions\NumberOfRecipientsExceeded;
use AwardForce\Modules\Broadcasts\Models\Broadcast;
use AwardForce\Modules\Entries\Search\Filters\IncludeCollaboratorsFilter;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Database\Eloquent\Casts\Json;
use Illuminate\Http\Request;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

abstract class BroadcastForm extends View
{
    use FormatsDates;

    private $caughtMaxRecipientsException;

    public function __construct(
        private Request $request,
        private Engine $translator,
        protected Manager $consumer
    ) {
    }

    public function canDelete(): bool
    {
        return $this->consumer->can('delete', 'Broadcasts');
    }

    /**
     * @return Broadcast
     */
    public function broadcast()
    {
        if (! $this->request->broadcast) {
            $filters = array_merge(
                Json::decode($this->request->filters ?? '') ?? [],
                array_except($this->request->query(), ['legalBasis']),
            );
            $user = Consumer::get()->user();

            $broadcast = Broadcast::create([
                'userId' => Consumer::user()->id,
                'seasonId' => SeasonFilter::getId(),
                'type' => $this->request->type,
                'filters' => $filters,
                'consolidateRecipients' => $this->request->consolidateRecipients ?? true,
                'senderName' => $this->request->senderName ?? $user->fullName(),
                'notifySender' => $this->request->notifySender ?? false,
                'senderAddress' => $this->request->senderAddress ?? $user->email,
                'legalBasis' => $this->request->legalBasis,
                'redirect' => $this->request->redirect ?? 'broadcast.index',
                'dueDate' => $this->request->dueDate ?? '',
                'dueTimezone' => $this->request->dueTimezone ?? null,
            ]);

            $broadcast->translated = translations_from_request($this->request->translated);

            return $broadcast;
        }

        return $this->translator->translate($this->request->broadcast);
    }

    /**
     * @return Recipients
     */
    public function recipients()
    {
        try {
            return $this->broadcast->recipients();
        } catch (NumberOfRecipientsExceeded $e) {
            $this->caughtMaxRecipientsException = true;

            return new Recipients;
        }
    }

    /**
     * @return Recipients
     */
    public function consolidatedRecipients()
    {
        try {
            return $this->broadcast->recipients()->consolidate();
        } catch (NumberOfRecipientsExceeded $e) {
            $this->caughtMaxRecipientsException = true;

            return new Recipients;
        }
    }

    /**
     * Returns max number of recipients if the limit was exceeded or 0 if limit was not exceeded.
     * The max is returned for usage in translation message.
     *
     * @return int
     */
    public function numberOfRecipientsExceeded()
    {
        $max = config('broadcasts.max_recipients_limit', 1000);

        if ($this->caughtMaxRecipientsException) {
            return $max;
        }

        return $this->consolidatedRecipients->count() > $max ? $max : 0;
    }

    public function legalOption()
    {
        return $this->request->broadcast ? $this->request->broadcast->legalBasis : $this->request->legalBasis;
    }

    public function includeCollaborators(): bool
    {
        return $this->request->broadcast
            ? array_get($this->request->broadcast->filters, IncludeCollaboratorsFilter::FILTER) === 'true'
            : $this->request->boolean(IncludeCollaboratorsFilter::FILTER);
    }

    public function dueDate()
    {
        if (! $this->request->broadcast) {
            return null;
        }

        return $this->formatDateForDatepicker('due', $this->request->broadcast->dueDate, $this->request->broadcast->dueTimezone, false);
    }

    public function defaultTimezone()
    {
        return setting('timezone');
    }
}
