<?php

namespace AwardForce\Modules\ScoreSets\Search;

use AwardForce\Library\Search\Columns\Form;
use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Library\Search\Filters\EntryFormFilter;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Library\Search\Filters\SlugFilter;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\ScoreSets\Search\Columns\ActionOverflow;
use AwardForce\Modules\ScoreSets\Search\Columns\Agreement;
use AwardForce\Modules\ScoreSets\Search\Columns\AllowUnsureDecisions;
use AwardForce\Modules\ScoreSets\Search\Columns\ApplicationVotes;
use AwardForce\Modules\ScoreSets\Search\Columns\CategoryVotes;
use AwardForce\Modules\ScoreSets\Search\Columns\ContentBlock;
use AwardForce\Modules\ScoreSets\Search\Columns\Fields;
use AwardForce\Modules\ScoreSets\Search\Columns\GalleryEndDate;
use AwardForce\Modules\ScoreSets\Search\Columns\GalleryEndTimezone;
use AwardForce\Modules\ScoreSets\Search\Columns\GalleryStartDate;
use AwardForce\Modules\ScoreSets\Search\Columns\GalleryStartTimezone;
use AwardForce\Modules\ScoreSets\Search\Columns\LockScores;
use AwardForce\Modules\ScoreSets\Search\Columns\MinPercentageToQualify;
use AwardForce\Modules\ScoreSets\Search\Columns\Mode;
use AwardForce\Modules\ScoreSets\Search\Columns\Name;
use AwardForce\Modules\ScoreSets\Search\Columns\Order;
use AwardForce\Modules\ScoreSets\Search\Columns\PreferencesQuantity;
use AwardForce\Modules\ScoreSets\Search\Columns\RegistrationForm;
use AwardForce\Modules\ScoreSets\Search\Columns\ResponsesQuantity;
use AwardForce\Modules\ScoreSets\Search\Columns\ResultCalculation;
use AwardForce\Modules\ScoreSets\Search\Columns\TopPickMode;
use AwardForce\Modules\ScoreSets\Search\Columns\URL;
use AwardForce\Modules\ScoreSets\Search\Columns\UserVotes;
use AwardForce\Modules\ScoreSets\Search\Columns\VoteLimits;
use AwardForce\Modules\ScoreSets\Search\Columns\VotesRevokable;
use AwardForce\Modules\ScoreSets\Search\Columns\VotesVisible;
use AwardForce\Modules\ScoreSets\Search\Columns\WinnersQuantity;
use AwardForce\Modules\ScoreSets\Search\Filters\ModeFilter;
use AwardForce\Modules\Search\FormRelated;
use AwardForce\Modules\Seasons\Search\Columns\LinkedSeason;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Created;
use Platform\Search\Columns\Slug;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ArchivedFilter;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\GroupingFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class ScoreSetsColumnator extends Columnator implements ApiColumnator, FormRelated
{
    use ApiColumns;

    /**
     * @return mixed
     */
    protected function baseColumns(): Columns
    {
        return new Columns([
            new ReactiveMarker,
            new ActionOverflow,
            new Name,
            new Mode,
            new URL,
            new Order,
            new LinkedSeason(trans('score-set.table.columns.season'), 'score-set.season'),
            new Form,
            Slug::forResource('score_sets'),
            Created::forResource('score_sets', consumer()->dateLocale()),
            Updated::forResource('score_sets', consumer()->dateLocale()),
            new Agreement,
            new RegistrationForm,
            new Fields,
            new ResponsesQuantity,
            new MinPercentageToQualify,
            new AllowUnsureDecisions,
            new TopPickMode,
            new WinnersQuantity,
            new PreferencesQuantity,
            new LockScores,
            new ResultCalculation,
            new ContentBlock,
            new VoteLimits,
            new UserVotes,
            new ApplicationVotes,
            new CategoryVotes,
            new VotesRevokable,
            new VotesVisible,
            new GalleryStartDate,
            new GalleryStartTimezone,
            new GalleryEndDate,
            new GalleryEndTimezone,
        ]);
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $columns = $this->columns($view);

        $dependencies = new Dependencies;
        $dependencies->add((new ColumnFilter(...$columns))->with(
            'score_sets.id',
            'score_sets.slug',
            'score_sets.form_id',
            'score_sets.deleted_at',
            'score_sets.archived_at',
            'score_sets.season_id',
            'score_sets.gallery_starts_tz',
            'score_sets.gallery_ends_tz'
        ));
        $dependencies->add(new SeasonalFilter($this->input['season'] ?? null, 'score_sets.season_id'));
        $dependencies->add(new EntryFormFilter($this->input));
        $dependencies->add(new GroupingFilter('score_sets.id'));
        $dependencies->add(new IncludeFilter('season', 'agreementContentBlock', 'roleRegistration', 'contentBlock', 'form:id,slug'));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new ArchivedFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));
        $dependencies->add(new SlugFilter($this->input, 'score_sets'));
        $dependencies->add(OrderFilter::fromColumns($columns, $this->input, 'score_sets.updated')->uniqueColumn('score_sets.id'));
        $dependencies->add(new ModeFilter(array_get($this->input, 'mode')));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('score-set.name'), 'ScoreSet', current_account_id(), $this->input))->restrictLanguage($language = consumer()->languageCode()));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('score-set.season'), 'Season', current_account_id()))->setJoinTable('seasons')->restrictLanguage($language));
        $dependencies->add((new TranslatedColumnSearchFilter($columns->translatedColumns('form'), 'Form', current_account_id()))->setJoinTable('score_sets')->restrictLanguage($language));

        return $dependencies;
    }

    /**
     * @return string|null
     */
    public function resource()
    {
        return 'ScoreSets';
    }

    public static function key(): string
    {
        return 'score_sets.search';
    }

    public static function exportKey(): string
    {
        return 'score_sets.export';
    }

    public function repository(): Repository
    {
        return app(ScoreSetRepository::class);
    }
}
