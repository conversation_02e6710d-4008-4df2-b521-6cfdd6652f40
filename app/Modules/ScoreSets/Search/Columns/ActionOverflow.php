<?php

namespace AwardForce\Modules\ScoreSets\Search\Columns;

use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use Illuminate\Support\HtmlString;
use Platform\Search\Columns\Columnator;

class ActionOverflow extends Columnator
{
    /**
     * ActionOverflow constructor.
     */
    public function __construct()
    {
        parent::__construct('score_sets.search', 'search.columnator.vue-button');
    }

    /**
     * Returns valid HTML for search list views.
     *
     * @return string|\Illuminate\Contracts\Support\Htmlable
     */
    public function html($record)
    {
        return new HtmlString(view('score-set.search.overflow', [
            'scoreSet' => $record,
            'season_id' => SeasonFilter::getId(),
        ])
            ->render());
    }
}
