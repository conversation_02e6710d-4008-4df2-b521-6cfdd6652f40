<?php

namespace AwardForce\Modules\ScoreSets\Models;

use AwardForce\Library\Database\Eloquent\Caching\HasRequestCache;
use AwardForce\Library\Database\Eloquent\Caching\HasTTLCache;
use AwardForce\Library\Database\Eloquent\HardDeletesRepository;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Library\Database\Repository\Builder\HasSlugBuilder;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Exports\Models\Exportable;
use AwardForce\Modules\Forms\Forms\Database\Behaviours\DeletedForms;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Seasons\Repositories\EloquentSeasonalRepository;
use AwardForce\Modules\Seasons\Traits\HasSeasonalBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Collection;
use Platform\Database\Eloquent\Archivable;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentScoreSetRepository extends Repository implements ScoreSetRepository
{
    use Archivable;
    use DeletedForms;
    use EloquentSeasonalRepository;
    use Exportable;
    use HardDeletesRepository;
    use HasQueryBuilder;
    use HasRequestCache;
    use HasSeasonalBuilder;
    use HasSlugBuilder;
    use HasTTLCache;

    public function __construct(ScoreSet $model)
    {
        $this->model = $model;
    }

    /**
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getByIds(array $ids)
    {
        return $this->getQuery()
            ->whereIn('id', $ids)
            ->orderBy('order', 'asc')
            ->get();
    }

    /**
     * Returns the ScoreSet given the slug, with the essential relations.
     *
     * @param  string  $slug
     * @return ScoreSet
     */
    public function getBySlugWithRelations($slug)
    {
        return $this->getByQuery('slug', $slug)->with(['season'])->first();
    }

    /**
     * Returns the ScoreSet given the slug, with the essential relations.
     *
     * @param  string  $slug
     * @return ScoreSet
     *
     * @throws ModelNotFoundException
     */
    public function requireBySlugWithRelations($slug)
    {
        $result = $this->getQuery()->with(['season'])->whereSlug($slug)->first();

        if (! $result) {
            $exception = with(new ModelNotFoundException)->setModel(get_class($this->model));

            throw $exception;
        }

        return $result;
    }

    /**
     * Returns all non-archived of the Score Sets in the provided season.
     *
     * @param  int  $seasonId
     * @param  array  $excludingModes
     * @param  string  $archived
     * @return Collection
     */
    public function getForSeason($seasonId, $excludingModes = [], $archived = 'none')
    {
        $query = $this->getQuery()
            ->whereSeasonId($seasonId)
            ->orderBy('order', 'asc');

        if (! empty($excludingModes)) {
            $query->whereNotIn('mode', $excludingModes);
        }

        return $this->archivedQuery($archived, $query)->get();
    }

    /**
     * Returns all non-archived of the Score Sets in the provided season.
     *
     * @param  array  $excludingModes
     * @return Collection
     */
    public function getForForm(int $formId, $excludingModes = [], $archived = 'none')
    {
        $query = $this->getQuery()
            ->whereFormId($formId)
            ->whereNull('score_sets.archived_at')
            ->orderBy('order', 'asc');

        if (! empty($excludingModes)) {
            $query->whereNotIn('mode', $excludingModes);
        }

        return $this->archivedQuery($archived, $query)->get();
    }

    /**
     * Returns the archived score sets for the specified season.
     *
     * @param  int  $seasonId
     * @param  array  $excludingModes
     * @return Collection
     */
    public function getArchivedForSeason($seasonId, $excludingModes = [])
    {
        $query = $this->getQuery()
            ->whereNotNull('score_sets.archived_at')
            ->orderBy('order', 'asc');

        if ($seasonId) {
            $query->where('score_sets.season_id', $seasonId);
        }

        if (! empty($excludingModes)) {
            $query->whereNotIn('mode', $excludingModes);
        }

        return $query->get();
    }

    /**
     * Returns a collection of score sets based on the season and required mode.
     *
     * @param  int  $seasonId
     * @param  string  $mode
     * @return Collection
     */
    public function getForSeasonAndMode($seasonId, $mode, ?string $archived = 'none')
    {
        return $this->getSeasonalQuery($seasonId, $mode, $archived)->get();
    }

    /**
     * Returns a collection of score sets for the provided form and mode.
     *
     * @param  string  $mode
     * @return Collection
     */
    public function getForFormAndMode(Form $form, $mode, ?string $archived = 'none')
    {
        return $this->getSeasonalQuery($form->seasonId, $mode, $archived)
            ->where('score_sets.form_id', $form->id)
            ->get();
    }

    /**
     * @param  int  $seasonId
     * @param  string  $mode
     * @return Builder
     */
    protected function getSeasonalQuery($seasonId, $mode, ?string $archived = 'none')
    {
        $query = $this->getQuery()
            ->whereSeasonId($seasonId)
            ->whereMode($mode)
            ->orderBy('order', 'asc');

        return $this->archivedQuery($archived, $query);
    }

    /**
     * Returns array of IDs for score sets in the specified season and mode.
     */
    public function idsForSeasonAndMode(int $season, string $mode): array
    {
        return $this->getQuery()
            ->whereSeasonId($season)
            ->whereMode($mode)
            ->whereNull('score_sets.archived_at')
            ->pluck('id')->all();
    }

    /**
     * Retrieve all score sets based on the following criterion ids.
     *
     * @return mixed
     */
    public function getAllByCriterionIds(array $criterionIds)
    {
        return $this->getQuery()
            ->join('scoring_criteria', 'scoring_criteria.scoreset_id', '=', 'score_sets.id')
            ->whereIn('scoring_criteria.id', $criterionIds)
            ->groupBy('score_sets.id')
            ->orderBy('score_sets.order', 'asc')
            ->get(['score_sets.*']);
    }

    /**
     * Returns the latest created score set for the specified season.
     *
     * @param  int  $seasonId
     * @param  array  $excludingModes
     * @return \AwardForce\Modules\ScoreSets\Models\ScoreSet
     */
    public function getLatest($seasonId, $excludingModes = [])
    {
        $query = $this->getQuery()
            ->forSeason($seasonId)
            ->whereNull('archived_at')
            ->latest();

        if (! empty($excludingModes)) {
            $query->whereNotIn('mode', $excludingModes);
        }

        return $query->first();
    }

    /**
     * Returns all score sets, eager loading the season.
     *
     * @return mixed
     */
    public function getAllWithSeason()
    {
        return $this->getQuery()->with(['season'])->get();
    }

    public function modeExists(int $seasonId, string $mode): bool
    {
        return $this->getQuery()
            ->whereSeasonId($seasonId)
            ->whereMode($mode)
            ->whereNull('score_sets.archived_at')
            ->exists();
    }

    /**
     * Returns the score set with the given id only if it is not protected.
     *
     * @return mixed
     */
    public function getUnprotectedById(int $scoreSetId)
    {
        return $this->getQuery()
            ->where('protected', false)
            ->whereNull('archived_at')
            ->find($scoreSetId);
    }

    /**
     * Return unprotected score sets in the given season.
     *
     * @return mixed
     */
    public function getUnprotectedInSeason(int $seasonId)
    {
        return $this->getQuery()
            ->select(['id'])
            ->where('protected', false)
            ->whereNull('archived_at')
            ->where('season_id', '=', $seasonId)
            ->get();
    }

    /**
     * @return mixed
     */
    public function getUnprotectedForPanels(array $panelIds)
    {
        return $this->getQuery()
            ->join('panels', 'panels.score_set_id', '=', 'score_sets.id')
            ->where('protected', false)
            ->whereNull('archived_at')
            ->whereIn('panels.id', $panelIds)
            ->pluck('score_sets.id')
            ->unique();
    }

    /**
     * Return a collection of the field ids related to the score set id (visibility matrix)
     */
    public function getFieldIds(int $scoreSetId): Collection
    {
        return \DB::query()->select('field_id')
            ->from('field_score_set')
            ->where('score_set_id', $scoreSetId)
            ->pluck('field_id', 'field_id');
    }

    public function getForMode($mode, ?string $archived = 'none'): Collection
    {
        $query = $this->getQuery()
            ->whereMode($mode);

        return $this->archivedQuery($archived, $query)->get();
    }

    private function archivedQuery(?string $archived, Builder $query): Builder
    {
        // 'all' - allow both non-archived and archived
        if ($archived === 'none') {
            $query->whereNull('archived_at');
        } elseif ($archived === 'only') {
            $query->whereNotNull('archived_at');
        }

        return $query;
    }

    public function getForUserByModes(array $modes, ?int $userId = null, array $roleIds = [], bool $guestContext = false)
    {
        $subQuery = with(new Assignment)
            ->selectRaw('1')
            ->whereRaw('assignments.score_set_id = score_sets.id')
            ->leftJoin('entries', 'assignments.entry_id', '=', 'entries.id')
            ->leftJoin('grant_statuses', function (JoinClause $join) {
                $join->on('grant_statuses.id', '=', 'entries.grant_status_id')
                    ->whereRaw('!(grant_statuses.lock_scoring <=> 1)');
            })
            ->whereNull('entries.deleted_at')
            ->whereNotIn('assignments.method', [
                Assignment::METHOD_STRAY,
                Assignment::METHOD_RECUSED,
            ])
            ->when(
                $guestContext,
                fn(Builder $query) => $query->whereIn('assignments.role_id', $roleIds),
                fn(Builder $query) => $query->where(
                    fn($query) => $query->where('assignments.judge_id', $userId)
                        ->orWhere(
                            fn($query) => $query->whereNull('assignments.judge_id')
                                ->whereIn('assignments.role_id', $roleIds)
                        )
                )
            )
            ->limit(1);

        return $this->getQuery()
            ->select('score_sets.id')
            ->whereNull('score_sets.archived_at')
            ->whereIn('score_sets.mode', $modes)
            ->whereNull('score_sets.deleted_at')
            ->havingRaw('('.$subQuery->toSqlWithBindings().') IS NOT NULL')
            ->get();
    }
}
