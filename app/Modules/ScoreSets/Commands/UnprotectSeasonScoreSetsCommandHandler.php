<?php

namespace AwardForce\Modules\ScoreSets\Commands;

use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use Platform\Database\Eloquent\Enums\TrashedMode;

class UnprotectSeasonScoreSetsCommandHandler
{
    /** @var ScoreSetRepository */
    public $scoreSets;

    public function __construct(ScoreSetRepository $scoreSets)
    {
        $this->scoreSets = $scoreSets;
    }

    public function handle(UnprotectSeasonScoreSetsCommand $command)
    {
        $this->scoreSets->includeDeletedForms();

        $this->scoreSets->season($command->season->id)
            ->fields(['id', 'protected'])
            ->trashed(TrashedMode::All)
            ->get()
            ->each->unprotect();
    }
}
