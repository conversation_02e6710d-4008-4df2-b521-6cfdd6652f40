<?php

namespace AwardForce\Modules\ReviewFlow\Data;

use AwardForce\Library\Database\Eloquent\Caching\HasRequestCache;
use AwardForce\Library\Database\Eloquent\HardDeletesRepository;
use AwardForce\Library\Database\Eloquent\Repository;
use AwardForce\Modules\Forms\Forms\Database\Behaviours\DeletedForms;
use AwardForce\Modules\Forms\Forms\Traits\HasFormBuilder;
use AwardForce\Modules\Seasons\Repositories\EloquentSeasonalRepository;
use AwardForce\Modules\Seasons\Traits\HasSeasonalBuilder;
use Platform\Database\Eloquent\HasQueryBuilder;

class EloquentReviewStageRepository extends Repository implements ReviewStageRepository
{
    use DeletedForms;
    use EloquentSeasonalRepository;
    use HardDeletesRepository;
    use HasFormBuilder;
    use HasQueryBuilder;
    use HasRequestCache;
    use HasSeasonalBuilder;

    protected $restrictByAccount = true;

    public function __construct(ReviewStage $model)
    {
        $this->model = $model;
    }

    /**
     * Returns all Review Stages for the specified season.
     *
     * @param  int  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function forSeason($seasonId)
    {
        return $this->getQuery()
            ->whereSeasonId($seasonId)
            ->get();
    }

    /**
     * Returns all Review Stages for the specified form.
     *
     * @return \Platform\Database\Eloquent\Collection
     */
    public function forForm(int $formId)
    {
        return $this->getQuery()
            ->whereFormId($formId)
            ->get();
    }

    /**
     * Return all Review Stages for a specified season that
     * are triggered by entry submission.
     *
     * @param  int  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getStagesStartedByEntrySubmission($seasonId)
    {
        return $this->getQuery()
            ->whereSeasonId($seasonId)
            ->whereStartOnSubmit(true)
            ->get();
    }

    /**
     * Retrieves any stages that should be kicked off as a result of the required stage and action.
     *
     * @param  int  $stageId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getSubsequentStages($stageId, ReviewStageAction $action)
    {
        return $this->getQuery()
            ->join('next_review_stage', 'next_review_stage.next_stage_id', '=', 'review_stages.id')
            ->where('next_review_stage.'.$action->toString(), 1)
            ->where('next_review_stage.current_stage_id', $stageId)
            ->select(['review_stages.*'])
            ->get();
    }

    /**
     * Retrieves all Review Stages by ids in the specified season.
     *
     * @param  int  $seasonId
     * @return \Platform\Database\Eloquent\Collection
     */
    public function getByIdsInSeason(array $ids, $seasonId)
    {
        return $this->getQuery()
            ->whereIn('id', $ids)
            ->where('season_id', $seasonId)
            ->get();
    }

    /**
     * Clears stop_notification_id
     *
     * @return void
     */
    public function clearReviewStageStopNotifications(int $notificationId)
    {
        $this->getQuery()->where('stop_notification_id', $notificationId)->update(['stop_notification_id' => '0']);
    }

    /**
     * Clears proceed_notification_id
     *
     * @return void
     */
    public function clearReviewStageProceedNotifications(int $notificationId)
    {
        $this->getQuery()->where('proceed_notification_id', $notificationId)->update(['proceed_notification_id' => '0']);
    }

    /**
     * @return void
     */
    public function clearReviewStageStartNotifications(int $notificationId)
    {
        $this->getQuery()->where('start_notification_id', $notificationId)->update(['start_notification_id' => '0']);
    }

    public function reviewByReferee(): self
    {
        $this->query()->where('review_by', ReviewStage::REVIEW_REFEREE);

        return $this;
    }

    public function excludeReviewByTypes(array $reviewByTypes): self
    {
        $this->query()->whereNotIn('review_by', $reviewByTypes);

        return $this;
    }
}
