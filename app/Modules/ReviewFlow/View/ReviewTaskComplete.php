<?php

namespace AwardForce\Modules\ReviewFlow\View;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Models\Entry;
use Illuminate\Http\Request;
use Platform\View\View;
use Tectonic\LaravelLocalisation\Translator\Engine;

class ReviewTaskComplete extends View
{
    protected $request;
    protected $entries;
    protected $translator;
    protected $contentBlock;

    public function __construct(Request $request, EntryRepository $entries, Engine $translator)
    {
        $this->request = $request;
        $this->entries = $entries;
        $this->translator = $translator;
    }

    public function setContentBlock($contentBlock)
    {
        $this->contentBlock = $this->translator->translate($contentBlock);
    }

    public function contentBlock()
    {
        return $this->contentBlock;
    }

    public function contentBlockData(): array
    {
        if (! $this->contentBlock()) {
            return [];
        }

        $reviewTask = $this->request->reviewTask;

        $entry = $this->entry();

        $parentCategory = $entry->category->hasParentCategory() ?
            translate($entry->category->parent) : null;

        return Vertical::replaceArrayKeys([
            'review_url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]),
            'first_name' => $entry->user->firstName,
            'last_name' => $entry->user->lastName,
            'entry_name' => $entry->title,
            'entry_slug' => (string) $entry->slug,
            'entry_local_id' => $entry->localId,
            'parent_category' => $parentCategory ? $parentCategory->name : '',
            'category' => $entry->category->name,
            'chapter' => $entry->chapter->name,
        ]);
    }

    public function redirectTo(): ?string
    {
        $route = $this->request->get('redirectTo');

        return $route ? $this->customRedirectRouteToUrl($route) : null;
    }

    private function entry(): Entry
    {
        /** @noinspection PhpIncompatibleReturnTypeInspection */
        return translate($this->entries->getOneByIdWithCategories($this->request->reviewTask->entryId));
    }

    private function customRedirectRouteToUrl(string $routeName): ?string
    {
        return match ($routeName) {
            'entry.manager.view' => $this->managerViewRoute(),
            'review-flow.task.manage' => route($routeName),
            'review-flow.task.review' => $this->reviewTask(),
            default => null // Don't allow redirects for non-whitelisted
        };
    }

    private function managerViewRoute(): string
    {
        return route('entry.manager.view', [
            'entry' => $this->entry(),
            'vtab' => 'entry.manager.tabs.review-flow',
        ]);
    }

    private function reviewTask(): string
    {
        return route('review-flow.task.review', (string) $this->request->reviewTask->token);
    }
}
