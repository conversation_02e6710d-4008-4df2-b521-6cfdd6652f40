<?php

namespace AwardForce\Modules\ReviewFlow\Search;

use AwardForce\Library\Search\Columns\ReactiveMarker;
use AwardForce\Library\Search\Filters\ExcludeDeletedFormFilter;
use AwardForce\Library\Search\Filters\SeasonalFilter;
use AwardForce\Modules\Entries\Search\Filters\EntryFilter;
use AwardForce\Modules\ReviewFlow\Data\ReviewTaskRepository;
use AwardForce\Modules\ReviewFlow\Search\Columns\ActionAt;
use AwardForce\Modules\ReviewFlow\Search\Columns\ActionOverflow;
use AwardForce\Modules\ReviewFlow\Search\Columns\CategoryShortcode;
use AwardForce\Modules\ReviewFlow\Search\Columns\Created;
use AwardForce\Modules\ReviewFlow\Search\Columns\Decision;
use AwardForce\Modules\ReviewFlow\Search\Columns\Entry;
use AwardForce\Modules\ReviewFlow\Search\Columns\EntryId;
use AwardForce\Modules\ReviewFlow\Search\Columns\EntryLocalId;
use AwardForce\Modules\ReviewFlow\Search\Columns\EntrySlug;
use AwardForce\Modules\ReviewFlow\Search\Columns\EntrySubmittedAt;
use AwardForce\Modules\ReviewFlow\Search\Columns\Reviewer;
use AwardForce\Modules\ReviewFlow\Search\Columns\ReviewerEmail;
use AwardForce\Modules\ReviewFlow\Search\Columns\ReviewerName;
use AwardForce\Modules\ReviewFlow\Search\Columns\ReviewStage;
use AwardForce\Modules\ReviewFlow\Search\Columns\ReviewUrl;
use AwardForce\Modules\ReviewFlow\Search\Columns\Season;
use AwardForce\Modules\ReviewFlow\Search\Columns\Thumbnail;
use AwardForce\Modules\ReviewFlow\Search\Columns\Token;
use AwardForce\Modules\ReviewFlow\Search\Enhancers\EntryThumbnails;
use AwardForce\Modules\ReviewFlow\Search\Filters\CategorySearchFilter;
use AwardForce\Modules\ReviewFlow\Search\Filters\ChapterManagerRoleFilter;
use AwardForce\Modules\ReviewFlow\Search\Filters\DecisionSearchFilter;
use AwardForce\Modules\ReviewFlow\Search\Filters\EntryFilter as ReviewEntryFilter;
use AwardForce\Modules\ReviewFlow\Search\Filters\ReviewStageFilter;
use AwardForce\Modules\ReviewFlow\Search\Filters\ReviewTaskKeywordFilter;
use AwardForce\Modules\ReviewFlow\Search\Filters\TaskReviewerFilter;
use AwardForce\Modules\ReviewFlow\Search\Filters\TokenFilter;
use Platform\Database\Eloquent\Repository;
use Platform\Search\ApiColumnator;
use Platform\Search\ApiColumns;
use Platform\Search\Columnator;
use Platform\Search\Columns;
use Platform\Search\Columns\Updated;
use Platform\Search\Defaults;
use Platform\Search\Dependencies;
use Platform\Search\Filters\ColumnFilter;
use Platform\Search\Filters\Dates\DateFilter;
use Platform\Search\Filters\IncludeFilter;
use Platform\Search\Filters\OrderFilter;
use Platform\Search\Filters\PaginationFilter;
use Platform\Search\Filters\TranslatedColumnSearchFilter;
use Platform\Search\Filters\TrashedFilter;

class ManageReviewTasksColumnator extends Columnator implements ApiColumnator
{
    use ApiColumns;

    private ReviewStage $reviewStage;
    private CategoryShortcode $categoryShortcode;

    /**
     * @return mixed
     */
    protected function baseColumns(): Columns
    {
        return new Columns([
            new ReactiveMarker,
            new ActionOverflow($this->key()),
            new Thumbnail,
            new EntryId,
            $this->categoryShortcode = new CategoryShortcode,
            new EntrySlug,
            new EntryLocalId,
            new Entry,
            $this->reviewStage = new ReviewStage,
            new Season,
            new Reviewer,
            new ReviewerName,
            new ReviewerEmail,
            new ReviewUrl,
            new Decision,
            new Created,
            new ActionAt,
            new Token,
            Updated::forResource('review_tasks', consumer()->dateLocale()),
        ]);
    }

    /**
     * All available dependencies for the area.
     */
    public function availableDependencies(Defaults $view): Dependencies
    {
        $dependencies = new Dependencies;

        $dependencies->add((new ColumnFilter(...$this->columns($view)))->with(
            'review_tasks.id',
            'review_tasks.token',
            'review_tasks.entry_id',
            'review_tasks.review_stage_id',
            'entries.category_id',
        ));
        $dependencies->add(new IncludeFilter([
            'assignees',
            'assignees.currentMembership',
            'entry',
            'entry.category:id,slug',
            'entry.tags' => function ($query) {
                $query->orderBy('tag');
            },
            'entry.entrant.currentMembership',
            'reviewStage',
            'reviewStage.season',
            'entry.chapter:id',
            'entry.form:id,type',
        ]));
        $dependencies->add(new EntryFilter('review_tasks.entry_id'));
        $dependencies->add(new CategorySearchFilter);
        $dependencies->add(new ReviewEntryFilter($this->input));
        $dependencies->add(new ReviewStageFilter($this->input));
        $dependencies->add(new TaskReviewerFilter($this->input));
        $dependencies->add(new DecisionSearchFilter($this->input));
        $dependencies->add(new ChapterManagerRoleFilter);
        $dependencies->add(new SeasonalFilter($this->input['season'] ?? null, 'review_stages.season_id'));
        $dependencies->add(new ReviewTaskKeywordFilter($this->input['keywords'] ?? '', ['entries.title', 'entries.slug', 'entries.local_id', $this->reviewStage->translatedField(), $this->categoryShortcode->translatedField()]));
        $dependencies->add(new TokenFilter($this->input));
        $dependencies->add(new PaginationFilter($this->input));
        $dependencies->add(new TrashedFilter($this->input));
        $dependencies->add(OrderFilter::fromColumns($this->columns($view)->push(new EntrySubmittedAt), $this->input, 'manage_review_tasks.entry_submitted_at')->uniqueColumn('review_tasks.id'));
        $dependencies->add(new ExcludeDeletedFormFilter('entries'));
        $dependencies->add((new TranslatedColumnSearchFilter($this->availableColumns()->translatedColumns(['manage_review_tasks.review_stage']), 'ReviewStage', current_account_id()))->setJoinTable('review_stages')->restrictLanguage($language = consumer()->languageCode()));
        $dependencies->add((new TranslatedColumnSearchFilter($this->availableColumns()->translatedColumns(['manage_review_tasks.category_shortcode']), 'Category', current_account_id()))->setJoinTable('categories')->restrictLanguage($language));

        $dependencies->add(new DateFilter($this->input, 'created_at', 'created_at'));
        $dependencies->add(new DateFilter($this->input, 'updated_at', 'updated_at'));
        $dependencies->add(new DateFilter($this->input, 'action_at', 'action_at'));

        // Enhancers
        $dependencies->add(app(EntryThumbnails::class));

        return $dependencies;
    }

    /**
     * @return string|null
     */
    public function resource()
    {
        return 'EntriesAll';
    }

    public static function key(): string
    {
        return 'manage_review_tasks.search';
    }

    public static function exportKey(): string
    {
        return 'manage_review_tasks.export';
    }

    public function repository(): Repository
    {
        return app(ReviewTaskRepository::class);
    }
}
