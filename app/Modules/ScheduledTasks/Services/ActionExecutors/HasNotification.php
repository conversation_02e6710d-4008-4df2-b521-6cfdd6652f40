<?php

namespace AwardForce\Modules\ScheduledTasks\Services\ActionExecutors;

use AwardForce\Modules\Notifications\Data\NotificationRepository;
use Illuminate\Support\Arr;
use Platform\Database\Eloquent\Enums\TrashedMode;

trait HasNotification
{
    private function validateAndSetNotification(bool $withTrashed = false): void
    {
        if (! $notificationId = Arr::get($this->payload, 'notification_id')) {
            return;
        }

        $this->notification = app(NotificationRepository::class)
            ->trashed($withTrashed ? TrashedMode::All : TrashedMode::None)
            ->fields(['*'])
            ->primary($notificationId)
            ->require();
    }
}
