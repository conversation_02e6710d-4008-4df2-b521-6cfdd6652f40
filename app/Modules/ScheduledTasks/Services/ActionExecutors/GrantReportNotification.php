<?php

namespace AwardForce\Modules\ScheduledTasks\Services\ActionExecutors;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\GrantReports\Models\GrantReport;

trait GrantReportNotification
{
    public function whenGrantReportEvent(GrantReport $grantReport)
    {
        $entry = $grantReport->entry;
        $user = $entry->entrant;

        return Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'first_name' => $user->firstName,
            'last_name' => $user->lastName,
            'entry_name' => $entry->title,
            'entry_slug' => (string) $entry->slug,
            'entry_local_id' => local_id($entry),
            'parent_category' => $this->getParentCategoryName($entry->category),
            'category' => translate($entry->category)->name,
            'chapter' => translate($entry->chapter)->name,
            'account_url' => current_account_url(),
            'report_name' => translate($grantReport->form)->name,
            'report_due' => \HTML::localisedDateTime($grantReport->dueDate, setting('default-locale', 'international')),
            'report_url' => current_account_url().route(
                'grant-report.entrant.edit',
                ['grantReport' => (string) $grantReport->slug],
                false
            ),
            'report_slug' => (string) $grantReport->slug,
            'application_slug' => (string) $entry->slug,
            'user_slug' => (string) $user->slug,
        ]);
    }
}
