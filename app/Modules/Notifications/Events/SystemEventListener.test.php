<?php

namespace AwardForce\Modules\Notifications\Events;

use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\UrlShortener\UrlShortenerService;
use AwardForce\Library\Values\Amount;
use AwardForce\Library\Values\Currency;
use AwardForce\Modules\Accounts\Services\MembershipService;
use AwardForce\Modules\Assignments\Events\AssignmentWasCompleted;
use AwardForce\Modules\Assignments\Events\AssignmentWasCompletedByJudge;
use AwardForce\Modules\Assignments\Events\AssignmentWasCreated;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Documents\Events\DocumentWasCreated;
use AwardForce\Modules\Documents\Models\Document;
use AwardForce\Modules\Entries\Events\EntryResubmissionWasRequired;
use AwardForce\Modules\Entries\Events\EntryWasEligible;
use AwardForce\Modules\Entries\Events\EntryWasIneligible;
use AwardForce\Modules\Entries\Events\EntryWasInvited;
use AwardForce\Modules\Entries\Events\EntryWasModerated;
use AwardForce\Modules\Entries\Events\EntryWasResubmitted;
use AwardForce\Modules\Entries\Events\EntryWasSubmitted;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Collaboration\Events\CollaboratorWasInvited;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\Fund;
use AwardForce\Modules\GrantReports\Events\GrantReportWasCreated;
use AwardForce\Modules\GrantReports\Events\GrantReportWasSubmitted;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Grants\Events\GrantStatusWasChanged;
use AwardForce\Modules\Identity\Users\Events\GlobalCommunicationChannelWasAdded;
use AwardForce\Modules\Identity\Users\Events\UserHasRegistered;
use AwardForce\Modules\Identity\Users\Events\UserWasAdded;
use AwardForce\Modules\Identity\Users\Events\UserWasInvited;
use AwardForce\Modules\Identity\Users\Models\GlobalCommunicationChannel;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Commands\SendEligibilityNotificationCommand;
use AwardForce\Modules\Notifications\Commands\SendNotificationCommand;
use AwardForce\Modules\Notifications\Commands\SendSpecificNotificationCommand;
use AwardForce\Modules\Notifications\Commands\SendTaggedNotificationCommand;
use AwardForce\Modules\Notifications\Commands\ThrowInvalidRecipient;
use AwardForce\Modules\Notifications\Data\Context\Category as CategoryContext;
use AwardForce\Modules\Notifications\Data\Context\Form as FormContext;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Services\Courier;
use AwardForce\Modules\Notifications\Services\Recipients\RecipientResolver;
use AwardForce\Modules\Notifications\Services\Recipients\Resolvers\RecipientsResolver;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasCreated;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasProceeded;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasReassigned;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasStopped;
use AwardForce\Modules\ReviewFlow\Listeners\ReviewTaskRecipients;
use AwardForce\Modules\ReviewFlow\Services\ReviewTaskGenerator;
use AwardForce\Modules\Tags\Events\TagWasAdded;
use AwardForce\Modules\Tags\Models\Tag;
use AwardForce\Modules\Tags\Services\TagManager;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Str;
use Mockery as m;
use Platform\Events\EventDispatcher;
use Platform\Features\Features;
use Platform\Kessel\Hyperdrive;
use Platform\Test\EventAssertions;
use Ramsey\Uuid\Uuid;
use Spatie\Html\Elements\A;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;
use Tests\Stubs\Notifications\TestEmailChannelProvider;
use Tests\Stubs\Notifications\TestSmsChannelProvider;

final class SystemEventListenerTest extends BaseTestCase
{
    use Database;
    use EventAssertions;
    use EventDispatcher;
    use Laravel;

    private $courier;
    private $smsProvider;
    private $emailProvider;
    private SystemEventListener $listener;
    private TagManager $tagManager;
    private MembershipService $memberships;
    private $resolver;

    public function init()
    {
        $this->resolver = new RecipientResolver;
        $this->resolver->registerResolver(new RecipientsResolver);
        $this->listener = app(SystemEventListener::class);
    }

    public function testItSendsInvitation(): void
    {
        $shortener = m::mock(UrlShortenerService::class);

        $shortener->shouldReceive('shorten')->once()->andReturns(fake()->url());

        app()->instance(UrlShortenerService::class, $shortener);

        $user = $this->muffin(User::class);

        Bus::fake();

        $this->dispatch(new UserWasInvited($user, 'qwerty'));

        Bus::assertDispatched(SendNotificationCommand::class);
    }

    public function testItSendsAShortLinkInInvitation(): void
    {
        Bus::fake();

        $shortener = m::mock(UrlShortenerService::class);

        $shortener->shouldReceive('shorten')->once()->andReturns($shortUrl = fake()->url());

        app()->instance(UrlShortenerService::class, $shortener);

        $user = $this->muffin(User::class);

        $this->dispatch(new UserWasInvited($user, fake()->text(10)));

        Bus::assertDispatched(SendNotificationCommand::class, function ($params) use ($shortUrl) {
            $data = $params->data;

            /** @var \Illuminate\Contracts\Support\Htmlable */
            $joinLink = Arr::get($data, 'join_link');

            $this->assertStringContainsString($shortUrl, $joinLink->toHtml());

            return true;
        });
    }

    public function testItSendsSmsForSmsOnlyTaggingOfEntries(): void
    {
        current_account()->defineFeatures(new Features([new \Platform\Features\Feature('mobile_registration_sms', 'enabled')]));
        $this->smsProvider = new TestSmsChannelProvider;
        $this->emailProvider = new TestEmailChannelProvider;

        $this->courier = new Courier;
        $this->courier->registerChannel($this->emailProvider);
        $this->courier->registerChannel($this->smsProvider);

        $this->app->instance(Courier::class, $this->courier);

        $this->tagManager = app(TagManager::class);
        $this->memberships = app(MembershipService::class);

        // Tags
        $tagTriggeringNotification = $this->muffin(Tag::class, ['tag' => 'tag_triggering_notification']);
        $tagDoingNothing = $this->muffin(Tag::class, ['tag' => 'do_nothing']);

        // Setup the sms-only notification that will be used for the trigger
        $notification = $this->muffin(Notification::class, ['trigger' => 'entry.tagged']);
        $notification->saveTranslation('en_GB', 'body', '', $this->account->id);
        $notification->saveTranslation('en_GB', 'smsBody', 'This is an sms-only notification.', $this->account->id);
        $this->tagManager->tag($notification, $tagTriggeringNotification->tag);

        // Create entry
        $category = $this->muffin(Category::class);
        $chapter = $this->muffin(Chapter::class);
        $entrant = $this->muffin(User::class, ['mobile' => '+***********']);
        $entry = $this->muffin(Entry::class, [
            'userId' => $entrant->id,
            'chapterId' => $chapter->id,
            'categoryId' => $category->id,
        ]);
        $entry->user->registerMembership($this->account, 'en_GB');

        // Case 1 - No notification expected
        $event = new TagWasAdded($entry, $tagDoingNothing);
        $this->listener->whenEntryWasTagged($event);

        $this->assertNull($this->smsProvider->status);
        $this->assertNull($this->emailProvider->status);

        // Case 2 - expected SMS notification
        $event = new TagWasAdded($entry, $tagTriggeringNotification);
        $this->listener->whenEntryWasTagged($event);

        $this->assertSame('delivered sms', $this->smsProvider->status);
        $this->assertNull($this->emailProvider->status);
    }

    public function testNotificationLanguageWhenReviewStageStarted_reviewByEntrant(): void
    {
        [$entrant, $entry, $stage, $emailChannel] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_ENTRANT,
            'de_DE'
        );

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_ENTRANT,
            current_account_id(),
            [],
            [],
            $entrant,
            true
        );

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $events = $reviewTask->pendingEvents();
        $this->dispatchAll($events);

        $this->assertEquals(1, count($events));
        $this->assertInstanceOf(ReviewTaskWasCreated::class, $events[0]);

        $languagesOfRecipients = $emailChannel->allRecipients->map(function ($recipient) {
            return $recipient->language();
        })->toArray();

        $this->assertContains('de_DE', $languagesOfRecipients);

        // Now switch language
        $reviewTask->entry->entrant->currentMembership->language = 'en_GB';
        $reviewTask->entry->entrant->currentMembership->save();
        $this->dispatchAll($events);

        $languagesOfRecipients = $emailChannel->allRecipients->map(function ($recipient) {
            return $recipient->language();
        })->toArray();

        $this->assertContains('en_GB', $languagesOfRecipients);
    }

    public function testNotificationLanguageWhenReviewStageStarted_reviewByManager(): void
    {
        [$entrant, $entry, $stage, $emailChannel] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_MANAGER,
            'pl_PL'
        );

        $manager1 = $this->setupUserWithRole('ChapterManager');
        $manager1->currentMembership->language = 'fr_FR';
        $manager1->currentMembership->save();
        $manager2 = $this->setupUserWithRole('ChapterManager');
        $manager2->currentMembership->language = 'en_GB';
        $manager2->currentMembership->save();

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_MANAGER,
            current_account_id(),
            [$manager1->id, $manager2->id],
            [],
            null,
            true
        );

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $events = $reviewTask->pendingEvents();
        $this->dispatchAll($events);

        $this->assertEquals(1, count($events));
        $this->assertInstanceOf(ReviewTaskWasCreated::class, $events[0]);

        $languagesOfRecipients = $emailChannel->allRecipients->map(function ($recipient) {
            return $recipient->language();
        })->toArray();

        $this->assertContains('fr_FR', $languagesOfRecipients);
        $this->assertContains('en_GB', $languagesOfRecipients);
    }

    public function testNotificationWhenReviewStageStarted_reviewByEntrantWithOnlyMobile(): void
    {
        [$entrant, $entry, $stage] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_ENTRANT,
            'de_DE'
        );

        $entrant->email = null;
        $entrant->mobile = '+***********';
        $entrant->save();

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_ENTRANT,
            current_account_id(),
            [],
            [],
            $entrant,
            true
        );

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $events = $reviewTask->pendingEvents();
        $this->dispatchAll($events);

        $this->assertEquals(1, count($events));
        $this->assertInstanceOf(ReviewTaskWasCreated::class, $events[0]);
    }

    public function testNotificationWhenReviewStageStarted_reviewByManagerWithOnlyMobile(): void
    {
        [$entrant, $entry, $stage] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_MANAGER,
            'pl_PL'
        );

        $manager = $this->setupUserWithRole('ChapterManager');
        $manager->email = null;
        $manager->mobile = '+***********';
        $manager->save();

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_MANAGER,
            current_account_id(),
            [$manager->id],
            [],
            null,
            true
        );

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $events = $reviewTask->pendingEvents();
        $this->dispatchAll($events);

        $this->assertEquals(1, count($events));
        $this->assertInstanceOf(ReviewTaskWasCreated::class, $events[0]);
    }

    public function testNotificationLanguageWhenReviewStageStarted_reviewByField(): void
    {
        [$entrant, $entry, $stage, $emailChannel] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'fr_FR'
        );

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_FIELD,
            current_account_id(),
            [],
            ['name' => 'John', 'email' => $entrant->email],
            null,
            true
        );

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $events = $reviewTask->pendingEvents();
        $this->dispatchAll($events);

        $this->assertEquals(1, count($events));
        $this->assertInstanceOf(ReviewTaskWasCreated::class, $events[0]);

        $languagesOfRecipients = $emailChannel->allRecipients->map(function ($recipient) {
            return $recipient->language();
        })->toArray();

        $this->assertContains('fr_FR', $languagesOfRecipients);
    }

    public function testItSendsActiveReviewStageNotifications(): void
    {

        [$entrant, $entry, $stage, , $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'fr_FR'
        );
        $notification->update(['active' => true]);

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_FIELD,
            current_account_id(),
            [],
            ['name' => 'John', 'email' => $entrant->email],
            null,
            true
        );

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        Bus::fake();

        $notification->update(['active' => true]);
        $this->listener->whenReviewStageWasStarted(new ReviewTaskWasCreated($reviewTask));

        Bus::assertDispatched(SendSpecificNotificationCommand::class);
    }

    public function testItDoesntSendActiveReviewStageNotificationsIfFeatureDisabled(): void
    {
        Feature::spy();
        Feature::shouldReceive('enabled')->with('review_flow')->andReturn(false);

        [$entrant, $entry, $stage, , $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'fr_FR'
        );
        $notification->update(['active' => true]);

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_FIELD,
            current_account_id(),
            [],
            ['name' => 'John', 'email' => $entrant->email],
            null,
            true
        );

        Bus::fake();

        $notification->update(['active' => true]);
        $this->listener->whenReviewStageWasStarted(new ReviewTaskWasCreated($reviewTask));

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    public function testItDoesntSendInactiveReviewStageNotifications(): void
    {

        [$entrant, $entry, $stage, , $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'fr_FR'
        );
        $notification->update(['active' => false]);

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_FIELD,
            current_account_id(),
            [],
            ['name' => 'John', 'email' => $entrant->email],
            null,
            true
        );

        Bus::fake();

        $this->listener->whenReviewStageWasStarted(new ReviewTaskWasCreated($reviewTask));

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    /**
     * There would be an exception thrown here if Recipients\Factory::createFromString did not return NullRecipient on invalid email
     */
    public function testNullRecipientException(): void
    {

        $user = $this->muffin(User::class, ['email' => 'me@me']);
        Bus::fake();

        $this->listener->whenUserHasRegistered(new UserHasRegistered($user));

        Bus::assertDispatched(SendNotificationCommand::class);
        Bus::assertDispatched(ThrowInvalidRecipient::class);
    }

    private function setupNotificationAndEntrant($reviewBy, $entrantLanguage)
    {
        $courier = new Courier;
        $courier->registerChannel($emailChannel = new TestEmailChannelProvider());

        $this->app->singleton(Courier::class, function ($app) use ($courier) {
            return $courier;
        });

        $notification = $this->muffin(Notification::class, ['trigger' => 'review.stage.started']);
        $notification->saveTranslation('en_GB', 'body', 'English', $this->account->id);
        $notification->saveTranslation('de_DE', 'body', 'German', $this->account->id);
        $notification->saveTranslation('fr_FR', 'body', 'French', $this->account->id);
        $notification->saveTranslation('pl_PL', 'body', 'Polish', $this->account->id);

        $entrant = $this->setupUserWithRole('Entrant');
        $entrant->currentMembership->language = $entrantLanguage;
        $entrant->currentMembership->save();
        app(Manager::class)->set(new UserConsumer($entrant));

        $stage = $this->muffin(ReviewStage::class, [
            'mode' => 'notification',
            'review_by' => $reviewBy,
            'start_notification_id' => $notification->id,
        ]);
        $category = $this->muffin(Category::class);
        $chapter = $this->muffin(Chapter::class);
        $entry = $this->muffin(Entry::class, [
            'userId' => $entrant->id,
            'chapterId' => $chapter->id,
            'categoryId' => $category->id,
            'submitted_at' => Carbon::now(),
        ]);

        return [$entrant, $entry, $stage, $emailChannel, $notification];
    }

    public function testChannelConfirmation(): void
    {

        $user = $this->muffin(User::class, ['email' => '<EMAIL>']);

        $communicationChannel = GlobalCommunicationChannel::make([
            'id' => Uuid::uuid4()->toString(),
            'globalUserId' => $user->globalUser->id->toString(),
            'channel' => 'email',
            'confirmedAt' => null,
        ]);
        $communicationChannel->setRelation('globalUser', $user->globalUser);
        app()->instance(Courier::class, $courier = m::mock(Courier::class));

        $courier->shouldReceive('deliver')->once()->with(m::on(function ($notification) {
            $data = $notification->replace;
            $this->assertArrayHasKey('verification_link', $data);
            $this->assertInstanceOf(A::class, $data['verification_link']);
            $this->assertArrayHasKey('code', $data);
            preg_match('/<a[^>]*>(.*?)<\/a>/s', $data['verification_link'], $titleMatch);
            $this->assertEquals(trans('auth.request_login_code.verification-link-text'), $titleMatch[1]);

            return true;
        }), m::any());

        $this->listener->whenGlobalCommunicationChannelWasAdded(new GlobalCommunicationChannelWasAdded($communicationChannel));
    }

    private function attachFundAllocationToEntry(Entry $entry): Entry
    {
        /** @var Fund $fund */
        $fund1 = $this->muffin(Fund::class);

        /** @var Fund $fund */
        $fund2 = $this->muffin(Fund::class);

        $fund1->saveTranslation('en_GB', 'name', 'fund_1_test', $this->account->id);
        $fund2->saveTranslation('en_GB', 'name', 'fund_2_test', $this->account->id);

        /** @var Allocation $allocation1 */
        $allocation1 = $this->muffin(Allocation::class, [
            'entry_id' => $entry->getKey(),
            'fund_id' => $fund1->getKey(),
            'amount' => new Amount(9.99, new Currency('USD')),
        ]);

        /** @var Allocation $allocation2 */
        $allocation2 = $this->muffin(Allocation::class, [
            'entry_id' => $entry->getKey(),
            'fund_id' => $fund2->getKey(),
            'amount' => new Amount(8.99, new Currency('USD')),
        ]);

        return $entry;
    }

    /**
     * @throws \Exception
     */
    public function testWhenEntryWasSubmittedWithFundAllocations(): void
    {
        [$entrant, $entry, $stage, $emailChannel, $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'en_GB'
        );

        $entry = $this->attachFundAllocationToEntry($entry);

        Bus::fake();

        $this->listener->whenEntryWasSubmitted(new EntryWasSubmitted($entry));

        Bus::assertDispatched(SendNotificationCommand::class, function ($params) {
            $data = $params->data;

            $this->assertTrue(Str::containsAll($data['fund_allocations'], ['fund_1_test', '$9.99', 'fund_2_test', '$8.99']));

            return true;
        });
    }

    public function testWhenEntryWasSubmittedConvertSpecialCharactersToHTMLEntities(): void
    {
        [$entrant, $entry, $stage, $emailChannel, $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'en_GB'
        );

        $entry->title = '<h1>entryTitle</h1>';

        $entrant->firstName = '<h1>firstName</h1>';
        $entrant->lastName = '<h1>lastName</h1>';
        $entrant->save();

        $entry->category->saveTranslation('en_GB', 'name', '<h1>category</h1>', current_account_id());

        $parentCategory = $this->muffin(Category::class);
        $parentCategory->saveTranslation('en_GB', 'name', '<h1>parentCategory</h1>', current_account_id());
        $entry->category->makeChildOf($parentCategory);

        $entry->chapter->saveTranslation('en_GB', 'name', '<h1>chapter</h1>', current_account_id());

        Bus::fake();

        $this->listener->whenEntryWasSubmitted(new EntryWasSubmitted($entry));

        Bus::assertDispatched(SendNotificationCommand::class, function ($params) {
            $data = $params->data;

            $this->assertEquals('&lt;h1&gt;entryTitle&lt;/h1&gt;', $data['entry_name']);
            $this->assertEquals('&lt;h1&gt;firstName&lt;/h1&gt;', $data['first_name']);
            $this->assertEquals('&lt;h1&gt;lastName&lt;/h1&gt;', $data['last_name']);
            $this->assertEquals('&lt;h1&gt;category&lt;/h1&gt;', $data['category']);
            $this->assertEquals('&lt;h1&gt;parentCategory&lt;/h1&gt;', $data['parent_category']);
            $this->assertEquals('&lt;h1&gt;chapter&lt;/h1&gt;', $data['chapter']);

            return true;
        });
    }

    public function testWhenEntryWasSubmittedAssignmentCreatedWithJudgesNameAndSurname(): void
    {
        [,$entry] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'en_GB'
        );

        $notification = $this->muffin(Notification::class, [
            'trigger' => 'assignment.created',
        ]);

        $assignment = $this->muffin(Assignment::class, [
            'notification_id' => $notification->id,
            'entry_id' => $entry->id,
        ]);

        $event = new EntryWasSubmitted($entry);

        Bus::fake();

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $this->listener->whenEntryWasSubmitted($event);

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function ($params) use ($assignment) {
            $data = $params->data;

            $this->assertEquals($assignment->judge->first_name, $data['first_name']);
            $this->assertEquals($assignment->judge->last_name, $data['last_name']);

            return true;
        });
    }

    /**
     * @throws \Exception
     */
    public function testWhenEntryWasResubmittedWithFundAllocations(): void
    {
        [$entrant, $entry, $stage, $emailChannel, $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'en_GB'
        );

        $entry = $this->attachFundAllocationToEntry($entry);

        Bus::fake();

        $this->listener->whenEntryWasResubmitted(new EntryWasResubmitted($entry));

        Bus::assertDispatched(SendNotificationCommand::class, function ($params) {
            $data = $params->data;

            $this->assertTrue(Str::containsAll($data['fund_allocations'], ['fund_1_test', '$9.99', 'fund_2_test', '$8.99']));

            return true;
        });
    }

    /**
     * @throws \Exception
     */
    public function testWhenEntryWasModeratedWithFundAllocations(): void
    {
        Bus::fake();

        $entry = $this->attachFundAllocationToEntry($this->muffin(Entry::class));

        $this->listener->whenEntryWasModerated(new EntryWasModerated($entry));

        Bus::assertDispatched(SendNotificationCommand::class, function ($params) {
            $data = $params->data;

            $this->assertTrue(Str::containsAll($data['fund_allocations'], ['fund_1_test', '$9.99', 'fund_2_test', '$8.99']));

            return true;
        });
    }

    /**
     * @throws \Exception
     */
    public function testWhenEntryWasTaggedWithFundAllocations(): void
    {
        [$entrant, $entry, $stage, $emailChannel, $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_MANAGER,
            'en_GB'
        );

        $entry = $this->attachFundAllocationToEntry($entry);

        $notification = $this->muffin(Notification::class, ['trigger' => 'entry.tagged']);
        $tag = $this->muffin(Tag::class);
        app(TagManager::class)->tag($notification, $tag->tag);

        Bus::fake();
        $this->listener->whenEntryWasTagged(new TagWasAdded($entry, $tag));

        Bus::assertDispatched(SendTaggedNotificationCommand::class, function ($params) {
            $data = $params->data;

            $this->assertTrue(Str::containsAll($data['fund_allocations'], ['fund_1_test', '$9.99', 'fund_2_test', '$8.99']));

            return true;
        });
    }

    /**
     * @throws \Exception
     */
    public function testWhenReviewStageWasStartedWithFundAllocations(): void
    {
        [$entrant, $entry, $stage, $emailChannel, $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'en_GB'
        );

        $entry = $this->attachFundAllocationToEntry($entry);

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_FIELD,
            current_account_id(),
            [],
            ['name' => 'John', 'email' => $entrant->email],
            null,
            true
        );

        Bus::fake();

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $this->listener->whenReviewStageWasStarted(new ReviewTaskWasCreated($reviewTask));

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function ($params) {
            $data = $params->data;

            $this->assertTrue(Str::containsAll($data['fund_allocations'], ['fund_1_test', '$9.99', 'fund_2_test', '$8.99']));

            return true;
        });
    }

    /**
     * @throws \Exception
     */
    public function testWhenReviewTaskWasProceededWithFundAllocations(): void
    {
        [$entrant, $entry, $stage, , $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'en_GB'
        );

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_FIELD,
            current_account_id(),
            [],
            ['name' => 'John', 'email' => $entrant->email],
            null,
            true
        );

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $entry = $this->attachFundAllocationToEntry($entry);

        /** @var ReviewStage $reviewStage */
        $reviewStage = $this->muffin(ReviewStage::class, [
            'mode' => 'notification',
            'proceed_notification_id' => $notification->getKey(),
            'review_by' => 'manager',
        ]);

        Bus::fake();

        $this->listener->whenReviewTaskWasProceeded(new ReviewTaskWasProceeded(reviewTask: $reviewTask, reviewStage: $reviewStage));

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function ($params) {
            $data = $params->data;

            $this->assertTrue(Str::containsAll($data['fund_allocations'], ['fund_1_test', '$9.99', 'fund_2_test', '$8.99']));

            return true;
        });
    }

    /**
     * @throws \Exception
     */
    public function testWhenReviewTaskWasStoppedWithFundAllocations(): void
    {
        [$entrant, $entry, $stage, , $notification] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'en_GB'
        );

        $reviewTask = ReviewTask::generate(
            $entry->id,
            $stage->id,
            ReviewTask::LINKED_BY_FIELD,
            current_account_id(),
            [],
            ['name' => 'John', 'email' => $entrant->email],
            null,
            true
        );

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $entry = $this->attachFundAllocationToEntry($entry);

        /** @var ReviewStage $reviewStage */
        $reviewStage = $this->muffin(ReviewStage::class, [
            'mode' => 'notification',
            'stop_notification_id' => $notification->getKey(),
            'review_by' => 'manager',
        ]);

        Bus::fake();

        $this->listener->whenReviewTaskWasStopped(new ReviewTaskWasStopped(reviewTask: $reviewTask, reviewStage: $reviewStage));

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function ($params) {
            $data = $params->data;

            $this->assertTrue(Str::containsAll($data['fund_allocations'], ['fund_1_test', '$9.99', 'fund_2_test', '$8.99']));

            return true;
        });
    }

    public function testNoNotificationWhenReviewStageStarted_reviewByWrongOptionableField(): void
    {
        [$entrant, $entry, $stage, $emailChannel] = $this->setupNotificationAndEntrant(
            ReviewStage::REVIEW_FIELD,
            'fr_FR'
        );

        $dropDown = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'options' => "email1@email1.email1\r\nemail2@wrong", 'type' => function () {
            return 'drop-down-list';
        }]);

        $stage->nomineeEmailFieldId = $dropDown->id;
        $stage->save();

        app(ValuesService::class)->syncValuesForObject([
            (string) $dropDown->slug => 'email2@wrong',
        ], $entry);

        $reviewTask = app(ReviewTaskGenerator::class)->generateTaskForEntry($entry, $stage);

        $events = $reviewTask->pendingEvents();
        $this->dispatchAll($events);

        $this->assertEquals(1, count($events));
        $this->assertInstanceOf(ReviewTaskWasCreated::class, $events[0]);

        // NullRecipientException not thrown

        $this->assertEmpty($emailChannel->allRecipients);
    }

    public function testItSendsEligibilityNotifications(): void
    {

        $this->muffin(Notification::class);
        $notification = $this->muffin(Notification::class);

        $entry = $this->muffin(Entry::class);
        $event = new EntryWasEligible($entry, (string) $notification->slug);

        \Bus::fake();
        $this->listener->whenEligibilityWasChanged($event);

        Bus::assertDispatched(SendEligibilityNotificationCommand::class, function (SendEligibilityNotificationCommand $command) use ($notification) {
            $this->assertEquals($command->notification->id, $notification->id);

            return true;
        });
    }

    public function testItSendsIneligibilityNotifications(): void
    {

        $notification = $this->muffin(Notification::class);

        $entry = $this->muffin(Entry::class);
        $event = new EntryWasIneligible($entry, (string) $notification->slug);

        \Bus::fake();
        $this->listener->whenEligibilityWasChanged($event);

        Bus::assertDispatchedTimes(SendEligibilityNotificationCommand::class, 1);
    }

    public function testItDoesNotEligibilityNotificationsIfEventDoesNotHaveNotification(): void
    {

        $entry = $this->muffin(Entry::class);
        $event = new EntryWasEligible($entry, null);

        \Bus::fake();
        $this->listener->whenEligibilityWasChanged($event);

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    public function testItSendsAssignmentWasCreatedNotifications(): void
    {

        $notification = $this->muffin(Notification::class, [
            'trigger' => 'assignment.created',
        ]);

        $assignment = $this->muffin(Assignment::class, [
            'notification_id' => $notification->id,
        ]);
        $event = new AssignmentWasCreated($assignment);

        app()->instance(Hyperdrive::class, $hyperdrive = m::mock(Hyperdrive::class));
        $hyperdrive->shouldReceive('create')
            ->with('short-url', ['url' => route('clear.login')."?redirect={$assignment->url()}", 'brand' => current_account_brand()])
            ->once()
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        \Bus::fake();
        $this->listener->whenAssignmentWasCreated($event);

        Bus::assertDispatched(SendSpecificNotificationCommand::class);
    }

    public function testItDoesNotSendsAssignmentWasCreatedNotificationsWhenNotificationNotAssigned(): void
    {

        $assignment = $this->muffin(Assignment::class);
        $event = new AssignmentWasCreated($assignment);

        \Bus::fake();
        $this->listener->whenAssignmentWasCreated($event);

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    public function testItDoesNotSendsAssignmentWasCreatedNotificationsWhenEntryIsNotSubmitted(): void
    {

        $notification = $this->muffin(Notification::class, [
            'trigger' => 'assignment.created',
        ]);

        $assignment = $this->muffin(Assignment::class, [
            'notification_id' => $notification->id,
        ]);

        $assignment->entry->submittedAt = null;
        $assignment->entry->save();

        $event = new AssignmentWasCreated($assignment);

        \Bus::fake();
        $this->listener->whenAssignmentWasCreated($event);

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    public function testItSendsAssignmentWasCompletedNotifications(): void
    {

        $assignment = $this->muffin(Assignment::class);
        $event = new AssignmentWasCompletedByJudge($assignment);

        \Bus::fake();
        $this->listener->whenAssignmentWasCompleted($event);

        Bus::assertDispatched(SendNotificationCommand::class, function (SendNotificationCommand $command) use ($assignment) {
            $this->assertEquals($assignment->judge->firstName, array_get($command->data, 'first_name'));
            $this->assertEquals($assignment->judge->lastName, array_get($command->data, 'last_name'));

            return true;
        });
    }

    public function testItSendsFormInvitationNotificationNewUser(): void
    {

        $user = $this->muffin(User::class, ['created_by' => 'invited', 'confirmed_at' => null]);
        $entry = $this->muffin(Entry::class, ['user_id' => $user->id]);
        $notification = $this->muffin(Notification::class);

        Bus::fake();
        $this->listener->whenFormInvitationWasCreated(
            new EntryWasInvited($user, $entry, 'A **message**', $notification)
        );

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function ($params) {
            $data = $params->data;
            $this->assertStringContainsString('/register/invited', $data['invite_link']);
            $this->assertStringNotContainsString('/entry-form/entrant/', $data['invite_link']);

            return true;
        });
    }

    public function testItSendsFormInvitationNotificationNewUserAndNoCategory(): void
    {

        $user = $this->muffin(User::class, ['created_by' => 'invited', 'confirmed_at' => null]);
        $entry = $this->muffin(Entry::class, ['user_id' => $user->id, 'category_id' => null]);
        $notification = $this->muffin(Notification::class);

        Bus::fake();
        $this->listener->whenFormInvitationWasCreated(
            new EntryWasInvited($user, $entry, 'A **message**', $notification)
        );

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function ($params) {
            $data = $params->data;
            $this->assertStringContainsString('/register/invited', $data['invite_link']);
            $this->assertStringNotContainsString('/entry-form/entrant/', $data['invite_link']);

            return true;
        });
    }

    public function testItSendsFormInvitationNotificationExistingUser(): void
    {

        $user = $this->muffin(User::class);
        $entry = $this->muffin(Entry::class, ['user_id' => $user->id]);
        $notification = $this->muffin(Notification::class);

        Bus::fake();
        $this->listener->whenFormInvitationWasCreated(
            new EntryWasInvited($user, $entry, 'A **message**', $notification)
        );

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function ($params) {
            $data = $params->data;
            $this->assertStringContainsString('/entry-form/entrant/', $data['invite_link']);
            $this->assertStringNotContainsString('/register/invited', $data['invite_link']);

            return true;
        });
    }

    public function testItSendsFormInvitationNotificationInUsersLanguage(): void
    {

        $language = 'zh_CN';
        $user = $this->muffin(User::class);
        $user->registerMembership(current_account(), $language);
        $entry = $this->muffin(Entry::class, ['user_id' => $user->id]);
        $notification = $this->muffin(Notification::class);

        Bus::fake();
        $this->listener->whenFormInvitationWasCreated(
            new EntryWasInvited($user, $entry, 'A **message**', $notification)
        );

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function ($params) use ($language) {
            $this->assertEquals($language, $params->defaultRecipient->language());

            return true;
        });
    }

    public function testShouldNotSendNotification(): void
    {

        $user = $this->muffin(User::class);
        $entry = $this->muffin(Entry::class, ['user_id' => $user->id]);

        Bus::fake();
        $this->listener->whenFormInvitationWasCreated(
            new EntryWasInvited($user, $entry, 'A **message**', null)
        );

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    public function testAssignmentWasCompletedNotificationGoesToJudge(): void
    {

        $assignment = $this->muffin(Assignment::class);
        $event = new AssignmentWasCompletedByJudge($assignment);

        \Bus::fake();
        $this->listener->whenAssignmentWasCompleted($event);

        Bus::assertDispatched(SendNotificationCommand::class, function (SendNotificationCommand $command) use ($assignment) {
            $this->assertEquals($assignment->judge->email, (string) $command->defaultRecipient->destination());

            return true;
        });
    }

    public function testAssignmentWasCompletedByGuestShouldNotSendNotification(): void
    {

        $guestUser = $this->muffin(User::class, [
            'first_name' => '',
            'last_name' => '',
            'email' => null,
            'guest_token' => 'GHfSTvinsaNAETQWWENqJAu888iYKhYf',
            'created_by' => 'guest',
        ]);
        $assignment = $this->muffin(Assignment::class, ['judge_id' => $guestUser->id]);
        $event = new AssignmentWasCompletedByJudge($assignment);

        \Bus::fake();
        $this->listener->whenAssignmentWasCompleted($event);

        Bus::assertNotDispatched(SendNotificationCommand::class);
    }

    public function testItShouldntSendEligibilityNotifications(): void
    {

        $notification = $this->muffin(Notification::class, ['active' => 0]);

        $entry = $this->muffin(Entry::class);
        $event = new EntryWasEligible($entry, (string) $notification->slug);

        \Bus::fake();
        $this->listener->whenEligibilityWasChanged($event);

        Bus::assertNotDispatched(SendEligibilityNotificationCommand::class);
    }

    public function testItShouldntSendIneligibilityNotifications(): void
    {

        $notification = $this->muffin(Notification::class, ['active' => 0]);

        $entry = $this->muffin(Entry::class);
        $event = new EntryWasIneligible($entry, (string) $notification->slug);

        \Bus::fake();
        $this->listener->whenEligibilityWasChanged($event);

        Bus::assertNotDispatched(SendEligibilityNotificationCommand::class);
    }

    public function testItShouldSendConfirmationNotificationsWhenUserHasRegistered(): void
    {
        $user = $this->muffin(User::class);

        Bus::fake();
        $event = new UserHasRegistered($user);
        $this->listener->whenUserHasRegistered($event);

        Bus::assertDispatched(SendNotificationCommand::class);
    }

    public function testItShouldSendConfirmationNotificationsWhenUserWasAdded(): void
    {
        $user = $this->muffin(User::class);

        Bus::fake();
        $event = new UserWasAdded($user);
        $this->listener->whenUserHasRegistered($event);

        Bus::assertDispatched(SendNotificationCommand::class);
    }

    public function testShouldSendGrantStatusWasChangedNotifications(): void
    {
        Feature::shouldReceive('enabled')->with('grants')->andReturn(true);

        $entry = $this->muffin(Entry::class);

        $event = new GrantStatusWasChanged($entry);
        \Bus::fake();
        $this->listener->whenGrantStatusWasChanged($event);

        Bus::assertDispatched(SendNotificationCommand::class);
    }

    public function testItShouldSendDocumentWasCreatedNotification(): void
    {
        $document = $this->muffin(Document::class, ['shared' => false]);

        Bus::fake();
        $event = new DocumentWasCreated($document);
        $this->listener->whenDocumentWasCreated($event);

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);

        $notification = $this->muffin(Notification::class, ['trigger' => 'document.created']);

        $event = new DocumentWasCreated($document, $notification->id);
        $this->listener->whenDocumentWasCreated($event);

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);

        $sharedDocument = $this->muffin(Document::class, ['shared' => true]);

        Config::set('filesystems.disks.s3-au.bucket_url', 'https://s3-testing.amazonaws.com/my-documents/');
        Config::set('services.aws.cloudfront.au.domain', null);

        $event = new DocumentWasCreated($sharedDocument, $notification->id);
        $this->listener->whenDocumentWasCreated($event);

        Bus::assertDispatched(SendSpecificNotificationCommand::class, function (SendSpecificNotificationCommand $notificationCommand) {
            $this->assertNotEmpty($notificationCommand->data['attachments']);
            $this->assertEquals('document.docx', $notificationCommand->data['attachments'][0]);

            return true;
        });
    }

    public function testItShouldNotSendDocumentWasCreatedNotificationWhenNotSharedWithUser(): void
    {
        $document = $this->muffin(Document::class, ['shared' => false]);

        $event = new DocumentWasCreated($document);
        Bus::fake();
        $this->listener->whenDocumentWasCreated($event);

        Bus::assertNotDispatched(SendNotificationCommand::class);
    }

    public function testShouldSendGrantReportWasSubmittedNotifications(): void
    {

        $grantReport = $this->muffin(GrantReport::class);

        \Bus::fake();
        $this->dispatch(new GrantReportWasSubmitted($grantReport));

        Bus::assertDispatched(SendNotificationCommand::class, function (SendNotificationCommand $command) use ($grantReport) {
            $this->assertSame($command->trigger, 'grant.report.submitted');
            $this->assertSame((string) $command->defaultRecipient->destination(), (string) $grantReport->user->email);
            $this->assertArrayHasKey('report_name', $command->data);
            $this->assertArrayHasKey('report_url', $command->data);

            return true;
        });
    }

    public function testShouldSendGrantReportWasCreatedNotifications(): void
    {

        $grantReport = $this->muffin(GrantReport::class);

        \Bus::fake();
        $this->dispatch(new GrantReportWasCreated($grantReport));

        //Bus::assertDispatched(SendNotificationCommand::class);
        Bus::assertDispatched(SendNotificationCommand::class, function (SendNotificationCommand $command) use ($grantReport) {
            $this->assertSame($command->trigger, 'grant.report.scheduled');
            $this->assertSame((string) $command->defaultRecipient->destination(), (string) $grantReport->user->email);

            return true;
        });
    }

    public function testReviewTaskWasReassignedShouldSendSpecificNotification(): void
    {
        $notification = $this->muffin(Notification::class, ['trigger' => 'review.stage.started', 'recipients' => '<EMAIL>', 'active' => true]);
        $reviewStage = $this->muffin(ReviewStage::class, ['start_notification_id' => $notification->id]);
        $reviewTask = $this->muffin(ReviewTask::class, ['review_stage_id' => $reviewStage->id]);

        $this->mock(Hyperdrive::class)
            ->shouldReceive('create')
            ->once()
            ->with('short-url', ['url' => route('review-flow.task.view', ['reviewTask' => $reviewTask->token]), 'brand' => current_account_brand()])
            ->andReturn(['shortUrl' => 'https://cr4.ce/shortenedUrl']);

        $this->mock(ReviewTaskRecipients::class)->shouldReceive('get')->andReturn($this->resolver->getRecipients($notification));

        \Bus::fake();

        $this->dispatch(new ReviewTaskWasReassigned($reviewTask, $notification));

        Bus::assertDispatched(SendSpecificNotificationCommand::class);
    }

    public function testReviewTaskWasReassignedShouldNotSendSpecificNotification(): void
    {
        $notification = $this->muffin(Notification::class, ['trigger' => 'review.stage.started', 'recipients' => '<EMAIL>', 'active' => true]);
        $reviewStage = $this->muffin(ReviewStage::class, ['start_notification_id' => $notification->id]);
        $reviewTask = $this->muffin(ReviewTask::class, ['review_stage_id' => $reviewStage->id]);

        $this->mock(ReviewTaskRecipients::class)->shouldReceive('getRecipientsForReviewStage')->andReturn($this->resolver->getRecipients($notification));

        \Bus::fake();

        $this->dispatch(new ReviewTaskWasReassigned($reviewTask, null));

        Bus::assertNotDispatched(SendSpecificNotificationCommand::class);
    }

    public function testAssignmentWasCompletedByJudgeEventShouldSendSpecificNotification(): void
    {
        $assignment = $this->muffin(Assignment::class);

        Bus::fake();
        $this->dispatch(new AssignmentWasCompletedByJudge($assignment));

        Bus::assertDispatched(SendNotificationCommand::class, function (SendNotificationCommand $command) use ($assignment) {
            $this->assertSame($command->trigger, 'assignment.judging.completed');
            $this->assertSame((string) $command->defaultRecipient->destination(), (string) $assignment->judge->email);

            return true;
        });
    }

    public function testAssignmentWasCompletedEventShouldNotSendSpecificNotification(): void
    {
        $assignment = $this->muffin(Assignment::class);

        Bus::fake();
        $this->dispatch(new AssignmentWasCompleted($assignment));

        Bus::assertNotDispatched(SendNotificationCommand::class);
    }

    public function testSendsNotificationToCollaborator(): void
    {
        $collaborator = $this->muffin(Collaborator::class);

        Bus::fake();
        $this->dispatch(new CollaboratorWasInvited($collaborator, $message = str_random()));

        Bus::assertDispatched(SendNotificationCommand::class, function (SendNotificationCommand $command) use ($message, $collaborator) {
            $this->assertSame($command->trigger, 'collaborator.invited');
            $this->assertSame(account_name(), array_get($command->data, 'account_name'));
            $this->assertSame($message, array_get($command->data, 'message'));
            $this->assertSame($collaborator->submittable->title, array_get($command->data, $collaborator->submittable->verticalKey()));
            $this->assertEquals(link_to($collaborator->submittable->editRoute()), array_get($command->data, 'invite_link'));
            $this->assertContainsInstanceOf(FormContext::class, $command->additionalContext);
            $this->assertContainsInstanceOf(CategoryContext::class, $command->additionalContext);

            return true;
        });
    }

    public function testWhenGlobalCommunicationWasAddedForAnInvitedUserSixDigitCodeShouldNotBeSent()
    {

        $user = $this->muffin(User::class, ['email' => '<EMAIL>', 'created_by' => User::CREATED_INVITED]);

        $communicationChannel = GlobalCommunicationChannel::make([
            'id' => Uuid::uuid4()->toString(),
            'globalUserId' => $user->globalUser->id->toString(),
            'channel' => 'email',
            'confirmedAt' => null,
        ]);
        $communicationChannel->setRelation('globalUser', $user->globalUser);
        app()->instance(Courier::class, $courier = m::mock(Courier::class));

        $courier->shouldNotReceive('deliver');

        $this->listener->whenGlobalCommunicationChannelWasAdded(new GlobalCommunicationChannelWasAdded($communicationChannel));
    }

    public function testItSendsSmsFoasdasdOfEntries(): void
    {
        $this->emailProvider = new TestEmailChannelProvider;

        $this->courier = new Courier;
        $this->courier->registerChannel($this->emailProvider);

        $this->app->instance(Courier::class, $this->courier);

        $this->tagManager = app(TagManager::class);

        $tagTriggeringNotification = $this->muffin(Tag::class, ['tag' => 'tag_triggering_notification']);

        $notification = $this->muffin(Notification::class, ['trigger' => 'entry.tagged']);
        $notification->saveTranslation('en_GB', 'body', 'This is an Email notification', $this->account->id);
        $this->tagManager->tag($notification, $tagTriggeringNotification->tag);

        $category = $this->muffin(Category::class);
        $chapter = $this->muffin(Chapter::class);
        $entrantWithNoMembership = $this->muffin(User::class, ['email' => '<EMAIL>']);
        $entryNoMembership = $this->muffin(Entry::class, [
            'userId' => $entrantWithNoMembership->id,
            'chapterId' => $chapter->id,
            'categoryId' => $category->id,
        ]);

        $entrantWithMembership = $this->muffin(User::class, ['email' => '<EMAIL>']);
        $entrantWithMembership->registerMembership($this->account, 'en_GB');
        $entryWithMembership = $this->muffin(Entry::class, [
            'userId' => $entrantWithMembership->id,
            'chapterId' => $chapter->id,
            'categoryId' => $category->id,
        ]);

        // Case 1 - User has Membership, should deliver email
        $event = new TagWasAdded($entryWithMembership, $tagTriggeringNotification);
        $this->listener->whenEntryWasTagged($event);

        $this->assertEquals('delivered email', $this->emailProvider->status);
        $this->emailProvider->status = null; //reset status

        // Case 2 - User does not have Membershio, should not deliver email
        $eventNoMembership = new TagWasAdded($entryNoMembership, $tagTriggeringNotification);
        $this->listener->whenEntryWasTagged($eventNoMembership);

        $this->assertNull($this->emailProvider->status);
    }

    public function testItDoesNotDispatchSendTaggedNotificationWhenNoNotificationCanBeFound(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'tag_triggering_notification']);

        $entry = $this->muffin(Entry::class);
        $event = new TagWasAdded($entry, $tag);

        Bus::fake();
        $this->listener->whenEntryWasTagged($event);

        Bus::assertNotDispatched(SendTaggedNotificationCommand::class);
    }

    public function testEntryResubmissionSendsNotification()
    {
        Bus::fake();
        $entry = $this->muffin(Entry::class);

        $this->listener->whenEntryResubmissionWasRequired(new EntryResubmissionWasRequired($entry));

        Bus::assertDispatched(SendNotificationCommand::class, function (SendNotificationCommand $command) use ($entry) {
            $user = $entry->user;

            $this->assertEquals('resubmission.required', $command->trigger);
            $this->assertEquals($command->defaultRecipient->name(), "$user->firstName $user->lastName");
            $this->assertEquals(current_account_url(), $command->data['account_url']);
            $this->assertEquals($user->firstName, $command->data['first_name']);
            $this->assertEquals($user->lastName, $command->data['last_name']);
            $this->assertEquals($entry->category->name, $command->data['category']);
            $this->assertEquals($entry->chapter->name, $command->data['chapter']);
            $this->assertEquals($entry->seasonId, $command->seasonId);

            //Form validation
            $this->assertCount(2, $command->additionalContext); // entry_id and form context
            $this->assertEquals($entry->id, $command->additionalContext['field_foreign_id']);
            $this->assertInstanceOf(FormContext::class, $command->additionalContext[0]);
            $this->assertEquals(new FormContext($entry->form->id), $command->additionalContext[0]);

            return true;
        });
    }
}
