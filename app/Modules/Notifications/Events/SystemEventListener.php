<?php

namespace AwardForce\Modules\Notifications\Events;

use AwardForce\Library\Facades\Vertical;
use AwardForce\Modules\Assignments\Events\AssignmentWasCompletedByJudge;
use AwardForce\Modules\Assignments\Events\AssignmentWasCreated;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Authentication\Services\SixDigitsCode;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Documents\Events\DocumentWasCreated;
use AwardForce\Modules\Ecommerce\Orders\Events\OrderWasCreated;
use AwardForce\Modules\Entries\Events\EligibilityEvent;
use AwardForce\Modules\Entries\Events\EntryResubmissionWasRequired;
use AwardForce\Modules\Entries\Events\EntryWasInvited;
use AwardForce\Modules\Entries\Events\EntryWasModerated;
use AwardForce\Modules\Entries\Events\EntryWasResubmitted;
use AwardForce\Modules\Entries\Events\EntryWasSubmitted;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Events\CollaboratorWasInvited;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Funding\Services\FundAllocationsTable;
use AwardForce\Modules\GrantReports\Events\GrantReportWasCreated;
use AwardForce\Modules\GrantReports\Events\GrantReportWasSubmitted;
use AwardForce\Modules\Grants\Events\GrantStatusWasChanged;
use AwardForce\Modules\Grants\Models\GrantStatus;
use AwardForce\Modules\Identity\Users\Contracts\GlobalCommunicationChannelRepository;
use AwardForce\Modules\Identity\Users\Events\GlobalCommunicationChannelWasAdded;
use AwardForce\Modules\Identity\Users\Events\RoleWasGranted;
use AwardForce\Modules\Identity\Users\Events\UserHasRegistered;
use AwardForce\Modules\Identity\Users\Events\UserWasAdded;
use AwardForce\Modules\Identity\Users\Events\UserWasInvited;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Tokens\InvitationToken;
use AwardForce\Modules\Notifications\Commands\SendEligibilityNotificationCommand;
use AwardForce\Modules\Notifications\Commands\SendNotificationCommand;
use AwardForce\Modules\Notifications\Commands\SendSpecificNotificationCommand;
use AwardForce\Modules\Notifications\Commands\SendTaggedNotificationCommand;
use AwardForce\Modules\Notifications\Data\Context\Category as CategoryContext;
use AwardForce\Modules\Notifications\Data\Context\Form as FormContext;
use AwardForce\Modules\Notifications\Data\Context\GrantStatusOption;
use AwardForce\Modules\Notifications\Data\Context\ModerationOption as ModerationOptionContext;
use AwardForce\Modules\Notifications\Data\Context\Role as RoleContext;
use AwardForce\Modules\Notifications\Data\Context\ScoreSet as ScoreSetContext;
use AwardForce\Modules\Notifications\Data\NotificationRepository;
use AwardForce\Modules\Notifications\Services\Recipients\Factory as RecipientFactory;
use AwardForce\Modules\Notifications\Services\Recipients\NullRecipient;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasCreated;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasProceeded;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasReassigned;
use AwardForce\Modules\ReviewFlow\Events\ReviewTaskWasStopped;
use AwardForce\Modules\ReviewFlow\Events\SendReviewTaskNotificationWasRequested;
use AwardForce\Modules\ReviewFlow\Services\Recipients\Recipients;
use AwardForce\Modules\ScheduledTasks\Services\ActionExecutors\GrantReportNotification;
use AwardForce\Modules\Tags\Events\TagWasAdded;
use Html;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Support\Facades\Log;
use Platform\Tokens\TokenManager;
use Tectonic\LaravelLocalisation\Facades\Translator;
use Tectonic\LaravelLocalisation\Translator\Engine;

/**
 * The system event listener acts as a conduit for sending notifications based on specific events.
 * As a result, this listener can know quite a bit about events, but only those events. This ensures
 * that all notifications are managed and dealt with in a single location, rather than having to chase
 * them down everywhere. Also, considering that notifications are the responsibility of the
 * Notifications module, then it makes sense that it should sit here.
 */
class SystemEventListener
{
    use DispatchesJobs;
    use FundAllocationsTable;
    use GrantReportNotification;
    use NotificationHelper;

    /**
     * SystemEventListener constructor.
     */
    public function __construct(
        private Engine $translator,
        private GlobalCommunicationChannelRepository $communicationChannels,
        private Recipients $reviewFlowRecipients,
        private TokenManager $tokens,
        private SixDigitsCode $sixDigitsCode,
        private NotificationRepository $notifications
    ) {
    }

    /**
     * Dispatches the user.registered notification.
     */
    public function whenGlobalCommunicationChannelWasAdded(GlobalCommunicationChannelWasAdded $event)
    {
        // Ignore, if already confirmed.
        if (! $event->communicationChannel->canVerify() || ! $event->sendConfirmation || ($user = $event->communicationChannel->globalUser->localUser)->wasInvited) {
            return;
        }

        $token = $this->sixDigitsCode->generateTokenForCommunicationChannel($user, $event->communicationChannel);
        $this->sixDigitsCode->send($user, $token, $user->{$event->communicationChannel->channel});
    }

    public function whenUserHasRegistered(UserHasRegistered|UserWasAdded $event): void
    {
        // Generate confirmation notification
        $user = $event->user;
        $recipient = $this->getRecipient($user);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'confirmation_url' => current_account_url(),
            'confirmation_code' => current_account_url(),
            'first_name' => $user->firstName,
            'last_name' => $user->lastName,
            'name' => $user->name,
            'user_slug' => $user->slug,
        ]);

        $this->dispatch(new SendNotificationCommand('user.registered', $recipient, $data));
    }

    public function whenUserWasInvited(UserWasInvited $event)
    {
        $invitationToken = new InvitationToken($event->user, current_account());

        $fullUrl = route('invitation', [
            'invitationToken' => $this->tokens->create($invitationToken),
        ]);

        $shortUrl = shorten_url($fullUrl);

        Log::info('user.invited', Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'join_link' => link_to($shortUrl),
            'message' => $event->message,
        ]));

        $this->dispatch(new SendNotificationCommand(
            'user.invited',
            RecipientFactory::createFromUser($event->user, current_account()->defaultLanguage()->code),
            Vertical::replaceArrayKeys([
                'account_name' => $this->getAccountName(),
                'join_link' => link_to($shortUrl),
                'message' => $event->message,
            ])
        ));
    }

    /**
     * Dispatches the `entry.submitted` notification.
     *
     * @throws \Exception
     */
    public function whenEntryWasSubmitted(EntryWasSubmitted $event): void
    {
        /** @var Category $category */
        $category = translate($event->entry->category);
        $chapter = translate($event->entry->chapter);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars($chapter?->name),
            'entry_id' => $event->entry->id,
            'entry_local_id' => local_id($event->entry),
            'entry_name' => htmlspecialchars($event->entry->title),
            'entry_slug' => $event->entry->slug,
            'first_name' => htmlspecialchars($event->entry->user->firstName),
            'last_name' => htmlspecialchars($event->entry->user->lastName),
            'parent_category' => $this->getParentCategoryName($category),
            'user_slug' => $event->entry->user->slug,
            'fund_allocations' => $this->fundAllocationsMarkdownTable($event->entry->allocations),
        ]);

        $this->sendEntrySubmittedNotification($event->entry, $data);
        $this->sendAssignmentCreatedNotifications($event->entry, $data);
    }

    public function whenGrantReportWasCreated(GrantReportWasCreated $event): void
    {
        $data = $this->whenGrantReportEvent($event->grantReport());

        $this->dispatch(
            new SendNotificationCommand(
                $event->trigger(),
                $this->getRecipient($event->grantReport()->user),
                $data,
                [
                    'field_foreign_id' => ($report = $event->grantReport())->id,
                    new FormContext($report->formId),
                ],
                $event->grantReport()->seasonId
            )
        );
    }

    public function whenGrantReportWasSubmitted(GrantReportWasSubmitted $event): void
    {
        $data = $this->whenGrantReportEvent($event->grantReport());

        $this->dispatch(
            new SendNotificationCommand(
                $event->trigger(),
                $this->getRecipient($event->grantReport()->user),
                $data,
                [
                    'field_foreign_id' => ($report = $event->grantReport())->id,
                    new FormContext($report->formId),
                ],
                $event->grantReport()->seasonId
            )
        );
    }

    public function whenGrantStatusWasChanged(GrantStatusWasChanged $event): void
    {
        $category = translate($event->entry->category);
        $chapter = translate($event->entry->chapter);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'category' => htmlspecialchars($category->name),
            'chapter' => htmlspecialchars($chapter->name),
            'entry_id' => $event->entry->id,
            'entry_local_id' => local_id($event->entry),
            'entry_name' => htmlspecialchars($event->entry->title),
            'entry_slug' => $event->entry->slug,
            'first_name' => htmlspecialchars($event->entry->user->firstName),
            'last_name' => htmlspecialchars($event->entry->user->lastName),
            'parent_category' => $this->getParentCategoryName($category),
            'user_slug' => $event->entry->user->slug,
            'fund_allocations' => $this->fundAllocationsMarkdownTable($event->entry->allocations),
        ]);

        $this->sendGrantStatusWasChangedNotification($event->entry, $data);
    }

    private function sendGrantStatusWasChangedNotification(Entry $entry, array $data): void
    {
        $this->dispatch(
            new SendNotificationCommand(
                'grant.status.changed',
                $this->getRecipient($entry->user),
                $data,
                [
                    'field_foreign_id' => $entry->id,
                    new GrantStatusOption($entry->grant_status_id ?? GrantStatus::NO_STATUS),
                    new FormContext($entry->formId),
                    new CategoryContext($entry->categoryId),
                ],
                $entry->seasonId
            )
        );
    }

    private function sendEntrySubmittedNotification(Entry $entry, array $data): void
    {
        $this->dispatch(
            new SendNotificationCommand(
                'entry.submitted',
                $this->getRecipient($entry->user),
                $data,
                [
                    'field_foreign_id' => $entry->id,
                    new FormContext($entry->formId),
                    new CategoryContext($entry->categoryId),
                ],
                $entry->seasonId
            )
        );
    }

    private function sendAssignmentCreatedNotifications(Entry $entry, array $data): void
    {
        $assignments = app(AssignmentRepository::class)->getByEntryAndNotificationTrigger($entry->id, 'assignment.created');

        $assignments->each(function (Assignment $assignment) use ($entry, $data) {
            $data = array_merge($data, Vertical::replaceArrayKeys([
                'assignment_url' => $this->assignmentUrl($assignment),
                'entrant_email' => $entry->user->email,
                'first_name' => $assignment->judge->firstName,
                'last_name' => $assignment->judge->lastName,
            ]));

            $this->dispatch(
                new SendSpecificNotificationCommand(
                    $assignment->notification,
                    $this->getRecipient($assignment->judge),
                    $data,
                    [],
                    $entry->seasonId
                )
            );
        });
    }

    /**
     * Dispatches the entry.resubmitted notification.
     *
     * @throws \Exception
     */
    public function whenEntryWasResubmitted(EntryWasResubmitted $event)
    {
        // Default recipient
        $recipient = $this->getRecipient($event->entry->user);
        $category = translate($event->entry->category);
        $chapter = translate($event->entry->chapter);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars($chapter?->name),
            'entry_id' => $event->entry->id,
            'entry_local_id' => local_id($event->entry),
            'entry_name' => htmlspecialchars($event->entry->title),
            'entry_slug' => $event->entry->slug,
            'first_name' => htmlspecialchars($event->entry->user->firstName),
            'last_name' => htmlspecialchars($event->entry->user->lastName),
            'user_slug' => $event->entry->user->slug,
            'fund_allocations' => $this->fundAllocationsMarkdownTable($event->entry->allocations),
        ]);

        $this->dispatch(
            new SendNotificationCommand(
                'entry.resubmitted',
                $recipient,
                $data,
                [
                    'field_foreign_id' => $event->entry->id,
                    new FormContext($event->entry->formId),
                    new CategoryContext($event->entry->categoryId),
                ],
                $event->entry->seasonId
            )
        );
    }

    public function whenEntryWasModerated(EntryWasModerated $event)
    {
        $entry = $event->entry;
        $user = $entry->user;

        // Default recipient
        $recipient = $this->getRecipient($user);
        $category = translate($entry->category);
        $chapter = translate($entry->chapter);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars($chapter?->name),
            'entry_id' => $entry->id,
            'entry_local_id' => local_id($entry),
            'entry_name' => htmlspecialchars($entry->title),
            'entry_slug' => $entry->slug,
            'first_name' => htmlspecialchars($user->firstName),
            'last_name' => htmlspecialchars($user->lastName),
            'parent_category' => $this->getParentCategoryName($category),
            'user_slug' => $user->slug,
            'fund_allocations' => $this->fundAllocationsMarkdownTable($entry->allocations),
        ]);

        $this->dispatch(
            new SendNotificationCommand(
                'entry.moderated',
                $recipient,
                $data,
                [
                    'field_foreign_id' => $event->entry->id,
                    new FormContext($event->entry->formId),
                    new CategoryContext($event->entry->categoryId),
                    new ModerationOptionContext($entry->moderationStatus),
                ],
                $entry->seasonId
            )
        );
    }

    /**
     * Dispatches the entry.submitted notification.
     *
     * @throws \Exception
     */
    public function whenEntryWasTagged(TagWasAdded $event)
    {
        if (! $event->model instanceof Entry) {
            return;
        }

        $entry = $event->model;

        $notifications = $this->notifications->getByTriggerAndTags(
            $trigger = 'entry.tagged',
            $entry->seasonId,
            $tags = $event->tag != null ? [$event->tag->tag] : [],
            $context = [
                'field_foreign_id' => $entry->id,
                new FormContext($entry->formId),
                new CategoryContext($entry->categoryId),
            ],
        );

        // If there is no notification to send, there is no reason to load the rest of the data from the database.
        if ($notifications->isEmpty()) {
            return;
        }

        $category = translate($entry->category);
        $chapter = translate($entry->chapter);

        // Default recipient
        $recipient = $this->getRecipient($entry->user);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars($chapter?->name),
            'entry_id' => $entry->id,
            'entry_local_id' => local_id($entry),
            'entry_name' => htmlspecialchars($entry->title),
            'entry_slug' => $entry->slug,
            'first_name' => htmlspecialchars($entry->user->firstName),
            'last_name' => htmlspecialchars($entry->user->lastName),
            'parent_category' => $this->getParentCategoryName($category),
            'user_slug' => $entry->user->slug,
            'fund_allocations' => $this->fundAllocationsMarkdownTable($entry->allocations),
        ]);

        $this->dispatch(
            new SendTaggedNotificationCommand(
                $trigger,
                $recipient,
                $data,
                $tags,
                $entry->seasonId,
                $context,
                $notifications
            )
        );
    }

    /**
     * Dispatches the "payment.pending" & "payment.success" notifications
     */
    public function whenOrderWasCreated(OrderWasCreated $event)
    {
        $recipient = $this->getRecipient($event->order->user);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'first_name' => htmlspecialchars($event->order->user->first_name),
            'last_name' => htmlspecialchars($event->order->user->last_name),
            'user_slug' => $event->order->user->slug,
            'invoice_url' => route('order.pdf.invoice', ['order' => $event->order]),
            'order_id' => \HTML::invoiceNumber($event->order),
            'payment_amount' => format_amount($event->order->getTotal()),
        ]);

        $trigger = $event->order->paymentMethod == 'invoice' ? 'payment.pending' : 'payment.success';

        // Dispatch notification
        $this->dispatch(new SendNotificationCommand($trigger, $recipient, $data, [], $event->order->seasonId));
    }

    /**
     * Dispatches the role.granted notification.
     */
    public function whenRoleWasGranted(RoleWasGranted $event)
    {
        $user = $event->user;
        $recipient = $this->getRecipient($user);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'first_name' => htmlspecialchars($user->firstName),
            'last_name' => htmlspecialchars($user->lastName),
            'name' => htmlspecialchars($user->name),
            'user_slug' => $user->slug,
        ]);

        if ($user->hasConfirmed()) {
            $data['confirmation_url'] = trans('notifications.messages.already_confirmed.url');
            $data['confirmation_code'] = trans('notifications.messages.already_confirmed.code');
        } else {
            $channel = $this->communicationChannels->getByUserAndChannel((string) $user->globalId, $recipient->channel());

            // If no channel, exit quietly to prevent blocking parent process.
            if (! $channel) {
                return;
            }

            $data['confirmation_url'] = current_account_url();
            $data['confirmation_code'] = current_account_url();
        }

        $this->dispatch(new SendNotificationCommand(
            'role.granted',
            $recipient,
            $data,
            [new RoleContext($event->role->id)]
        ));
    }

    /**
     * Dispatches the review.stage.started notification
     */
    public function whenReviewStageWasStarted(ReviewTaskWasCreated $event)
    {
        $this->sendReviewStageNotification($event->reviewTask->reviewStage->startNotification, $event->reviewTask);
    }

    /**
     * Dispatch specified review-flow.stage.completed notification when a review task was approved
     */
    public function whenReviewTaskWasProceeded(ReviewTaskWasProceeded $event)
    {
        $this->sendReviewStageNotification($event->reviewStage->proceedNotification, $event->reviewTask);
    }

    /**
     * Dispatch specified review-flow.stage.completed notification when a review task was rejected
     */
    public function whenReviewTaskWasStopped(ReviewTaskWasStopped $event)
    {
        $this->sendReviewStageNotification($event->reviewStage->stopNotification, $event->reviewTask);
    }

    public function whenReviewTaskWasReassigned(ReviewTaskWasReassigned $event)
    {
        if ($notification = $event->notification) {
            $this->sendReviewStageNotification($notification, $event->reviewTask, true);
        }
    }

    /**
     * Dispatch specified review-flow.stage.started notification when requested to resend the notification.
     */
    public function whenSendReviewTaskNotificationWasRequested(SendReviewTaskNotificationWasRequested $event)
    {
        $this->sendReviewStageNotification($event->reviewTask->reviewStage->startNotification, $event->reviewTask);
    }

    /**
     * @param  string  $channel
     * @return mixed
     */
    private function destination(User $user, $channel)
    {
        if ($channel == 'mobile') {
            return $user->mobile;
        }

        if ($channel == 'email') {
            return $user->email;
        }
    }

    /**
     * Send a review stage notification
     */
    private function sendReviewStageNotification($notification, $reviewTask, bool $sendToReviewTaskRecipients = false)
    {
        if (! $notification || ! $notification->active || feature_disabled('review_flow')) {
            return;
        }

        $recipients = $this->reviewFlowRecipients->get($reviewTask->reviewStage, $reviewTask, $notification, $sendToReviewTaskRecipients);

        $recipients->each(function ($recipient) use ($reviewTask, $notification) {
            if ($reviewTask->reviewStage->reviewByField() && $recipient instanceof NullRecipient) {
                return;
            }
            $entry = $this->translator->translate(
                $reviewTask->entry->load('category', 'chapter'),
                $recipient->language()
            );

            $data = Vertical::replaceArrayKeys([
                'review_url' => shorten_url(route('review-flow.task.view', ['reviewTask' => $reviewTask->token])),
                'account_name' => $this->getAccountName(),
                'account_url' => current_account_url(),
                'category' => htmlspecialchars($entry->category?->name),
                'chapter' => htmlspecialchars($entry->chapter?->name),
                'entry_id' => $entry->id,
                'entry_local_id' => local_id($entry),
                'entry_name' => htmlspecialchars($entry->title),
                'entry_slug' => $entry->slug,
                'entrant_name' => htmlspecialchars($entry->user->fullName()),
                'entrant_email' => $entry->user->email,
                'first_name' => htmlspecialchars($recipient->firstName()),
                'last_name' => htmlspecialchars($recipient->lastName()),
                'parent_category' => $this->getParentCategoryName($entry->category),
                'fund_allocations' => $this->fundAllocationsMarkdownTable($entry->allocations),
            ]);

            $this->dispatch(
                new SendSpecificNotificationCommand(
                    $notification,
                    $recipient,
                    $data,
                    ['field_foreign_id' => $reviewTask->id],
                    $reviewTask->entry->seasonId
                )
            );
        });
    }

    /**
     * @return string
     */
    public function getParentCategoryName(?Category $category)
    {
        if ($category === null) {
            return '';
        }

        $parentCategory = $category->hasParentCategory() ? $category->parent : null;

        return $parentCategory ? htmlspecialchars(translate($parentCategory)->name) : '';
    }

    public function whenEligibilityWasChanged(EligibilityEvent $event)
    {
        if (! $event->notification()) {
            return;
        }

        $recipient = $this->getRecipient($event->entry->user);

        $category = translate($event->entry->category);
        $chapter = translate($event->entry->chapter);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars($chapter?->name),
            'entry_id' => $event->entry->id,
            'entry_local_id' => local_id($event->entry),
            'entry_name' => htmlspecialchars($event->entry->title),
            'entry_slug' => $event->entry->slug,
            'first_name' => htmlspecialchars($event->entry->user->firstName),
            'last_name' => htmlspecialchars($event->entry->user->lastName),
            'parent_category' => $this->getParentCategoryName($category),
            'user_slug' => $event->entry->user->slug,
        ]);

        $this->dispatch(
            new SendEligibilityNotificationCommand(
                $event->notification(),
                $recipient,
                $data,
                ['field_foreign_id' => $event->entry->id],
                $event->entry->seasonId
            )
        );
    }

    public function whenAssignmentWasCreated(AssignmentWasCreated $event): void
    {
        if (! $event->notification() || ! $event->assignment->entry->submitted()) {
            return;
        }

        [$entry, $recipient, $category, $chapter] = $this->getDependenciesForAssignmentNotifications($event);

        $data = Vertical::replaceArrayKeys([
            'assignment_url' => $this->assignmentUrl($event->assignment),
            'account_name' => $this->getAccountName(),
            'first_name' => htmlspecialchars($event->recipient()->firstName),
            'last_name' => htmlspecialchars($event->recipient()->lastName),
            'entry_name' => htmlspecialchars($entry->title),
            'entry_slug' => $entry->slug,
            'entry_local_id' => local_id($entry),
            'entrant_email' => $entry->user->email,
            'parent_category' => $this->getParentCategoryName($category),
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars($chapter?->name),
            'account_url' => current_account_url(),
        ]);

        $this->dispatch(
            new SendSpecificNotificationCommand(
                $event->notification(),
                $recipient,
                $data,
                [],
                $entry->seasonId
            )
        );
    }

    private function assignmentUrl(Assignment $assignment): string
    {
        return shorten_url(route('clear.login').'?redirect='.$assignment->url());
    }

    public function whenAssignmentWasCompleted(AssignmentWasCompletedByJudge $event): void
    {
        if ($event->recipient()->guest()) {
            return;
        }

        [$entry, $recipient, $category, $chapter] = $this->getDependenciesForAssignmentNotifications($event);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'first_name' => htmlspecialchars($event->recipient()->firstName),
            'last_name' => htmlspecialchars($event->recipient()->lastName),
            'entry_name' => htmlspecialchars($entry->title),
            'entry_slug' => $entry->slug,
            'entry_local_id' => local_id($entry),
            'parent_category' => $this->getParentCategoryName($category),
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars($chapter?->name),
            'account_url' => current_account_url(),
            'fund_allocations' => $this->fundAllocationsMarkdownTable($entry->allocations),
        ]);

        $this->dispatch(
            new SendNotificationCommand(
                'assignment.judging.completed',
                $recipient,
                $data,
                [
                    new ScoreSetContext($event->assignment->scoreSetId),
                ],
                $entry->seasonId
            )
        );
    }

    private function getDependenciesForAssignmentNotifications(AssignmentWasCreated|AssignmentWasCompletedByJudge $event): array
    {
        return [
            $entry = $event->assignment->entry,
            $this->getRecipient($event->recipient()),
            translate($entry->category),
            translate($entry->chapter),
        ];
    }

    public function whenFormInvitationWasCreated(EntryWasInvited $event)
    {
        if (! $event->notification()) {
            return;
        }

        $link = link_to_route('entry-form.entrant.edit', null, ['entry' => (string) $event->entry()->slug]);

        if ($event->user()->wasInvited) {
            $link = link_to_route('invitation', null, [
                'invitationToken' => $this->tokens->create(
                    new InvitationToken(
                        $event->user(),
                        current_account(),
                        route('entry-form.entrant.edit', ['entry' => (string) $event->entry()->slug])
                    )
                ),
            ]);
        }

        Log::info('entry.invited', $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'invite_link' => $link,
            'message' => $event->message(),
        ]));

        $this->dispatch(new SendSpecificNotificationCommand(
            $event->notification(),
            RecipientFactory::createFromUser($event->user(), $event->user()->preferredLanguageForUser()->code()),
            $data
        ));
    }

    public function whenDocumentWasCreated(DocumentWasCreated $event): void
    {
        if (! $event->document->shared || ! $event->notification()) {
            return;
        }

        $data = Vertical::replaceArrayKeys(array_merge($event->document->associatable->getMergeFields(), [
            'account_name' => current_account_name(),
            'account_url' => current_account_url(),
            'date_document_created' => Html::localisedDate($event->document->createdAt, consumer()->dateLocale()),
        ]));

        $data['attachments'] = [$event->file()?->file];

        $this->dispatch(new SendSpecificNotificationCommand(
            $event->notification(),
            $this->getRecipient($event->recipient()),
            $data
        ));
    }

    public function whenCollaboratorWasInvited(CollaboratorWasInvited $event): void
    {
        /** @var Submittable $submittable */
        $route = ($submittable = $event->collaborator->submittable)->editRoute();

        $link = link_to($route);

        if (($user = $event->collaborator->user)->wasInvited) {
            $link = link_to_route('invitation', null, [
                'invitationToken' => $this->tokens->create(
                    new InvitationToken(
                        $user,
                        current_account(),
                        $route
                    )
                ),
            ]);
        }

        $this->dispatch(new SendNotificationCommand(
            'collaborator.invited',
            RecipientFactory::createFromUser($user, current_account()->defaultLanguage()->code),
            Vertical::replaceArrayKeys([
                'account_name' => $this->getAccountName(),
                'invite_link' => $link,
                'message' => $event->message,
                $submittable->verticalKey() => $submittable->title,
            ]),
            [
                'field_foreign_id' => $submittable->id,
                new FormContext($submittable->getFormId()),
                new CategoryContext($submittable->getCategoryId()),
            ],
            $submittable->getSeasonId()
        ));
    }

    public function whenEntryResubmissionWasRequired(EntryResubmissionWasRequired $event): void
    {
        $category = Translator::shallow($event->entry->category);
        $chapter = Translator::shallow($event->entry->chapter);

        $data = Vertical::replaceArrayKeys([
            'account_name' => $this->getAccountName(),
            'account_url' => current_account_url(),
            'category' => htmlspecialchars($category?->name),
            'chapter' => htmlspecialchars($chapter?->name),
            'entry_id' => $event->entry->id,
            'entry_local_id' => local_id($event->entry),
            'entry_name' => htmlspecialchars($event->entry->title),
            'entry_slug' => $event->entry->slug,
            'first_name' => htmlspecialchars($event->entry->user->firstName),
            'last_name' => htmlspecialchars($event->entry->user->lastName),
            'parent_category' => $this->getParentCategoryName($category),
            'user_slug' => $event->entry->user->slug,
            'fund_allocations' => $this->fundAllocationsMarkdownTable($event->entry->allocations),
        ]);

        $this->dispatch(new SendNotificationCommand(
            $event->trigger(),
            $this->getRecipient($event->entry->user),
            $data,
            ['field_foreign_id' => $event->entry->id, new FormContext($event->entry->formId)],
            $event->entry->seasonId
        ));
    }
}
