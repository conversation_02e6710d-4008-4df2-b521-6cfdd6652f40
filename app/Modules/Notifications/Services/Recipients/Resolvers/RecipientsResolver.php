<?php

namespace AwardForce\Modules\Notifications\Services\Recipients\Resolvers;

use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Services\Recipients\Factory;
use AwardForce\Modules\Notifications\Services\Recipients\NullRecipient;
use AwardForce\Modules\Notifications\Services\Recipients\Recipients;

class RecipientsResolver implements Resolver
{
    /**
     * Determines whether or not the resolver implementation can determine the recipients needed.
     *
     * @return bool
     */
    public function canResolve(Notification $notification)
    {
        return $notification->recipientOption == 'recipients';
    }

    /**
     * Returns the recipient for this particular notification.
     *
     * @return Recipients|null
     */
    public function recipients(Notification $notification, ?array $context = null)
    {
        $destinations = preg_split('/[,;]/', $notification->recipients);

        return (new Recipients($destinations))->map(function ($destination) {
            return Factory::createFromString($destination);
        })->filter(function ($recipient) {
            return ! ($recipient instanceof NullRecipient);
        });
    }
}
