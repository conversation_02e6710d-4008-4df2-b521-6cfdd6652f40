<?php

namespace AwardForce\View\Components\Dashboards;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Content\Terms\Facades\Term;
use AwardForce\Modules\Forms\Forms\Services\FormSelectorService;
use AwardForce\Modules\NewDashboard\Clients\Embeddable as EmbeddableClient;
use AwardForce\Modules\NewDashboard\Contracts\DashboardRepository;
use AwardForce\Modules\Seasons\Contracts\SeasonRepository;
use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\Http\Request;
use Illuminate\View\Component;
use Ramsey\Uuid\UuidInterface;

class Embeddable extends Component
{
    private readonly EmbeddableClient $client;
    public readonly string $baseUrl;

    public function __construct(
        readonly private SeasonRepository $seasons,
        readonly private FormSelectorService $forms,
        readonly private DashboardRepository $dashboards,
        readonly private ChapterRepository $chapters,
        readonly private Request $request,
        readonly private UuidInterface $dashboardId,
    ) {
        $this->client = new EmbeddableClient(
            config('services.embeddable'),
            $this->dashboardId,
        );

        $this->baseUrl = $this->client->baseUrl;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.dashboards.embeddable');
    }

    public function token(): string
    {
        return $this->client->getToken();
    }

    public function variables(): array
    {
        $selectedSeason = translate($this->seasons->getActive())->name;
        $selectedForm = translate($this->forms->get())?->name;

        $variables = [
            'Season' => $selectedSeason,
            'Seasons' => [$selectedSeason],
            'Form' => $selectedForm,
            'Forms' => [$selectedForm],
            'Entries' => sentence_case(Term::plural('entry')),
            'Completion' => trans('dashboard.widgets.entry_completion.title'),
            'Daily volume' => trans('dashboard.widgets.entry_volume.title'),
            'Applications by category' => trans('dashboard.widgets.category_entries.title'),
            'Applications by chapter' => trans('dashboard.widgets.chapter_entries.title'),
        ];

        if (Consumer::isChapterManager()) {
            $variables['Chapter slugs'] = $this->chapters->getByManager(consumer_id())->pluck('slug');
        }

        return $variables;
    }
}
