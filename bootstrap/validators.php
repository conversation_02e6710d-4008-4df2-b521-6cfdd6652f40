<?php

use Award<PERSON><PERSON>ce\Library\Authorization\Consumer;
use AwardForce\Library\Count\CharacterCounter;
use AwardForce\Library\Count\WordCounter;
use AwardForce\Modules\Categories\Contracts\CategoryRepository;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Forms\Tabs\Database\Repositories\TabRepository;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Identity\Users\Services\AllowedEmailDomain;
use AwardForce\Modules\PaymentSubscriptions\Rules\VatNumber;
use Illuminate\Support\Str;
use Platform\Support\Values\Email;
use Platform\Support\Values\PhoneNumber;

// TODO extract these validators to the corresponding modules

/**
 * A collection of additional validators for global use.
 *
 * @authors <PERSON>
 *
 * @date 25th November 2014
 */

/**
 * Custom validator to ensure input is only of a decimal value.
 *
 * @param  string  $attribute Not used.
 * @param  string  $email
 * @return bool
 */
Validator::extend('decimal', function ($attribute, $value) {
    return (bool) preg_match('#\d+\.\d+#i', $value);
});

/**
 * Only really applies to the mobile field. Checks to see whether or not the mobile number
 * is unique to the account the user is signing up for.
 *
 * @param  string  $attribute Not used.
 * @param  string  $mobile
 * @return bool
 */
Validator::extend('unique_mobile', function ($attribute, $mobile) {
    $userRepository = App::make(UserRepository::class);

    return ! $userRepository->getByMobileAndAccount($mobile, CurrentAccount::get());
});

/**
 * The translated_required validator ensures that all of the translation fields have a value.
 * This is useful for ensuring that fields are not left empty.
 *
 * Usage:
 *
 * 'translated' => 'translation_required',  <-- Ensures that all of the translated fields are not empty.
 * 'translated' => 'translation_required:field1,field2',  <-- Ensures the specified translated fields are not empty.
 *
 * Note: It will check all expected languages for all specified fields.
 */
Validator::extend('translation_required', function ($attribute, $translations, $parameters, $validator) {
    if (! is_array($translations)) {
        return false;
    }

    // If parameters are provided, check they are all provided in the $translations array
    if ($parameters) {
        if (array_diff($parameters, array_keys($translations))) {
            return false;
        }

        // From this point on, we only care about the following translations
        $translations = array_intersect_key($translations, array_flip($parameters));
    }

    $defaultLanguage = CurrentAccount::get()->defaultLanguage()->code;

    foreach ($translations as $field => $translation) {
        if (! is_array($translation)
            || ! isset($translation[$defaultLanguage])
            || is_null($translation[$defaultLanguage])
            || ! is_string($translation[$defaultLanguage])
            || trim($translation[$defaultLanguage]) === ''
        ) {
            $validator->errors()->add("translated.{$field}.{$defaultLanguage}", trans('validation.required', ['attribute' => $validator->customAttributes[$field] ?? $field]));
        }
    }

    return true;
});

/**
 * The merge_fields_present validator is used to validate inputs containing replaceable values (i.e. Notifications),
 * for the presence of the specified merge fields.
 *
 * These inputs always come under `translated` key in AF4, so this rule takes care of all languages of the input.
 *
 * Usage :
 * 'translated.body' => 'merge_fields_present:{account_name},{confirmation_url}'
 *
 * In order to have 'nice' validation errors, it is recommended to add the validated field in translations `validation.attributes`.
 * I.e. to validate `translated.smsBody`, the `'validation.attributes.smsBody' => 'SMS body'` translation should be present
 */
Validator::extend('merge_fields_present', function ($attribute, $fields, $parameters, $validator) {
    if (! is_array($fields) || ! $parameters) {
        return false;
    }

    $field = strpos($attribute, '.') === false ? $attribute : explode('.', $attribute)[1];
    foreach ($parameters as $parameter) {
        foreach ($fields as $language => $value) {
            // Don't show language prefix when we have just one language
            $errorAttribute = count($fields) === 1 ? trans('validation.attributes.'.$field)
                : $language.' '.trans('validation.attributes.'.$field);

            if (Lang::has('validation.merge_fields_extra.'.$parameter)) {
                $errorAttribute .= ' '.trans('validation.merge_fields_extra.'.$parameter);
            }

            if (! Str::contains($value, $parameter)) {
                $validator->errors()->add(
                    "translated.{$field}.{$language}",
                    trans(
                        'validation.merge_field_present',
                        [
                            'parameter' => $parameter,
                            'attribute' => $errorAttribute,
                        ]
                    )
                );
            }
        }
    }

    return true;
});

/**
 * The merge_fields_present_if validator is used to validate inputs containing replaceable values (i.e. Notifications),
 * for the presence of the specified merge fields, only if the first condition is true.
 * If the condition is `true`, this rule will invoke `merge_fields_present` for the set of parameters
 *
 * This rule could be replaced with the `exclude_unless` rule in Laravel versions >=6
 *
 * Usage :
 * 'translated.body' => 'merge_fields_present_if:trigger=user.registered,{account_name},{confirmation_code}',
 */
Validator::extend('merge_fields_present_if', function ($attribute, $fields, $parameters, $validator) {
    if (! is_array($fields) || ! $parameters || ! isset($validator->extensions['merge_fields_present'])) {
        return false;
    }

    [$rule, $value] = explode('=', array_shift($parameters));

    if (! isset($validator->getData()[$rule]) || $validator->getData()[$rule] !== $value) {
        return true;
    }

    return $validator->extensions['merge_fields_present']($attribute, $fields, $parameters, $validator);
});

Validator::extend('valid_roles', function ($attribute, $roles) {
    return empty($roles) ? true : app(RoleRepository::class)->hasAllIds($roles);
});

/**
 * Max word count validator.
 *
 * All whitespace and punctuation will be ignored, including punctuation defined in Unicode blocks:
 *
 * The General Punctuation (U+2000-U+206F)
 * Supplemental Punctuation (U+2E00-U+2E7F)
 */
Validator::extend('max_words', function ($attribute, $value, $parameters) {
    return WordCounter::count($value) <= $parameters[0];
});

/**
 * Max character count validator.
 */
Validator::extend('max_characters', function ($attribute, $value, $parameters) {
    return CharacterCounter::count($value) <= $parameters[0];
});

/**
 * Min word count validator.
 */
Validator::extend('min_words', function ($attribute, $value, $parameters) {
    return WordCounter::count($value) >= $parameters[0];
});

/**
 * Min character count validator.
 */
Validator::extend('min_characters', function ($attribute, $value, $parameters) {
    return CharacterCounter::count($value) >= $parameters[0];
});

/**
 * Validates a comma or semi-colon delimited string value for emails and international mobile
 * phone numbers. If any value provided is invalid, an appropriate error message will be shown.
 */
Validator::extend('multiple_recipients', function ($attribute, $recipients) {
    $recipients = str_replace(' ', '', $recipients);
    $recipients = explode(',', str_replace([';', ':'], ',', $recipients));

    foreach ($recipients as $recipient) {
        if (! Email::check($recipient) && ! PhoneNumber::check($recipient)) {
            return false;
        }
    }

    return true;
});

/**
 * Validates a comma or semi-colon delimited string value for emails. If any value provided is invalid,
 * an appropriate error message will be shown.
 */
Validator::extend('multiple_emails', function ($attribute, $recipients) {
    $recipients = str_replace(' ', '', $recipients);
    $recipients = explode(',', str_replace([';', ':'], ',', $recipients));

    foreach ($recipients as $recipient) {
        if (! Email::check($recipient)) {
            return false;
        }
    }

    return true;
});

/**
 * Validates a phone number value to ensure it is of the required format.
 * All phone numbers entered must be done in an international format.
 *
 * @return bool
 */
Validator::extend('phone_format', function ($attribute, $value) {
    $safeValue = preg_replace('#[\s\(\)\[\]]+#', '', $value);
    $passed = (bool) preg_match('#^\+[0-9]+$#', $safeValue);

    return $passed;
});

/**
 * Validates that the minimum amount of contributors for the given tab have been submitted.
 */
Validator::extendImplicit('min_contributors', function ($attribute, $value, $parameters, $validator) {
    // Remove contributors template from the request
    $contributors = collect(array_get($validator->getData(), 'contributors'))->forget('new');

    return $contributors->where('tab', $parameters[0])->count() >= $parameters[1];
});

Validator::replacer('min_contributors', function ($message, $attribute, $rule, $parameters) {
    $tab = translate(app(TabRepository::class)->getById($parameters[0]));

    return str_replace([':min', ':tab'], [$parameters[1], $tab->name], $message);
});

/**
 * Validates that the maximum amount of contributors for the given tab have been submitted.
 */
Validator::extend('max_contributors', function ($attribute, $value, $parameters, $validator) {
    // Remove contributors template from the request
    $contributors = collect(array_get($validator->getData(), 'contributors'))->forget('new');

    return $contributors->where('tab', $parameters[0])->count() <= $parameters[1];
});

Validator::replacer('max_contributors', function ($message, $attribute, $rule, $parameters) {
    $tab = translate(app(TabRepository::class)->getById($parameters[0]));

    return str_replace([':max', ':tab'], [$parameters[1], $tab->name], $message);
});

/**
 * Validates that the given email is from the list of domains allowed for registration.
 * This rule should be ignored on profile updates where the email remains unchanged.
 */
Validator::extend('restrict_email_domain', function ($attribute, $value) {
    $user = Consumer::get()->user();
    if ($user && $user->email == $value) {
        return true;
    }

    return app(AllowedEmailDomain::class)->domainAllowed($value);
});

/**
 * Validates a string contains a valid domain on each line.
 */
Validator::extend('valid_domains', function ($attribute, $value) {
    $validDomain = '/(?=^.{4,253}$)(^((?!-)([a-zA-Z0-9-]{1,63}|\*{1})(?<!-)\.)+[a-zA-Z]{2,63}$)/';

    $setting = trim($value);
    $domains = array_map('trim', preg_split('/\r\n|\n|\r/', $setting));

    foreach ($domains as $domain) {
        $wildcardCount = substr_count($domain, '*');
        if (! preg_match($validDomain, $domain) || $wildcardCount > 1 || ($wildcardCount == 1 && substr($domain, 0, 1) !== '*')) {
            return false;
        }
    }

    return true;
});

/**
 * Validate numeric fields with our allowed character set.
 *
 * @return bool
 */
Validator::extend('numeric_field', function ($attribute, $value) {
    return (bool) preg_match('/^[0-9 .,+-]*$/', $value);
});

/**
 * Validates that the current user has not reached the maximum number of entries in the selected category.
 *
 * Usage:
 *
 * 'category_id' => 'category_max_entries:2' <-- ensures the entry limit for the category with id 2 has not been reached
 * 'category_id' => 'category_max_entries:2,40' <-- same behaviour, excluding the entry with id 40 from the count (used for updates)
 */
Validator::extend('category_max_entries', function ($attribute, $value, $parameters) {
    $category = app(CategoryRepository::class)->getById($parameters[0]);

    if (! $category || is_null($category->entrantMaxEntries)) {
        return true;
    }

    $entries = app(EntryRepository::class)->getForUserInCategory(Consumer::get()->id(), $category->id);

    if (! empty($parameters[1])) {
        $entries = $entries->reject(function ($entry) use ($parameters) {
            return $entry->id == $parameters[1];
        });
    }

    return $entries->count() < $category->entrantMaxEntries;
});

Validator::replacer('category_max_entries', function ($message, $attribute, $rule, $parameters) {
    $category = app(CategoryRepository::class)->getById($parameters[0]);

    return str_replace(':max', $category->entrantMaxEntries, $message);
});

Validator::extend('unique_domain', function ($attribute, $value, $parameters, $context) {
    $root = $context->getData()[array_shift($parameters)];
    $domain = $value.'.'.$root;

    return ! DB::table('domains')
        ->where('domain', $domain)
        ->when(array_shift($parameters), function ($query, $account) {
            $query->where('account_id', '<>', $account);
        })
        ->exists();
});

/**
 * domain_whitelabel: anything after the first dot must be in the whitelist, or the whole string must be in the whitelist
 */
Validator::extend('domain_custom', function ($attribute, $value): bool {
    $blacklist = config('domains.white_label');

    return array_first($blacklist, function ($domain) use ($value) {
        return ends_with($value, $domain);
    }, false) === false;
});

Validator::extend('account_owner', function ($attribute, $value, $parameters, $validator): bool {
    $user = request()->user ?: request()->get('user');
    if ($user && $user->ownsAccounts() && (is_null(\Auth::user()) || $user->id != \Auth::user()->id)) {
        return false;
    }

    return true;
});

Validator::extend('current_account_owner', function ($attribute, $value, $parameters, $validator): bool {
    $user = request()->user ?: request()->get('user');
    if ($user && $user->ownerOfCurrentAccount() && (is_null(\Auth::user()) || $user->id != \Auth::user()->id)) {
        return false;
    }

    return true;
});

Validator::extend('consumer_password', function ($attribute, $value, $parameters, $validator): bool {
    $globalUser = \Consumer::user() ? \Consumer::user()->globalUser : null;

    if ($globalUser && \Hash::check($value, $globalUser->password)) {
        return true;
    }

    return false;
});

Validator::extend('vat_number', function ($attribute, $value, $parameters, $validator): bool {
    $rule = new VatNumber;
    $countryCodeKey = $parameters[0] ?? null;
    if ($countryCodeKey) {
        $countryCode = array_get($validator->getData(), $countryCodeKey);
        $iso3316 = config('countries.list.'.$countryCode.'.iso-3316', $countryCode);
        $value = $iso3316.$value;
    }

    return $rule->passes($attribute, $value);
});

Validator::extend('max_sms_characters', function ($attribute, $value): bool {
    return strlen(str_replace(["\r\n", "\n", "\r"], ' ', $value)) <= config('notifications.sms.max_characters');
});
