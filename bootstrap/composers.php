<?php

use AwardForce\Library\Composers\AccountHeaderComposer;
use AwardForce\Library\Composers\AccountNotFoundComposer;
use AwardForce\Library\Composers\AccountSuspendedComposer;
use AwardForce\Library\Composers\ActiveFiltersComposer;
use AwardForce\Library\Composers\ApplicationComposer;
use AwardForce\Library\Composers\AssetsComposer;
use AwardForce\Library\Composers\BreadcrumbComposer;
use AwardForce\Library\Composers\ClientGACodeComposer;
use AwardForce\Library\Composers\CookieNoticeComposer;
use AwardForce\Library\Composers\DatepickerLocaleComposer;
use AwardForce\Library\Composers\DelightedSurveyComposer;
use AwardForce\Library\Composers\DomainSetupComposer;
use AwardForce\Library\Composers\DownloadFiltersComposer;
use AwardForce\Library\Composers\ElevioComposer;
use AwardForce\Library\Composers\FooterBarComposer;
use AwardForce\Library\Composers\FooterComposer;
use AwardForce\Library\Composers\LanguagesComposer;
use AwardForce\Library\Composers\LegalBasisComposer;
use AwardForce\Library\Composers\MainMenuComposer;
use AwardForce\Library\Composers\PDFComposer;
use AwardForce\Library\Composers\PusherComposer;
use AwardForce\Library\Composers\RemoveFilterComposer;
use AwardForce\Library\Composers\SearchableFieldsComposer;
use AwardForce\Library\Composers\SharingTagsComposer;
use AwardForce\Library\Composers\SplashWithMenuComposer;
use AwardForce\Library\Composers\SupportedCurrenciesComposer;
use AwardForce\Library\Composers\SupportedLanguagesComposer;
use AwardForce\Library\Composers\SystemEmailComposer;
use AwardForce\Library\Composers\TimezoneComposer;
use AwardForce\Library\Composers\TrackerComposer;
use AwardForce\Library\Composers\TrialAccountComposer;
use AwardForce\Library\Composers\UnauthorizedComposer;
use AwardForce\Library\Composers\UserAccountFieldsComposer;
use AwardForce\Library\Composers\UserMenuComposer;
use AwardForce\Library\Composers\VueDataComposer;
use AwardForce\Library\Emails\Composers\EmailComposer;
use AwardForce\Library\View\GlobalComposer;
use AwardForce\Modules\Agreements\Composers\AgreementsComposer;
use AwardForce\Modules\Assignments\Composers\AssignJudgesComposer;
use AwardForce\Modules\Assignments\Composers\AssignmentComments;
use AwardForce\Modules\Awards\Composers\CertificateComposer;
use AwardForce\Modules\Categories\View\CategoryAttachmentsComposer;
use AwardForce\Modules\Categories\View\CategorySelectComposer;
use AwardForce\Modules\Categories\View\ParentSelectComposer;
use AwardForce\Modules\Chapters\Composers\AddChapterComposer;
use AwardForce\Modules\Chapters\Composers\ChapterAttachmentsComposer;
use AwardForce\Modules\Chapters\Composers\ChapterManagerComposer;
use AwardForce\Modules\Chapters\Composers\ChapterMetadataComposer;
use AwardForce\Modules\Chapters\Composers\ChapterSelectComposer;
use AwardForce\Modules\Chapters\Composers\ChapterTabsComposer;
use AwardForce\Modules\Comments\Composers\CommentsComposer;
use AwardForce\Modules\Content\Blocks\Composers\ContentBlockFormComposer;
use AwardForce\Modules\Contract\Composers\ContractCreatorComposer;
use AwardForce\Modules\Documents\Composers\CreateDocumentComposer;
use AwardForce\Modules\Ecommerce\Cart\Composers\CartComposer;
use AwardForce\Modules\Ecommerce\Cart\Composers\SecurePayComposer;
use AwardForce\Modules\Ecommerce\Orders\Composers\ExportOrdersComposer;
use AwardForce\Modules\Ecommerce\Orders\Composers\InvoiceComposer;
use AwardForce\Modules\Entries\Composers\AddContractComposer;
use AwardForce\Modules\Entries\Composers\BlockSavedViewsComposer;
use AwardForce\Modules\Entries\Composers\EntrantEntryComposer;
use AwardForce\Modules\Entries\Composers\EntryCompleteComposer;
use AwardForce\Modules\Entries\Composers\EntryEligibilityComposer;
use AwardForce\Modules\Entries\Composers\EntryFeedbackComposer;
use AwardForce\Modules\Entries\Composers\EntryModerationComposer;
use AwardForce\Modules\Entries\Composers\EntryReviewTasks;
use AwardForce\Modules\Entries\Composers\QuickManager;
use AwardForce\Modules\Entries\Composers\ScoreMatrix;
use AwardForce\Modules\Exports\Composers\ExportActionComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\ChapterComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\CountryFieldComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\CountryRegionFieldComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\DateFieldComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\DropDownListFieldComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\FieldTabsComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\FileFieldDisplayComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\FileFieldFormComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\PhoneFieldComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\RadioFieldComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\TablePreviewComposer;
use AwardForce\Modules\Forms\Fields\View\Compose\TimeFieldComposer;
use AwardForce\Modules\Forms\Forms\View\Composers\FormActionsComposer;
use AwardForce\Modules\Forms\Forms\View\Composers\FormSelectorComposer;
use AwardForce\Modules\Forms\Tabs\View\Compose\TabFormComposer;
use AwardForce\Modules\Funding\Composers\FundAllocatorComposer;
use AwardForce\Modules\Galleries\Composers\GalleryComposer;
use AwardForce\Modules\GrantReports\Composers\GrantReportCompleteComposer;
use AwardForce\Modules\GrantReports\Composers\GrantReportComposer;
use AwardForce\Modules\GrantReports\Composers\ReportFormsComposer;
use AwardForce\Modules\Grants\Composers\GrantStatusSelectorComposer;
use AwardForce\Modules\Identity\Roles\Composers\RoleSelectComposer;
use AwardForce\Modules\Identity\Users\Composers\ProfileSecurityComposer;
use AwardForce\Modules\Identity\Users\Composers\UserConfirmationComposer;
use AwardForce\Modules\Identity\Users\Composers\UserProfileComposer;
use AwardForce\Modules\Identity\Users\Composers\UserProfileLabelsComposer;
use AwardForce\Modules\Judging\Composers\LeaderboardComposer;
use AwardForce\Modules\Judging\Composers\LeaderboardVipJudgingComposer;
use AwardForce\Modules\Judging\Composers\LeaderboardVotingComposer;
use AwardForce\Modules\Judging\Composers\ProgressComposer;
use AwardForce\Modules\Judging\Composers\ProgressExportPanelsComposer;
use AwardForce\Modules\Judging\Composers\QualifyingEntriesComposer;
use AwardForce\Modules\Judging\Composers\ScoringFormComposer;
use AwardForce\Modules\Judging\Composers\ScoringPDFComposer;
use AwardForce\Modules\Judging\Composers\ScoringSlideshowComposer;
use AwardForce\Modules\Judging\Composers\TopPickFiltertronComposer;
use AwardForce\Modules\Judging\Composers\VIPEntrantDetails;
use AwardForce\Modules\Judging\Composers\VipJudgingProgressExportComposer;
use AwardForce\Modules\Payments\Composers\CheckoutComposer;
use AwardForce\Modules\Payments\Composers\CybersourceComposer;
use AwardForce\Modules\Payments\Composers\DiscountFormComposer;
use AwardForce\Modules\Payments\Composers\TierFormComposer;
use AwardForce\Modules\ReviewFlow\Composers\BulkInitiateReviewStageComposer;
use AwardForce\Modules\ReviewFlow\Composers\ReviewFlowForm;
use AwardForce\Modules\Rounds\Composers\OpenRoundsComposer;
use AwardForce\Modules\ScoreSets\Composers\ScoreSetSelectComposer;
use AwardForce\Modules\ScoreSets\View\ScoreSetAttachmentsComposer;
use AwardForce\Modules\ScoringCriteria\Composers\ScoringCriterionFormComposer;
use AwardForce\Modules\Search\Composers\FiltertronTrayComposer;
use AwardForce\Modules\Seasons\Composers\AvailableSeasonsComposer;
use Platform\Composers\Pagination;

View::composers([
    ActiveFiltersComposer::class => ['partials.page.active-filters', 'broadcast.active-filters'],
    AddChapterComposer::class => ['chapter.add'],
    AccountHeaderComposer::class => ['entry.common.header', 'judging.index', 'voting.index', 'qualifying.index', 'top-pick.index', 'gallery.index', 'errors.401', 'judge-dashboard.index', 'gallery-dashboard.index'],
    AccountNotFoundComposer::class => ['errors.account-not-found'],
    AccountSuspendedComposer::class => ['errors.account-suspended'],
    AddContractComposer::class => ['entry.partials.add-contract-modal'],
    AgreementsComposer::class => ['registration.agreements'],
    ApplicationComposer::class => ['layouts.fullpage', 'layouts.splash', 'layouts.slideshow', 'layouts.splash-with-menu'],
    AssetsComposer::class => ['layouts.fullpage', 'layouts.installation', 'layouts.splash', 'layouts.slideshow', 'layouts.empty', 'provisioning.layout', 'layouts.splash-with-menu', 'layouts.error'],
    AssignmentComments::class => ['assignment.search.comments'],
    AvailableSeasonsComposer::class => ['partials.page.selectors.season', 'html.buttons.marker-action-tag'],
    FormActionsComposer::class => ['partials.list-actions.add-resource', 'partials.list-actions.add-resource-inline', 'partials.list-actions.edit-form', 'judging-dashboard.index'],
    FormSelectorComposer::class => ['partials.page.selectors.vue-form'],
    BreadcrumbComposer::class => ['partials.header.breadcrumbs'],
    BulkInitiateReviewStageComposer::class => ['partials.list-actions.review', 'entry.partials.initiate-review-modal'],
    CartComposer::class => ['ecommerce.cart'],
    CategoryAttachmentsComposer::class => ['category.tabs.images'],
    CategorySelectComposer::class => ['category.select'],
    CertificateComposer::class => ['award.certificate'],
    ChapterMetadataComposer::class => ['chapter.index'],
    ChapterManagerComposer::class => ['chapter.form', 'chapter.tabs.details'],
    ChapterSelectComposer::class => ['entry.manager.index'],
    ChapterTabsComposer::class => ['chapter.add', 'chapter.edit'],
    ChapterAttachmentsComposer::class => ['chapter.tabs.images'],
    CheckoutComposer::class => ['ecommerce.checkout'],
    CybersourceComposer::class => ['ecommerce.partials.cybersource-form'],
    ClientGACodeComposer::class => ['partials.footer.client-analytics', 'partials.header.assets'],
    CommentsComposer::class => ['comments.comments', 'judging.partials.criterion.comments-view-only'],
    ContractCreatorComposer::class => ['partials.list-actions.add-contract'],
    CountryFieldComposer::class => ['html.field.search.country', 'html.field.form.country', 'html.form.countries', 'review-flow.task.fields', 'review-flow.task.referee'],
    CountryRegionFieldComposer::class => ['html.form.countries-regions'],
    CookieNoticeComposer::class => ['partials.misc.cookie-notice'],
    ContentBlockFormComposer::class => ['content-block.form'],
    DateFieldComposer::class => ['html.field.form.datetime', 'html.field.form.date', 'judging.fields.date', 'judging.fields.datetime', 'review-flow.task.fields.date', 'review-flow.task.fields.datetime', 'field.columnator.fields.date', 'field.columnator.fields.datetime'],
    DatepickerLocaleComposer::class => ['html.field.form.datetime', 'html.field.form.date', 'html.field.form.time', 'html.form.datetime', 'html.form.date', 'review-flow.task.fields.date', 'review-flow.task.fields.datetime', 'review-flow.task.fields.time', 'entry.partials.schedule-report', 'entry.manager.view'],
    DelightedSurveyComposer::class => ['partials.footer.delighted-survey'],
    DiscountFormComposer::class => ['discount.form'],
    DomainSetupComposer::class => ['misc.domain-setup'],
    TrackerComposer::class => ['partials.footer.tracker'],
    ElevioComposer::class => ['partials.footer.elevio'],
    EmailComposer::class => ['emails.templates.main', 'emails.templates.main-raw', 'broadcast.email-preview'],
    EntryCompleteComposer::class => ['entry.entrant.complete', 'entry.manager.complete'],
    EntryEligibilityComposer::class => ['entry.common.eligibility'],
    EntryFeedbackComposer::class => ['entry.feedback.view'],
    EntryModerationComposer::class => ['entry.manager.index', 'entry.manager.view', 'entry.manager.preview', 'entry.manager.search.action-overflow'],
    EntryReviewTasks::class => ['entry.manager.review-tasks'],
    ScoringPDFComposer::class => ['judging.pdf.score'],
    EntrantEntryComposer::class => ['entry.entrant.search.action-overflow'],
    ExportActionComposer::class => ['export.partials.export-action'],
    ExportOrdersComposer::class => ['order.export'],
    FieldTabsComposer::class => ['field.search.tab'],
    FileFieldDisplayComposer::class => ['judging.fields.file', 'judging.fields.pdf.file'],
    FileFieldFormComposer::class => ['html.field.form.file', 'review-flow.task.fields.file'],
    FiltertronTrayComposer::class => ['search.filtertron.filtertron-tray', 'search.filtertron.filtertron-search'],
    FooterBarComposer::class => ['partials.footer.footer-bar', 'partials.misc.app'],
    FundAllocatorComposer::class => ['partials.list-actions.fund-allocations'],
    GalleryComposer::class => ['gallery.index'],
    GrantStatusSelectorComposer::class => ['partials.list-actions.grant-status-selector', 'grant.status.select', 'entry.common.grant-manager'],
    GrantReportCompleteComposer::class => ['grant-report.entrant.complete', 'grant-report.manager.complete'],
    GrantReportComposer::class => ['partials.list-actions.schedule-report', 'grant-report.manager.search.action-overflow'],
    InvoiceComposer::class => ['payment.invoice.pdf'],
    PDFComposer::class => ['layouts.pdf', 'entry.pdf.judge', 'judging.pdf.score', 'entry.pdf.entrant', 'entry.pdf.packing-slip', 'entry.pdf.blank-form'],
    LanguagesComposer::class => ['setting.languages', 'users.profile', 'users.tabs.profile'],
    LeaderboardComposer::class => ['leaderboard.index'],
    LeaderboardVipJudgingComposer::class => ['leaderboard.modes.vip_judging'],
    LeaderboardVotingComposer::class => ['leaderboard.modes.voting'],
    LegalBasisComposer::class => ['partials.notifications.legal-basis', 'notification.form'],
    MainMenuComposer::class => ['layouts.fullpage', 'layouts.splash-with-menu'],
    OpenRoundsComposer::class => ['round.open-rounds.button', 'round.open-rounds.form'],
    Pagination::class => ['partials.page.pagination'],
    ParentSelectComposer::class => ['category.parent-select'],
    ProfileSecurityComposer::class => ['users.profile.security'],
    ProgressComposer::class => ['leaderboard.progress.index'],
    ProgressExportPanelsComposer::class => ['leaderboard.progress.export.panels'],
    PusherComposer::class => ['partials.footer.pusher', 'entry-form.entrant.edit', 'entry-form.entrant.start', 'entry-form.manager.edit', 'entry-form.manager.start', 'grant-report.entrant.edit', 'grant-report.entrant.start', 'grant-report.manager.edit', 'grant-report.manager.start'],
    QualifyingEntriesComposer::class => ['qualifying.index'],
    QuickManager::class => ['entry.manager.view', 'entry.manager.preview', 'judging.score', 'qualifying.decide', 'top-pick.show', 'voting.vote', 'gallery.view'],
    RadioFieldComposer::class => ['html.field.form.radio', 'review-flow.task.fields.radio'],
    RemoveFilterComposer::class => ['html.buttons.remove-filter'],
    ReviewFlowForm::class => ['partials.list-actions.reassign-reviewer'],
    RoleSelectComposer::class => ['role.select'],
    ReportFormsComposer::class => ['entry.partials.schedule-report', 'partials.list-actions.schedule-report', 'entry.manager.view'],
    ScoreMatrix\VipJudging::class => ['entry.manager.score-matrix'],
    ScoreMatrix\Qualifying::class => ['entry.manager.qualifying-matrix'],
    ScoreMatrix\TopPick::class => ['entry.manager.top-pick-matrix'],
    ScoreMatrix\Voting::class => ['entry.manager.voting-matrix'],
    ScoreSetAttachmentsComposer::class => ['score-set.tabs.display'],
    ScoreSetSelectComposer::class => ['score-set.select'],
    ScoringCriterionFormComposer::class => ['scoring-criteria.form'],
    ScoringFormComposer::class => ['judging.score'],
    ScoringSlideshowComposer::class => ['judging.slideshow'],
    SearchableFieldsComposer::class => ['field.searchable'],
    DropDownListFieldComposer::class => ['html.field.search.drop-down-list', 'html.field.form.drop-down-list', 'review-flow.task.fields.drop-down-list'],
    SharingTagsComposer::class => ['partials.header.sharing-tags', 'partials.misc.social-sharing'],
    SystemEmailComposer::class => ['emails.system.main'],
    SupportedLanguagesComposer::class => ['users.profile', 'users.tabs.profile', 'registration.fields'],
    SupportedCurrenciesComposer::class => ['discount.form', 'tier.form'],
    TabFormComposer::class => ['tab.form'],
    //TODO this file doesn't exist
    // ThemeSettingsComposer::class => ['theme.index'],
    TierFormComposer::class => ['tier.form', 'entry.partials.schedule-report', 'entry.manager.view'],
    TimeFieldComposer::class => ['html.field.form.time', 'judging.fields.time'],
    TimezoneComposer::class => ['html.field.datetime', 'html.field.form.datetime', 'html.field.form.date', 'review-flow.task.fields.datetime', 'field.columnator.fields.date', 'field.columnator.fields.datetime', 'entry.partials.schedule-report', 'entry.manager.view'],
    TopPickFiltertronComposer::class => ['top-pick.index'],
    TrialAccountComposer::class => ['partials.misc.trial-account', 'partials.footer.trial-tracker'],
    UserAccountFieldsComposer::class => ['users.tabs.account-fields', 'users.complete'],
    UserConfirmationComposer::class => ['users.confirm'],
    UserMenuComposer::class => ['layouts.fullpage', 'layouts.splash-with-menu'],
    UserProfileComposer::class => ['users.profile'],
    UserProfileLabelsComposer::class => ['users.profile.email-label', 'users.profile.mobile-label'],
    VIPEntrantDetails::class => ['judging.score', 'entry.pdf.judge', 'judging.pdf.score'],
    VipJudgingProgressExportComposer::class => ['leaderboard.progress.export.vip-judging-content'],
    PhoneFieldComposer::class => ['entry-form.entrant.edit', 'entry-form.entrant.start', 'entry-form.manager.edit', 'entry-form.manager.start', 'users.common.common-fields', 'html.field.form.phone', 'registration.fields', 'review-flow.task.fields.phone', 'home.index'],
    ChapterComposer::class => ['entry-form.entrant.edit', 'entry-form.entrant.start', 'entry-form.manager.edit', 'entry-form.manager.start', 'price.form'],
    TablePreviewComposer::class => ['judging.fields.table-preview', 'entry.blank-form-pdf.table-preview', 'document.fields.table'],
    SecurePayComposer::class => ['ecommerce.partials.securepay-form'],
    AssignJudgesComposer::class => ['partials.list-actions.assign-judges'],
    CreateDocumentComposer::class => ['partials.list-actions.create-document', 'partials.list-actions.create-document-singular', 'funding.allocation.entry'],
    DownloadFiltersComposer::class => ['download.partials.filters'],
    VueDataComposer::class => ['partials.misc.vue-data'],
    SplashWithMenuComposer::class => ['layouts.splash-with-menu'],
    FooterComposer::class => ['partials.footer.foot'],
    BlockSavedViewsComposer::class => ['grant-report.entrant.index', 'entry.entrant.index'],
    UnauthorizedComposer::class => ['errors.401'],
]);

View::composer('*', GlobalComposer::class);
