import { defineConfig } from 'vitest/config';

export default defineConfig({
	test: {
		include: [
			'resources/assets/js/src/**/*.spec.ts',
			'resources/assets/js/vitest/**/*{.test,.spec,Spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
		],
		alias: {
			vue: 'vue/dist/vue.esm.js',
			'@': '/resources/assets/js/src/',
		},
		globals: true,
		setupFiles: ['/resources/assets/js/vitest/setup.ts'],
		environment: 'jsdom',
	},
});
