<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Default Elasticsearch Connection Name
    |--------------------------------------------------------------------------
    |
    | Here you may specify which of the Elasticsearch connections below you wish
    | to use as your default connection for all work. Of course.
    |
    */

    'default' => env('ELASTIC_CONNECTION', 'default'),

    /*
    |--------------------------------------------------------------------------
    | Elasticsearch Connections
    |--------------------------------------------------------------------------
    |
    | Here are each of the Elasticsearch connections setup for your application.
    | Of course, examples of configuring each Elasticsearch platform.
    |
    */

    'connections' => [

        'default' => [

            'servers' => [

                [
                    'host' => env('ELASTIC_HOST', '127.0.0.1'),
                    'port' => env('ELASTIC_PORT', 9200),
                    'user' => env('ELASTIC_USER', ''),
                    'pass' => env('ELASTIC_PASS', ''),
                    'scheme' => env('ELASTIC_SCHEME', 'http'),
                ],

            ],

            'index' => env('ELASTIC_INDEX', 'af_index'),

            // Elasticsearch handlers
            // 'handler' => new MyCustomHandler(),

            'logging' => [
                'enabled' => env('ELASTIC_LOGGING_ENABLED', false),
                'level' => env('ELASTIC_LOGGING_LEVEL', 'all'),
                'location' => env('ELASTIC_LOGGING_LOCATION', base_path('storage/logs/elasticsearch.log')),
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Elasticsearch Indices
    |--------------------------------------------------------------------------
    |
    | Here you can define your indices, with separate settings and mappings.
    | Edit settings and mappings and run 'php artisan es:index:update' to update
    | indices on elasticsearch server.
    |
    | 'my_index' is just for test. Replace it with a real index name.
    |
    */

    'indices' => [

        'event_logs' => [
            'aliases' => [
                'af4_event_logs',
            ],

            'settings' => [
                'number_of_shards' => 5,
                'number_of_replicas' => 1,
                // As long as we use pagination we have to specify a number that can contain all the paginated results
                // The bigger the page number -  the longer the loading time (mostly if we jump thousands of pages)
                'max_result_window' => ********,
                'analysis' => [
                    'analyzer' => [
                        'folding' => [
                            'tokenizer' => 'standard',
                            'filter' => ['standard', 'lowercase', 'asciifolding'],
                        ],
                    ],
                ],
            ],

            'mappings' => [
                'event_logs' => [
                    'properties' => [
                        'old_id' => ['type' => 'integer', 'index' => true],
                        'resource' => ['type' => 'text', 'fielddata' => true],
                        'user_id' => ['type' => 'integer', 'index' => true],
                        'jedi_id' => ['type' => 'integer', 'index' => true],
                        'created_at' => ['type' => 'date', 'format' => "yyyy-MM-dd' 'HH:mm:ss", 'index' => true],
                        'action' => ['type' => 'text', 'fielddata' => true],
                        'description' => ['type' => 'text', 'fielddata' => true],
                        'data' => ['type' => 'object', 'enabled' => false],
                        'slug' => ['type' => 'text', 'fielddata' => true],
                        'season_id' => ['type' => 'integer', 'index' => true],
                        'account_id' => ['type' => 'integer', 'index' => true],
                        'ip' => ['type' => 'text', 'fielddata' => true],
                    ],
                ],
            ],
        ],
        'event_logs_01' => [
            'aliases' => [
                'af4_event_logs_01',
            ],

            'settings' => [
                'number_of_shards' => 5,
                'number_of_replicas' => 1,
                // As long as we use pagination we have to specify a number that can contain all the paginated results
                // The bigger the page number -  the longer the loading time (mostly if we jump thousands of pages)
                'max_result_window' => ********,
                'analysis' => [
                    'analyzer' => [
                        'folding' => [
                            'tokenizer' => 'standard',
                            'filter' => ['standard', 'lowercase', 'asciifolding'],
                        ],
                    ],
                ],
            ],

            'mappings' => [
                'event_logs' => [
                    'properties' => [
                        'old_id' => ['type' => 'integer', 'index' => true],
                        'resource' => ['type' => 'text', 'fielddata' => true],
                        'user_id' => ['type' => 'integer', 'index' => true],
                        'jedi_id' => ['type' => 'integer', 'index' => true],
                        'created_at' => ['type' => 'date', 'format' => "yyyy-MM-dd' 'HH:mm:ss", 'index' => true],
                        'action' => ['type' => 'text', 'fielddata' => true],
                        'description' => ['type' => 'text', 'fielddata' => true],
                        'data' => ['type' => 'object', 'enabled' => false],
                        'slug' => ['type' => 'text', 'fielddata' => true],
                        'season_id' => ['type' => 'integer', 'index' => true],
                        'account_id' => ['type' => 'integer', 'index' => true],
                        'ip' => ['type' => 'text', 'fielddata' => true],
                    ],
                ],
            ],
        ],

        'usage_logs' => [
            'aliases' => [
                'af4_usages',
            ],

            'settings' => [
                'number_of_shards' => 5,
                'number_of_replicas' => 1,
                'max_result_window' => ********,
                'analysis' => [
                    'analyzer' => [
                        'folding' => [
                            'tokenizer' => 'standard',
                            'filter' => ['standard', 'lowercase', 'asciifolding'],
                        ],
                    ],
                ],
            ],

            'mappings' => [
                'usage' => [
                    'properties' => [
                        'event' => ['type' => 'keyword', 'index' => true],
                        'status' => ['type' => 'keyword', 'index' => true],
                        'metrics' => ['type' => 'object', 'enabled' => false],
                        'metadata' => ['type' => 'object', 'enabled' => false],
                        'loggable_id' => ['type' => 'integer', 'index' => true],
                        'loggable_type' => ['type' => 'keyword', 'index' => true],
                        'account_id' => ['type' => 'integer', 'index' => true],
                        'user_id' => ['type' => 'integer', 'index' => true],
                        'processing_id' => ['type' => 'keyword', 'index' => true],
                        'created_at' => ['type' => 'date', 'format' => 'epoch_millis', 'index' => false],
                        'updated_at' => ['type' => 'date', 'format' => 'epoch_millis', 'index' => false],
                    ],
                ],
            ],
        ],
    ],
];
