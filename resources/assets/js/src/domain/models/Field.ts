import { Category, CategoryOption } from '@/domain/models/Category';

type Value<T = unknown> = T;

enum FieldResource {
	RESOURCE_ATTACHMENTS = 'Attachments',
	RESOURCE_FORMS = 'Entries',
	RESOURCE_USERS = 'Users',
	RESOURCE_CONTRIBUTORS = 'Contributors',
	RESOURCE_REFEREES = 'Referees',
}

enum FieldType {
	CONTENT = 'content',
	FILE = 'file',
	TABLE = 'table',
	TEXTAREA = 'textarea',
	TEXT = 'text',
	URL = 'url',
	DATE = 'date',
	CHECKBOXLIST = 'checkboxlist',
	CURRENCY = 'currency',
	DATETIME = 'datetime',
	TIME = 'time',
	FORMULA = 'formula',
}

type Field = {
	id: number;
	slug: string;
	type: FieldType;
	categoryOption: CategoryOption;
	categories: Category[];
	resource: FieldResource;
	required: boolean;
	entrantReadAccess: boolean;
	entrantWriteAccess: boolean;
	maximumWords: number;
	maximumCharacters: number;
};

export { type Field, type Value, FieldResource, FieldType };
