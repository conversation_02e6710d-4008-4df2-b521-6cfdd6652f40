var $ = require('jquery');
var toastr = require('toastr');

/**
 * Marker class
 */
module.exports = function () {
	// eslint-disable-next-line @typescript-eslint/no-this-alias
	var parent = this;
	var ids = [];

	// Add id into array
	this.add = function (id) {
		ids.push(id);
	};

	// Remove id from array
	this.remove = function (id) {
		for (var i = ids.length; i--; ) {
			if (ids[i] === id) {
				ids.splice(i, 1);
			}
		}
	};

	// Retrieve all ids
	this.getIds = function () {
		return ids;
	};

	// Total number of ids available for submission
	this.count = function () {
		return ids.length;
	};

	// Adds all of the current IDs into the form as 'selected[]' elements.
	this.addIdsToForm = function (form) {
		$(ids).each(function (i) {
			form.append($('<input>').attr({ type: 'hidden', name: 'selected[]', value: ids[i] }));
		});
	};

	// Toggle all checkboxes when toggle is changed.
	this.toggleChanged = function () {
		// Save state, as we need to reset after the change event
		var checked = $(this).prop('checked');

		// Toggle all
		$(this).parents('table').first().find('.marker-checkbox').prop('checked', checked).change();

		// Reset state :-)
		$(this).prop('checked', checked);
	};

	// Add/Remove id when marker is changed.
	this.markerChanged = function () {
		var id = $(this).data('id');

		if ($(this).prop('checked')) {
			parent.add(id);
		} else {
			parent.remove(id);
		}

		$('input.marker-toggle').prop('checked', false);
	};

	this.idsExist = function () {
		return parent.count() > 0;
	};

	this.displayError = function () {
		toastr.info(App.selectedItemsMessage.clarify());
	};

	// jQuery Bindings
	$('input.marker-toggle').on('change', this.toggleChanged);
	$('input.marker-checkbox').on('change', this.markerChanged);
};
