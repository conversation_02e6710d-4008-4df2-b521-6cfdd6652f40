import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import {
	deletedFile,
	deleteFileUrl,
	imageDataUrl,
	uploadedFile,
	uploadingFile,
} from '@/lib/components/Shared/editor/uploads/uploadsLogic';
import { File, FileId } from '@/lib/components/Shared/editor/uploads/Uploads.types';
import Vue, { Ref, ref } from 'vue';

vi.mock('tectoastr', () => ({
	tectoastr: vi.fn((foo = null) => foo),
}));

vi.mock('toastr', () => ({
	toastr: vi.fn((foo = null) => foo),
}));

// eslint-disable-next-line @typescript-eslint/naming-convention
let _pendingUploads = 1;
// eslint-disable-next-line @typescript-eslint/naming-convention
let _commentsEdited = 0;
// eslint-disable-next-line @typescript-eslint/naming-convention
const _userId = 99;
const mockCommentsService = () => ({
	pendingUploads: () => _pendingUploads,

	changePendingUploadsBy: (changeBy: number) => {
		_pendingUploads += changeBy;
	},

	userId: () => _userId,

	commentsEdited: () => _commentsEdited,

	changeCommentsEditedBy: (changeBy: number) => {
		_commentsEdited += changeBy;
	},
});

describe('uploadsLogic', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	it('uploadingFile assigns user id', () => {
		const file = { id: 1, name: 'test.txt' };
		const files: Ref<File[]> = ref([]);
		const commentsService = mockCommentsService();
		const userId = 456;
		const prevPendingUploads = commentsService.pendingUploads();

		uploadingFile(file, files, commentsService, userId);

		expect(files.value[0].name).toEqual('test.txt');
		expect(files.value[0].userId).toEqual(456);
		expect(commentsService.pendingUploads()).toEqual(prevPendingUploads + 1);
	});

	it('imageDataUrl sets image property', () => {
		const fileA = { remoteId: 83, id: 'temp_id_a', temp: false, name: 'foo.jpg' };
		const fileB = { remoteId: 84, id: 'temp_id_b', temp: false, name: 'bar.jpg' };
		const files: Ref<File[]> = ref([fileA, fileB]);
		const fakeDataUrl = 'data:foobar';

		expect(files.value[1].image).toEqual(undefined);

		imageDataUrl(fakeDataUrl, fileB, files);

		expect(files.value[1].image).toEqual('data:foobar');
	});

	it('uploadedFile sets proper file id and marks file as non temporary', () => {
		const file1Temp = { id: 'temp_id_1', temp: true };
		const file2Ready = { id: 22, oldId: 'temp_id_2', temp: false };
		const files: Ref<File[]> = ref([file1Temp, file2Ready]);
		const completeFileTokens: FileId[] = [];
		const canceledFiles: Ref<FileId[]> = ref([]);
		const store = mockCommentsService();
		const userId = 456;

		expect(files.value.length).toEqual(2);
		expect(files.value[0].id).toEqual('temp_id_1');
		expect(files.value[1].id).toEqual(22);
		expect(files.value[0].temp).toEqual(true);
		expect(files.value[1].temp).toEqual(false);

		const file1Uploaded = { remoteId: 11, id: 'temp_id_1', temp: false, name: 'test.txt' };
		uploadedFile(completeFileTokens, file1Uploaded, files, store, canceledFiles, userId);

		expect(files.value.length).toEqual(2);
		expect(files.value[0].id).toEqual(22);
		expect(files.value[1].id).toEqual(11);
		expect(files.value[0].temp).toEqual(false);
		expect(files.value[1].temp).toEqual(false);
	});

	it('deleteFileUrl is valid', () => {
		const comment = { id: 7, slug: 'hUeHuE' };
		const file = { id: 9 };

		deleteFileUrl(file, comment);

		expect(deleteFileUrl(file, comment)).toEqual('/comment/file/9/hUeHuE');
	});

	it('deletedFile removes the file from files', async () => {
		Vue.prototype.$http = {
			delete: () => Promise.resolve({}),
		};

		const comment = { id: 9, slug: 'hOhOhO' };
		const fileA = { id: 4 };
		const fileB = { id: 5 };
		const files = ref([fileA, fileB]);

		expect(files.value.map((f) => f.id)).toEqual([fileA.id, fileB.id]);

		const callbackState = { counter: 0 };
		const options = {
			finally: () => {
				callbackState.counter++;
			},
		};

		const asyncDeleteFile = async () => {
			await deletedFile(fileA, files, comment, options);
		};

		await asyncDeleteFile();

		expect(callbackState.counter).toEqual(1);
		expect(files.value.map((f) => f.id)).toEqual([fileB.id]);
	});
});
