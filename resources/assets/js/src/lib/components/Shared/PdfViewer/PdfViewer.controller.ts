import { useContainer } from '@/domain/services/Container';
import { v4 as uuid } from 'uuid';
import { VueHooks } from '@/domain/services/VueHooks';
import { nextTick, ref, type Ref } from 'vue';

import { createPdfViewer } from '@/lib/components/Shared/PdfViewer/lib';

type Props = {
	name: string;
	src: string;
};

type View = {
	uniquePdfContainerId: Ref<string>;
};

const pdfViewerController = (props: Props): View => {
	const { onMounted, onBeforeUnmount } = useContainer<VueHooks>();

	let pdfViewer: ReturnType<typeof createPdfViewer> | null = null;

	const uniquePdfContainerId = ref<string>(`pdf-container-${uuid()}`);

	onMounted(async () => {
		await nextTick();

		pdfViewer = createPdfViewer(uniquePdfContainerId.value, props.src);
		await pdfViewer.init();
	});

	onBeforeUnmount(() => {
		if (pdfViewer) {
			pdfViewer.destroy();
			pdfViewer = null;
		}
	});

	return {
		uniquePdfContainerId,
	};
};

export { pdfViewerController, type Props, type View };
