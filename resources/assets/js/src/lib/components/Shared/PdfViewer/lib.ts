import * as pdfjs from 'pdfjs-dist';
import type { PageViewport } from 'pdfjs-dist/types/src/display/display_utils.js';
import { pipe } from 'fp-ts/function';
import { chain, Either, isLeft, left, right } from 'fp-ts/Either';
import { from<PERSON><PERSON><PERSON>, TaskEither, chain as te<PERSON>hain, left as teLeft, tryCatch } from 'fp-ts/TaskEither';
import type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist/types/src/display/api.js';

try {
	pdfjs.GlobalWorkerOptions.workerSrc = '/pdf.worker.min.js';
} catch (e) {
	console.error('pdfjs.GlobalWorkerOptions.workerSrc error: ', e);
}

type PdfViewerState = {
	loadingProgress: number;
	scale: number;
	pdfDocProxy: PDFDocumentProxy | null;
};

type RenderContext = {
	page: number;
	pageProxy: PDFPageProxy;
	viewport: PageViewport;
	pageCanvas: HTMLCanvasElement;
	canvasWrapper: HTMLElement;
};

const createPdfViewer = (containerId: string, src: string) => {
	const state: PdfViewerState = {
		loadingProgress: 30,
		scale: 1,
		pdfDocProxy: null,
	};
	let pageScrollObserver: IntersectionObserver | null = null;

	const getTotalPages = () => state.pdfDocProxy?.numPages || 0;

	const getContainer = (): Either<string, HTMLElement> => {
		const container = document.getElementById(containerId);
		if (!container) {
			return left('Container not found');
		}

		return right(container as HTMLElement);
	};

	const getCurrentPageInput = (container: HTMLElement): Either<string, HTMLInputElement> => {
		const pageInput = container.parentElement?.querySelector('input[name="pdf-current-page"]') as HTMLInputElement;
		if (!pageInput) {
			return left('Page input not found');
		}

		return right(pageInput);
	};

	const getCurrentPage = (): Either<string, number> =>
		pipe(
			getContainer(),
			chain(getCurrentPageInput),
			chain((pageInput) => {
				const page = parseInt(pageInput.value, 10);
				return isNaN(page) ? left('Invalid page number') : right(page);
			})
		);

	const clearContainer = (): Either<string, HTMLElement> =>
		pipe(
			getContainer(),
			chain((container) => {
				container.innerHTML = '';
				return right(container);
			})
		);

	const getPageProxy = ({
		page,
	}: Pick<RenderContext, 'page'>): TaskEither<string, Pick<RenderContext, 'page' | 'pageProxy'>> =>
		tryCatch(
			async () => {
				if (!state.pdfDocProxy) {
					throw new Error('PDF document not loaded');
				}

				const pageProxy = await state.pdfDocProxy.getPage(page);

				return { page, pageProxy };
			},
			(err) => `Failed to get PDF page proxy for page ${page}: ${err}`
		);

	const getViewport = (
		renderContext: Pick<RenderContext, 'page' | 'pageProxy'>,
		scale = state.scale
	): Either<string, Pick<RenderContext, 'page' | 'pageProxy' | 'viewport'>> => {
		const viewport = renderContext.pageProxy.getViewport({ scale });
		if (!viewport) {
			return left('Failed to get viewport');
		}

		return right({ ...renderContext, viewport });
	};

	const calculateFitScale = (viewport: PageViewport, fitType: 'width' | 'height'): Either<string, number> =>
		pipe(
			getContainer(),
			chain((container) => {
				const scale =
					fitType === 'width'
						? (container.clientWidth - 20) / viewport.width
						: (container.clientHeight - 20) / viewport.height;

				return right(scale);
			})
		);

	const getFitScale = (fitType: 'width' | 'height'): TaskEither<string, number> =>
		pipe(
			getPageProxy({ page: 1 }),
			teChain((renderContext) => fromEither(getViewport(renderContext, 1))),
			teChain(({ viewport }) => fromEither(calculateFitScale(viewport, fitType)))
		);

	const renderCanvas = (
		renderContext: Omit<RenderContext, 'pageCanvas' | 'canvasWrapper'>
	): TaskEither<string, Omit<RenderContext, 'canvasWrapper'>> => {
		const pageCanvas = document.createElement('canvas');
		pageCanvas.width = renderContext.viewport.width;
		pageCanvas.height = renderContext.viewport.height;

		const canvasContext = pageCanvas.getContext('2d');
		if (!canvasContext) {
			return teLeft('Failed to get canvas context');
		}

		const ctx = {
			canvasContext,
			viewport: renderContext.viewport,
		};

		return tryCatch(
			async () => {
				const renderTask = renderContext.pageProxy.render(ctx);
				await renderTask.promise;

				return { ...renderContext, pageCanvas };
			},
			(err) => `Failed to render canvas: ${err}`
		);
	};

	const renderCanvasWrapperPage = (
		renderContext: Omit<RenderContext, 'canvasWrapper'>
	): Either<string, RenderContext> => {
		const canvasWrapper = document.createElement('div');
		canvasWrapper.setAttribute('data-page-number', renderContext.page.toString());
		canvasWrapper.style.width = `${renderContext.viewport.width}px`;
		canvasWrapper.style.height = `${renderContext.viewport.height}px`;

		canvasWrapper.appendChild(renderContext.pageCanvas);

		return right({ ...renderContext, canvasWrapper });
	};

	const getCanvasWrapper = (page: number): Either<string, HTMLElement> =>
		pipe(
			getContainer(),
			chain((container) => {
				const canvasWrapper = container.querySelector(`[data-page-number="${page}"]`) as HTMLElement;
				if (!canvasWrapper) {
					return left(`Canvas wrapper for page ${page} not found`);
				}

				return right(canvasWrapper);
			})
		);

	const renderPage = (container: HTMLElement, renderContext: RenderContext): Either<string, void> => {
		container.appendChild(renderContext.canvasWrapper);
		return right(undefined);
	};

	const renderPageInContainer = (renderContext: RenderContext): Either<string, void> =>
		pipe(
			getContainer(),
			chain((container) => renderPage(container, renderContext))
		);

	const renderSinglePage = (page: number): TaskEither<string, void> =>
		pipe(
			getPageProxy({ page }),
			teChain((renderContext) => fromEither(getViewport(renderContext))),
			teChain(renderCanvas),
			teChain((renderContext) => fromEither(renderCanvasWrapperPage(renderContext))),
			teChain((renderContext) => fromEither(renderPageInContainer(renderContext)))
		);

	const renderPdf = (): TaskEither<string, void> =>
		tryCatch(
			async () => {
				const scaleResult = await getFitScale('width')();
				if (isLeft(scaleResult)) {
					throw new Error(scaleResult.left);
				}

				state.scale = scaleResult.right;

				// Render all pages
				for (let i = 1; i <= getTotalPages(); i++) {
					const result = await renderSinglePage(i)();
					if (isLeft(result)) {
						throw new Error(result.left);
					}
				}
			},
			(err) => `Failed to render pages: ${err}`
		);

	const handleProgress = (progressData: { loaded: number; total: number }): Either<string, void> => {
		state.loadingProgress = progressData.total
			? Math.round((progressData.loaded / progressData.total) * 100)
			: state.loadingProgress + 1;

		return pipe(
			getContainer(),
			chain((container) => {
				const progressBar = container.querySelector('.pdf__loading') as HTMLElement;
				if (progressBar) {
					progressBar.style.width = `${state.loadingProgress}%`;
				}

				return right(undefined);
			})
		);
	};

	const loadPdf = (): TaskEither<string, void> =>
		tryCatch(
			async () => {
				const loadingTask = pdfjs.getDocument(src);
				loadingTask.onProgress = handleProgress;
				state.pdfDocProxy = await loadingTask.promise;
			},
			(err) => `Failed to load PDF document: ${err}`
		);

	const updateCanvas = ({ pageProxy, viewport }: Pick<RenderContext, 'pageProxy' | 'viewport'>, pageNumber: number) =>
		pipe(
			fromEither(
				pipe(
					getCanvasWrapper(pageNumber),
					chain((canvasWrapper) => {
						const pageCanvas = canvasWrapper.querySelector('canvas') as HTMLCanvasElement;
						if (!pageCanvas) {
							return left('Page canvas not found');
						}

						pageCanvas.width = viewport.width;
						pageCanvas.height = viewport.height;

						canvasWrapper.style.width = `${viewport.width}px`;
						canvasWrapper.style.height = `${viewport.height}px`;

						const canvasContext = pageCanvas.getContext('2d');
						if (!canvasContext) {
							return left('Failed to get canvas context');
						}

						const renderContext = {
							canvasContext,
							viewport,
						};

						return right(renderContext);
					})
				)
			),
			teChain((renderContext) =>
				tryCatch(
					async () => {
						const renderTask = pageProxy.render(renderContext);
						await renderTask.promise;
					},
					(err) => `Failed to render page ${pageNumber}: ${err}`
				)
			)
		);

	const updatePageScale = async (pageNumber: number, newScale: number): Promise<void> => {
		const result = await pipe(
			getPageProxy({ page: pageNumber }),
			teChain((renderContext) => fromEither(getViewport(renderContext, newScale))),
			teChain((renderContext) => updateCanvas(renderContext, pageNumber))
		)();

		if (isLeft(result)) {
			console.error('Error updating page scale:', result.left);
		}
	};

	const updateAllPagesScale = async (newScale: number): Promise<void> => {
		state.scale = newScale;

		if (pageScrollObserver) {
			pageScrollObserver.disconnect();
		}

		for (let i = 1; i <= getTotalPages(); i++) {
			await updatePageScale(i, newScale);
		}

		if (pageScrollObserver) {
			setupPageScrollObserver();
		}
	};

	const fitToContainer = async (fitType: 'width' | 'height'): Promise<void> => {
		const result = await getFitScale(fitType)();

		if (isLeft(result)) {
			console.error(`Error calculating ${fitType} scale:`, result.left);
			return;
		}

		await updateAllPagesScale(result.right);
	};

	const validatePageNumber = (page: number): Either<string, number> => {
		if (page < 1) {
			return left('Page number must be greater than 0');
		}

		if (page > getTotalPages()) {
			return left('Page number must be less than total pages');
		}

		return right(page);
	};

	const setCurrentPage = (page: number): Either<string, number> =>
		pipe(
			getContainer(),
			chain(getCurrentPageInput),
			chain((pageInput) => {
				pageInput.value = page.toString();
				return right(page);
			})
		);

	const goToPage = (page: number): Either<string, void> =>
		pipe(
			validatePageNumber(page),
			chain(setCurrentPage),
			chain(getCanvasWrapper),
			chain((canvasWrapper) => {
				canvasWrapper.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
				return right(undefined);
			})
		);

	const navigateToPage = async (page: number): Promise<void> => {
		const result = goToPage(page);
		if (isLeft(result)) {
			console.error('Navigation error:', result.left);
		}
	};

	const goToPreviousPage = async (): Promise<void> => {
		const result = getCurrentPage();
		if (isLeft(result)) {
			console.error('Error getting current page:', result.left);
			return;
		}

		await navigateToPage(result.right - 1);
	};

	const goToNextPage = async (): Promise<void> => {
		const result = getCurrentPage();
		if (isLeft(result)) {
			console.error('Error getting current page:', result.left);
			return;
		}

		await navigateToPage(result.right + 1);
	};

	const createToolbarButton = (config: {
		name: string;
		label: string;
		icon?: { name: string; class?: string };
		action: () => void | Promise<void>;
	}): HTMLButtonElement => {
		const button = document.createElement('button');
		button.type = 'button';
		button.classList.add('pdf__toolbar-button');
		button.addEventListener('click', config.action);

		if (config.icon) {
			const icon = document.createElement('i');
			icon.className = `af-icons af-icons-${config.icon.name} ${config.icon.class || ''}`;
			button.prepend(icon);
		}

		const srOnly = document.createElement('span');
		srOnly.className = 'sr-only';
		srOnly.innerText = config.label;
		button.appendChild(srOnly);

		return button;
	};

	const prepareToolbarButtons = (): Either<string, HTMLButtonElement[]> => {
		const controls = [
			{
				name: 'zoom-out',
				label: 'Zoom out',
				icon: { name: 'zoom-out' },
				action: zoomOut,
			},
			{
				name: 'zoom-in',
				label: 'Zoom in',
				icon: { name: 'zoom-in' },
				action: zoomIn,
			},
			{
				name: 'fit-width',
				label: 'Fit width',
				icon: { name: 'fit-width' },
				action: fitWidth,
			},
			{
				name: 'fit-height',
				label: 'Fit height',
				icon: { name: 'fit-height' },
				action: fitHeight,
			},
			{
				name: 'page-previous',
				label: 'Previous page',
				icon: { name: 'chevron-up', class: '-m--t--6' },
				action: goToPreviousPage,
			},
			{
				name: 'page-next',
				label: 'Next page',
				icon: { name: 'chevron-down', class: '-m--t--6' },
				action: goToNextPage,
			},
		];

		const buttons = controls.map((control) => createToolbarButton(control));
		return right(buttons);
	};

	const renderToolbarButtons = (buttons: HTMLButtonElement[]): Either<string, HTMLElement> => {
		const toolbar = document.createElement('div');
		toolbar.classList.add('pdf__toolbar');
		buttons.forEach((button) => {
			toolbar.appendChild(button);
		});
		return right(toolbar);
	};

	const preparePageInput = (): Either<string, HTMLInputElement> => {
		const input = document.createElement('input');
		input.type = 'number';
		input.name = 'pdf-current-page';
		input.min = '1';
		input.max = getTotalPages().toString();
		input.value = '1';
		input.addEventListener('change', (e) => {
			const page = parseInt((e.target as HTMLInputElement).value || '1');
			goToPage(page);
		});

		return right(input);
	};

	const renderPagePagination = (toolbar: HTMLElement): Either<string, HTMLElement> =>
		pipe(
			preparePageInput(),
			chain((input) => {
				const label = document.createElement('label');
				const labelSpan = document.createElement('span');
				labelSpan.innerText = 'Page number';
				labelSpan.classList.add('sr-only');

				const span = document.createElement('span');
				span.innerText = ` / ${getTotalPages().toString()}`;
				label.appendChild(input);
				label.appendChild(labelSpan);
				label.appendChild(span);
				toolbar.appendChild(label);

				return right(toolbar);
			})
		);

	const renderHeader = (header: HTMLElement): Either<string, void> =>
		pipe(
			getContainer(),
			chain((container) => {
				if (container.parentElement) {
					container.parentElement.prepend(header);
				}

				return right(undefined);
			})
		);

	const setupPageScrollObserver = (): Either<string, void> =>
		pipe(
			getContainer(),
			chain((container) => {
				pageScrollObserver = new IntersectionObserver(
					(entries) => {
						entries.forEach((entry) => {
							if (entry.isIntersecting) {
								const index = Array.from(container.querySelectorAll('[data-page-number]')).indexOf(entry.target as HTMLElement);
								if (index !== -1) {
									setCurrentPage(index + 1);
								}
							}
						});
					},
					{
						root: container,
						threshold: 0.5,
					}
				);

				return right(container);
			}),
			chain((container) => {
				const canvasWrappers = container.querySelectorAll('[data-page-number]');
				canvasWrappers.forEach((wrapper) => {
					pageScrollObserver?.observe(wrapper);
				});

				return right(undefined);
			})
		);

	const renderToolbar = (): Either<string, void> =>
		pipe(
			prepareToolbarButtons(),
			chain(renderToolbarButtons),
			chain(renderPagePagination),
			chain((toolbar) => {
				const header = document.createElement('div');
				header.classList.add('pdf__header');
				header.appendChild(toolbar);

				return right(header);
			}),
			chain(renderHeader)
		);

	const init = async () => {
		const result = await pipe(
			fromEither(clearContainer()),
			teChain(loadPdf),
			teChain(renderPdf),
			teChain(() => fromEither(renderToolbar())),
			teChain(() => fromEither(setupPageScrollObserver()))
		)();

		if (isLeft(result)) {
			console.error('Error:', result.left);
		}
	};

	const destroy = () => {
		pipe(
			clearContainer(),
			chain(() => {
				if (state.pdfDocProxy) {
					state.pdfDocProxy.destroy();
				}

				return right(undefined);
			})
		);
	};

	const adjustZoom = async (factor: number): Promise<void> => {
		const newScale = state.scale * factor;
		await updateAllPagesScale(newScale);
	};

	const zoomIn = async (): Promise<void> => {
		await adjustZoom(1.2);
	};

	const zoomOut = async (): Promise<void> => {
		await adjustZoom(0.8);
	};

	const fitWidth = async (): Promise<void> => {
		await fitToContainer('width');
	};

	const fitHeight = async (): Promise<void> => {
		await fitToContainer('height');
	};

	return {
		init,
		destroy,
	};
};

export { createPdfViewer, type PdfViewerState };
