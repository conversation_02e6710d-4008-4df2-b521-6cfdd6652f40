<template>
  <div ref="quickGrantManager">
    <div class="form-group">
      <label :for="'grantStatus'">
        {{ lang.get('grants.titles.status') }}
      </label>
      <select-field
        id="grantStatus"
        :key="currentStatus"
        :empty-option="true"
        :items="grantStatuses"
        id-property="slug"
        :value="currentStatus"
        name="grantStatus"
        value-property="name"
        @selected="currentStatusUpdated"
      />
    </div>
    <div class="form-group">
      <label for="grantEndsAt">
        {{ lang.get('grants.titles.grant_ends_at') }}
      </label>
      <localised-datepicker
        id="grantEndsAt"
        :key="dateChanged"
        tabindex="0"
        mode="date"
        :value="currentEndDate"
        :include-timezone="false"
        :preselect-current-date="false"
        :highlight-today="false"
        :hidden-fields="false"
        @changed="endDateUpdated"
      />
    </div>
    <div v-if="buttonsVisible" class="form-group">
      <button role="button" class="btn btn-primary btn-sm" @click="save">
        {{ lang.get('buttons.save') }}
      </button>
      <button role="button" class="btn btn-secondary btn-sm" @click="reset">
        {{ lang.get('buttons.cancel') }}
      </button>
    </div>
  </div>
</template>

<script>
import toastr from 'toastr';
import { route } from '@/lib/utils';
import { SelectField } from 'vue-bootstrap';
import LocalisedDatepicker from '@/lib/components/Shared/LocalisedDatepicker';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin';

export default {
  components: {
    SelectField,
    LocalisedDatepicker
  },
  mixins: [langMixin],
  props: {
    entryId: {
      type: String,
      required: true
    },
    grantStatuses: {
      type: Array,
      required: true
    },
    selectedStatus: {
      type: String,
      default: null
    },
    endDate: {
      type: String,
      default: ''
    },
    timezone: {
      type: String,
      required: true
    },
    routes: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      originalStatus: this.selectedStatus,
      originalEndDate: this.endDate,
      currentStatus: this.selectedStatus,
      currentEndDate: {
        datetime: this.endDate,
        timezone: this.timezone
      },
      dateChanged: false
    };
  },
  computed: {
    buttonsVisible() {
      return (
        this.originalEndDate != this.currentEndDate.datetime ||
        this.originalStatus !== this.currentStatus
      );
    }
  },
  methods: {
    save() {
      const routeData = !this.currentStatus
        ? {}
        : {
            grantStatus: this.currentStatus
          };

      this.$http
        .post(route(this.routes['grant.status.add-to-entries'], routeData), {
          selected: [this.entryId],
          endDate: this.currentEndDate.datetime
        })
        .then(
          () => {
            toastr.success(this.lang.get('judging.autosaver.success'));

            this.originalStatus = this.currentStatus;
            this.originalEndDate = this.currentEndDate.datetime;

            // Reset the datepicker if the status was removed
            if (!this.currentStatus) {
              this.dateChanged = true;
              this.$nextTick(() => {
                this.originalEndDate = null;
                this.currentEndDate.datetime = '';
                this.dateChanged = false;
              });
            }
          },
          error => {
            toastr.error(
              error.response.data.message ||
                this.lang.get('judging.autosaver.error')
            );
          }
        );
    },
    endDateUpdated(value) {
      this.currentEndDate.datetime = value;
    },
    currentStatusUpdated(name, value) {
      this.currentStatus = value;
    },
    reset() {
      this.currentStatus = this.originalStatus;
      this.currentEndDate.datetime = this.originalEndDate;
      this.dateChanged = true;
      this.$nextTick(() => {
        this.dateChanged = false;
      });
    }
  }
};
</script>
