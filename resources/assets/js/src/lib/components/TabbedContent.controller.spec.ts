import { getUrlParameter } from '@/lib/url';
import { afterEach, beforeEach, describe, expect, it, Mock, SpyInstance, vi } from 'vitest';
import { Props, tabbedContentController } from '@/lib/components/TabbedContent.controller';

vi.mock('jquery', () => ({
	default: {
		on: vi.fn(),
		off: vi.fn(),
	},
}));

vi.mock('@/vendor/pjax/pjax', () => ({
	default: {
		state: () => ({}),
	},
}));

vi.mock('@/lib/url', () => ({
	getUrlParameter: vi.fn(),
	setUrlParameter: vi.fn((url, param, value) => `${url}?${param}=${value}`),
}));

vi.mock('@/domain/services/Container', () => ({
	useContainer: () => ({
		onBeforeMount: vi.fn(),
		onBeforeUnmount: vi.fn(),
		onMounted: vi.fn(),
	}),
}));

vi.mock('@/lib/utils', () => ({
	getGlobal: vi.fn(() => ({
		window: { location: { href: '' } },
		history: { pushState: vi.fn(), replaceState: vi.fn() },
	})),
}));

describe('tabbedContentController', () => {
	let props: Props;
	let emit: Mock;
	let pushState: SpyInstance;
	let replaceState: SpyInstance;
	const defaultLocation = window.location.href;

	beforeEach(() => {
		props = {
			tabs: [
				{ id: 'tab1', name: 'Tab 1' },
				{ id: 'tab2', name: 'Tab 2', active: true },
				{ id: 'tab3', name: 'Tab 3', disabled: true },
				{ id: 'tab4', name: 'Tab 4' },
			],
			whenDisabledRedirectTo: '/redirect',
		};
		emit = vi.fn();
		pushState = vi.spyOn(window.history, 'pushState');
		replaceState = vi.spyOn(window.history, 'replaceState');
		vi.restoreAllMocks();
	});

	afterEach(() => {
		vi.resetAllMocks();
		// Reset the location to the default value
		Object.defineProperty(window, 'location', { value: { href: defaultLocation } });
	});

	it('initializes with the correct tab', () => {
		(getUrlParameter as Mock).mockReturnValue('tab2');

		const view = tabbedContentController(props, { emit: vi.fn() });

		expect(view.currentTab.value?.id).toBe('tab2');
	});

	it('selects a new tab correctly', () => {
		(getUrlParameter as Mock).mockReturnValue('tab2');

		const view = tabbedContentController(props, { emit });

		view.selectTab('tab1');

		expect(view.currentTab.value?.id).toBe('tab1');
		expect(emit).toHaveBeenCalledWith('tabSelected', { id: 'tab1', name: 'Tab 1', active: false, disabled: false });
		expect(pushState).toHaveBeenCalledWith({ tab: 'tab1' }, '', defaultLocation + '?vtab=tab1');
	});

	it('handles disabled tabs', () => {
		Object.defineProperty(window, 'location', { value: { href: defaultLocation } }); // Make location writable
		(getUrlParameter as Mock).mockReturnValue('tab1');

		const controller = tabbedContentController(props, { emit });

		controller.selectTab('tab3');
		expect(window.location.href).toBe('/redirect');
		expect(pushState).not.toHaveBeenCalled();
		expect(replaceState).not.toHaveBeenCalled();
	});

	it('selects the next tab correctly', () => {
		(getUrlParameter as Mock).mockReturnValue('tab1');
		const view = tabbedContentController(props, { emit });

		view.navigation.value.selectNext();

		expect(view.currentTab.value?.id).toBe('tab2');
	});

	it('selects the previous tab correctly', () => {
		(getUrlParameter as Mock).mockReturnValue('tab2');
		const controller = tabbedContentController(props, { emit });

		controller.navigation.value.selectPrev();

		expect(controller.currentTab.value?.id).toBe('tab1');
	});

	it('does not select next if on the last tab', () => {
		(getUrlParameter as Mock).mockReturnValue('tab4');
		const view = tabbedContentController(props, { emit });

		view.navigation.value.selectNext();

		expect(view.currentTab.value?.id).toBe('tab4');
	});

	it('does not select previous if on the first tab', () => {
		(getUrlParameter as Mock).mockReturnValue('tab1');
		const view = tabbedContentController(props, { emit });

		view.navigation.value.selectPrev();

		expect(view.currentTab.value?.id).toBe('tab1');
	});

	it('handles popstate events correctly', () => {
		(getUrlParameter as Mock).mockReturnValue('tab1');
		const view = tabbedContentController(props, { emit });

		view.selectTab('tab2', false);

		expect(view.currentTab.value?.id).toBe('tab2');
		expect(pushState).not.toHaveBeenCalled();
		expect(replaceState).toHaveBeenCalledWith({ tab: 'tab2' }, '', defaultLocation + '?vtab=tab2');
	});

	it('returns liveTabs', () => {
		const view = tabbedContentController({ whenDisabledRedirectTo: undefined, ...props }, { emit });
		expect(view.liveTabs.value).toEqual([
			{ id: 'tab1', name: 'Tab 1', active: false, disabled: false },
			{ id: 'tab2', name: 'Tab 2', active: true, disabled: false },
			{ id: 'tab3', name: 'Tab 3', active: false, disabled: true },
			{ id: 'tab4', name: 'Tab 4', active: false, disabled: false },
		]);
	});
});
