<template>
	<validator :field-id="agreement.name" field-type="checkbox">
		<div class="form-group">
			<div class="checkbox styled">
				<input
					:id="agreement.name"
					:name="agreement.name"
					type="checkbox"
					value="1"
					:checked="agreement.checked"
					@change="onChange"
				/>
				<label v-output="agreement.label" :for="agreement.name"></label>
			</div>
		</div>
		<instant-errors></instant-errors>
	</validator>
</template>

<script>
import Validator from '@/lib/components/Fields/validator/Validator';
import InstantErrors from '@/lib/components/Fields/validator/InstantErrors';

export default {
	components: {
		Validator,
		InstantErrors,
	},
	props: {
		agreement: {
			type: Object,
			required: true,
		},
	},
	methods: {
		onChange({ target }) {
			this.$emit('input', target.checked ? target.value : null);
		},
	},
};
</script>
