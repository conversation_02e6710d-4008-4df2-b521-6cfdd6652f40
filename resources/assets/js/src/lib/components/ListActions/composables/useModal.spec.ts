import { ModalEvents } from '@/lib/components/ListActions/composables/useModal.events';
import { nextTick } from 'vue';
import { useModal } from '@/lib/components/ListActions/composables/useModal';
import { describe, expect, it, vi } from 'vitest';

describe('useModal', () => {
	it('should toggle the modal', async () => {
		const emit = vi.fn();
		const name = 'modal';
		const { showModal, toggle } = useModal(name, emit);

		expect(showModal.value).toBe(false);

		toggle();

		expect(showModal.value).toBe(true);

		await nextTick();

		expect(emit).toHaveBeenCalledTimes(1);
		expect(emit).toHaveBeenCalledWith(ModalEvents.Reveal, name);

		toggle();

		expect(showModal.value).toBe(false);

		await nextTick();

		expect(emit).toHaveBeenCalledTimes(1);
	});

	it('should close the modal', async () => {
		const emit = vi.fn();
		const name = 'modal';
		const { showModal, modalClosed } = useModal(name, emit);

		showModal.value = true;

		expect(showModal.value).toBe(true);

		modalClosed();

		expect(showModal.value).toBe(false);

		await nextTick();

		expect(emit).toHaveBeenCalledTimes(1);
		expect(emit).toHaveBeenCalledWith(ModalEvents.Reveal, null);
	});
});
