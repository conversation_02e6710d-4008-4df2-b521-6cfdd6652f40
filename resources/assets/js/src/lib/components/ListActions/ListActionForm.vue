<template>
	<form method="POST" :action="route" accept-charset="UTF-8" :class="formClass">
		<input name="_method" type="hidden" :value="method" />
		<input v-for="id in ids" :key="id" type="hidden" name="selected[]" :value="id" />
		<slot></slot>
		<button
			v-if="showSubmit"
			class="submit"
			:class="buttonClass ? buttonClass : 'dropdown-menu-item focus-outline'"
			@click.prevent="submit"
		>
			{{ sanitise(labels.button) }}
		</button>
	</form>
</template>

<script>
import { sanitise } from '@/domain/utils/DataSanitiser';

const toastr = require('toastr');

/**
 * The ListActionForm component.
 */
export default {
	props: {
		ids: {
			type: Array,
			default: () => [],
		},
		labels: {
			type: Object,
			default: () => ({}),
		},
		route: {
			type: String,
			required: true,
		},
		method: {
			type: String,
			default: 'POST',
		},
		showSubmit: {
			type: <PERSON>olean,
			default: true,
		},
		buttonClass: {
			type: String,
			required: false,
			default: '',
		},
		formClass: {
			type: String,
			required: false,
			default: '',
		},
	},
	methods: {
		sanitise,
		submit() {
			if (this.ids.length === 0) {
				toastr.info(App.selectedItemsMessage.clarify());
				return;
			}

			this.$emit('submitted');
		},
	},
};
</script>
