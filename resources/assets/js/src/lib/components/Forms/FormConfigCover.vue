<template>
  <div>
    <div class="panel panel-default">
      <div class="panel-body">
        <div class="panel-title">
          <h4>{{ lang.get('form.form.cover_image.label') }}</h4>
        </div>
        <uploader
          :files="form.coverImage ? [form.coverImage] : []"
          :options="form.uploaderOptions"
          :button-label="lang.get('files.buttons.single')"
          @deleted="deleteCoverImage"
          @status="onStatus"
          @imageDataUrl="onPreviewUpdate"
          @uploaded="(...args) => onUploaded([...args])"
        />
      </div>
    </div>

    <div class="panel panel-default">
      <div class="panel-body">
        <div class="panel-title">
          <h4>
            <label id="callToActionLabel">{{ lang.get('form.form.call_to_action.label') }}</label>
          </h4>
        </div>
        <div class="form-group">
          <multilingual
            labelled-by="callToActionLabel"
            :resource="form"
            property="callToAction"
            @input="onTranslated"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import Uploader from '@/lib/components/Uploader/Uploader';
import Multilingual from '@/lib/components/Translations/Multilingual';

const toastr = require('toastr');

export default {
  inject: ['lang'],
  components: {
    Uploader,
    Multilingual
  },
  props: {
    form: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      translated:
        typeof this.form.translated[Symbol.iterator] === 'function'
          ? Object.fromEntries(this.form.translated)
          : this.form.translated,
      uploadStatus: null,
      preview: this.form.coverImageUrl
    };
  },
  created() {
    this.onInput()
  },
  computed: {
    coverStyles() {
      return this.form.coverImage ? 'form-group' : 'form-group field-content';
    }
  },
  methods: {
    onUploaded(data) {
      this.$emit('uploaded', data[1]);
    },
    onPreviewUpdate(url) {
      this.preview = url;
      this.onInput();
    },
    deleteCoverImage(id, fileId) {
      if (fileId) {
        this.$http
          .delete(`/entry-form/manager/form/${this.form.slug}/image/${fileId}`)
          .then(
            () => { this.$emit('clear'); this.preview = null; this.onInput(); },
            () => toastr.error(this.lang.get('miscellaneous.alerts.generic'))
          );
      }
    },
    onStatus(s) {
      this.uploadStatus = s;
      this.onInput();
    },
    onTranslated(translated, lang, property, translation) {
      if (!this.translated[lang]) this.translated[lang] = {};
      this.translated[lang][property] = translation;
      this.$emit('translated', property, translated);
      this.onInput();
    },
    onInput() {
      this.$emit('input', JSON.parse(JSON.stringify(this.$data)));
    }
  }
};
</script>
