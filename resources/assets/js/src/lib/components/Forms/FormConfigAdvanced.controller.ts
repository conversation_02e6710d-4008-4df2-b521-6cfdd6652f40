import type { ContentBlock } from '@/domain/models/Tab';
import { trans } from "@/domain/dao/Translations";
import { computed, ComputedRef, ref, WritableComputedRef } from "vue";
import type { FormSettings, FormType } from "@/domain/models/Form";
import { FormSettingsEmitters, FormSettingsEvents } from "@/lib/components/Forms/FormConfigAdvanced.events";

type Props = {
	settings: FormSettings;
	formType: FormType;
	contentBlocks: ContentBlock[];
	selectedContentBlock: string;
};

type Ctx = {
	emit: FormSettingsEmitters;
};

type View = {
	modelSettings: WritableComputedRef<FormSettings>;
	onContentBlockUpdated: (slug: string) => void;
	homeSettings: ComputedRef<Partial<FormSettings>>;
	manageSettings: ComputedRef<Partial<FormSettings>>;
	applicationSettings: ComputedRef<Partial<FormSettings>>;
	roleSetting: ComputedRef<Partial<FormSettings>>;
	updateContentBlockId: (slug: string) => void;
	selectedContentBlock: ReturnType<typeof ref>;
	lang: ReturnType<typeof trans>;
};

const formConfigAdvancedController = (props: Props, { emit }: Ctx): View => {
	const modelSettings = computed({
		get: () => props.settings,
		set: (newValue) => emit(FormSettingsEvents.SettingsUpdated, newValue),
	});

	const homeKeys = ['promoted'];
	const manageKeys = ['invitationOnly', 'moderationForChapterManagers', 'minimumSimilarityPercentage'];
	const roleKeys = ['roleVisibility'];
	const excluded = [...homeKeys, ...manageKeys, ...roleKeys];
	const applicationKeys = Object.keys(props.settings).filter((key) => !excluded.includes(key));

	const extractSettings = (keys: string[], source: FormSettings): Partial<FormSettings> => {
		const result: Partial<FormSettings> = {};
		keys.filter((key) => key in props.settings).forEach((key) =>
			key in source ? (result[key as keyof FormSettings] = source[key as keyof FormSettings]) : null
		);
		return result;
	};

	const homeSettings = computed(() => extractSettings(homeKeys, props.settings));

	const manageSettings = computed(() => extractSettings(manageKeys, props.settings));

	const roleSetting = computed(() => extractSettings(roleKeys, props.settings));

	const applicationSettings = computed(() => extractSettings(applicationKeys, props.settings));

	const onContentBlockUpdated = (slug: string) => emit(FormSettingsEvents.ContentBlockSelected, slug);

	const localSelectedContentBlock = ref(props.selectedContentBlock);
	const updateContentBlockId = (slug: string) => {
		localSelectedContentBlock.value = slug;
	};

	return {
		modelSettings,
		onContentBlockUpdated,
		homeSettings,
		manageSettings,
		applicationSettings,
		roleSetting,
		updateContentBlockId,
		selectedContentBlock: localSelectedContentBlock,
		lang: trans(),
	};
};

export { Props, View, formConfigAdvancedController };
