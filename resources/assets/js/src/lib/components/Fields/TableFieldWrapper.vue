<template>
	<div class="table-field-input-container">
		<table-field
			id
			name
			:field-id="fieldService.lockableId"
			:is-collaborative-or-updatable="fieldService.isFormCollaborativeOrUpdatable"
			:configuration="field.configuration ? JSON.parse(field.configuration) : {}"
			:configuration-translated="field.configurationTranslated ? JSON.parse(field.configurationTranslated) : {}"
			:input="value ? JSON.parse(value) : {}"
			:language="field.language"
			:default-language="field.defaultLanguage"
			:labels="field.labels ? JSON.parse(field.labels) : {}"
			:disabled="disabledOrCollaborationLocked"
			@changed="onInput($event)"
			@rebuilt="onInput($event, true)"
			@toggle="onToggle"
		></table-field>
	</div>
</template>

<script>
import Field from './Field.vue';
import TableField from './TableField.vue';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';

export default {
	components: {
		TableField,
	},
	extends: Field,
	data() {
		return {
			locked: false,
		};
	},
	methods: {
		onToggle(lock) {
			if (lock) {
				collaborationUIBus.on(CollaborationUISignals.SAVE_BUTTON_CLICKED, () => {
					this.onBlur();
					collaborationUIBus.off(CollaborationUISignals.SAVE_BUTTON_CLICKED);
				});
			}

			this.fieldService.locks.set(lock);
		},
	},
	created() {
		this.fieldService.locks.subscribe((lock) => (this.locked = lock));
	},
};
</script>
