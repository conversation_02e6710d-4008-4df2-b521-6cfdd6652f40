<template>
	<text-editor
		:id="getId()"
		:value="value"
		:markdown-guide-label="lang.get('miscellaneous.markdown_guide.label')"
		:disabled="disabledOrCollaborationLocked"
		:in-active-tab="inActiveTab"
		:toolbar="toolbarItems"
		:allows-inline-images="true"
		@input="onInput"
		@blur="onBlur"
		@focus="onFocus"
	/>
</template>

<script>
import Field from '@/lib/components/Fields/Field.vue';
import TextEditor from '@/lib/components/Shared/editor/TextEditor.vue';
import { useTimer } from '@/domain/utils/Timer';
import { defaultToolbarItems } from '@/lib/components/Shared/editor/ckeditor/toolbar/Toolbar';

export default {
	inject: ['lang'],
	components: {
		TextEditor,
	},
	extends: Field,
	data() {
		return {
			blurTimer: useTimer(() => {
				this.validate();
				if (!this.disabledOrCollaborationLocked) {
					this.fieldService.locks.set(false);
				}

				this.focused = false;
			}, 300),
			focused: false,
		};
	},
	props: {
		enableMarkdown: {
			type: Boolean,
			default: false,
		},
		autosaveInterval: {
			type: Number,
			default: 5000, // value will be autosaved every 5 seconds
		},
	},
	computed: {
		toolbarItems() {
			return this.enableMarkdown ? defaultToolbarItems : [];
		},
	},
	methods: {
		onBlur() {
			this.blurTimer.start();
		},
		onFocus() {
			this.blurTimer.stop();

			// Block to prevent locking it twice
			if (this.focused) return;

			if (!this.disabledOrCollaborationLocked) {
				this.fieldService.locks.set(true);
			}

			this.focused = true;
		},
	},
};
</script>
