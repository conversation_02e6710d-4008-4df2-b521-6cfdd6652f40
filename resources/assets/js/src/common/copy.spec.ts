import tectoastr from 'tectoastr';
import { useCopy } from '@/common/copy';
import { beforeEach, describe, expect, it, vi } from 'vitest';

vi.mock('tectoastr', () => ({
	default: {
		info: vi.fn(),
	},
}));

describe('useCopy', () => {
	beforeEach(() => {
		global.document = {
			createElement: vi.fn(() => ({
				value: '',
				select: vi.fn(),
			})),
			body: {
				appendChild: vi.fn(),
				removeChild: vi.fn(),
			},
			execCommand: vi.fn(),
		} as unknown as Document;
	});

	it('copies the provided value to clipboard and shows a toast message', () => {
		const message = 'Copied to clipboard!';
		const { copy } = useCopy(message);

		copy('test value');

		expect(global.document.createElement).toHaveBeenCalledWith('input');
		expect(global.document.execCommand).toHaveBeenCalledWith('copy');
		expect(tectoastr.info).toHaveBeenCalledWith(message);
	});

	it('does not show a toast if no message is provided', () => {
		document.execCommand = vi.fn();
		vi.spyOn(tectoastr, 'info');

		const { copy } = useCopy(undefined);
		copy('test value');

		expect(document.execCommand).toHaveBeenCalledWith('copy');
		expect(tectoastr.info).not.toHaveBeenCalled();
	});
});
