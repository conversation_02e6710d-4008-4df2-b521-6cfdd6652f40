import { apiRequest } from '@/modules/billing/services/Api';
import { deleteSubscriptionAccount } from '@/modules/billing/components/subscriptions/SubscriptionsDeleteAccount.api';
import toastr from 'toastr';
import { Ref, ref, SetupFunction } from 'vue';
import { trans, Trans } from '@/domain/dao/Translations';

type Props = {
	subscriptionId: string;
};

type View = {
	lang: Trans;
	showConfirmDelete: Ref<boolean>;
	deleteAccount: () => void;
	showDeleteConfirmationDialog: () => void;
	hideDeleteConfirmationDialog: () => void;
};

const subscriptionsDeleteAccountController: SetupFunction<Props, View> = (props): View => {
	const showConfirmDelete = ref(false);
	const lang = trans();

	const deleteAccount = async () => {
		await apiRequest(
			deleteSubscriptionAccount({ subscriptionId: props.subscriptionId })
				.then(() => (window.location.href = '/billing?message=account-deleted'))
				.catch(() => toastr.error(lang.get('billing.subscriptions.messages.upgrade_plan_error')))
		);
	};

	return {
		lang: trans(),
		showConfirmDelete,
		deleteAccount,
		showDeleteConfirmationDialog: () => (showConfirmDelete.value = true),
		hideDeleteConfirmationDialog: () => (showConfirmDelete.value = false),
	};
};

export { Props, View, subscriptionsDeleteAccountController };
