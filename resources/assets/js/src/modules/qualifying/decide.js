var mountVueComponent = require('@/lib/mount-vue-component').mountVueComponent;

var $ = require('jquery');
/* eslint-disable @typescript-eslint/naming-convention */
var Audio = require('../../lib/audio.js');
var Cards = require('../../lib/cards.js');
var tectoastr = require('tectoastr');
var VideoThumbnails = require('../../lib/video-thumbnails.js');
var isMobile = require('@/lib/navigation-is-mobile');
const { registerAutosaveCommentListeners } = require('@/lib/autosaver-comment');
/* eslint-enable @typescript-eslint/naming-convention */

module.exports = function () {
	if (!isMobile()) {
		$('.header-controls').stickybits({
			useStickyClasses: true,
		});
	}

	/**
	 * Cards
	 */
	var cards = new Cards();
	cards.setup('ul.cards:not(.vertical-gallery)', 'li', 'fitRows');

	/**
	 * Audio
	 */
	var audio = new Audio();
	audio.setup('jp-jplayer');

	/**
	 * Video
	 */
	var videoThumbnails = new VideoThumbnails();
	videoThumbnails.setup('preview-container.video');

	/**
	 * Decide (pass/unsure/fail) buttons
	 */
	var buttons = $('.decide-button, .undecide-button');
	var undecideButton = $('.undecide-button');

	$(buttons).on('click', function () {
		var button = $(this);
		if (!button.data('url')) {
			return;
		}

		var decision = button.data('decision');

		// Check if already deciding
		if (button.hasClass('deciding')) {
			return;
		}

		button.addClass('deciding');

		// Send new decision to server
		$.ajax({
			type: 'POST',
			url: button.data('url'),
			data: { decision: decision },
			dataType: 'json',
		})
			.done(function () {
				// Update button
				buttons.removeClass('deciding pass fail unsure');
				button.addClass(decision);

				// Show/hide Undecide Button
				undecideButton.toggleClass('hidden', !decision);
			})
			.fail(function (response) {
				// Throw error
				tectoastr.response(response.responseJSON, response.responseJSON.message || button.data('failed'));

				// Toggle button back
				button.removeClass('deciding');
			});
	});

	/**
	 * Autosave comments
	 */
	registerAutosaveCommentListeners([...buttons, ...undecideButton]);

	import('@/lib/components/Video/VideoPlayer').then((videoPlayer) => {
		mountVueComponent('video-player', videoPlayer);
	});

	import('@/lib/components/Video/VideoModal.vue').then((videoModal) => {
		mountVueComponent('video-modal', videoModal);
	});

	import('@/lib/components/Comments/Comments.vue').then((comments) => {
		mountVueComponent('comments', comments);
	});

	import('@/lib/components/QuickManager/QuickManager.vue').then((quickManager) => {
		mountVueComponent('quick-manager', quickManager);
	});

	import('@/lib/components/Shared/PdfViewer/PdfViewer.vue').then((pdfViewer) => {
		mountVueComponent('pdf-viewer', pdfViewer);
	});
};
