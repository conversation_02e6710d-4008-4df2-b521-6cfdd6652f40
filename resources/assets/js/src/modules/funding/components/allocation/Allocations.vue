<template>
	<div id="funding-panel" ref="fundingPanel" class="panel panel-default">
		<div class="panel-body">
			<div class="panel-title">
				<h4>{{ lang.get('funding.titles.allocation.entry') }}</h4>
			</div>
			<table v-if="allocations.length > 0" class="table funding-hide-empty">
				<thead>
					<tr>
						<th colspan="2">{{ lang.get('funding.table.columns.fund') }}</th>
						<th class="text-right">{{ lang.get('allocation-payments.table.columns.allocated') }}</th>
						<th class="text-right">{{ lang.get('allocation-payments.table.columns.paid') }}</th>
						<th class="overflow-cell"></th>
					</tr>
				</thead>
				<tbody>
					<allocation
						v-for="(allocation, index) in allocations"
						:key="index"
						v-model="allocations[index]"
						:index="index"
						:allocation-permissions="allocationPermissions"
						:funds="funds"
						:locale="locale"
						:document-templates="documentTemplates"
						:document-template-translations="documentTemplateTranslations"
						:notifications="notifications"
						:file-types="fileTypes"
						:create-document-route="createDocumentRoute"
						:create-document-labels="createDocumentLabels"
						:translations="translations"
						@close-open-forms="closeOpenForms"
						@delete="displayDeleteModalPrompt(index)"
						@reveal="$emit('reveal', $event)"
					></allocation>
				</tbody>
				<tfoot>
					<tr class="totals-row">
						<td v-if="allocationPermissions.canCreate">
							<button
								v-if="!modeState.isEditMode()"
								class="btn btn-sm btn-secondary funding-add"
								@click.prevent="addAllocation({}, true)"
							>
								{{ lang.get('funding.actions.add-allocation') }}
							</button>
							<button
								v-if="modeState.isEditMode()"
								class="btn btn-sm btn-primary funding-save"
								:disabled="modeState.isSavingMode()"
								@click.stop.prevent="saveAllocation"
							>
								{{ lang.get('buttons.save') }}
							</button>
							<button
								v-if="modeState.isEditMode()"
								:disabled="modeState.isSavingMode()"
								class="btn btn-sm btn-primary funding-cancel"
								@click.prevent="cancelAllocation({}, true)"
							>
								{{ lang.get('buttons.cancel') }}
							</button>
						</td>
						<td v-if="!allocationPermissions.canCreate && !modeState.isEditMode()"></td>
						<td class="text-right">
							<span class="funding-hide-empty">{{ lang.get('funding.table.total') }}</span>
						</td>
						<td id="funding-totals" v-output="funding.totals" class="text-right amount"></td>
						<td id="funding-paid" v-output="funding.paid" class="text-right"></td>
						<td class="overflow-cell"></td>
					</tr>
				</tfoot>
			</table>
			<div v-if="allocationPermissions.canCreate && allocations.length === 0" class="funding-show-empty">
				<button class="btn btn-sm btn-secondary funding-add" @click.prevent="addAllocation({}, true)">
					{{ lang.get('funding.actions.add-allocation') }}
				</button>
			</div>
		</div>

		<confirmation-simple-modal
			v-if="displayDeleteModal"
			:default-language="defaultLanguage"
			:language="language"
			:translations="translations"
			@confirmed="deleteAllocation"
			@closed="displayDeleteModal = false"
		/>
	</div>
</template>

<script>
import langMixin from '@/lib/components/Translations/mixins/lang-mixin.js';
import modeState from './states/mode.state';
import Allocation from './Allocation.vue';
import { makeAllocation } from './AllocationType';
import { sortBy } from 'lodash';
import { mapState } from 'vuex';
import ConfirmationSimpleModal from '@/lib/components/Shared/ConfirmationSimpleModal.vue';

const tectoastr = require('tectoastr');
const _ = require('underscore');

export default {
	name: 'Allocations',

	components: {
		Allocation,
		ConfirmationSimpleModal,
	},

	mixins: [langMixin],

	props: {
		originalFunding: {
			type: Object,
			default: () => {},
		},
		allocationPermissions: {
			type: Object,
			default: () => {},
		},
		existingFunds: {
			type: Object,
			default: () => {},
		},
		locale: {
			type: String,
			required: true,
		},
		translations: {
			type: Object,
			default: () => {},
		},
		documentTemplates: {
			type: Array,
			required: true,
		},
		documentTemplateTranslations: {
			type: [Object, Array],
			required: true,
		},
		notifications: {
			type: Array,
			required: true,
		},
		fileTypes: {
			type: Object,
			required: true,
		},
		createDocumentRoute: {
			type: String,
			required: true,
		},
		createDocumentLabels: {
			type: Object,
			default: () => ({}),
		},
	},

	data() {
		return {
			modeState,
			allocations: [],
			funds: this.getFunds(this.existingFunds),
			displayDeleteModal: false,
			allocationToBeDeletedIndex: null,
			funding: {
				allocations: [],
				totals: '',
				paid: '',
				routes: [],
				messages: [],
			},
		};
	},

	computed: {
		...mapState('allocationPayments', ['modalIsOpen', 'hasChanges']),

		currentEditAllocation() {
			return this.allocations?.[this.modeState.currentAllocationIndex];
		},
	},

	watch: {
		modalIsOpen(newValue, oldValue) {
			if (newValue === false && oldValue === true) {
				this.closeModal();
			}
		},
	},

	mounted() {
		this.initialiseFunding();

		window.addEventListener('keydown', this.keysListener);
		sortBy(this.funding.allocations, ['id']).forEach((allocation) => {
			this.addAllocation(allocation);
		});
	},

	methods: {
		keysListener(event) {
			if (event.code === 'Escape') {
				if (this.modeState.isEditMode()) {
					this.cancelAllocation();
				}
			}

			if (event.code === 'Enter') {
				if (this.modeState.isEditMode()) {
					this.saveAllocation();
				}
			}
		},

		initialiseFunding() {
			const { allocations, totals, paid, routes, messages } = this.originalFunding;
			this.funding = {
				allocations,
				totals,
				paid,
				routes,
				messages,
			};
		},

		displayDeleteModalPrompt(index) {
			this.cancelAllocation();
			this.displayDeleteModal = true;
			this.allocationToBeDeletedIndex = index;
		},

		getFunds(funds) {
			let fundsList = [];
			Object.keys(funds).forEach((key) => {
				fundsList.push({
					id: parseInt(key.replace('id-', '')),
					value: funds[key].label,
					currency: funds[key].currency,
					name: funds[key].name,
					available: funds[key].available,
				});
			});

			return fundsList.sort((a, b) => a.value.localeCompare(b.value));
		},

		registerEscapeListener() {
			this.$refs.fundingPanel.on('keypress', '.allocation-amount input', (event) => {
				if (event.which === 13 && this.modeState.isEditMode()) {
					this.saveAllocation();
				}
			});
		},

		addAllocation(allocation = {}, openAfterAppend = false) {
			if (!allocation?.fundId && this.funds.length > 0) {
				allocation.fundId = this.funds[0].id;
			}

			const index = this.allocations.push(makeAllocation(allocation));
			if (openAfterAppend) {
				this.modeState.send(this.modeState.Transitions.EDIT, index - 1);
			}
		},

		cancelAllocation() {
			if (this.currentEditAllocation?.canBeCancelledAndDelete()) {
				this.allocations.splice(this.modeState.currentAllocationIndex, 1);
			}

			this.modeState.send(this.modeState.Transitions.EMPTY);
		},

		getSavingParameters() {
			return {
				allocation: this.currentEditAllocation.id,
				fund: this.currentEditAllocation.fundId,
				amount: this.currentEditAllocation.amount,
			};
		},

		getSavingPromise() {
			if (this.currentEditAllocation.hasDBRecord()) {
				return this.$http.put(this.funding.routes.update, this.getSavingParameters());
			}

			return this.$http.post(this.funding.routes.add, this.getSavingParameters());
		},

		saveAllocation() {
			let currentAllocationIndex = this.modeState.currentAllocationIndex;
			this.modeState.send(this.modeState.Transitions.SAVE, currentAllocationIndex);
			return this.getSavingPromise()
				.then((response) => {
					this.modeState.send(this.modeState.Transitions.EMPTY);
					this.handleSuccessResponse(response);
				})
				.catch((error) => {
					this.handleErrorResponse(error);
					this.modeState.send(this.modeState.Transitions.EDIT, currentAllocationIndex);
				});
		},

		handleSuccessResponse(response) {
			this.funding.totals = response.data.totals;
			this.funding.paid = response.data.paid;
			this.funds = this.getFunds(response.data.funds);
			this.allocations = sortBy(response.data.allocations, ['id']).map((allocation) => makeAllocation(allocation));
		},

		handleErrorResponse(error) {
			if (error?.response?.status === 422) {
				tectoastr.error(_.flatten(_.values(JSON.parse(error?.response?.request?.responseText))).join(' '));
			} else {
				tectoastr.error(this.funding.messages.error);
			}
		},

		deleteAllocation() {
			let index = this.allocationToBeDeletedIndex;
			if (index < 0) {
				return;
			}

			this.modeState.send(this.modeState.Transitions.DELETE, index);
			const allocation = this.allocations[index];

			this.$http
				.delete(this.funding.routes.delete, {
					data: {
						selected: [allocation.id],
					},
				})
				.then((response) => this.handleSuccessResponse(response))
				.catch((error) => this.handleErrorResponse(error))
				.then(() => {
					this.displayDeleteModal = false;
					this.modeState.send(this.modeState.Transitions.EMPTY);
				});
		},

		closeModal() {
			if (this.hasChanges) {
				this.modeState.send(this.modeState.Transitions.EDIT, this.modeState.currentAllocationIndex);
				this.saveAllocation().finally(() => {
					this.modeState.send(this.modeState.Transitions.EMPTY);
					this.closeOpenForms();
				});
			} else {
				this.modeState.send(this.modeState.Transitions.EMPTY);
				this.closeOpenForms();
			}
		},

		closeOpenForms() {
			this.allocations = this.allocations.filter((allocation) => allocation.id !== null);
		},
	},
};
</script>

<style scoped>
table {
	margin-bottom: 0;
}

table .totals-row > td {
	padding-bottom: 0;
}

table .totals-row > td:first-child {
	padding: 15px 15px 0px 0px;
}
</style>
