var $ = require('jquery');

var Filters = require('../../common/UIClasses/Filters');
var SelectAll = require('../../common/UIClasses/SelectAll');

/**
 * Visibility matrix
 */
module.exports = function() {

    ( new Filters )
        .addFilter($('#categoryFilter'), 'categories')
        .addFilter($('#resourceFilter'), 'resource')
        .addFilter($('#typeFilter'), 'type')
        .addResetTrigger($('#reset'))
        .addOnChangeCallback(
            function( isAnyFilterFailedTest ) {
                let count = $('.fieldRow')
                    .removeClass('hidden')
                    .map( (key,row) => $(row) )
                    .filter( (key,row) => isAnyFilterFailedTest(row) )
                    .each( (key,row) => row.addClass('hidden') )
                    .length;

                let filterInfoMessage = count => $('#filterInfo').data( count > 1 ? 'hidden-fields' : 'hidden-field' );
                let filterInfo = count => count ? '<i>'+count+' '+filterInfoMessage(count)+'</i>' : '';

                $('#filterInfo').html(filterInfo(count));

                selectAll.refresh();
            }
        );


    let selectAll = ( new SelectAll )
        .addTrigger($('#selectAll'))
        .addTargets($('.fieldCheckbox'));
};
