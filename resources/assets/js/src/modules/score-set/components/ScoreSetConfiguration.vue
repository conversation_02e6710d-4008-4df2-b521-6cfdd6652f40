<script>
import { disableForm, Multiselect } from 'vue-bootstrap';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin.js';
import tabularMixin from '@/lib/components/Navigation/mixins/tabular-mixin.js';
import linksMixin from '@/lib/components/Shared/mixins/links-mixin.js';
import featuresMixin from '@/lib/components/Shared/mixins/features-mixin.js';
import AttachmentTypes from '@/lib/components/Attachments/AttachmentTypes';
import VisibilityMatrix from '@/lib/components/Shared/VisibilityMatrix';
import Layout from '@/modules/score-set/components/Layout.vue';
import LocalisedDatepicker from '@/lib/components/Shared/LocalisedDatepicker';
import JudgingModes from '@/lib/components/Judging/JudgingModes';
import Uploader from '@/lib/components/Uploader/Uploader';
import HelpIcon from '@/lib/components/Shared/HelpIcon.vue';

const toastr = require('toastr');

export default {
	components: {
		Multiselect,
		AttachmentTypes,
		Layout,
		JudgingModes,
		VisibilityMatrix,
		LocalisedDatepicker,
		HelpIcon,
		Uploader,
	},
	mixins: [langMixin, tabularMixin, disableForm, linksMixin, featuresMixin],
	props: {
		scoreSet: {
			type: Object,
			required: true,
		},
		deleteScoreSetCoverImageUrl: {
			type: String,
			required: true,
		},
		coverImage: {
			type: Object,
			default: () => null,
			required: false,
		},
		canSave: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			conditionalCheckbox: {
				comments: ['commentRequired'],
				abstention: ['commentOnAbstention'],
				commentScores: ['commentScoresRequired'],
				duplicateEntryCount: ['duplicateEntryList'],
				'settings[referee][displayReferees]': ['hideRefereeName', 'hideRefereeEmail'],
			},
			mode: this.scoreSet.mode,
			comments: this.scoreSet.comments,
			commentRequired: this.scoreSet.commentRequired,
			abstention: this.scoreSet.abstention,
			commentOnAbstention: this.scoreSet.commentOnAbstention,
			commentScores: this.scoreSet.commentScores,
			commentScoresRequired: this.scoreSet.commentScoresRequired,
			duplicateEntryCount: this.scoreSet.duplicateEntryCount,
			duplicateEntryList: this.scoreSet.duplicateEntryList,
			uploadingImage: false,
			displayReferees: this.scoreSet.displayReferees,
			hideRefereeName: this.scoreSet.hideRefereeName,
			hideRefereeEmail: this.scoreSet.hideRefereeEmail,
			fileToken: this.coverImage ? this.coverImage.token : null,
			files: this.coverImage ? [this.coverImage] : [],
		};
	},
	computed: {
		savingDisabled() {
			return !this.canSave || this.uploadingImage;
		},
	},
	methods: {
		selectMode(mode) {
			this.mode = mode;
		},
		conditionalChange(event) {
			const conditionalCheckbox = this.conditionalCheckbox[event.target.name];
			if (conditionalCheckbox && !event.target.checked) {
				conditionalCheckbox.forEach((checkbox) => (this[checkbox] = false));
			}
		},
		uploaded(token, file) {
			if (token && file) {
				this.fileToken = token;
				this.files = [file];
			}
		},
		completed() {
			this.uploadingImage = false;
		},
		uploading(id, status) {
			if (!this.uploadingImage && status === true) {
				this.uploadingImage = true;
			}
		},
		cancelUpload() {
			this.uploadingImage = false;
		},
		deleteFile(id, remoteId) {
			this.files = [];
			if (remoteId) {
				this.$http.delete(this.deleteScoreSetCoverImageUrl + '/' + remoteId).then(
					() => {},
					() => {
						toastr.error(this.lang.get('miscellaneous.alerts.generic'));
					}
				);
			}
		},
	},
};
</script>
