<template>
	<div>
		<table v-if="reports.length" class="table">
			<thead>
				<tr>
					<th>{{ labels.grant_report }}</th>
					<th>{{ labels.due_date }}</th>
					<th>{{ labels.status }}</th>
					<th class="overflow-cell"></th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="report in reports" :key="`report-${report.slug}`">
					<td>
						<span v-if="report.deletedForm" class="deleted">{{ report.name }}</span>
						<a
							v-else
							:href="route(routes['grant-report.manager.preview'], { grantReport: report.slug })"
							target="_blank"
							rel="noopener noreferrer"
						>
							{{ report.name }}
						</a>
					</td>
					<td>
						<div class="form-group">
							<date-timezone
								v-if="!isEditing(report.id)"
								:id="report.slug + '-dueDate'"
								:name="report.slug + '-dueDate'"
								:date="report.dueDate"
								:timezone="report.dueDateTimezone || 'UTC'"
							/>
							<localised-datepicker
								v-else
								:id="'report-' + report.id + '-dueDate'"
								:name="'report-' + report.id + '-dueDate'"
								mode="datetime"
								:value="{ datetime: report.dueDate }"
								:include-timezone="false"
								:highlight-today="true"
								:disabled="false"
								@update:value="(value) => onUpdateDueDate(value)"
							/>
						</div>
					</td>
					<td v-output="status(report)"></td>
					<td>
						<grant-report-overflow-menu
							:report="report"
							:labels="labels"
							:routes="routes"
							@deleted="confirmAndDelete(report)"
							@updateDueDate="updateDueDate(report)"
						/>
					</td>
				</tr>
			</tbody>
		</table>
		<div v-if="editingDueDate">
			<button class="btn btn-primary btn-sm" :disabled="saving" @click.prevent="saveUpdateDueDate">
				{{ labels['save'] }}
			</button>
			<button class="btn btn-primary btn-sm" @click.prevent="cancelEditingDueDate">
				{{ labels['cancel'] }}
			</button>
		</div>
		<button
			v-if="!editingDueDate && canAddGrantReports"
			class="btn btn-secondary btn-sm"
			:disabled="reportForms.length === 0"
			@click.prevent="revealModal"
		>
			{{ labels['add'] }}
		</button>
		<div>
			<portal-target v-if="showModal" name="schedule-grant-report" multiple></portal-target>
			<schedule-grant-report
				ref="scheduleModal"
				:ids="[entry]"
				:labels="labels"
				:report-forms="reportForms"
				:show-label="false"
				name="schedule-grant-report"
				method="POST"
				:route="route(routes['grant-report.create'])"
				@submitted="(event) => save(event)"
			></schedule-grant-report>
		</div>
		<confirmation-modal
			modal-id="modal-target-confirm-grant-report-deletion"
			:show-modal="confirmDeletion"
			:confirmation="labels['delete_confirmation']"
			:confirm-button-label="labels['continue']"
			:cancel-button-label="labels['cancel']"
			@closed="confirmDeletion = false"
			@confirmed="remove"
		/>
	</div>
</template>

<script>
import toastr from 'toastr';
import { route } from '@/lib/utils.js';
import { SelectField } from 'vue-bootstrap';
import ConfirmationModal from '@/lib/components/Shared/ConfirmationModal';
import LocalisedDatepicker from '@/lib/components/Shared/LocalisedDatepicker';
import DateTimezone from '@/lib/components/Shared/DateTimezone';
import GrantReportOverflowMenu from './GrantReportOverflowMenu';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin.js';
import ScheduleGrantReport from '@/lib/components/ListActions/ScheduleGrantReport';

export default {
	components: {
		ScheduleGrantReport,
		LocalisedDatepicker,
		SelectField,
		ConfirmationModal,
		DateTimezone,
		GrantReportOverflowMenu,
	},
	mixins: [langMixin],
	props: {
		entry: {
			type: String,
			required: true,
		},
		reportForms: {
			type: Array,
			required: true,
		},
		grantReports: {
			type: Array,
			required: true,
		},
		labels: {
			type: Object,
			required: true,
		},
		routes: {
			type: Object,
			required: true,
		},
		dueDate: {
			type: Object,
			default: null,
		},
		canAddGrantReports: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			saving: false,
			reportForm: null,
			selectedReportForm: null,
			confirmDeletion: false,
			editingDueDate: false,
			showModal: false,
			newReport: [],
			reports: [],
		};
	},
	mounted() {
		this.reports = this.formatReports(this.grantReports);
	},
	methods: {
		route,
		status(report) {
			return this.labels[report.status] ?? '';
		},
		formatDueDate(dueDate) {
			return { datetime: dueDate, timezone: 'UTC' };
		},
		onSelected(name, selected) {
			this.reportForm = selected;
		},
		isEditing(id) {
			return this.reports.filter((r) => r.id === id).shift()?.editing;
		},
		revealModal() {
			this.showModal = true;
			this.$refs.scheduleModal.toggle();
		},
		cancelEditingDueDate() {
			this.editingDueDate = false;
			this.reports.map((r) => (r.editing = false));
		},
		save(event) {
			this.saving = true;

			this.$http
				.post('/grant-report/create', {
					selected: [this.entry],
					reportFormId: event.reportFormId,
					dueDate: event.dueDate.datetime,
				})
				.then(
					(response) => {
						this.saving = false;
						this.reports = this.formatReports(response.data.reports || []);
					},
					(error) => {
						toastr.error(error.response.data.message || this.labels['saveError']);
					}
				);
		},
		confirmAndDelete(reportForm) {
			this.selectedReportForm = reportForm;
			this.confirmDeletion = true;
		},
		formatReports(reports) {
			return JSON.parse(JSON.stringify(reports.map((r) => Object.assign(r, { editing: false }))));
		},
		getId() {
			return this.entry;
		},
		remove() {
			const slug = this.selectedReportForm.slug;

			this.$http
				.delete(
					route(this.routes['grant-report.delete'], {
						grantReport: slug,
					})
				)
				.then(
					() => {
						this.reports = this.reports.filter((c) => c.slug !== slug);
					},
					(error) => {
						toastr.error(error.response.data.message || this.labels['deleteError']);
					}
				);
		},
		updateDueDate(report) {
			this.reports.map((r) => (r.editing = false));
			this.reports.filter((r) => r.id === report.id).shift().editing = this.editingDueDate = true;
			this.selectedReportForm = report;
		},
		saveUpdateDueDate() {
			let dueDate = this.newReport.updatedDueDate ?? this.selectedReportForm.dueDate;
			this.$http
				.put(
					route(this.routes['grant-report.update.due_date'], {
						grantReport: this.selectedReportForm.slug,
					}),
					{ dueDate: dueDate }
				)
				.then(
					(response) => {
						const index = this.reports.findIndex((r) => r.id === this.selectedReportForm.id);
						this.reports[index].editing = this.editingDueDate = false;
						this.reports[index].dueDate = dueDate;
						this.reports[index].status = response.data.grantReport.status;
					},
					(error) => {
						toastr.error(error.response.data.message || this.labels['deleteError']);
					}
				);
		},
		onUpdated(value) {
			if (value.datetime) {
				this.newReport.dueDate = value.datetime;
			} else {
				this.newReport.dueDateTimezone = value.timezone;
			}
		},
		onUpdateDueDate(value) {
			if (value.datetime) {
				this.newReport.updatedDueDate = value.datetime;
			}
		},
	},
};
</script>

<style scoped>
.table tr:first-child > td {
	border-top: none;
}
</style>
