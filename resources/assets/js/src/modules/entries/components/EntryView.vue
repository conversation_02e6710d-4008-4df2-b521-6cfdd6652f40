<script>
import CreateDocument from '@/lib/components/ListActions/CreateDocument';
import EditDocument from '@/modules/documents/components/EditDocument';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin';
import TabbedContent from '@/lib/components/TabbedContent.vue';
import TabContent from '@/modules/theme/layout/TabContents.vue';
import QuickManager from '@/lib/components/QuickManager/QuickManager.vue';
import QuickGrantManager from '@/lib/components/QuickManager/QuickGrantManager.vue';
import DescriptionListItem from '@/lib/components/Shared/DescriptionListItem.vue';
import ListAction from '@/lib/components/ListActions/ListAction.vue';
import Resubmission from '@/lib/components/ListActions/Resubmission.vue';
import EditUser from '@/lib/components/Shared/EditUser.vue';
import DeadlineEditor from '@/modules/entries/components/DeadlineEditor.vue';
import Comments from '@/lib/components/Comments/Comments.vue';
import Contracts from '@/modules/entries/components/Contracts.vue';
import GrantReports from '@/modules/entries/components/GrantReports.vue';
import Deletism from '@/lib/components/ListActions/Deletism.vue';
import Undeletism from '@/lib/components/ListActions/Undeletism.vue';
import AllocationPayments from '@/modules/allocation-payment/components/AllocationPayments.vue';
import Allocations from '@/modules/funding/components/allocation/Allocations.vue';
import { Popover } from 'vue-bootstrap';
import EntryReviewFlow from '@/modules/entries/components/EntryReviewFlow.vue';
import FormInviter from '@/lib/components/ListActions/FormInviter.vue';
import VideoThumbnails from '@/lib/video-thumbnails.js';
import Audio from '@/lib/audio';
import FileMetadata from '@/modules/entries/components/FileMetadata.vue';
import $ from 'jquery';
import VideoModal from '@/lib/components/Video/VideoModal.vue';
import VideoPlayer from '@/lib/components/Video/VideoPlayer.vue';
import HelpIcon from '@/lib/components/Shared/HelpIcon.vue';
import PdfViewer from '@/lib/components/Shared/PdfViewer/PdfViewer.vue';

export default {
	components: {
		TabbedContent,
		TabContent,
		'overview-quick-manager': QuickManager,
		'quick-manager': QuickManager,
		'quick-grant-manager': QuickGrantManager,
		DescriptionListItem,
		ListAction,
		Resubmission,
		EditUser,
		DeadlineEditor,
		Comments,
		Contracts,
		GrantReports,
		Deletism,
		Undeletism,
		AllocationPayments,
		Allocations,
		Popover,
		EntryReviewFlow,
		FormInviter,
		EditDocument,
		CreateDocument,
		FileMetadata,
		VideoPlayer,
		VideoModal,
		HelpIcon,
		PdfViewer,
	},
	mixins: [langMixin],
	data() {
		return {
			reveal: null,
		};
	},
	computed: {
		editDocumentRevealed() {
			return this.reveal === 'edit-document';
		},
		createDocumentRevealed() {
			return this.reveal === 'create-document';
		},
		formInviterRevealed() {
			return this.reveal === 'form-inviter';
		},
	},
	methods: {
		onReveal(action) {
			this.reveal = this.reveal !== action ? action : null;
		},
		initVideoPlayer() {
			let videoThumbnails = new VideoThumbnails();
			videoThumbnails.setup('preview-container.video');
		},
		initAudioPlayer() {
			let audio = new Audio();
			audio.setup('jp-jplayer');
		},
		initRecuseButton() {
			const recuseButton = document.querySelector('.recusal-show-add');
			if (recuseButton) {
				recuseButton.addEventListener('click', () => {
					recuseButton.style.display = 'none';
					document.querySelector('.recusal-add form').classList.remove('hidden');
				});
			}

			$('#recuse-user').select2({
				ajax: {
					url: '/users/autocomplete/current',
					dataType: 'json',
					delay: 250,
					data: function (searchTerm) {
						return {
							name: searchTerm,
						};
					},
					results: function (data) {
						return {
							results: data,
						};
					},
					cache: true,
				},
				formatResult: function (user) {
					return user.firstName + ' ' + user.lastName;
				},
				formatSelection: function (user) {
					return user.firstName + ' ' + user.lastName;
				},
				minimumInputLength: 3,
			});
		},
	},
	mounted() {
		this.initVideoPlayer();
		this.initAudioPlayer();
		this.initRecuseButton();
	},
};
</script>
