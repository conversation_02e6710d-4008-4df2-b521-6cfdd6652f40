<template>
	<div>
		<table v-if="existingContracts.length" class="table">
			<thead>
				<tr>
					<th>{{ labels.contract }}</th>
					<th>{{ labels.signed }}</th>
					<th class="overflow-cell"></th>
				</tr>
			</thead>
			<tbody>
				<tr v-for="contract in existingContracts" :key="`contract-${contract.slug}`">
					<td v-if="contract.signedAt">
						<a
							:href="
								route(routes['entry.manager.contract-pdf'], {
									entry: entry,
									contract: contract.slug,
								})
							"
							target="_blank"
							class="ignore"
							rel="noopener noreferrer"
						>
							{{ contract.title }}
						</a>
					</td>
					<td v-else>{{ contract.title }}</td>
					<td v-output="status(contract)"></td>
					<td>
						<contract-overflow-menu
							:contract="contract"
							:labels="labels"
							@deleted="(contract) => confirmAndDelete(contract)"
						/>
					</td>
				</tr>
			</tbody>
		</table>
		<button
			v-if="!adding"
			class="btn btn-secondary btn-sm"
			:disabled="contentBlocks.length === 0"
			@click.prevent="adding = true"
		>
			{{ labels['add'] }}
		</button>
		<div v-else>
			<div class="form-group">
				<select-field
					name="contentBlocks"
					:items="contentBlocks"
					:empty-option="false"
					id-property="slug"
					value-property="title"
					@selected="onSelected"
				/>
			</div>
			<button class="btn btn-primary btn-sm" :disabled="saving" @click.prevent="save">
				{{ labels['save'] }}
			</button>
			<button class="btn btn-primary btn-sm" @click.prevent="adding = false">
				{{ labels['cancel'] }}
			</button>
		</div>
		<confirmation-modal
			modal-id="modal-target-confirm-contract-deletion"
			:show-modal="confirmDeletion"
			:confirmation="labels['confirmation']"
			:confirm-button-label="labels['continue']"
			:cancel-button-label="labels['cancel']"
			@closed="confirmDeletion = false"
			@confirmed="remove"
		/>
	</div>
</template>

<script>
import toastr from 'toastr';
import { route } from '@/lib/utils.js';
import { SelectField } from 'vue-bootstrap';
import ConfirmationModal from '@/lib/components/Shared/ConfirmationModal';
import ContractOverflowMenu from './ContractOverflowMenu';

export default {
	components: {
		SelectField,
		ConfirmationModal,
		ContractOverflowMenu,
	},
	props: {
		entry: {
			type: String,
			required: true,
		},
		contracts: {
			type: Array,
			required: true,
		},
		contentBlocks: {
			type: Array,
			default: () => [],
		},
		labels: {
			type: Object,
			required: true,
		},
		routes: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			adding: false,
			saving: false,
			existingContracts: [],
			contentBlock: null,
			selectedContract: null,
			confirmDeletion: false,
		};
	},
	created() {
		this.existingContracts = JSON.parse(JSON.stringify(this.contracts));
	},
	methods: {
		route,
		status(contract) {
			return contract.signedAt ? contract.signedAt : this.labels['unsigned'];
		},
		onSelected(name, selected) {
			this.contentBlock = selected;
		},
		save() {
			this.saving = true;
			this.$http
				.post(
					route(this.routes['entry.manager.add-contract'], {
						entry: this.entry,
						contentBlock: this.contentBlock,
					})
				)
				.then(
					(response) => {
						this.existingContracts = response.data.contracts || [];
						this.adding = false;
						this.saving = false;
					},
					(error) => {
						toastr.error(error.response.data.message || this.labels['saveError']);
					}
				);
		},
		confirmAndDelete(contract) {
			this.selectedContract = contract;
			this.confirmDeletion = true;
		},
		remove() {
			const slug = this.selectedContract.slug;

			this.$http
				.delete(
					route(this.routes['entry.manager.delete-contract'], {
						entry: this.entry,
						contract: slug,
					})
				)
				.then(
					() => {
						this.existingContracts = this.existingContracts.filter((c) => c.slug !== slug);
					},
					(error) => {
						toastr.error(error.response.data.message || this.labels['deleteError']);
					}
				);
		},
	},
};
</script>

<style scoped>
.table tr:first-child > td {
	border-top: none;
}
</style>
