<template>
  <div>
    <div v-if="!canDisplaySetTitle">
      <div class="date-output">
        {{ displayDeadline }} <a href="#" role="button" @click="showDatepicker">{{ lang.get('shared.edit') }}</a>
      </div>
      <div class="timezone-output">{{ formattedTimezone }}</div>
    </div>
    <a href="#" v-if="canDisplaySetTitle" role="button" @click="showDatepicker">
      <span>{{ lang.get('shared.set') }}</span>
    </a>
    <localised-datepicker
      v-if="datepickerVisible"
      id="deadline"
      ref="deadlinePicker"
      :value="deadlineValue"
      mode="datetime"
      :hidden-fields="true"
      :default-timezone="timezoneValue"
      @changed="deadlineChanged"
      @escape="hideDatePicker"
      @close="hideDatePicker"
    ></localised-datepicker>
  </div>
</template>

<script>
import LocalisedDatepicker from '@/lib/components/Shared/LocalisedDatepicker';
import { mapState } from 'vuex';
import langMixin from '@/lib/components/Translations/mixins/lang-mixin';
const toastr = require('toastr');

export default {
  components: {
    LocalisedDatepicker
  },
  mixins: [langMixin],
  props: {
    deadline: {
      type: String,
      default: ''
    },
    timezone: {
      type: String,
      default: ''
    },
    entrySlug: {
      type: String,
      required: true
    },
    formattedDeadline: {
      type: String,
      default: ''
    },
    formattedTimezone: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      deadlineValue: { datetime: this.deadline },
      timezoneValue: this.timezone || this.defaultTimezone,
      datepickerVisible: false,
      displayDeadline: this.formattedDeadline
    };
  },
  computed: {
    ...mapState('global', ['defaultTimezone']),
    isOpen() {
      return this.datepickerVisible === true;
    },
    canDisplaySetTitle() {
      return !this.datepickerVisible && (this.deadlineValue.datetime === '' || this.deadlineValue.datetime === null);
    }
  },
  methods: {
    showDatepicker() {
      this.datepickerVisible = true;
      setTimeout(() => this.$refs.deadlinePicker.$el.querySelector('input[type=text]')?.focus(), 50);
    },
    hideDatePicker() {
      if (this.isOpen) {
        this.datepickerVisible = false;
      }
    },
    deadlineChanged(v) {
      if (this.deadlineValue.datetime !== v && (v !== null || this.deadlineValue.datetime !== '')) {
        this.updateDeadline(v);
      }
    },
    updateDeadline(v) {
      this.$http
        .post('/entry/manager/' + this.entrySlug + '/deadline', {
          deadline: v
        })
        .then(
          r => {
            this.deadlineValue.datetime = v;
            this.displayDeadline = r.data.formattedDeadline;
          },
          e => {
            toastr.error(e.response.data.message || document.getElementById('alerts-generic').innerHTML);
          }
        );
    }
  }
};
</script>
