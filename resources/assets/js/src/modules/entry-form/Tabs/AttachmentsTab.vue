<template>
	<div class="row">
		<entry-fields :tab="tab" :transition="transition" class="col-xs-12" />
		<div class="col-xs-12">
			<div class="attachment-container">
				<div class="attachments cards">
					<attachment-configurator v-if="configurationMode && Object.keys(attachments).length === 0" :tab="tab" />
					<uploader
						v-if="uploaderOptions || tab.readonly"
						:handles-temporary-files="isCollaborative"
						:options="uploaderOptions"
						:attachments="tabAttachments"
						:attachment-mode="true"
						:can-delete="canDelete"
						:can-upload="canUpload"
						:files="files"
						:file-limit="tab.maxAttachments"
						:file-count="fileCount"
						:disabled="disabled"
						:min-video-length="minVideoLength"
						:max-video-length="maxVideoLength"
						:image-dimension-constraints="imageDimensionConstraints"
						:in-active-tab="isInActiveTab"
						:handle-errors="true"
						@uploading="onUploading"
						@uploadingFile="uploadingFile"
						@uploaded="uploadedFile"
						@deleted="deletedFile"
						@movedLeft="movedLeft"
						@movedRight="movedRight"
						@imageDataUrl="onImageDataUrl(...arguments)"
						@error="onError"
					>
						<template slot="btn-upload-text">
							<translation :text="lang.get('files.buttons.attachments')" />
						</template>
						<template slot="file-limit-reached-text">
							<translation :text="lang.get('files.messages.file_limit')" />
						</template>
					</uploader>
				</div>
			</div>
		</div>
		<div v-if="tab.acceptAttachmentLinks || existingEntryLinks.length > 0" class="col-xs-12">
			<entry-links
				:tab="tab"
				:file-limit="tab.maxAttachments"
				:file-count="fileCount"
				:disabled="disabled"
				:add-link-is-visible="tab.acceptAttachmentLinks"
			>
				<template slot="file-limit-reached-text">
					<translation :text="lang.get('files.messages.file_limit')" />
				</template>
			</entry-links>
		</div>
		<div v-if="tab.maxAttachments || tab.minAttachments" class="col-xs-12">
			{{
				lang.choice('files.messages.attachment_counter', tab.maxAttachments || fileCount, {
					counter: tab.maxAttachments ? fileCount + '/' + tab.maxAttachments : fileCount,
				})
			}}
			<template v-if="tab.minAttachments">
				(<span v-output="lang.get('files.messages.minimum_required', { count: tab.minAttachments })"></span>)
			</template>
		</div>

		<buttons-container v-if="isLoaded && !configure" class="col-xs-12" />
	</div>
</template>

<script lang="ts">
import { mapActions, mapGetters, mapMutations, mapState } from 'vuex';
import EntryFields from '../Fields/EntryFields';
import FileUpload from '@/lib/components/Uploader/FileUpload';
import Uploader from '@/lib/components/Uploader/Uploader';
import AttachmentConfigurator from '../Configuration/AttachmentConfigurator';
import EntryLinks from '../EntryLinks/EntryLinks';
import Translation from '@/modules/interface-text/components/Translation';
import ButtonsContainer from '@/modules/entry-form/Buttons/ButtonsContainer';
import { getSubmittableUrl, parseValueAsInt } from '@/lib/utils';
import ImageDimensionConstraints from '@/lib/components/Uploader/ImageDimensionConstraints';
import Status from '@/lib/components/Uploader/Status';
import ProcessingStatus from '@/lib/components/Uploader/ProcessingStatus';
import { useAttachmentsService } from '@/modules/entry-form/Collaboration/services/Attachments.service';
import { useLinksService } from '@/modules/entry-form/Collaboration/services/Links.service';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';
import { collaboratorAccess, defaultCollaboratorAccess } from '@/domain/models/Collaborator';

const toastr = require('toastr');

export default {
	components: {
		Uploader,
		FileUpload,
		EntryFields,
		AttachmentConfigurator,
		EntryLinks,
		Translation,
		ButtonsContainer,
	},
	inject: ['lang'],
	props: {
		tab: {
			type: Object,
			required: true,
		},
		transition: {
			type: String,
			default: 'push',
		},
		configure: {
			type: Boolean,
			required: true,
		},
	},
	data() {
		return {
			tempPreviews: [],
			attachmentsRef: null,
			linksRef: null,
			isCollaborative: false,
			access: defaultCollaboratorAccess,
		};
	},
	computed: {
		existingEntryLinks() {
			return this.linksByTab(this.tab.id);
		},
		fileCount() {
			return (this.links[this.tab.id] || []).length + this.files.length;
		},
		uploadedAttachments() {
			return this.tabAttachments.filter((attachment) =>
				[ProcessingStatus.OK, Status.COMPLETED].includes(attachment.file.status)
			);
		},
		uploadingAttachments() {
			return this.tabAttachments.filter(
				(attachment) => ![ProcessingStatus.OK, Status.COMPLETED].includes(attachment.file.status)
			);
		},
		tabAttachments() {
			return this.attachmentsByTab(this.tab.id);
		},
		canDelete() {
			return !this.locks.readOnly;
		},
		canUpload() {
			return !this.locks.readOnly;
		},
		files() {
			return this.tabAttachments.map((a) => a.file);
		},
		role() {
			return this.isManager ? 'manager' : 'entrant';
		},
		uploaderOptions() {
			return this.tab.uploaderOptions ? this.tab.uploaderOptions.clarify() : null;
		},
		disabled() {
			return (
				(!this.isManager && !this.tabIsVisible(this.tab)) ||
				this.configurationMode ||
				!this.uploaderOptions ||
				this.locks.ineligible ||
				(!this.hasManagerRole && this.locks.readOnly) ||
				(this.tabEligibilityReadOnly(this.tab) && !this.isEligible) ||
				this.access.canEdit === false
			);
		},
		isInActiveTab() {
			return this.selectedTab.id === this.tab.id;
		},
		minVideoLength() {
			return parseValueAsInt(this.tab.minVideoLength);
		},
		maxVideoLength() {
			return parseValueAsInt(this.tab.maxVideoLength);
		},
		imageDimensionConstraints(): ImageDimensionConstraints {
			return this.tab.imageDimensionConstraints;
		},
		...mapGetters('entryForm', [
			'attachmentByFile',
			'attachmentsByTab',
			'selectedTab',
			'tabIsVisible',
			'tabEligibilityReadOnly',
			'isEligible',
			'submittableType',
			'linksByTab',
			'isLoaded',
		]),
		...mapState('entryForm', ['entry', 'isManager', 'locks', 'links', 'attachments']),
		...mapState('entryFormConfiguration', ['configurationMode']),
		...mapState('entryFormApi', ['errorBag']),
	},
	created() {
		const { isCollaborative } = useCollaborativeSubmittable();
		this.isCollaborative = isCollaborative;

		this.attachmentsRef = useAttachmentsService();
		this.linksRef = useLinksService().documentService;

		this.loadAttachments().then(() => {
			this.orderAttachmentsFrom1ToN();

			this.attachmentsRef.set(this.attachments);
			this.linksRef.set(this.links);

			this.attachmentsRef.subscribe((attachments) => {
				this.storeAttachments({ attachments });
				this.sortAttachmentsByOrder({ tabId: this.tab.id });
				this.orderAttachmentsFrom1ToN();
			}, true);

			this.linksRef.subscribe((links) => {
				this.storeLinks({ links });
			}, true);
		});

		this.access = defaultCollaboratorAccess;

		collaborationUIBus.on(CollaborationUISignals.UPDATED_COLLABORATORS, (payload) => {
			this.access = collaboratorAccess(payload.myself);
		});

		this.attachmentsRef.myAccess().then((access) => {
			this.access = access;
		});
	},
	watch: {
		uploadedAttachments() {
			this.attachmentsRef.set(this.attachments || []);
		},
		existingEntryLinks() {
			this.linksRef.set(this.links);
		},
	},
	methods: {
		deletedFile(fileId, remoteId, isNotCaption) {
			if (!isNotCaption) {
				return this.deleteCaption(remoteId);
			}

			this.deleteAttachment({
				tabId: this.tab.id,
				attachment: this.attachmentByFile(fileId),
			});
			this.orderAttachmentsFrom1ToN();
			this.deleteAttachmentRequest(remoteId);
		},
		deleteAttachmentUrl(fileId) {
			const url = getSubmittableUrl(this.submittableType);
			return '/' + url + '/' + this.role + '/' + this.entry.slug + '/attachment/' + fileId;
		},
		deleteCaption(remoteId) {
			this.$http.delete('/file/own/' + remoteId).catch((error) => {
				toastr.error(error.response.data.message || document.getElementById('alerts-generic').innerHTML);
			});
		},
		deleteAttachmentRequest(fileId) {
			this.$http.delete(this.deleteAttachmentUrl(fileId)).then(
				() => {
					this.orderAttachmentsRequest();
				},
				(error) => {
					toastr.error(error.response.data.message || document.getElementById('alerts-generic').innerHTML);
				}
			);
		},
		onImageDataUrl(imageDataUrl, file) {
			this.tempPreviews[file.id] = imageDataUrl;
		},
		imageDataUrlForFile(file) {
			for (let fileId of Object.keys(this.tempPreviews)) {
				if (fileId === file.oldId) {
					return this.tempPreviews[fileId];
				}
			}

			return null;
		},
		maxOrder() {
			return this.tabAttachments.reduce(
				(accumulator, attachment) => (accumulator < attachment.order ? attachment.order : accumulator),
				0
			);
		},
		movedLeft(attachment) {
			const newOrder = attachment.order - 1;
			const previousAttachment = this.tabAttachments.find((a) => a.order === newOrder);

			if (previousAttachment) {
				this.updateAttachment({
					attachment: previousAttachment,
					properties: {
						order: newOrder + 1,
					},
				});
			}

			this.updateAttachment({
				attachment: attachment,
				properties: {
					order: newOrder,
				},
			});

			this.sortAttachmentsByOrder({ tabId: this.tab.id });
			this.orderAttachmentsFrom1ToN();
			this.orderAttachmentsRequest();
		},
		movedRight(attachment) {
			const newOrder = attachment.order + 1;
			const nextAttachment = this.tabAttachments.find((a) => a.order === newOrder);

			if (nextAttachment) {
				this.updateAttachment({
					attachment: nextAttachment,
					properties: {
						order: newOrder - 1,
					},
				});
			}

			this.updateAttachment({
				attachment: attachment,
				properties: {
					order: newOrder,
				},
			});

			this.sortAttachmentsByOrder({ tabId: this.tab.id });
			this.orderAttachmentsFrom1ToN();
			this.orderAttachmentsRequest();
		},
		orderAttachmentsRequest() {
			let counter = 1;
			const orderData = this.tabAttachments
				.map((a) => a.fileId)
				.reduce((acc, fileId) => {
					acc[fileId] = counter++;
					return acc;
				}, {});

			this.orderAttachments({
				url: this.orderAttachmentsUrl(),
				data: orderData,
			}).then(
				() => {},
				(error) => {
					toastr.error(error.response.data.message || document.getElementById('alerts-generic').innerHTML);
				}
			);
		},
		orderAttachmentsUrl() {
			const submittable = window.location.href.search('entry-form') > 0 ? 'entry' : 'grant-report';
			return '/' + submittable + '/' + this.role + '/' + this.entry.slug + '/attachment/order';
		},
		orderAttachmentsFrom1ToN() {
			this.tabAttachments.forEach((a, index) => {
				const nextOrder = index + 1;
				if (a.order !== nextOrder) {
					this.updateAttachment({
						attachment: a,
						properties: {
							order: nextOrder,
						},
					});
				}
			});
		},
		/**
		 * When uploading a new file, create temporary attachment, so we can display it.
		 */
		uploadingFile(file) {
			file = Object.assign({}, file);
			this.addAttachment({
				tabId: this.tab.id,
				attachmentId: 'temp-attachment-' + file.id,
				file: file,
				fileId: file.id,
				order: this.maxOrder() + 1,
			});
		},
		/**
		 * When file has finished uploading, remove temp attachment and create attachment with real id.
		 */
		uploadedFile(completeFileTokens, file, attachmentId) {
			if (file && attachmentId) {
				const attachment = this.attachmentByFile(file.oldId);
				this.updateAttachment({
					attachment: attachment,
					properties: {
						id: attachmentId,
						fileId: file.id,
						file: file,
					},
				});
				this.copyAttachmentValues({
					oldId: 'temp-attachment-' + file.oldId,
					newId: attachment.id,
				});

				this.orderAttachmentsFrom1ToN();
				this.orderAttachmentsRequest();
				this.cleanValidationErrors();
			}
		},
		onUploading(uploaderId, isUploading) {
			this.setUploaderStatus({ uploaderId, isUploading });
		},
		...mapActions('entryForm', ['setTabAttachments', 'addAttachment', 'orderAttachments', 'loadAttachments']),
		onError(errors) {
			this.storeFieldErrors({
				key: 'Attachments-Attachment-tab-' + this.tab.id,
				errors: errors,
				tab: this.tab.id,
			});
		},
		cleanValidationErrors() {
			this.deleteTabIdApiErrors(this.tab.id);
			this.removeFromErrorBag({
				type: 'Tab',
				tab: 'Attachments',
				tabId: this.tab.id,
			});
		},
		...mapActions('entryForm', ['addAttachment', 'orderAttachments']),
		...mapMutations('entryForm', [
			'setTabLinks',
			'storeLinks',
			'storeAttachments',
			'copyAttachmentValues',
			'deleteAttachment',
			'deleteTabIdApiErrors',
			'sortAttachmentsByOrder',
			'updateAttachmentImage',
			'updateAttachment',
			'setUploaderStatus',
			'storeFieldErrors',
		]),
		...mapMutations('entryFormApi', ['removeFromErrorBag']),
	},
};
</script>
