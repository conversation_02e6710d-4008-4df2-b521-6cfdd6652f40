<template>
	<filtertron-tray-section
		v-if="visible"
		id="field-configurator-options"
		:title="lang.get('fields.configuration.options.label')"
	>
		<div class="fields island">
			<auto-scoring-toggle v-if="visibleAutoScoring" :status="!!field.autoScoring" @change="toggleAutoScoring" />
			<auto-tag-toggle v-if="visibleAutoTag" :status="field.autoTag" @change="toggleAutoTag" />
			<template v-if="hasOptions">
				<draggable-options
					id="draggable-options"
					:field="field"
					:supported-languages="supportedLanguages"
					:default-language="defaultLanguage"
					:language="lang.locale"
					@errors="handleErrors"
					v-on="$listeners"
				></draggable-options>
			</template>
			<template v-if="isDate">
				<div class="form-group">
					<b>{{ lang.get('fields.form.timezone.text') }}</b>
					<div class="checkbox styled">
						<input
							id="include-timezone"
							type="checkbox"
							:checked="field.includeTimezone"
							@change="(e) => handleInput('includeTimezone', e.target.checked)"
						/>
						<label for="include-timezone">
							{{ lang.get('fields.form.timezone.label') }}
						</label>
					</div>
				</div>
				<div class="form-group">
					<div class="checkbox styled">
						<input
							id="preselect-current-date"
							type="checkbox"
							:checked="field.preselectCurrentDate"
							@change="(e) => handleInput('preselectCurrentDate', e.target.checked)"
						/>
						<label for="preselect-current-date">
							{{ lang.get('fields.form.preselect_current_date.label') }}
						</label>
					</div>
				</div>
			</template>
			<template v-if="field.type === 'currency'">
				<div class="form-group">
					<label for="currency">{{ lang.get('fields.form.currency.label') }}</label>
					<select-field
						name="currencySelector"
						:items="currencies.list"
						:value="field.currency || currencies.defaultCurrency"
						:empty-option="false"
						@selected="(name, value) => handleCurrencyInput(value)"
					/>
				</div>
			</template>
			<template v-if="field.type === 'drop-down-list'">
				<div class="checkbox styled">
					<input
						id="autocomplete"
						type="checkbox"
						:checked="field.autocomplete"
						@change="(e) => handleInput('autocomplete', e.target.checked)"
					/>
					<label for="autocomplete">
						{{ lang.get('fields.form.autocomplete.label') }}
					</label>
				</div>
			</template>
			<template v-if="field.type === 'file'">
				<div class="form-group">
					<label>{{ lang.get('fields.form.max-file-size.label') }}</label>
					<input
						type="number"
						:value="field.maxFileSize"
						class="form-control"
						@input="(e) => handleInput('maxFileSize', toInt(e.target.value))"
					/>
				</div>
			</template>
			<template v-if="field.type === 'text' || field.type === 'textarea'">
				<div class="form-group">
					<label>
						{{ lang.get('fields.form.maximum_words.label') }}
						<help-icon :content="lang.get('fields.form.maximum_words.help')" />
					</label>
					<input
						type="number"
						:value="field.maximumWords"
						class="form-control"
						@input="(e) => handleInput('maximumWords', toInt(e.target.value))"
					/>
				</div>
				<div class="form-group">
					<label>
						{{ lang.get('fields.form.minimum_words.label') }}
						<help-icon :content="lang.get('fields.form.minimum_words.help')" />
					</label>
					<input
						type="number"
						:value="field.minimumWords"
						class="form-control"
						@input="(e) => handleInput('minimumWords', toInt(e.target.value))"
					/>
				</div>
				<div class="form-group">
					<label>
						{{ lang.get('fields.form.maximum_characters.label') }}
						<help-icon :content="lang.get('fields.form.maximum_characters.help')" />
					</label>
					<input
						type="number"
						:value="field.maximumCharacters"
						class="form-control"
						@input="(e) => handleInput('maximumCharacters', toInt(e.target.value))"
					/>
				</div>
				<div class="form-group">
					<label>
						{{ lang.get('fields.form.minimum_characters.label') }}
						<help-icon :content="lang.get('fields.form.minimum_characters.help')" />
					</label>
					<input
						type="number"
						:value="field.minimumCharacters"
						class="form-control"
						@input="(e) => handleInput('minimumCharacters', toInt(e.target.value))"
					/>
				</div>
			</template>
			<template v-if="field.type === 'checkbox'">
				<div v-if="!!field.autoScoring" class="form-group">
					<checkbox-configurator
						:auto-="field"
						:score="field.options !== undefined && field.options[0] !== undefined ? field.options[0].score : 0"
						@input="(v) => handleInput('options', [{ id: '1', score: v }])"
					></checkbox-configurator>
				</div>
			</template>
			<template v-if="hasPlagiarismDetection">
				<div class="form-group">
					<div class="checkbox styled">
						<input
							id="plagiarism-detection"
							type="checkbox"
							:checked="field.plagiarismDetection"
							@change="(e) => handleInput('plagiarismDetection', e.target.checked)"
						/>
						<label for="plagiarism-detection">
							{{ lang.get('fields.form.plagiarism_detection.label') }}
						</label>
					</div>
				</div>
			</template>
			<template v-if="isSearchable">
				<div class="form-group">
					<div class="checkbox styled">
						<input
							id="searchable"
							:disabled="field.isRecalculating || field.protection === 'maximum'"
							type="checkbox"
							:checked="field.searchable"
							@change="(e) => handleInput('searchable', e.target.checked)"
						/>
						<label for="searchable">
							{{ lang.get('fields.form.searchable.label') }}
						</label>
					</div>
				</div>
			</template>
			<template v-if="field.type === 'table'">
				<expandable-config>
					<template slot-scope="{ maxDimensions }">
						<div class="table-field-configurator-container">
							<table-field-configurator
								class="man"
								:style="maxDimensions"
								:configuration="field.configuration ? JSON.parse(field.configuration) : {}"
								:configuration-translated="field.configurationTranslated ? JSON.parse(field.configurationTranslated) : {}"
								:labels="tableFieldConfiguratorLabels"
								:input-types="tableFieldInputTypes"
								:language="defaultLanguage"
								:default-language="field.defaultLanguage"
								:supported-languages="supportedLanguages"
								:has-entries="field.hasEntries"
								@updated="onTableConfigurationUpdated"
							/>
						</div>
					</template>
				</expandable-config>
			</template>
			<template v-if="field.type === 'formula'">
				<formula-field-configurator
					:configuration="field.configuration ? JSON.parse(field.configuration) : {}"
					:field-titles="entryFieldTitles"
					@updated="onFormulaConfigurationUpdated"
				/>
			</template>
		</div>
	</filtertron-tray-section>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex';
import FiltertronTraySection from '@/lib/components/Filtertron/FiltertronTraySection';
import HelpIcon from '@/lib/components/Shared/HelpIcon';
import Multilingual from '@/lib/components/Translations/Multilingual';
import TableFieldConfigurator from '@/lib/components/Fields/TableFieldConfigurator';
import ExpandableConfig from '../ExpandableConfig';
import handleInputMixin from '../handle-input-mixin.js';
import toIntMixin from '../to-int-mixin.js';
import DraggableOptions from '../DraggableOptions';
import { SelectField } from 'vue-bootstrap';
import AutoScoringToggle from '@/lib/components/Fields/AutoScoringToggle';
import AutoTagToggle from '@/lib/components/Fields/AutoTagToggle';
import CheckboxConfigurator from '@/lib/components/Fields/CheckboxConfigurator';
import FormulaFieldConfigurator from '@/lib/components/Fields/FormulaFieldConfigurator';
import { featureEnabled } from '@/services/global/features.interface';

export default {
	inject: ['lang', 'currencies'],
	components: {
		FiltertronTraySection,
		HelpIcon,
		Multilingual,
		TableFieldConfigurator,
		ExpandableConfig,
		DraggableOptions,
		SelectField,
		AutoScoringToggle,
		AutoTagToggle,
		CheckboxConfigurator,
		FormulaFieldConfigurator,
	},
	mixins: [handleInputMixin, toIntMixin],
	props: {
		field: {
			type: Object,
			required: true,
		},
	},
	data() {
		return {
			options: null,
		};
	},
	computed: {
		...mapGetters('entryForm', ['entryFieldTitles']),
		...mapState('entryForm', [
			'plagiarismDetection',
			'searchableFields',
			'tableFieldConfiguratorLabels',
			'tableFieldInputTypes',
		]),
		...mapState('global', ['supportedLanguages', 'defaultLanguage']),
		visible() {
			if (this.field.type && this.field.type === 'formula') {
				return featureEnabled('formula_field');
			}

			return this.field.type && this.field.type !== 'content' && this.field.type !== 'time';
		},
		hasOptions() {
			return ['checkboxlist', 'drop-down-list', 'radio'].includes(this.field.type);
		},
		isDate() {
			return this.field.type === 'date' || this.field.type === 'datetime';
		},
		isSearchable() {
			return this.searchableFields.includes(this.field.type);
		},
		hasPlagiarismDetection() {
			return this.plagiarismDetection && (this.field.type === 'textarea' || this.field.type === 'file');
		},
		visibleAutoScoring() {
			return this.field.resource === 'Entries' && (this.hasOptions || this.field.type === 'checkbox');
		},
		visibleAutoTag() {
			return this.field.resource === 'Entries' && this.hasOptions;
		},
	},
	created() {
		if (this.hasOptions) {
			this.options = this.field.options.map((option) => option.id).join('\r\n');
		}
	},
	methods: {
		...mapMutations('entryFormConfiguration', ['setHasErrors']),
		handleErrors(hasErrors) {
			this.setHasErrors(hasErrors);
		},
		onTableConfigurationUpdated(configuration, translations) {
			if (this.field.configuration !== configuration || this.field.configurationTranslated !== translations) {
				this.$emit('input', {
					configuration: configuration,
					configurationTranslated: translations,
				});
			}
		},
		onFormulaConfigurationUpdated(configuration) {
			if (this.field.configuration !== configuration) {
				this.$emit('input', {
					configuration: configuration,
				});
			}
		},
		handleCurrencyInput(value) {
			if ((this.field.currency || this.currencies.defaultCurrency) !== value) {
				this.handleInput('currency', value);
			}
		},
		toggleAutoScoring(value) {
			this.$emit('input', {
				autoScoring: value,
			});
		},
		toggleAutoTag(value) {
			this.$emit('input', {
				autoTag: value,
			});
		},
	},
};
</script>
