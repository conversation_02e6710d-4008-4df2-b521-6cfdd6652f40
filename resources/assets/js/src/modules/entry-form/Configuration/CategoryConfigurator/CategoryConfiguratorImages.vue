<template>
	<div>
		<div class="form-group">
			<label>{{ lang.get('category.form.image_heading.label') }}</label>
			<multilingual
				:supported-languages="supportedLanguages"
				:resource="category"
				:disabled="readOnly"
				property="image_heading"
				@input="handleHeadingInput"
			/>
		</div>
		<div class="form-group">
			<label>{{ lang.get('category.form.max_image_width.label') }}</label>
			<input
				type="number"
				step="1"
				name="maxImageWidth"
				:value="category.maxImageWidth"
				:disabled="readOnly"
				class="form-control"
				@input="(e) => handleInput('maxImageWidth', toInt(e.target.value))"
			/>
		</div>
		<uploader
			v-if="category.uploaderOptions"
			:files="category.files"
			:can-delete="!readOnly"
			:can-upload="!readOnly"
			:options="
				typeof category.uploaderOptions === 'string' ? category.uploaderOptions.clarify() : category.uploaderOptions
			"
			:button-label="lang.get('files.buttons.images')"
			@deleted="deleteFile"
			@completed="reloadCategory"
		/>
	</div>
</template>

<script>
import { mapMutations } from 'vuex';
import Multilingual from '@/lib/components/Translations/Multilingual';
import Uploader from '@/lib/components/Uploader/Uploader';
import handleInputMixin from '../handle-input-mixin.js';
import handleTranslatedInputMixin from '../handle-translated-input-mixin.js';
import toIntMixin from '../to-int-mixin.js';
import featuresMixin from '@/lib/components/Shared/mixins/features-mixin';
import linksMixin from '@/lib/components/Shared/mixins/links-mixin';

const toastr = require('toastr');

export default {
  inject: ['lang'],
  components: {
    Multilingual,
    Uploader
  },
  mixins: [handleInputMixin, handleTranslatedInputMixin, toIntMixin, featuresMixin, linksMixin],
  props: {
    category: {
      type: Object,
      required: true
    },
		readOnly: {
			type: Boolean,
			default: false,
		},
  },
  methods: {
    ...mapMutations('entryForm', ['replaceCategoryFiles', 'addFileTokens']),
    deleteCategoryImageUrl(fileId) {
      if (this.isNewCategory()) return '/file/own/' + fileId;
      return '/category/' + this.category.slug + '/image/' + fileId;
    },
    reloadCategoryUrl() {
      return '/entry-form/manager/categories/' + this.category.slug;
    },
    deleteFile(id, remoteId) {
      if (remoteId) {
        this.$http.delete(this.deleteCategoryImageUrl(remoteId)).then(
          () => {
            this.reloadCategory();
          },
          () => {
            toastr.error(this.lang.get('miscellaneous.alerts.generic'));
          }
        );
      }
    },
    reloadCategory(fileTokens) {
      if (this.isNewCategory()) {
        this.addFileTokens({ fileTokens: fileTokens, category: this.category });
        return;
      }

			// After the category images are uploaded (and processed) it's
			// necessary to reload the category's files, so that the sponsor logos
			// get refreshed on the entry form.
			this.$http.get(this.reloadCategoryUrl()).then(
				(response) => {
					if (response && response.status === 200) {
						this.replaceCategoryFiles({
							oldCategory: this.category,
							newCategory: response.data.category,
						});
					}
				},
				() => {
					toastr.error(this.lang.get('miscellaneous.alerts.generic'));
				}
			);
		},
		handleHeadingInput(translated) {
			const heading = (translated[this.lang.locale] || {})['image_heading'] || '';

			this.$emit('input', {
				translated: translated,
				heading: heading,
			});
		},
		isNewCategory() {
			return this.category.slug === 'newcategory';
		},
	},
};
</script>
