<template>
  <filtertron-tray-section
    v-if="sponsorsEnabled && chapter.createdAt"
    id="chapter-configurator-images"
    :title="lang.get('chapters.configuration.images.label')"
    :initially-collapsed="true"
  >
    <div class="fields island">
      <div class="form-group">
        <label>
          {{ lang.get('chapters.form.image_heading.label') }}
        </label>
        <multilingual
          :supported-languages="supportedLanguages"
          :resource="chapter"
          property="image_heading"
          @input="handleHeadingInput"
        />
      </div>
      <div class="form-group">
        <label>{{ lang.get('chapters.form.max_image_width.label') }}</label>
        <input
          type="text"
          :value="chapter.maxImageWidth"
          class="form-control"
          @input="e => handleInput('maxImageWidth', toInt(e.target.value))"
        />
      </div>
      <uploader
        v-if="chapter.uploaderOptions"
        :files="chapter.files"
        :options="chapter.uploaderOptions.clarify()"
        :button-label="lang.get('files.buttons.images')"
        class="pbx"
        @deleted="deleteFile"
        @completed="reloadChapter"
      />
    </div>
  </filtertron-tray-section>
</template>

<script>
import { mapMutations, mapState } from 'vuex';
import FiltertronTraySection from '@/lib/components/Filtertron/FiltertronTraySection';
import Multilingual from '@/lib/components/Translations/Multilingual';
import Uploader from '@/lib/components/Uploader/Uploader';
import handleInputMixin from '../handle-input-mixin.js';
import handleTranslatedInputMixin from '../handle-translated-input-mixin.js';
import toIntMixin from '../to-int-mixin.js';

const toastr = require('toastr');

export default {
  inject: ['lang'],
  components: {
    FiltertronTraySection,
    Multilingual,
    Uploader
  },
  mixins: [handleInputMixin, handleTranslatedInputMixin, toIntMixin],
  props: {
    chapter: {
      type: Object,
      required: true
    }
  },
  computed: {
    ...mapState('entryForm', ['sponsorsEnabled'])
  },
  methods: {
    ...mapMutations('entryForm', ['replaceChapterFiles']),
    deleteChapterImageUrl(fileId) {
      return '/chapter/' + this.chapter.slug + '/image/' + fileId;
    },
    reloadChapterUrl() {
      return '/entry-form/manager/chapters/' + this.chapter.slug;
    },
    deleteFile(id, remoteId) {
      if (remoteId) {
        this.$http.delete(this.deleteChapterImageUrl(remoteId)).then(
          () => {
            this.reloadChapter();
          },
          () => {
            toastr.error(this.lang.get('miscellaneous.alerts.generic'));
          }
        );
      }
    },
    reloadChapter() {
      // After the chapter images are uploaded (and processed) it's
      // necessary to reload the chapter's files, so that the sponsor logos
      // get refreshed on the entry form.
      this.$http.get(this.reloadChapterUrl()).then(
        response => {
          if (response && response.status === 200) {
            this.replaceChapterFiles({
              oldChapter: this.chapter,
              newChapter: response.data.chapter
            });
          }
        },
        () => {
          toastr.error(this.lang.get('miscellaneous.alerts.generic'));
        }
      );
    },
    handleHeadingInput(translated) {
      const heading =
        (translated[this.lang.locale] || {})['image_heading'] || '';

      this.$emit('input', {
        translated: translated,
        heading: heading
      });
    }
  }
};
</script>
