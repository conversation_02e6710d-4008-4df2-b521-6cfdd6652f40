import { refereeService } from '@/modules/entry-form/RefereeService';
import toastr from 'toastr';
import { useFieldService } from '@/modules/entry-form/Collaboration/services/Field.service';
import { collaborationUIBus, CollaborationUISignals } from '@/domain/signals/Collaboration';
import { CollaboratorAccess, collaboratorAccess, defaultCollaboratorAccess } from '@/domain/models/Collaborator';
import { computed, ComputedRef, Ref, ref, SetupFunction, watch } from 'vue';
import { MultiFocus, useMultiFocus } from '@/domain/utils/FocusSemaphore';
import { postInitiateReviewStage, postResend } from '@/modules/entry-form/Referee.api';
import { Referee, RefereeProps } from '@/modules/entry-form/RefereeTypes';
import { RefereesEmitters, RefereesEvents } from '@/lib/components/Collaboration/Referees.events';
import { trans, Trans } from '@/domain/dao/Translations';

type View = {
	lang: Trans;
	onInput: (field: string, referee: Referee, value: string) => void;
	deleteReferee: (referee: Referee) => void;
	canResendRequest: ComputedRef<boolean>;
	canSendRequest: ComputedRef<boolean>;
	completed: Ref<boolean>;
	requestSent: Ref<boolean>;
	isSending: Ref<boolean>;
	initiateReviewStage: () => void;
	resend: () => void;
	hasError: (field: string) => boolean;
	ajaxInProgress: ComputedRef<boolean>;
	multiFocus: MultiFocus;
	lockableId: string;
	disabledOrCollaborationLocked: ComputedRef<boolean>;
	disabledRequestButton: ComputedRef<boolean>;
	disabledResendRequestButton: ComputedRef<boolean>;
	access: Ref<CollaboratorAccess>;
};

type Ctx = {
	emit: RefereesEmitters;
};

const refereeController: SetupFunction<RefereeProps, View> = (props: RefereeProps, { emit }: Ctx): View => {
	const service = refereeService(props);
	const lang = trans();
	const collaborationLocked = ref(false);
	const fieldService = useFieldService(service.lockableId);
	const collaborationService = service.collaborationService;
	const access = ref(defaultCollaboratorAccess);
	const ajaxInProgress = service.ajaxInProgress;
	const multiFocus = useMultiFocus((state) => {
		fieldService.locks.set(state);

		collaborationService.api.updateReferee(props.referee);
	});

	fieldService.locks.subscribe((lock: boolean) => {
		collaborationLocked.value = lock;
	});

	fieldService.values.subscribe((referee: any) => {
		service.updateReferee(referee);
	});

	const completed = ref(props.referee.requestCompleted);
	watch(
		() => props.referee.requestCompleted,
		(newValue) => {
			completed.value = newValue;
		}
	);

	const requestSent = ref(props.referee.requestSent);
	watch(
		() => props.referee.requestSent,
		(newValue) => {
			requestSent.value = newValue;
		}
	);

	const canResendRequest = computed(() => requestSent.value && !completed.value);
	const canSendRequest = computed(() => !requestSent.value && !completed.value);

	const isSending = ref(false);
	const initiateReviewStage = async () => {
		isSending.value = true;

		try {
			await service.autosave();

			service.validate(props.referee, 'name');
			service.validate(props.referee, 'email');
			if (service.hasError('name', props.referee.id) || service.hasError('email', props.referee.id)) {
				return;
			}

			const response = await postInitiateReviewStage(
				service.entrySlug,
				service.formType
			)({
				entry: props.referee.submittableId,
				referee: props.referee.id,
			});
			// eslint-disable-next-line @typescript-eslint/naming-convention
			const verifiedResponse = response as { review_task: number; request_sent_at: string };
			service.saveReviewTaskId(props.referee, verifiedResponse.review_task, verifiedResponse.request_sent_at);

			emit(RefereesEvents.InitiatedReviewStage, props.referee);
		} catch (error) {
			console.error(error);
		} finally {
			isSending.value = false;
		}
	};

	const resend = () => {
		isSending.value = true;
		postResend(props.referee.reviewTask, service.formType)()
			.then(() => {
				toastr.success(lang.get('entries.messages.referee-resent'));
			})
			.catch((error) => {
				console.error(error);
			})
			.finally(() => {
				isSending.value = false;
			});
	};

	const deleteReferee = (referee: Referee) => {
		emit(RefereesEvents.DeleteReferee, referee);
	};

	collaborationUIBus.on(CollaborationUISignals.UPDATED_COLLABORATORS, (payload) => {
		access.value = collaboratorAccess(payload.myself);
	});

	fieldService.myAccess().then((myAccess) => {
		access.value = myAccess;
	});

	const disabledOrCollaborationLocked = computed(
		() => requestSent.value || props.eligibleReadonly || collaborationLocked.value || !access.value.canEdit
	);

	const disabledRequestButton = computed(
		() => isSending.value || ajaxInProgress.value || disabledOrCollaborationLocked.value
	);

	const disabledResendRequestButton = computed(() => isSending.value || ajaxInProgress.value || completed.value);

	return {
		lang,
		onInput: (field: string, referee: Referee, value: string) => service.onInput(field, referee, value),
		deleteReferee,
		canResendRequest,
		canSendRequest,
		completed,
		requestSent,
		isSending,
		initiateReviewStage,
		resend,
		hasError: (field: string) => service.hasError(field, props.referee.id),
		ajaxInProgress,
		multiFocus,
		lockableId: service.lockableId,
		disabledOrCollaborationLocked,
		disabledRequestButton,
		disabledResendRequestButton,
		access,
	};
};

export { refereeController, View };
