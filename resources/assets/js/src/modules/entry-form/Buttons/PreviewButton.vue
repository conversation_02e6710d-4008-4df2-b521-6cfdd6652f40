<template>
	<button class="btn btn-tertiary btn-lg preview-button ignore" :disabled="disabled" @click="preview">
		<translation :text="lang.get('entries.form.preview')" />
	</button>
</template>

<script>
import { mapActions, mapState } from 'vuex';

export default {
	inject: ['lang'],
	computed: {
		disabled() {
			return this.configurationMode || this.uploadInProgress;
		},
		...mapState('entryForm', ['uploadInProgress']),
		...mapState('entryFormConfiguration', ['configurationMode']),
	},
	methods: {
		...mapActions('entryFormApi', { preview: 'previewEntry' }),
	},
};
</script>
