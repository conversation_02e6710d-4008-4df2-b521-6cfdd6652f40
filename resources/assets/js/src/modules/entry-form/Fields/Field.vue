<template>
	<component
		:is="fieldContainerComponent"
		:field="field"
		:value="value"
		:validation-errors="errors"
		:element-id="elementId"
		:field-service-id="fieldServiceId"
	>
		<component
			:is="fieldComponent"
			v-model="value"
			:field-service="fieldService"
			:field="field"
			:name="name"
			:has-error="hasError"
			:element-id="elementId"
			:foreign-id="foreignId"
			:disabled="isDisabled"
			:is-manager="isManager"
			:validation-errors.sync="instantErrors"
			:empty-option="true"
			:country="country"
			:phone-with-leading-zeros="phoneWithLeadingZeros"
			:in-active-tab="isInActiveTab"
			:enable-markdown="enableMarkdown"
			:init-service="initService"
			@uploading="onUploading"
			@input="onInput"
		/>
		<template slot="controls">
			<slot name="controls"></slot>
		</template>
	</component>
</template>

<script>
import { mapGetters, mapMutations, mapState } from 'vuex';
import components, { fieldTypes } from '@/lib/components/Fields';
import AttachmentFieldContainer from './AttachmentFieldContainer';
import ContributorFieldContainer from './ContributorFieldContainer';
import RefereeFieldContainer from './RefereeFieldContainer';
import EntryFieldContainer from './EntryFieldContainer';
import fieldStateMixin from './field-state-mixin.js';
import { useFieldService } from '@/modules/entry-form/Collaboration/services/Field.service';
import { useCollaborativeSubmittable } from '@/modules/entry-form/Collaboration/services/CollaborativeSubmittable';

export default {
	inject: ['lang'],
	components: {
		AttachmentFieldContainer,
		ContributorFieldContainer,
		RefereeFieldContainer,
		EntryFieldContainer,
		...components,
	},
	mixins: [fieldStateMixin],
	props: {
		field: {
			type: Object,
			required: true,
		},
		resource: {
			type: Object,
			default: null,
		},
		tab: {
			type: Object,
			required: true,
		},
		disabled: {
			type: Boolean,
			default: false,
		},
		initService: {
			type: Boolean,
			default: true,
		},
	},
	data() {
		return {
			instantErrors: [],
			fieldService: null,
		};
	},
	created() {
		const { api } = useCollaborativeSubmittable();

		this.fieldService = useFieldService(this.fieldServiceId, api.updateField(this.field, this.resource?.id));
	},
	computed: {
		...mapGetters('entryForm', ['tabEligibilityReadOnly', 'isEligible']),
		fieldServiceId() {
			return `${this.elementId}_${this.field.slug}`;
		},
		isInActiveTab() {
			// eslint-disable-next-line eqeqeq
			return this.selectedTab.id == this.tab.id;
		},
		fieldComponent() {
			return fieldTypes[this.field.type];
		},
		fieldContainerComponent() {
			switch (this.field.resource) {
				case 'Attachments':
					return 'AttachmentFieldContainer';
				case 'Contributors':
					return 'ContributorFieldContainer';
				case 'Referees':
					return 'RefereeFieldContainer';
				default:
					return 'EntryFieldContainer';
			}
		},
		elementId() {
			switch (this.field.resource) {
				case 'Attachments':
					return 'Attachments-' + this.resource.id;
				case 'Contributors':
					return 'Contributors-' + this.resource.id;
				case 'Referees':
					return 'Referees-' + this.resource.id;
				default:
					return 'Entries';
			}
		},
		errors() {
			return this.instantErrors
				.map((error) =>
					this.lang.get('validation.' + error, {
						attribute: this.field.labelStripped,
					})
				)
				.concat(
					(
						this.errorBag.find((error) => this.isMyError(error)) || {
							message: [],
						}
					).message
				);
		},
		hasError() {
			return this.errors.length > 0;
		},
		apiError() {
			switch (this.field.resource) {
				case 'Attachments':
					return {
						type: 'AttachmentField',
						fieldSlug: this.field.slug,
						fileId: this.resource.fileId,
					};
				case 'Contributors':
					return {
						type: 'ContributorField',
						fieldSlug: this.field.slug,
						resourceId: this.resource.id,
					};
				case 'Entries':
					return {
						type: 'EntryField',
						fieldSlug: this.field.slug,
					};
				case 'Referees':
					return {
						type: 'RefereeField',
						fieldSlug: this.field.slug,
						resourceId: this.resource.id,
					};
				default:
					return null;
			}
		},
		isDisabled() {
			return (
				this.disabled ||
				(!this.tabIsVisible(this.tab) && !this.isManager) ||
				(!this.isManager && !this.field.entrantWriteAccess) ||
				!this.attachedToCategory(this.field) ||
				(this.configurationMode && this.field.dimmed) ||
				(this.configurationMode && this.field.type === 'file') ||
				this.locks.lockedOnSubmission ||
				this.locks.ineligible ||
				(!this.hasManagerRole && this.locks.readOnly) ||
				(!this.hasManagerRole && this.locks.seasonNotActive) ||
				(this.tabEligibilityReadOnly(this.tab) && !this.isEligible) ||
				this.tab.readonly
			);
		},
		enableMarkdown() {
			return this.field.enableMarkdown && this.field.resource === 'Entries';
		},
		name() {
			return (this.field.label || '').toLowerCase();
		},
		...mapState('entryForm', ['isManager', 'locks', 'hasManagerRole']),
		...mapState('entryFormApi', ['errorBag']),
		...mapState('entryFormConfiguration', ['configurationMode']),
		...mapState('global', ['country', 'phoneWithLeadingZeros']),
		...mapGetters('entryForm', {
			foreignId: 'entryId',
			selectedTab: 'selectedTab',
			tabIsVisible: 'tabIsVisible',
			attachedToCategory: 'attachedToCategory',
		}),
	},
	watch: {
		instantErrors(val) {
			this.storeFieldErrors({
				key: this.resource
					? 'field-' + this.resource + '-' + this.resource.slug + '-' + this.field.slug
					: 'field-' + this.field.slug,
				errors: val,
				tab: this.tab.id,
			});
		},
	},
	methods: {
		...mapMutations('entryForm', ['setUploaderStatus', 'storeFieldErrors', 'setFormEdited']),
		...mapMutations('entryFormApi', ['removeFromErrorBag']),
		onUploading(uploaderId, isUploading) {
			this.setUploaderStatus({ uploaderId, isUploading });
		},
		clearApiErrors() {
			this.removeFromErrorBag(this.apiError);
		},
		onInput(value, clearApiErrors = true) {
			this.setFormEdited(true);
			if (clearApiErrors) {
				this.clearApiErrors();
			}
		},
		isMyError(error) {
			switch (this.field.resource) {
				case 'Attachments':
					return (
						error.type === 'AttachmentField' &&
						error.fieldSlug === this.field.slug &&
						String(error.fileId) === String(this.resource.fileId)
					);
				case 'Contributors':
					return (
						error.type === 'ContributorField' &&
						error.fieldSlug === this.field.slug &&
						String(error.resourceId) === String(this.resource.id)
					);
				case 'Referees':
					return (
						error.type === 'RefereeField' &&
						error.fieldSlug === this.field.slug &&
						String(error.resourceId) === String(this.resource.id)
					);
				case 'Entries':
					return error.type === 'EntryField' && error.fieldSlug === this.field.slug;
				default:
					return false;
			}
		},
	},
};
</script>
