<template>
	<button class="btn-link mbs mrn" @click="copyToClipboard">
		<slot />
	</button>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import tectoastr from 'tectoastr';

export default defineComponent({
	name: 'Copy<PERSON>utton',
	props: {
		contentElementId: {
			type: String,
			required: true,
		},
		successMessage: {
			type: String,
			required: true,
		},
	},
	setup(props) {
		const copyToClipboard = async () => {
			const contentElement = document.getElementById(props.contentElementId);
			if (!contentElement) return;

			await navigator.clipboard.write([
				new ClipboardItem({
					'text/html': new Blob([contentElement.innerHTML], { type: 'text/html' }),
					'text/plain': new Blob([contentElement.textContent || ''], { type: 'text/plain' }),
				}),
			]);

			tectoastr.success(props.successMessage);
		};

		return {
			copyToClipboard,
		};
	},
});
</script>
