<template>
	<div class="input-group upload-wrapper">
		<uploader
			v-if="uploaderOptions"
			ref="uploader"
			:file-limit="1"
			:files="files"
			:border="true"
			:language="language"
			:field-required="true"
			:options="uploaderOptions.clarify()"
			:button-label="lang.get('files.buttons.single')"
			@uploaded="uploaded"
		/>
		<input v-if="fileToken" v-model="fileToken" type="hidden" name="fileToken[]" />
	</div>
</template>

<script lang="ts">
import { defineComponent } from 'vue';
import Uploader from '@/lib/components/Uploader/Uploader.vue';
import { wiredUploaderController } from '@/modules/document-templates/components/Uploader/WiredUploader.controller.ts';

export default defineComponent({
	name: 'WiredUploader',
	components: {
		Uploader,
	},
	props: {
		uploaderOptions: {
			type: String,
			required: true,
		},
		documentTemplate: {
			type: Object,
			required: true,
		},
		language: {
			type: String,
			required: true,
		},
	},
	setup: wiredUploaderController,
});
</script>

<style lang="scss" scoped>
.upload-wrapper {
	margin-bottom: 5px;

	&.input-group {
		width: 100%;
		margin-bottom: 0;
	}
}

.multilingual {
	.action-card {
		border-left-width: 0 !important;
	}
}
</style>
