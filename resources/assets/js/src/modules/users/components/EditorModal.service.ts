import * as Pintura from '@pqina/pintura';
import * as ThemeUtils from '@/domain/utils/ThemeUtils';
import { vueData } from '@/domain/services/VueData';
import { Rect, Shape, Size, Vector } from '@pqina/pintura';

type CanvasShapes = {
	decorationShapes: Shape[];
	annotationShapes: Shape[];
	interfaceShapes: Shape[];
	frameShapes: Shape;
};

type CanvasState = {
	annotationShapesDirty: boolean;
	backgroundColor: number[];
	blendShapesDirty: boolean;
	decorationShapesDirty: boolean;
	foregroundColor: number[];
	frameShapesDirty: boolean;
	images: {
		backgroundColor: number[];
		colorMatrix: number[];
		convolutionMatrix: number[];
		data: ImageBitmap | ImageData;
		gamma: number;
		opacity: number;
		origin: Vector;
		resize: number;
		rotation: { x: number; y: number; z: number };
		scale: number;
		size: Size;
		translation: Vector;
		vignette: number;
	};
	isInteracting: boolean;
	isInteractingFraction: number;
	lineColor: number[];
	opacity: number;
	rootRect: Rect;
	rotation: { x: number; y: number; z: number };
	scale: number;
	selectionRect: Rect;
	size: Size;
	stageRect: Rect;
	utilVisibility: {
		annotate: number;
		crop: number;
		decorate: number;
		filter: number;
		finetune: number;
		frame: number;
		redact: number;
		resize: number;
	};
};

const willRenderCanvas = (shapes: CanvasShapes, state: CanvasState) => {
	const { utilVisibility, selectionRect, lineColor, backgroundColor } = state;

	// Exit if crop utils is not visible
	if (utilVisibility.crop <= 0) return shapes;

	// Get variable shortcuts to the crop selection rect
	const { x, y, width, height } = selectionRect;

	return {
		// Copy all props from current shapes
		...shapes,

		// Now we add an inverted ellipse shape to the interface shapes array
		interfaceShapes: [
			{
				x: x + width * 0.5,
				y: y + height * 0.5,
				rx: width * 0.5,
				ry: height * 0.5,
				opacity: utilVisibility.crop,
				inverted: true,
				backgroundColor: [...backgroundColor, 0.8],
				strokeWidth: 1,
				strokeColor: [...lineColor],
			},
			// Spread all existing interface shapes onto the array
			...shapes.interfaceShapes,
		],
	};
};

const globalLanguage = vueData.language.locale;

const language = [
	'de_DE',
	'en_GB',
	'es_ES',
	'fr_FR',
	'hi_IN',
	'it_IT',
	'nb_NO',
	'nl_NL',
	'ru_RU',
	'sv_SE',
	'zh_CN',
].includes(globalLanguage)
	? globalLanguage
	: 'en_GB';

const locale = require(`@pqina/pintura/locale/${language}/index.js`).default;

const editorProps = {
	...Pintura.getEditorDefaults({
		locale,
	}),
	imageCropAspectRatio: 1,
	imageCropMinSize: {
		width: 128,
		height: 128,
	},
	willRenderCanvas,
	markupEditorShapeStyleControls: Pintura.createMarkupEditorShapeStyleControls({
		fontFamilyOptions: [['open-sans', 'Open Sans']],
	}),
};

const useEditorStyles = async () => {
	const styles = {
		'--editor-primary-bg-color': '',
		'--editor-primary-color': '',
		'--editor-primary-hover-bg-color': '',
		'--editor-primary-hover-color': '',
	};

	styles['--editor-primary-bg-color'] = `#${await ThemeUtils.getThemeVariableValue('theme-var-primary-button')}`;
	styles['--editor-primary-color'] = `#${await ThemeUtils.getThemeVariableValue('theme-var-primary-button-text')}`;
	styles['--editor-primary-hover-bg-color'] = `#${await ThemeUtils.getThemeVariableValue(
		'theme-var-primary-button-hover'
	)}`;
	styles['--editor-primary-hover-color'] = `#${await ThemeUtils.getThemeVariableValue(
		'theme-var-primary-button-hover-text'
	)}`;

	return styles;
};

export { editorProps, useEditorStyles };
