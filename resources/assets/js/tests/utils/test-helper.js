import { createLocalVue } from '@vue/test-utils';
import validation from '@/lib/store/modules/validation';
import Vuex from 'vuex';
import obfuscate from '@/lib/obfuscate';

const localVue = createLocalVue();
localVue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    validation
  }
});

export const useValidationStore = {
  store,
  localVue
};

export const overrideObfuscatedData = data => {
  window.App.obfuscatedData = obfuscate({
    ...window.App.obfuscatedData.clarify(),
    ...data
  });
};
