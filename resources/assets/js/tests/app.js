import $ from 'jquery';
import { Composer } from '@/domain/services/Composer';
import sinon from 'sinon';
import { VueDataProvider } from '@/domain/services/VueDataProvider';

// Wrap everything in a closure in order to avoid polluting the global scope
const originalStub = sinon.stub;
sinon.stub = function (...args) {
	try {
		return originalStub.apply(this, args);
	} catch (error) {
		console.error('Sinon stub error:', error.message);
		throw error;
	}
};

const originalSpy = sinon.spy;
sinon.spy = function (...args) {
	try {
		return originalSpy.apply(this, args);
	} catch (error) {
		console.error('Sinon spy error:', error.message);
		throw error;
	}
};

window.App = (function () {
	var obfuscate = require('@/lib/obfuscate.js');
	var clarify = require('@/lib/clarify.js');

	// Initialise globals, these are primarily used to populate the global Vuex store
	const appData = {
		language: 'en_GB',
		defaultLanguage: 'en_GB',
		momentLocale: 'en-gb',
		momentDateFormat: 'YYYY-MM-DD',
		momentTimeFormat: 'HH:mm',
		timezones: obfuscate([{ id: 'UTC', name: '(GMT +00:00) UTC' }]),
		region: 'au',
		currentBreadcrumb: null,
		app: 'awardforce',
		consumer: obfuscate({}),
		pjaxSelector: obfuscate(
			'#content a:not(.ignore):not([target="_blank"]):not([data-redirector]), #navigation a:not(.ignore), .tray-content a, .pjax-anchor'
		),
		flashMessage: obfuscate({}),
		validationErrors: obfuscate({}),
		getGlobal: { clarify },
		obfuscatedData: obfuscate({
			language: {
				locale: 'en_GB',
				fallback: 'en_GB',
			},
			routes: {},
			links: {},
			features: [],
			translations: {},
			config: {},
			settings: {},
			variables: {},
		}),
	};

	VueDataProvider.bootGlobalData(appData);
	return appData;
})();

class Chargebee {
	init() {
		return this;
	}
}
window.Chargebee = new Chargebee();

// Ensuring $.event.props is always initialized as an array before Pjax runs
$.event.props = [];

Composer.boot();
