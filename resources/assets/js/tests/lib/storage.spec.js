var expect = require('chai').expect;
var Storage = require('../../src/lib/storage.js');

describe('storage when using localStorage', () => {
  it('should set/get/remove a value', () => {
    const storage = new Storage();

    storage.set('key', 'value');
    expect(localStorage.getItem('key')).to.equal('value');

    const value = storage.get('key');
    expect(value).to.equal('value');

    storage.remove('key');
    expect(localStorage.getItem('key')).to.equal(null);
  });
});

describe('storage when using cookies', () => {
  it('should set/get/remove a value', () => {
    const storage = new Storage({ preferLocalStorage: false });

    storage.set('key', 'value');
    expect(document.cookie).to.equal('key=value');

    const value = storage.get('key');
    expect(value).to.equal('value');

    storage.remove('key');
    expect(document.cookie).to.equal('');
  });
});
