import { expect } from 'chai';
import FileUploadField from '@/lib/components/Fields/FileUploadField.vue';
import sinon from 'sinon';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';

const fileId = 4421;
const loggedInUserSlug = 'loggedInUserSlug';
const userEditedSlug = 'userEditedSlug';
const localVue = createLocalVue();
localVue.use(Vuex);

const deleteFileFromEntrySpy = sinon.spy();
const setErrorBagSpy = sinon.spy();

const getStore = (userSlug) =>
	new Vuex.Store({
		modules: {
			entryForm: {
				namespaced: true,
				getters: {
					tempFile: () => (id) => id,
				},
				state: {
					locks: {
						readOnly: false,
					},
				},
				mutations: {
					deleteFileFromEntry: deleteFileFromEntrySpy,
				},
			},
			entryFormApi: {
				namespaced: true,
				mutations: {
					setErrorBag: setErrorBagSpy,
				},
			},
			global: {
				namespaced: true,
				state: {
					userSlug: userSlug,
				},
			},
			validation: {
				namespaced: true,
				state: {},
				getters: {
					validationErrors: () => [],
				},
			},
		},
	});

describe('FileUploadField', () => {
	beforeEach(() => {});
	let wrapper;
	let store;

	it('should generate delete url for user slug', () => {
		store = getStore(userEditedSlug);
		wrapper = shallowMount(FileUploadField, {
			propsData: {
				resourceSlug: loggedInUserSlug,
				field: {
					resource: 'Users',
					type: 'file',
					uploaderOptions: {
						clarify: () => ({ foreignId: 1 }),
					},
				},
			},
			store,
			localVue,
		});

		const result = wrapper.vm.deleteFileUrl(fileId);

		expect(result).to.equal(`/user/${userEditedSlug}/file/${fileId}`);
	});

	it('should generate delete url for logged id user slug', () => {
		store = getStore(null);
		wrapper = shallowMount(FileUploadField, {
			propsData: {
				resourceSlug: loggedInUserSlug,
				field: {
					resource: 'Users',
					type: 'file',
					uploaderOptions: {
						clarify: () => ({ foreignId: 1 }),
					},
				},
			},
			store,
			localVue,
		});

		const result = wrapper.vm.deleteFileUrl(fileId);

		expect(result).to.equal(`/user/${loggedInUserSlug}/file/${fileId}`);
	});

	it('can delete video caption without deleting video file', async () => {
		store = getStore(null);
		wrapper = shallowMount(FileUploadField, {
			propsData: {
				field: {
					id: 31,
					resource: 'Entries',
					type: 'file',
					file: JSON.stringify({
						id: 257,
						original: 'h.mov',
						mime: 'video/quicktime',
						caption: {
							id: 305,
							mime: 'text/vtt',
						},
					}),
					uploaderOptions: {
						clarify: () => ({ foreignId: 1 }),
					},
				},
			},
			store,
			localVue,
			mocks: {
				$http: {
					delete: () => Promise.resolve(),
				},
			},
		});

		// delete caption
		await wrapper.vm.deletedFile(305, 305, false);
		expect(deleteFileFromEntrySpy.notCalled).to.be.true;

		// delete video
		await wrapper.vm.deletedFile(257, 257, true);
		expect(deleteFileFromEntrySpy.calledOnce).to.be.true;
		expect(deleteFileFromEntrySpy.calledWith(sinon.match.any, { fieldId: 31 })).to.be.true;
	});

	it('returns correct min video length', () => {
		store = getStore(null);
		wrapper = shallowMount(FileUploadField, {
			propsData: {
				field: {
					minVideoLength: 80,
					uploaderOptions: {
						clarify: () => ({ foreignId: 1 }),
					},
				},
			},
			store,
			localVue,
		});

		expect(wrapper.vm.minVideoLength).to.equal(80);
	});

	it('returns correct max video length', () => {
		store = getStore(null);
		wrapper = shallowMount(FileUploadField, {
			propsData: {
				field: {
					maxVideoLength: 100,
					uploaderOptions: {
						clarify: () => ({ foreignId: 1 }),
					},
				},
			},
			store,
			localVue,
		});

		expect(wrapper.vm.maxVideoLength).to.equal(100);
	});

	it('returns correct image dimension constraints', () => {
		store = getStore(null);
		wrapper = shallowMount(FileUploadField, {
			propsData: {
				field: {
					imageDimensionConstraints: {
						maxWidth: 250,
						minWidth: 100,
						maxHeight: 500,
						minHeight: 400,
					},
					uploaderOptions: {
						clarify: () => ({ foreignId: 1 }),
					},
				},
			},
			store,
			localVue,
		});

		expect(wrapper.vm.imageDimensionConstraints.maxWidth).to.equal(250);
		expect(wrapper.vm.imageDimensionConstraints.minWidth).to.equal(100);
		expect(wrapper.vm.imageDimensionConstraints.maxHeight).to.equal(500);
		expect(wrapper.vm.imageDimensionConstraints.minHeight).to.equal(400);
	});

	it('set error only when an error message is present', async () => {
		store = getStore('fakeSlug');
		wrapper = shallowMount(FileUploadField, {
			propsData: {
				field: {
					maxVideoLength: 100,
					uploaderOptions: {
						clarify: () => ({ foreignId: 1 }),
					},
				},
			},
			store,
			localVue,
		});

		await wrapper.vm.onError();
		await wrapper.vm.onError(null);
		expect(setErrorBagSpy.notCalled).to.be.true;

		await wrapper.vm.onError('this is an error');
		expect(setErrorBagSpy.calledOnce).to.be.true;
		expect(
			setErrorBagSpy.calledWith(sinon.match.any, [
				{ type: 'EntryField', fieldSlug: 'undefined', message: ['this is an error'] },
			])
		).to.be.true;
	});
});
