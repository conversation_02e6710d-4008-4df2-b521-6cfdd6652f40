import { expect } from 'chai';
import { features } from '@/services/global/features.interface';
import FileUpload from '@/lib/components/Uploader/FileUpload';
import ProcessingStatus from '@/lib/components/Uploader/ProcessingStatus.js';
import { shallowMount } from '@vue/test-utils';
import sinon from 'sinon';
import Status from '@/lib/components/Uploader/Status.js';
import Uploader from '@/lib/components/Uploader/Uploader';

const lang = { get: () => '', choice: () => '' };
const applicationLinks = { get: () => '' };
const featuresService = {
	get: () => '',
	features: [
		{
			feature: 'transcoding',
			enabled: true,
		},
	],
	enabled: function () {
		return true;
	},
	disabled: function () {
		return false;
	},
};

// eslint-disable-next-line @typescript-eslint/naming-convention
const uploaderFactory = (mocks = {}) => {
	const uploadedFiles = [
		{
			id: 'Id1',
			remoteId: 1001,
			name: 'temp/PrefixId1',
			original: '1.jpg',
			size: 2000,
			mime: 'image/jpg',
			loaded: 0,
			percent: 0,
			status: Status.QUEUED,
		},
		{
			id: 'Id2',
			remoteId: 1002,
			name: 'temp/PrefixId2',
			original: '2.jpg',
			size: 5000,
			mime: 'image/jpg',
			loaded: 0,
			percent: 0,
			status: Status.QUEUED,
		},
		{
			id: 'Id3',
			remoteId: 1003,
			name: 'temp/PrefixId3',
			original: '3.jpg',
			size: 9000,
			mime: 'image/jpg',
			loaded: 0,
			percent: 0,
			status: Status.COMPLETED,
		},
	];

	const uploader = shallowMount(Uploader, {
		provide: { lang, featuresService, applicationLinks },
		propsData: {
			options: {
				s3: {},
			},
		},
		data() {
			return {
				uploader: {},
				uploadedFiles: uploadedFiles,
				interval: {},
				fileView: () => FileUpload,
			};
		},
		mocks: mocks,
	});

	return uploader;
};

describe('Uploader', () => {
	let mountUploader;
	let featuresStub;

	beforeEach(() => {
		mountUploader = sinon.spy(Uploader.methods, 'mountUploader');
		featuresStub = sinon.stub(features.enabled, 'has');
	});

	afterEach(() => {
		sinon.restore();
	});

	it('gets/sets file properties', () => {
		const uploader = uploaderFactory();

		expect(uploader.vm.getFileProperty('Id2', 'loaded')).to.equal(0);

		uploader.vm.setFileProperty('Id2', 'loaded', 2500);
		expect(uploader.vm.getFileProperty('Id2', 'loaded')).to.equal(2500);
	});

	it('sets status', () => {
		const uploader = uploaderFactory();

		uploader.vm.setStatus('Id1', Status.FAILED, 'Something went wrong');

		const file = uploader.vm.uploadedFiles.find((f) => f.id === 'Id1');

		expect(file.status).to.equal(Status.FAILED);
		expect(file.statusMessage).to.equal('Something went wrong');
	});

	it('forgets file', () => {
		const uploader = uploaderFactory();

		uploader.vm.forgetFile('Id3');

		expect(uploader.vm.uploadedFiles.length).to.equal(2);
		expect(uploader.vm.uploadedFiles.find((f) => f.id === 'Id3')).to.be.undefined;
	});

	it('loads existing files', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			uploadedFiles: [],
		});

		await uploader.setProps({
			files: [
				{
					id: 12,
					resource: 'File field-12',
					resourceId: 12,
					token: 'qoFhOkQ8lxULKeME',
					file: 'files/W/V/g/c/S/t/IWfZa3wAHj/file.mp4',
					original: 'original.mp4',
					size: 1055736,
					mime: 'video/mp4',
					status: 'ok',
					statusMessage: null,
					transcodingStatus: 'completed',
					url: 'file-url',
				},
			],
		});

		uploader.vm.loadExistingFiles();

		expect(uploader.vm.uploadedFiles.length).to.equal(1);
		expect(uploader.vm.uploadedFiles[0].loaded).to.equal(1055736);
		expect(uploader.vm.uploadedFiles[0].name).to.be.null;
		expect(uploader.vm.uploadedFiles[0].percent).to.equal(100);
		expect(uploader.vm.uploadedFiles[0].remoteId).to.equal(12);
		expect(uploader.vm.uploadedFiles[0].status).to.equal(Status.COMPLETED);
		expect(uploader.vm.uploadedFiles[0].url).to.equal('file-url');
	});

	it('returns complete file tokens', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({ uploadedFiles: [{ status: Status.FAILED, token: 'yTwuy4NSgb6b4GzX' }] });
		expect(uploader.vm.completeFileTokens).to.equal('');

		await uploader.setData({ uploadedFiles: [{ status: Status.COMPLETED, token: 'GPdHkjLQQQB7RBHF' }] });
		expect(uploader.vm.completeFileTokens).to.equal('GPdHkjLQQQB7RBHF');

		await uploader.setData({
			uploadedFiles: [
				{ status: Status.COMPLETED, token: 'bs5QknroJXCyVB0j' },
				{ status: Status.COMPLETED, token: 'kN12jx2MwT5LtmQa' },
			],
		});
		expect(uploader.vm.completeFileTokens).to.equal('["bs5QknroJXCyVB0j","kN12jx2MwT5LtmQa"]');
	});

	it('overrides uploader settings before chunk upload', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			uploader: {
				settings: {
					multipart_params: {
						key: null,
					},
				},
			},
		});

		uploader.vm.onBeforeChunkUpload({}, { id: 'Id1' }, { chunk: 5 });

		expect(uploader.vm.uploader.settings.multipart_params.key).to.equal('temp/PrefixId1-5');
	});

	it('updates upload progress', () => {
		const uploader = uploaderFactory();

		uploader.vm.onUploadProgress({}, { id: 'Id1', loaded: 256, percent: 50 });

		const uploadedFile = uploader.vm.uploadedFiles.find((file) => file.id === 'Id1');

		expect(uploadedFile.loaded).to.equal(256);
		expect(uploadedFile.percent).to.equal(50);
	});

	it('handles file upload', async () => {
		const response = Promise.resolve({
			data: {
				token: 'SFtSkdEYLvVHkQ6Q',
				file: 3001,
			},
		});

		const uploader = uploaderFactory({
			$http: {
				post: () => response,
			},
		});

		await uploader.setProps({
			options: {
				routes: {
					upload: 'https://af.test/file/upload',
				},
			},
		});

		uploader.vm.onFileUploaded({}, { id: 'Id1' });

		return response.then(() => {
			const uploadedFile = uploader.vm.uploadedFiles.find((file) => file.id === 'Id1');

			expect(uploadedFile.token).to.equal('SFtSkdEYLvVHkQ6Q');
			expect(uploadedFile.remoteId).to.equal(3001);
			expect(uploader.vm.interval[uploadedFile.id]).to.not.be.null;

			window.clearInterval(uploader.vm.interval[uploadedFile.id]);
		});
	});

	it('processes a file', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			uploadedFiles: [{ id: 'Id2001', remoteId: 2001, status: Status.PROCESSING, statusMessage: null, videoHeight: 400 }],
		});

		uploader.vm.processFile(
			2001,
			ProcessingStatus.OK,
			null,
			'Status message',
			'https://af.local/d/2001',
			'https://af.local/image-url'
		);

		const processedFile = uploader.vm.uploadedFiles.find((file) => file.remoteId === 2001);

		expect(processedFile.status).to.equal(Status.COMPLETED);
		expect(processedFile.statusMessage).to.equal('Status message');
		expect(processedFile.url).to.equal('https://af.local/d/2001');
		expect(processedFile.image).to.equal('https://af.local/image-url');
		expect(processedFile.videoHeight).to.equal(400);

		expect(Object.keys(uploader.emitted())).to.include('uploaded');
	});

	it('should update caption remoteId of parent file if processed file is a caption', async () => {
		// updating the caption remoteId of parent the user is able to delete it immediately
		const uploader = uploaderFactory();

		await uploader.setData({
			uploadedFiles: [{ id: 'dqFhOkQ8lxULKeME', remoteId: 2003, status: Status.PROCESSING }],
			parentFile: { id: 2002, remoteId: 2002, status: Status.COMPLETED, statusMessage: null, caption: null },
			captionMode: true,
			filesAdded: 1,
		});

		uploader.vm.processFile(2003, ProcessingStatus.OK);

		const processedFile = uploader.vm.parentFile;
		expect(processedFile.caption.id).to.equal(2003);
	});

	it('retries transcoding', async () => {
		const response = Promise.resolve();
		const spy = sinon.spy();

		const uploader = uploaderFactory({
			$http: {
				post: (url) => {
					spy(url);
					return response;
				},
			},
		});

		await uploader.setProps({
			options: {
				routes: {
					retry: 'https://af.test/transcode/:slug/retry',
				},
			},
		});

		uploader.vm.retryTranscoding({ id: '123' });

		return response.then(() => {
			expect(spy.withArgs('https://af.test/transcode/123/retry').calledOnce).to.be.true;
		});
	});

	it('emits uploading event', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			uploadedFiles: [
				{ status: Status.UPLOADING, token: 'dFVbj28FsAmB1fAl' },
				{ status: Status.COMPLETED, token: 'yTwuy4NSgb6b4GzX' },
			],
		});

		uploader.vm.emitUploadingEvent();
		expect(Object.keys(uploader.emitted())).to.include('uploading');
	});

	it('does not emit uploading event', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			uploadedFiles: [
				{ status: Status.COMPLETED, token: 'dFVbj28FsAmB1fAl' },
				{ status: Status.COMPLETED, token: 'yTwuy4NSgb6b4GzX' },
			],
		});

		uploader.vm.emitUploadingEvent();
		expect(Object.keys(uploader.emitted())).to.not.include('uploading');
	});

	it('shows caption uploader', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			uploadedFiles: [],
		});

		await uploader.setProps({
			files: [
				{
					id: 12,
					resource: 'Attachment',
					resourceId: 12,
					token: 'qoFhOkQ8lxULKeME',
					file: 'files/W/V/g/c/S/t/IWfZa3wAHj/file.mp4',
					original: 'original.mp4',
					size: 1055736,
					mime: 'video/mp4',
					status: 'ok',
					statusMessage: null,
					transcodingStatus: 'completed',
					url: 'file-url',
					caption: {
						id: 13,
						resource: 'Caption',
						resourceId: 12,
						token: 'qoFhOkQ8lxULKeME',
						file: 'files/W/V/g/c/S/t/IWfZa3wAHj/file.vtt',
						original: 'original.vtt',
						size: 100,
						mime: 'text/vtt',
						status: 'ok',
						statusMessage: null,
						transcodingStatus: 'completed',
						url: 'caption-url',
					},
				},
			],
		});

		const button = uploader.find('.caption-uploader-button');
		expect(button.exists()).to.be.true;
	});

	it('handles file limit', async () => {
		const uploader = uploaderFactory();
		const files = [
			{
				id: 13,
				resource: 'File field-13',
				resourceId: 13,
				token: 'qoFhOkQ8lxULKeME',
				file: 'files/W/V/g/c/S/t/IWfZa3wAHj/file.mp4',
				original: 'original.mp4',
				size: 1055736,
				mime: 'video/mp4',
				status: 'ok',
				statusMessage: null,
				transcodingStatus: 'completed',
				url: 'file-url',
			},
			{
				id: 14,
				resource: 'File field-14',
				resourceId: 14,
				token: 'qoFhOkQ8lxULKeME',
				file: 'files/W/V/g/c/S/t/IWfZa3wAHj/file.mp4',
				original: 'original.mp4',
				size: 1055736,
				mime: 'video/mp4',
				status: 'ok',
				statusMessage: null,
				transcodingStatus: 'completed',
				url: 'file-url',
			},
		];
		await uploader.setProps({
			fileLimit: 4,
			files: files,
		});
		await uploader.setData({
			multiSelection: true,
			filesAdded: 0,
		});

		expect(uploader.vm.onFilesAdded([], files)).to.be.false;
		expect(uploader.vm.filesAdded).to.equal(0);

		// Singe file upload
		await uploader.setData({
			multiSelection: false,
		});
		class MockUploader {
			constructor(files) {
				this.files = files;
			}
			slice() {
				return true;
			}
			start() {
				return true;
			}
			removeFile(id) {
				this.files = this.files.filter((file) => file.id !== id);
			}
		}
		uploader.vm.onFilesAdded(new MockUploader(files), files);
		expect(uploader.vm.filesAdded).to.equal(1);
	});

	it('retries file upload that failed on processing', async () => {
		const response = Promise.resolve();
		const spy = sinon.spy();

		const uploader = uploaderFactory({
			$http: {
				post: (url) => {
					spy(url);
					return response;
				},
			},
		});

		await uploader.setProps({
			options: {
				routes: {
					upload: 'https://af.test/file/upload',
				},
			},
		});
		uploader.vm.uploadedFiles[0].statusMessage = 'failed';
		uploader.vm.retryUpload(uploader.vm.uploadedFiles[0]);

		return response.then(() => {
			expect(spy.withArgs('https://af.test/file/upload').calledOnce).to.be.true;
		});
	});

	/**
	 * ToDo: The call to `uploader.vm.initPlupload();` causes the entire test suite to stop before all tests are completed.
	 * We need to extract Plupload into its own service so we can mock it properly.
	 */
	it.skip('calls mountUploader on mounted when tab is active', () => {
		shallowMount(Uploader, {
			provide: { lang, featuresService, applicationLinks },
			propsData: { options: { s3: {} } },
			data() {
				return { uploader: false };
			},
		});

		expect(mountUploader.calledOnce).to.be.true;
	});

	it('it does not call mountUploader on mounted when tab is inactive', () => {
		shallowMount(Uploader, {
			provide: { lang, featuresService, applicationLinks },
			propsData: {
				inActiveTab: false,
				options: { s3: {} },
			},
			data() {
				return { uploader: false };
			},
		});

		expect(mountUploader.calledOnce).to.be.false;
	});

	it('it does not call mountUploader on mounted when uploader is already loaded', () => {
		shallowMount(Uploader, {
			provide: { lang, featuresService, applicationLinks },
			propsData: {
				options: { s3: {} },
			},
			data() {
				return { uploader: true };
			},
		});

		expect(mountUploader.calledOnce).to.be.false;
	});

	it('it does not call mountUploader on mounted when options are missing', () => {
		shallowMount(Uploader, {
			provide: { lang, featuresService, applicationLinks },
			data() {
				return { uploader: false };
			},
		});

		expect(mountUploader.calledOnce).to.be.false;
	});

	/**
	 * ToDo: The call to `uploader.vm.initPlupload();` causes the entire test suite to stop before all tests are completed.
	 * We need to extract Plupload into its own service so we can mock it properly.
	 */
	it.skip('adds min_video_length filter when minVideoLength prop is greater than 0', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			minVideoLength: 10,
		});

		uploader.vm.initPlupload();

		expect(uploader.vm.uploader.getOption('filters').min_video_length).to.equal(10);
	});
	/**
	 * ToDo: The call to `uploader.vm.initPlupload();` causes the entire test suite to stop before all tests are completed.
	 * We need to extract Plupload into its own service so we can mock it properly.
	 */
	it.skip('adds max_video_length filter when maxVideoLength prop is greater than 0', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			maxVideoLength: 100,
		});

		uploader.vm.initPlupload();

		expect(uploader.vm.uploader.getOption('filters').max_video_length).to.equal(100);
	});
	/**
	 * ToDo: The call to `uploader.vm.initPlupload();` causes the entire test suite to stop before all tests are completed.
	 * We need to extract Plupload into its own service so we can mock it properly.
	 */
	it.skip('adds image filter when image dimension constraints are is greater than 0', async () => {
		const uploader = uploaderFactory();

		await uploader.setData({
			imageDimensionConstraints: {
				maxWidth: 500,
				minWidth: 400,
				maxHeight: 300,
				minHeight: 200,
			},
		});

		uploader.vm.initPlupload();

		expect(uploader.vm.uploader.getOption('filters').max_image_width).to.equal(500);
		expect(uploader.vm.uploader.getOption('filters').min_image_width).to.equal(400);
		expect(uploader.vm.uploader.getOption('filters').max_image_height).to.equal(300);
		expect(uploader.vm.uploader.getOption('filters').min_image_height).to.equal(200);
	});

	it('should show error when handleErrors is true and there are fileErrors', async () => {
		const uploader = uploaderFactory();
		await uploader.setProps({
			handleErrors: true,
		});
		await uploader.setData({
			fileErrors: ['error1', 'error2'],
		});

		expect(uploader.vm.showErrors).to.be.true;
	});

	it('sets error message when setErrors is called with valid file and error code', async () => {
		const errorCode = 101;
		const uploader = uploaderFactory();
		await uploader.setProps({
			options: {
				s3: {},
				errors: {
					101: 'File is too large',
				},
			},
		});
		uploader.vm.setErrors(errorCode);
		expect(uploader.vm.fileErrors[0]).to.equal('File is too large');
	});

	it('does not set error message when setErrors is called with invalid error code', async () => {
		const errorCode = 102;
		const uploader = uploaderFactory();
		await uploader.setProps({
			options: {
				s3: {},
				errors: {
					101: 'File is too large',
				},
			},
		});
		uploader.vm.setErrors(errorCode);
		expect(uploader.vm.fileErrors.length).to.equal(0);
	});

	it('resets errors when resetErrors is called', () => {
		const uploader = uploaderFactory();
		uploader.vm.fileErrors = ['error1', 'error2'];
		uploader.vm.resetErrors();
		expect(uploader.vm.fileErrors.length).to.equal(0);
	});

	it('resets errors when onBrowse is called', async () => {
		const uploader = uploaderFactory();
		await uploader.setData({
			fileErrors: ['error1', 'error2'],
		});
		uploader.vm.onBrowse();
		expect(uploader.vm.fileErrors.length).to.equal(0);
	});

	it('resets errors when dragEnter is called with event targeting dnd-overlay', async () => {
		const uploader = uploaderFactory();
		const event = {
			target: {
				classList: {
					contains: () => true,
				},
			},
		};

		uploader.vm.fileErrors = ['error1', 'error2'];
		uploader.vm.dragEnter(event);

		expect(uploader.vm.fileErrors.length).to.be.equal(0);
	});

	it('updates caption on FileTranscription', async () => {
		featuresStub.returns(true);

		const uploader = uploaderFactory();
		await uploader.setData({
			uploadedFiles: [],
		});
		const files = [
			{
				id: 14,
				resource: 'File field-14',
				resourceId: 14,
				token: 'qoFhOkQ8lxULKeME',
				file: 'files/W/V/g/c/S/t/IWfZa3wAHj/file.mp4',
				original: 'original.mp4',
				size: 1055736,
				mime: 'video/mp4',
				status: 'ok',
				statusMessage: null,
				transcodingStatus: 'completed',
				url: 'file-url',
				caption: null,
			},
		];
		await uploader.setProps({
			fileLimit: 4,
			files: files,
		});

		uploader.vm.onFileTranscription({}, { data: { completed: true, file: { id: 14 }, caption: { id: 15 } } });

		expect(uploader.vm.uploadedFiles[0].caption.id).to.equal(15);
	});

	it('should not update caption on FileTranscription when is not completed', async () => {
		featuresStub.returns(true);

		const uploader = uploaderFactory();
		await uploader.setData({
			uploadedFiles: [],
		});
		const files = [
			{
				id: 14,
				resource: 'File field-14',
				resourceId: 14,
				token: 'qoFhOkQ8lxULKeME',
				file: 'files/W/V/g/c/S/t/IWfZa3wAHj/file.mp4',
				original: 'original.mp4',
				size: 1055736,
				mime: 'video/mp4',
				status: 'ok',
				statusMessage: null,
				transcodingStatus: 'completed',
				url: 'file-url',
				caption: null,
			},
		];
		await uploader.setProps({
			fileLimit: 4,
			files: files,
		});

		uploader.vm.onFileTranscription({}, { data: { completed: false, file: { id: 14 }, caption: { id: 15 } } });

		expect(uploader.vm.uploadedFiles[0].caption).to.equal(null);
	});

	it('should not update caption to a different file', async () => {
		featuresStub.returns(true);

		const uploader = uploaderFactory();
		await uploader.setData({
			uploadedFiles: [],
		});
		const files = [
			{
				id: 14,
				resource: 'File field-14',
				resourceId: 14,
				token: 'qoFhOkQ8lxULKeME',
				file: 'files/W/V/g/c/S/t/IWfZa3wAHj/file.mp4',
				original: 'original.mp4',
				size: 1055736,
				mime: 'video/mp4',
				status: 'ok',
				statusMessage: null,
				transcodingStatus: 'completed',
				url: 'file-url',
				caption: null,
			},
		];
		await uploader.setProps({
			fileLimit: 4,
			files: files,
		});

		uploader.vm.onFileTranscription({}, { data: { completed: false, file: { id: 12 }, caption: { id: 15 } } });

		expect(uploader.vm.uploadedFiles[0].caption).to.equal(null);
	});
});
