import { shallowMount } from '@vue/test-utils';
import { expect } from 'chai';
import PasswordResetForm from '@/modules/clear/component/PasswordResetForm.vue';
import sinon from 'sinon';
const urlSpy = sinon.spy();
const dataSpy = sinon.spy();

describe('PasswordResetForm', () => {
  it('handles button enabled or disabled', () => {
    const wrapper = shallowMount(PasswordResetForm, {
      propsData: {
        formAction: '/reset',
        token: 'test'
      }
    });

    expect(!!wrapper.vm.buttonEnabled).to.be.false;
    wrapper.vm.password = 'foo';
    expect(!!wrapper.vm.buttonEnabled).to.be.true;
  });

  it('handles submit', () => {
    const wrapper = shallowMount(PasswordResetForm, {
      propsData: {
        formAction: '/reset',
        token: 'test'
      },
      mocks: {
        $http: {
          post: (url, params) => {
            urlSpy(url);
            dataSpy(params);
          }
        }
      }
    });
    wrapper.vm.password = 'test';
    wrapper.vm.submit();

    expect(urlSpy.calledWith(wrapper.vm.formAction)).to.equal(true);
    expect(dataSpy.calledWith({ password: 'test', token: 'test' })).to.equal(true);
  });
});
