import entryFormConfiguration from '@/lib/store/modules/entry-form-configuration';
import { expect } from 'chai';
import { fieldTypes } from '@/lib/components/Fields';
import formulaField from '@/lib/store/modules/formula-field';
import global from '@/lib/store/modules/global';
import Language from '@/modules/provisioning/components/Language.vue';
import { overrideObfuscatedData } from '@/../tests/utils/test-helper';
import QuickRegister from '@/modules/clear/component/QuickRegister.vue';
import RegistrationEmail from '@/lib/components/Authentication/RegistrationEmail';
import RegistrationForm from '@/modules/clear/component/RegistrationForm.vue';
import RegistrationMobile from '@/lib/components/Clear/RegistrationMobile.vue';
import sinon from 'sinon';
import validation from '@/lib/store/modules/validation';
import Vuex from 'vuex';
import { createLocalVue, mount, shallowMount } from '@vue/test-utils';

const urlSpy = sinon.spy();
const dataSpy = sinon.spy();

const lang = { get: (v) => v };
const localVue = createLocalVue();
localVue.use(Vuex);
const consumer = {
	isNew: true,
	firstName: 'Test',
	lastName: 'User',
	email: '<EMAIL>',
	mobile: '+306999999999',
	preferredContact: '<EMAIL>',
};

const makeStore = (consumer) =>
	new Vuex.Store({
		modules: {
			authentication: {
				namespaced: true,
				state: {
					consumer: consumer,
				},
				getters: {
					consumer: (state) => state.consumer,
				},
			},
			global: {
				namespaced: true,
				state: {
					...global.state,
					supportedLanguages: ['en_GB', 'el_GR'],
					preferredLanguage: 'en_GB',
					defaultLanguage: 'en_GB',
				},
			},
			validation,
			entryFormConfiguration,
			formulaField,
		},
	});

const shallowFactory = (propsData = {}, store = {}, stubs = {}) =>
	shallowMount(RegistrationForm, {
		propsData: propsData,
		provide: { lang },
		store: store.authentication || makeStore(consumer),
		localVue,
		mocks: {
			$http: {
				post: (url, params) => {
					urlSpy(url);
					dataSpy(params);
				},
			},
		},
		stubs: stubs,
	});

const mountFactory = (propsData = {}, stubs = {}) =>
	mount(RegistrationForm, {
		propsData,
		provide: { lang },
		store: makeStore(consumer),
		localVue,
		mocks: {
			$http: {
				post: (url, params) => {
					urlSpy(url);
					dataSpy(params);
				},
			},
		},
		stubs: stubs,
	});

describe('RegistrationForm', () => {
	it('shows content block', async () => {
		const wrapper = shallowFactory(
			{
				contentBlock: {},
			},
			{},
			{
				ConsumerContentBlock: {
					template: '<span>The consumer content block</span>',
				},
			}
		);
		expect(wrapper.html()).to.not.contain('The consumer content block');

		await wrapper.setProps({
			contentBlock: {
				id: 1,
				key: 'test',
			},
		});

		expect(wrapper.html()).to.contain('The consumer content block');
	});

	it('shows form', async () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': false,
			},
		});
		let wrapper = shallowFactory();

		expect(wrapper.html()).to.not.contain('<form');

		await wrapper.setProps({
			invitationToken: 'test',
		});

		expect(wrapper.html()).to.contain('<form');

		await wrapper.setProps({
			invitationToken: '',
		});

		expect(wrapper.html()).to.not.contain('<form');
	});

	it('shows quick register if the user is not new', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': '1',
			},
		});
		let wrapper = shallowFactory({}, makeStore(consumer));
		expect(wrapper.findComponent(QuickRegister).exists()).to.be.false;

		consumer.isNew = false;
		wrapper = shallowFactory({}, makeStore(consumer));
		expect(wrapper.findComponent(QuickRegister).exists()).to.be.true;
		consumer.isNew = true;
	});

	it('shows the registration email component if the consumer has no email', () => {
		let wrapper = shallowFactory();
		expect(wrapper.findComponent(RegistrationEmail).exists()).to.be.false;
		expect(wrapper.html()).to.contain('<input id="registerEmail"');

		consumer.email = '';
		wrapper = shallowFactory({}, makeStore(consumer));
		expect(wrapper.findComponent(RegistrationEmail).exists()).to.be.true;
		expect(wrapper.html()).to.not.contain('<input id="registerEmail"');
	});

	it('shows the registration mobile component when enabled', async () => {
		// Consumer has mobile - component disabled
		let wrapper = shallowFactory();
		// expect(wrapper.html()).to.not.contain('<registrationmobile-stub');
		expect(wrapper.findComponent(RegistrationMobile).exists()).to.be.false;

		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': '1',
				'enable-mobile-registrations': '1',
			},
		});
		// Consumer has no mobile and mobile registrations are enabled - component enabled
		wrapper = shallowFactory({}, makeStore(consumer));
		expect(wrapper.findComponent(RegistrationMobile).exists()).to.be.true;

		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': '1',
				'mobile-required': '1',
			},
		});
		// Consumer has no mobile and mobile registrations are enabled - component enabled
		wrapper = shallowFactory({}, makeStore(consumer));
		expect(wrapper.findComponent(RegistrationMobile).exists()).to.be.true;
	});

	it('renders user fields', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': '1',
			},
		});

		const types = Object.keys(fieldTypes).filter((key) => key !== 'file' && key !== 'formula' && key !== 'content');

		const wrapper = mountFactory({
			fields: types.map((key) => ({
				slug: key + 'Slug',
				type: key,
				options: [1],
				mode: 'date',
			})),
		});
		types.forEach((key) => {
			expect(wrapper.html()).to.contain(`id="values.${key}Slug-label"`);
		});
	});

	it('renders correct agreements', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': '1',
				'require-agreement-to-terms': '1',
				'require-consent-to-notifications-and-broadcasts': '1',
			},
		});

		const wrapper = mountFactory({
			agreements: [
				{
					name: 'agreement1',
					label: 'agreement1-label',
					checked: true,
					setting: 'require-agreement-to-terms',
				},
				{
					name: 'agreement2',
					label: 'agreement2-label',
					checked: false,
					setting: 'require-consent-to-notifications-and-broadcasts',
				},
				{
					name: 'agreement3',
					label: 'agreement3-label',
					checked: false,
					setting: 'invalid-setting',
				},
			],
		});

		expect(wrapper.html()).to.contain('<input id="agreement1" name="agreement1" type="checkbox"');
		expect(wrapper.html()).to.contain('<input id="agreement2" name="agreement2" type="checkbox"');
		expect(wrapper.html()).to.not.contain('<input id="agreement3" name="agreement3" type="checkbox"');
	});

	it('determines optional email correctly', () => {
		let wrapper = mountFactory();

		overrideObfuscatedData({
			settings: {
				'mobile-required': true,
			},
		});

		wrapper = mountFactory();
		expect(wrapper.vm.emailIsOptional).to.be.true;

		overrideObfuscatedData({
			settings: {
				'email-required': true,
				'mobile-required': true,
			},
		});

		wrapper = mountFactory();
		expect(wrapper.vm.emailIsOptional).to.be.false;
	});

	it('determines form class based on content block', () => {
		let wrapper = mountFactory();
		expect(wrapper.vm.formContainerClass).to.equal('col-xs-12 col-md-4 col-md-offset-4');

		wrapper = mountFactory({
			contentBlock: {
				id: 1,
				key: 'test',
			},
		});
		expect(wrapper.vm.formContainerClass).to.equal('col-xs-12 col-md-4 ');
	});

	it('submits the form', () => {
		const wrapper = mountFactory();
		wrapper.vm.submit();
		expect(urlSpy.calledWith(wrapper.vm.formAction)).to.equal(true);
		expect(
			dataSpy.calledWith({
				firstName: consumer.firstName,
				lastName: consumer.lastName,
				registerEmail: consumer.email,
				registerMobile: consumer.mobile,
				password: '',
				values: {},
				role: '',
				invitationToken: '',
				...[],
			})
		).to.equal(true);
	});

	it('should render language component if invitationToken is present', async () => {
		let wrapper = shallowFactory();
		expect(wrapper.findComponent(Language).exists()).to.equal(false);

		await wrapper.setProps({ invitationToken: 'test' });
		expect(wrapper.findComponent(Language).exists()).to.equal(true);
	});

	it('do not show the registration mobile component when consumer has mail', async () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': '1',
				'enable-mobile-registrations': '1',
			},
		});
		consumer.email = '<EMAIL>';

		let wrapper = shallowFactory({}, makeStore(consumer));
		expect(wrapper.findComponent(RegistrationMobile).exists()).to.be.false;
	});

	it('should show first name and last name inputs when consumer has no name', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': '1',
			},
		});

		consumer.firstName = '';
		consumer.lastName = '';
		consumer.isNew = false;

		const wrapper = shallowFactory({}, makeStore(consumer));
		const quickRegister = wrapper.findComponent(QuickRegister);

		expect(quickRegister.find('registration-field-stub[name="firstName"]').exists()).to.be.true;
		expect(quickRegister.find('registration-field-stub[name="lastName"]').exists()).to.be.true;
		expect(quickRegister.find('p').exists()).to.be.false;
	});

	it('should show full name in QuickRegister slot when consumer has name', () => {
		overrideObfuscatedData({
			settings: {
				'app-site-registration-open': '1',
			},
		});

		consumer.firstName = 'John';
		consumer.lastName = 'Doe';
		consumer.fullName = 'John Doe';
		consumer.isNew = false;

		const wrapper = mountFactory();
		const quickRegister = wrapper.findComponent(QuickRegister);

		expect(quickRegister.find('registration-field-stub[name="firstName"]').exists()).to.be.false;
		expect(quickRegister.find('registration-field-stub[name="lastName"]').exists()).to.be.false;
		expect(quickRegister.findAll('p').at(1).text()).to.equal('John Doe');
	});
});
