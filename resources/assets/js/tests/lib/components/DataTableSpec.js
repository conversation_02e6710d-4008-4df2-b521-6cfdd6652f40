import { shallowMount } from '@vue/test-utils';
import { expect } from 'chai';
import DataTable from '../../../src/lib/components/DataTable.vue';

describe('DataTable', () => {
  it('checks all items', done => {
    const dataTable = shallowMount(DataTable, {
      template: '<table><th></th></table>',
      propsData: {
        ids: [500, 501]
      }
    });

    expect(dataTable.vm.selected).to.deep.equal([]);
    expect(dataTable.vm.allChecked).to.be.false;

    dataTable.vm.checkAll();

    expect(dataTable.vm.selected).to.deep.equal([500, 501]);
    expect(dataTable.vm.allChecked).to.be.true;

    dataTable.vm.$nextTick(() => {
      expect(Object.keys(dataTable.emitted())).to.include('update:selected');
      done();
    });
  });
});
