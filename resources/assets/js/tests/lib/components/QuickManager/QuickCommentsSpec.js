import { shallowMount } from '@vue/test-utils';
import { expect } from  'chai';
import sinon from 'sinon';
import QuickComments from '../../../../src/lib/components/QuickManager/QuickComments.vue';

const propsData = {
  postUrl: 'https://af.test/post',
  getUrl: 'https://af.test/get',
  token: 'AxcnDJfiePOedjqwvaNu',
  labels: {}
};

describe('QuickComments', () => {
  it('initialises with default state', () => {
    const quickComments = shallowMount(QuickComments, {
      propsData: propsData,
      mocks: {
        $http: {
          get: () => Promise.resolve({ data: [] })
        }
      }
    });

    expect(quickComments.vm.modalVisible).to.be.false;
    expect(quickComments.vm.buttonVisible).to.be.false;
    expect(quickComments.vm.buttonDisabled).to.be.true;
  });

  it('loads comments when created', () => {
    const response = Promise.resolve({ data: ['commentA', 'commentB'] });
    const quickComments = shallowMount(QuickComments, {
      propsData: propsData,
      mocks: {
        $http: {
          get: () => response
        }
      }
    });

    return response.then(() => {
      expect(quickComments.vm.comments).to.deep.equal(['commentA', 'commentB']);
    });
  });

  it('saves a new comment', () => {
    const postResponse = Promise.resolve();
    const urlSpy = sinon.spy();
    const tokenSpy = sinon.spy();
    const commentSpy = sinon.spy();
    const quickComments = shallowMount(QuickComments, {
      propsData: propsData,
      mocks: {
        $http: {
          get: () => Promise.resolve({ data: [] }),
          post: (url, params) => {
            urlSpy(url);
            tokenSpy(params.token);
            commentSpy(params.comment);

            return postResponse;
          }
        }
      }
    });

    quickComments.vm.comment = 'My Comment';
    quickComments.vm.saveComment();

    return postResponse.then(() => {
      expect(urlSpy.withArgs('https://af.test/post').calledOnce).to.be.true;
      expect(tokenSpy.withArgs('AxcnDJfiePOedjqwvaNu').calledOnce).to.be.true;
      expect(commentSpy.withArgs('My Comment').calledOnce).to.be.true;

      // Clears the comment
      expect(quickComments.vm.comment).to.equal('');
    });
  });
});
