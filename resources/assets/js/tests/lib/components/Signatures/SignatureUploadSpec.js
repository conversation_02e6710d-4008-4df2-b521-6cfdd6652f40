import { expect } from 'chai';
import obfuscate from '@/lib/obfuscate';
import { shallowMount } from '@vue/test-utils';
import SignatureUpload from '@/lib/components/Signatures/SignatureUpload';
import Uploader from '@/lib/components/Uploader/Uploader';

const lang = { get: () => '', choice: () => '' };
const applicationLinks = { get: () => '' };
const featuresService = {
	get: () => '',
	features: [],
	enabled: function () {
		return true;
	},
	disabled: function () {
		return false;
	},
};

describe('SignatureUpload', () => {
	it('has file upload component', () => {
		const signatureUploader = shallowMount(SignatureUpload, {
			provide: { lang, featuresService, applicationLinks },
			propsData: {
				supportedLanguages: ['en_GB'],
				language: 'en_GB',
				uploaderOptions: obfuscate({
					resourceId: 1,
					resource: 'Contracts',
					s3: {
						url: 'https://fakeurl/',
					},
					routes: {
						upload: 'https://fakeurl/file/upload',
						download: 'https://fakeurl/file/download/',
						status: 'https://fakeurl/file/status/',
						retry: 'https://fakeurl/transcode/:slug/retry',
					},
				}),
			},
			data() {
				return {
					fileToken: null,
				};
			},
		});
		expect(signatureUploader.findAllComponents(Uploader).length).to.equal(1);
	});
});
