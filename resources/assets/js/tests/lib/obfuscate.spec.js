var expect = require('chai').expect;
var obfuscate = require('../../src/lib/obfuscate.js');

describe('obfuscate function', () => {
  it('should obfuscate a string', () => {
    const string = 'test';

    expect(obfuscate(string)).to.equal('InRlc3Qi');
  });

  it('should obfuscate an object', () => {
    const object = { 'a': 'b', 'c': 'd' };

    expect(obfuscate(object)).to.equal('eyJhIjoiYiIsImMiOiJkIn0=');
  });
});
