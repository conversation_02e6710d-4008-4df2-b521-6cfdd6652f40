import { expect } from 'chai';
// import ping from '@/common/ping';
import sinon from 'sinon';
import tectoastr from 'tectoastr';
import { useValidationStore } from '../utils/test-helper';
import { windowLocation } from '@/lib/utils';
import { errorHandler, successHandler } from '@/lib/axios-handlers';

const store = useValidationStore.store;

describe('axios-handlers success', () => {
	let windowLocationStub;

	beforeEach(() => {
		windowLocationStub = sinon.stub(windowLocation);
	});

	afterEach(() => {
		sinon.restore();
	});

	it('does not redirect if X-Location header or data redirect url is not set', () => {
		const response = {
			status: 200,
			headers: {},
			data: {},
		};

		successHandler(response, useValidationStore.store);
		expect(windowLocationStub.setHref.called).to.equal(false);
	});

	it('redirects to X-Location header if set', () => {
		const response = {
			status: 200,
			headers: {
				'X-Location': '/foo',
			},
			data: {},
		};

		successHandler(response, useValidationStore.store);
		expect(windowLocationStub.setHref.calledWith('/foo')).to.equal(true);
	});

	it('redirects to data redirect url if set', () => {
		const response = {
			status: 200,
			headers: {},
			data: {
				redirectUrl: '/foo',
			},
		};

		successHandler(response, useValidationStore.store);
		expect(windowLocationStub.setHref.calledWith('/foo')).to.equal(true);
	});
});

describe('axios-handlers error', () => {
	let commitStub;
	let toasterErrorStub;
	let toasterWarningStub;

	beforeEach(() => {
		const store = useValidationStore.store;
		commitStub = sinon.stub(store, 'commit');
		toasterErrorStub = sinon.stub(tectoastr, 'error');
		toasterWarningStub = sinon.stub(tectoastr, 'warning');
	});

	afterEach(() => {
		sinon.restore();
	});

	it('sets validation errors if status code is 422', () => {
		const error = {
			response: {
				status: 422,
				data: {
					foo: 'bar',
				},
			},
		};

		errorHandler(error, store);
		expect(commitStub.calledWith('validation/setValidationErrors', { foo: 'bar' })).to.equal(true);
	});

	it('sets form error if status code is 401', () => {
		const error = {
			response: {
				status: 401,
				data: {
					message: 'foo',
				},
			},
		};

		errorHandler(error, store);
		expect(commitStub.calledWith('validation/setFormError', 'foo')).to.equal(true);
	});

	it('the ping function is called if status code is 401 and no message is set', () => {
		const error = {
			response: {
				status: 401,
				data: {},
			},
			request: {
				responseURL: 'http://foo.com',
			},
		};

		// const pingSpy = sinon.spy(ping);

		try {
			errorHandler(error, store);
		} catch (e) {
			expect(e.message).to.equal('Oops! [http://foo.com] [401]');
		}

		// expect(pingSpy.called).to.equal(true);
		expect(commitStub.calledWith('validation/setFormError')).to.equal(false);
		expect(toasterErrorStub.calledWith()).to.equal(false);
		// pingSpy.restore();
	});

	it('shows whoops and does not set form error if status code is 500', () => {
		const error = {
			response: {
				status: 500,
				data: {
					message: 'foo',
				},
			},
			request: {
				responseURL: 'http://foo.com',
			},
		};

		try {
			errorHandler(error, store);
		} catch (e) {
			expect(e.message).to.equal('Oops! [http://foo.com] [500]');
		}

		expect(commitStub.calledWith('validation/setFormError')).to.equal(false);
	});

	it('shows whoops and does not set form error if status code is 401 and no data is set', () => {
		const error = {
			response: {
				status: 401,
			},
			request: {
				responseURL: 'http://foo.com',
			},
		};

		try {
			errorHandler(error, store);
		} catch (e) {
			expect(e.message).to.equal('Oops! [http://foo.com] [401]');
		}

		expect(commitStub.calledWith('validation/setFormError')).to.equal(false);
	});

	it('returns error on abort', () => {
		const error = {
			response: {
				status: 500,
				statusText: 'abort',
			},
		};
		const result = errorHandler(error, store);
		expect(result).to.equal(error);
	});

	it('shows warning on timeout', () => {
		const error = {
			response: {
				status: 500,
				statusText: 'timeout',
			},
		};
		errorHandler(error, store);
		expect(commitStub.calledWith('validation/setFormError')).to.equal(false);
		expect(toasterWarningStub.calledWith()).to.equal(true);
	});

	it('shows whoops and toaster if status code is >=500 excluding 503', () => {
		const error = (status) => ({
			response: { status },
			request: { responseURL: 'http://foo.com' },
		});

		for (let status = 0; status <= 599; status++) {
			try {
				errorHandler(error(status), store);
				// eslint-disable-next-line no-empty
			} catch (e) {}

			expect(toasterErrorStub.calledWith()).to.equal(status >= 500 && status !== 503);
			toasterErrorStub.reset();
		}
	});
});
