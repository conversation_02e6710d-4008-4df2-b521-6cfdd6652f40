import { shallowMount } from '@vue/test-utils';
import { expect } from 'chai';
import OverflowMenu from '@/modules/forms/list/OverflowMenu.vue';

const lang = { get: () => '' };

const form = {
	id: 1,
	slug: 'vnkQWdQw',
	name: 'Form',
	type: 'report',
};

const routes = {
	'forms.edit': '',
	'forms.settings': '',
	'forms.select': '',
	'forms.delete': '',
	'forms.undelete': '',
};

describe('OverflowMenu', () => {
	it('shows links', () => {
		const menu = shallowMount(OverflowMenu, {
			provide: { lang },
			propsData: {
				form: Object.assign(form, { deletedAt: null }),
				canDelete: true,
				canUpdate: true,
				deletable: true,
				routes: routes,
			},
		});

		expect(menu.findAll('li').length).to.equal(5);
	});

	it('hides certain links when the form is deleted', () => {
		const menu = shallowMount(OverflowMenu, {
			provide: { lang },
			propsData: {
				form: Object.assign(form, { deletedAt: '2021-12-02 15:00:00' }),
				routes: routes,
			},
		});

		expect(menu.findAll('li').length).to.equal(1);
	});

	it('manages form inviter', async () => {
		const menu = shallowMount(OverflowMenu, {
			provide: { lang },
			propsData: {
				form: Object.assign(form, { type: 'entry', deletedAt: null, invitationConfig: {} }),
				canUpdate: true,
				routes: routes,
			},
		});

		expect(menu.find('#form-inviter').exists()).to.be.true;
		// form is soft deleted
		await menu.setProps({ form: { ...form, deletedAt: '2021-12-02 15:00:00' } });
		expect(menu.find('#form-inviter').exists()).to.be.false;
	});

	it('shows the correct screen reader label for list action', () => {
		const lang = { get: () => form.name };
		const menu = shallowMount(OverflowMenu, {
			provide: { lang },
			propsData: {
				form: Object.assign(form, { type: 'entry', deletedAt: null, invitationConfig: {} }),
				canUpdate: true,
				routes: routes,
			},
		});

		expect(menu.find('.sr-only').exists()).to.be.true;
	});
});
