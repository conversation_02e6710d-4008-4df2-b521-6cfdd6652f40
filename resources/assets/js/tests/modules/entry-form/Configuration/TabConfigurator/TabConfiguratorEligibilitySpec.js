import { expect } from 'chai';
import sinon from 'sinon';
import TabConfiguratorEligibility from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorEligibility.vue';
import TabConfiguratorEligibilityCommunication from '@/modules/entry-form/Configuration/TabConfigurator/TabConfiguratorEligibilityCommunication.vue';
import { useEntryFormContainer } from '@/modules/entry-form/EntryFormProvider';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';

const lang = { get: () => '' };
const localVue = createLocalVue();
localVue.use(Vuex);

// Mock the useEntryFormContainer
const mockSubmittable = {
	getEligibleContentBlocks: () => [{ id: 1, title: 'Eligible Block 1' }],
	getIneligibleContentBlocks: () => [{ id: 1, title: 'Ineligible Block 1' }],
	getEligibleNotifications: () => [
		{ id: 1, subject: 'Eligible Notification 1' },
		{ id: 2, subject: 'Eligible Notification 2' },
	],
	getIneligibleNotifications: () => [
		{ id: 1, subject: 'Ineligible Notification 1' },
		{ id: 2, subject: 'Ineligible Notification 2' },
	],
	updateTab: sinon.stub(),
};

sinon.stub(useEntryFormContainer).returns({
	Submittable: mockSubmittable,
	onMounted: (callback) => callback(), // Execute the callback immediately for testing
});

const store = new Vuex.Store({
	modules: {
		entryForm: {
			namespaced: true,
			state: {
				eligibleContentBlocks: [{ id: 1 }],
				ineligibleContentBlocks: [{ id: 1 }],
				eligibleNotifications: [{ id: 1 }, { id: 2 }],
				ineligibleNotifications: [{ id: 1 }, { id: 2 }],
			},
			mutations: {
				updateTab: () => {},
			},
		},
	},
});

const newTabConfiguratorEligibility = (propsData, component = TabConfiguratorEligibilityCommunication) =>
	shallowMount(component, {
		provide: { lang },
		propsData: {
			...propsData,
		},
		store,
		localVue,
	});

describe('TabConfiguratorEligibility', () => {
	describe('TabConfiguratorEligibility - assessment', () => {
		it('shows configuration options', () => {
			const tabConfiguratorEligibility = newTabConfiguratorEligibility(
				{
					tab: {
						id: 1,
						translated: {},
						type: 'Eligibility',
					},
				},
				TabConfiguratorEligibility
			);

			// Visible only in eligibility assessment
			expect(tabConfiguratorEligibility.findAll('#min-eligibility-score').length).to.equal(1);
		});
	});

	describe('TabConfiguratorEligibilityCommunication - communication', () => {
		it('shows configuration options', () => {
			const tabConfiguratorEligibility = newTabConfiguratorEligibility({
				tab: {
					id: 1,
					type: 'Eligibility',
				},
			});

			expect(tabConfiguratorEligibility.findAll('select').length).to.equal(4);
			expect(tabConfiguratorEligibility.findAll('input[type=checkbox]').length).to.equal(1);
			expect(tabConfiguratorEligibility.findAll('#eligible-content-blocks').length).to.equal(1);
			expect(tabConfiguratorEligibility.findAll('#ineligible-content-blocks').length).to.equal(1);
			expect(tabConfiguratorEligibility.findAll('#eligible-notifications').length).to.equal(1);
			expect(tabConfiguratorEligibility.findAll('#ineligible-notifications').length).to.equal(1);
		});

		it('emits correct events', () => {
			const tabConfiguratorEligibility = newTabConfiguratorEligibility({
				tab: {
					id: 1,
					type: 'Eligibility',
				},
			});

			tabConfiguratorEligibility.find('#eligible-notifications').trigger('change');

			expect(Object.keys(tabConfiguratorEligibility.emitted())).to.include('input');
			expect(Object.keys(tabConfiguratorEligibility.emitted().input[0][0])).to.include('eligibleNotification');

			tabConfiguratorEligibility.find('#ineligible-notifications').trigger('change');

			expect(Object.keys(tabConfiguratorEligibility.emitted())).to.include('input');
			expect(Object.keys(tabConfiguratorEligibility.emitted().input[1][0])).to.include('ineligibleNotification');
		});
	});
});
