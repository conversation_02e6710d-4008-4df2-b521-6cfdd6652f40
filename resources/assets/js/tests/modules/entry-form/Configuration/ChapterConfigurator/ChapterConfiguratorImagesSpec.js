import { createLocalVue, shallowMount } from '@vue/test-utils';
import { expect } from 'chai';
import Multilingual from '@/lib/components/Translations/Multilingual.vue';
import Uploader from '@/lib/components/Uploader/Uploader';
import obfuscate  from '@/lib/obfuscate.js';
import ChapterConfiguratorImages from '@/modules/entry-form/Configuration/ChapterConfigurator/ChapterConfiguratorImages.vue';
import Vuex from 'vuex';

const localVue = createLocalVue();
localVue.use(Vuex);

const store = new Vuex.Store({
  modules: {
    global: {
      namespaced: true,
      state: {
        supportedLanguages: ['en_GB'],
        defaultLanguage: 'en_GB'
      }
    },
    entryForm: {
      namespaced: true,
      state: {
        sponsorsEnabled: true
      }
    }
  }
});

const lang = { get: () => '' };

const newChapterConfiguratorImages = propsData => {
  return shallowMount(ChapterConfiguratorImages, {
    provide: { lang },
    propsData: {
      ...propsData
    },
    store,
    localVue
  });
};

describe('ChapterConfiguratorImages', () => {
  it('shows configuration options', () => {
    const chapterConfiguratorImages = newChapterConfiguratorImages({
      chapter: {
        id: 1,
        translated: {},
        createdAt: '2020-11-09 12:20:00',
        uploaderOptions: obfuscate({ s3: {} })
      }
    });

    expect(chapterConfiguratorImages.findAllComponents(Multilingual).length).to.equal(1);
    expect(chapterConfiguratorImages.findAllComponents(Uploader).length).to.equal(1);
  });
});
