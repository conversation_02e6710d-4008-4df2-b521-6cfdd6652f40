import actions from '@/lib/store/modules/entry-form/actions.js';
import apiMutations from '@/lib/store/modules/entry-form-api/mutations.js';
import AttachmentsTab from '@/modules/entry-form/Tabs/AttachmentsTab.vue';
import EntryLinks from '@/modules/entry-form/EntryLinks/EntryLinks';
import { expect } from 'chai';
import getters from '@/lib/store/modules/entry-form/getters.js';
import mutations from '@/lib/store/modules/entry-form/mutations.js';
import obfuscate from '@/lib/obfuscate.js';
import sinon from 'sinon';
import Uploader from '@/lib/components/Uploader/Uploader';
import Vue from 'vue';
import Vuex from 'vuex';
import { createLocalVue, shallowMount } from '@vue/test-utils';

/**
 * Don't show warning like:
 * [Vue warn]: Avoid mutating a prop directly since the value will be overwritten whenever the parent component
 * re-renders. Instead, use a data or computed property based on the prop's value. Prop being mutated: "fileCount"
 *
 * Started being a problem when <translation> component was added.
 */
Vue.config.silent = true;

const localVue = createLocalVue();
localVue.use(Vuex);

const lang = {
	get: (key) => {
		switch (key) {
			case 'files.buttons.attachments':
				return 'Add attachments';
		}
	},
	choice: (key) => {
		switch (key) {
			case 'files.messages.attachment_counter':
				return '{0} attachments|{1} attachment|[2,*] attachments';
		}
	},
};

const defaultGetters = {
	isLoaded: () => true,
	selectedTab: () => ({ id: 20 }),
	hasAttachments: () => false,
	submittableRoute: () => '/entry-form/entrant/AbCdEfGh/submit',
};

const defaultStore = () => ({
	modules: {
		entryForm: {
			namespaced: true,
			state: {
				attachments: {},
				conditionalVisibility: {
					Attachments: [],
				},
				entry: {
					slug: 'AbCdEfGh',
				},
				locks: {
					readOnly: false,
					ineligible: false,
				},
				values: {
					Attachments: [],
				},
				links: [],
			},
			getters: Object.assign({}, { ...getters, ...defaultGetters }),
			mutations: Object.assign({}, mutations),
			actions: Object.assign({}, { ...actions, ...{ orderAttachments: () => {} } }),
		},
		entryFormConfiguration: {
			namespaced: true,
			state: {
				configurationMode: false,
			},
		},
		entryFormApi: {
			namespaced: true,
			state: {
				errorBag: [],
			},
			mutations: Object.assign({}, apiMutations),
		},
	},
});

const attachment1 = {
	id: 600,
	accountId: 1,
	tabId: 20,
	entryId: 10,
	fileId: 40,
	order: 5,
	file: {
		id: 40,
		remoteId: 40,
		slug: '',
		token: 'dbqKyGHVpOXZb5WV',
		file: 'files/t/m/9/Y/R/1/YNaZsJTtgf/file.jpg',
		original: '40.jpg',
		size: 1,
		mime: 'image/jpeg',
		status: 'ok',
		transcodingStatus: null,
		image: 'https://imageurl',
		order: 1,
	},
};

const attachment2 = {
	id: 601,
	accountId: 1,
	tabId: 20,
	entryId: 10,
	fileId: 41,
	order: 4,
	file: {
		id: 41,
		remoteId: 41,
		slug: '',
		token: 'dbqKyGHVpOXZb6JK',
		file: 'files/t/m/9/Y/R/1/YNaZsUIkjh/file.jpg',
		original: '41.jpg',
		size: 2,
		mime: 'image/jpeg',
		status: 'ok',
		transcodingStatus: null,
		image: 'https://imageurl',
		order: 2,
	},
};

const attachment3 = {
	id: 602,
	accountId: 1,
	tabId: 20,
	entryId: 10,
	fileId: 42,
	order: 5,
	file: {
		id: 42,
		remoteId: 42,
		slug: '',
		token: 'urqKyGHVpOXZb5WV',
		file: 'files/t/m/9/Y/R/1/YNaZsJTtgf/file.mov',
		original: '42.mov',
		size: 200,
		mime: 'video/quicktime',
		status: 'ok',
		transcodingStatus: 'completed',
		image: null,
		order: 3,
		caption: {
			id: 43,
			remoteId: 43,
			file: 'files/t/m/9/Y/R/1/YNaZsJTtgf/file.vtt',
			mine: 'text/vtt',
			original: '42.vtt',
			size: 100,
			status: 'ok',
			token: 'dsqKyGHVpOXZb5WV',
		},
	},
};

const defaultTab = {
	id: 20,
	resource: 'Entries',
	type: 'Attachments',
	maxAttachments: 10,
	order: 10,
	uploaderOptions: obfuscate({
		s3: {
			url: 'https://fakeurl/',
			multi_selection: true,
		},
		routes: {
			upload: 'https://fakeurl/file/upload',
			download: 'https://fakeurl/file/download/',
			status: 'https://fakeurl/file/status/',
			retry: 'https://fakeurl/transcode/:slug/retry',
		},
	}),
};

describe('AttachmentsTab', () => {
	it('should not render uploader if uploaderOptions is not available', () => {
		const storeObject = defaultStore();

		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: { ...defaultTab, uploaderOptions: null },
			},
			store,
			localVue,
		});

		expect(attachmentsTab.findComponent(Uploader).exists()).to.be.false;
	});

	it('works as expected with no attachments', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.getters.attachmentsByTab = () => () => [];

		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: defaultTab,
			},
			store,
			localVue,
		});

		expect(attachmentsTab.findComponent(Uploader).exists()).to.be.true;
		expect(attachmentsTab.vm.tabAttachments).to.deep.equal([]);
	});

	it('properly uses attachments from the store', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.state.attachments = {
			20: [attachment1, attachment2],
		};

		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: defaultTab,
			},
			store,
			localVue,
		});

		expect(attachmentsTab.vm.tabAttachments).to.deep.equal([attachment1, attachment2]);
	});

	it('can delete attachment', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.state.attachments = {
			20: [attachment1, attachment2],
		};
		storeObject.modules.entryForm.getters.attachmentByFile = () => () => attachment1;

		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: defaultTab,
			},
			store,
			localVue,
		});

		Vue.prototype.$http = {
			delete: () => new Promise((resolve) => resolve()),
		};

		expect(attachmentsTab.vm.tabAttachments.length).to.equal(2);

		attachmentsTab.vm.deletedFile(attachment1.file.id, attachment1.file.remoteId, true);

		expect(attachmentsTab.vm.tabAttachments.length).to.equal(1);
		expect(attachmentsTab.vm.tabAttachments[0].file.id).to.equal(attachment2.file.id);
	});

	it('can delete caption only', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.state.attachments = {
			20: [attachment3],
		};
		storeObject.modules.entryForm.getters.attachmentByFile = () => () => attachment3;

		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: defaultTab,
				allowCaptionOnly: true,
			},
			store,
			localVue,
		});

		const deleteCaptionSpy = sinon.spy(() => new Promise((resolve) => resolve()));

		Vue.prototype.$http = { delete: deleteCaptionSpy };

		expect(attachmentsTab.vm.tabAttachments.length).to.equal(1);
		expect(attachmentsTab.vm.tabAttachments[0].file.caption).to.not.be.null;

		attachmentsTab.vm.deletedFile(attachment3.file.caption.id, attachment3.file.caption.remoteId, false);

		expect(deleteCaptionSpy.calledOnce).to.be.true;
		expect(deleteCaptionSpy.calledWith('/file/own/' + attachment3.file.caption.remoteId)).to.be.true;
	});

	// TODO: Temporarily disabled for collaboration work changes.
	// it('orders attachments from 1 to N', () => {
	// 	const storeObject = defaultStore();
	// 	storeObject.modules.entryForm.state.attachments = {
	// 		20: [attachment1, attachment2],
	// 	};
	//
	// 	const store = new Vuex.Store(storeObject);
	// 	const attachmentsTab = shallowMount(AttachmentsTab, {
	// 		provide: { lang },
	// 		propsData: {
	// 			tab: defaultTab,
	// 		},
	// 		store,
	// 		localVue,
	// 	});
	//
	// 	expect(attachmentsTab.vm.attachments[0].order).to.equal(1);
	// 	expect(attachmentsTab.vm.attachments[1].order).to.equal(2);
	//
	// 	attachmentsTab.vm.attachments[0].order = 20;
	// 	attachmentsTab.vm.attachments[1].order = 10;
	//
	// 	expect(attachmentsTab.vm.attachments[0].order).to.equal(20);
	// 	expect(attachmentsTab.vm.attachments[1].order).to.equal(10);
	//
	// 	attachmentsTab.vm.orderAttachmentsFrom1ToN();
	//
	// 	expect(attachmentsTab.vm.attachments[0].order).to.equal(1);
	// 	expect(attachmentsTab.vm.attachments[1].order).to.equal(2);
	// });

	// it('creates temp attachment during file upload', () => {
	// 	const storeObject = defaultStore();
	// 	storeObject.modules.entryForm.actions.setConditionalVisibility = () => [];
	// 	storeObject.modules.entryForm.state.attachments = {
	// 		20: [attachment1, attachment2],
	// 	};
	// 	storeObject.modules.entryForm.state.conditionalVisibility = () => ({ Attachments: {} });
	// 	storeObject.modules.entryForm.getters.attachmentFields = () => () => [];
	//
	// 	const store = new Vuex.Store(storeObject);
	// 	const attachmentsTab = shallowMount(AttachmentsTab, {
	// 		provide: { lang },
	// 		propsData: {
	// 			tab: defaultTab,
	// 		},
	// 		store,
	// 		localVue,
	// 	});
	//
	// 	expect(attachmentsTab.vm.attachments.length).to.equal(2);
	//
	// 	attachmentsTab.vm.uploadingFile({
	// 		id: 45,
	// 	});
	//
	// 	expect(attachmentsTab.vm.attachments.length).to.equal(3);
	// 	expect(attachmentsTab.vm.attachments[2].order).to.equal(3);
	// 	expect(attachmentsTab.vm.attachments[2].id).to.equal('temp-attachment-45');
	// });

	it('is disabled for entrants', () => {
		const storeObject = defaultStore();
		defaultTab.visibleToEntrants = 0;
		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: defaultTab,
			},
			store,
			localVue,
		});

		expect(attachmentsTab.vm.disabled).to.be.true;
	});

	it('is not disabled for managers', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.state.isManager = true;
		defaultTab.visibleToEntrants = 0;
		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: defaultTab,
			},
			store,
			localVue,
		});

		expect(attachmentsTab.vm.disabled).to.be.false;
	});

	it('it hides entry links if accept links and externally hosted videos as attachments is not checked and there are not links', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.state.isManager = true;

		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: { ...defaultTab, uploaderOptions: null, acceptAttachmentLinks: false },
			},
			store,
			localVue,
		});

		expect(attachmentsTab.vm.existingEntryLinks.length).to.equal(0);
		expect(attachmentsTab.findComponent(EntryLinks).exists()).to.be.false;
	});

	it('it displays entry links div even if accept links and externally hosted videos as attachments is not checked', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.state.isManager = true;
		storeObject.modules.entryForm.state.links[defaultTab.id] = [
			{
				id: 'new-5850771729-1667909179481',
				tabId: defaultTab.id,
				url: 'https://af.site/entry-form/entrant/ABCDEFG/edit?tabSlug=hijklm',
				extra: '',
			},
		];

		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: { ...defaultTab, uploaderOptions: null, acceptAttachmentLinks: false },
			},
			store,
			localVue,
		});

		expect(attachmentsTab.vm.existingEntryLinks.length).to.equal(1);
		expect(attachmentsTab.findComponent(EntryLinks).exists()).to.be.true;
	});

	it('stores field errors when onError is called', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.state.errors = {};
		storeObject.modules.entryForm.state.tabFieldsErrors = {};
		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: defaultTab,
			},
			store,
			localVue,
		});

		const errors = ['error1', 'error2'];

		attachmentsTab.vm.onError(errors);

		const storedErrors = store.state.entryForm.errors['Attachments-Attachment-tab-' + defaultTab.id];
		expect(storedErrors).to.deep.equal(errors);
	});

	it('removes tab errors when attachment is uploaded', () => {
		const storeObject = defaultStore();
		storeObject.modules.entryForm.state.attachments = {
			20: [attachment1, attachment2],
		};
		storeObject.modules.entryFormApi.state.errorBag = [
			{
				type: 'Tab',
				tabId: defaultTab.id,
			},
		];
		storeObject.modules.entryForm.state.tabFieldsErrors = {};
		storeObject.modules.entryForm.state.tabApiErrors = [defaultTab.id];
		const store = new Vuex.Store(storeObject);
		const attachmentsTab = shallowMount(AttachmentsTab, {
			provide: { lang },
			propsData: {
				tab: defaultTab,
			},
			store,
			localVue,
		});

		attachmentsTab.vm.uploadedFile([], { oldId: 40 }, 3);

		expect(storeObject.modules.entryForm.state.tabApiErrors).to.deep.equal([]);
		expect(storeObject.modules.entryFormApi.state.errorBag).to.deep.equal([]);
	});
});
