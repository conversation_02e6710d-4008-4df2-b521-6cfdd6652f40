import { Comment } from '@/lib/components/Comments/Comment.types';
import { newCommentControllerFactory } from '@/lib/components/Comments/NewComment.controller';
import { NewCommentEvents } from '@/lib/components/Comments/NewComment.events';
import { NewCommentProps } from '@/lib/components/Comments/NewComment.types';
import Vue from 'vue';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { commentUIBus, CommentUISignals } from '@/lib/components/Comments/signals/Comments';

vi.mock('underscore', () => ({
	underscore: vi.fn(),
}));

vi.mock('tectoastr', () => ({
	tectoastr: vi.fn((foo = null) => foo),
}));

vi.mock('toastr', () => ({
	toastr: vi.fn((foo = null) => foo),
}));

vi.mock('@/lib/utils', () => ({
	getGlobalData: (key: string) => {
		switch (key) {
			case 'translations':
				return {
					'en_GB.test': {
						a: {
							b: 'val-test-a-b',
							c: 'val-test-a-c',
						},
					},
				};
			case 'language':
				return {
					locale: 'en_GB',
					fallback: 'en_GB',
				};
		}
	},
	getGlobal: (key: string) => key,
	useGlobal: (key: string) => key,
}));

vi.mock('@/lib/store', () => ({
	default: {
		getters: { 'validation/hasMultilingualError': false },
		state: {
			global: {
				preferredLanguage: 'en_GB',
				supportedLanguages: ['en_GB', 'it_IT', 'nl_NL'],
			},
			validation: {
				validationErrors: {},
			},
			comments: {
				state: {
					pendingUploads: 2,
					userId: 99,
				},
			},
		},
	},
}));

vi.mock('@/lib/components/Comments/commentsService', () => ({
	commentsServiceFactory: () => ({
		pendingUploads: () => 0,
		changePendingUploadsBy: () => {},
		userId: () => 88,
		commentsEdited: () => 0,
		changeCommentsEditedBy: () => {},
	}),
}));

const comment: Comment = {
	id: 9,
	slug: 'aCoMmEnT',
	name: 'A comment',
	content: 'A comment content',
	userId: 77,
	temp: false,
	files: [],
};

const props = {
	comment: comment,
	token: '',
	readOnly: false,
	createUrl: 'create/a/comment/',
	updateUrl: 'update/a/comment/',
	translations: {},
	uploads: true,
	uploaderObject: {},
	uploaderOptions: {},
	allowsAttachments: true,
	allowsInlineImages: true,
} as NewCommentProps;

const emit = vi.fn();

describe('NewCommentController', () => {
	let oldVueHttp: unknown;

	beforeEach(() => {
		vi.clearAllMocks();
		oldVueHttp = Vue.prototype.$http;
	});

	afterEach(() => {
		Vue.prototype.$http = oldVueHttp;
		vi.restoreAllMocks();
	});

	it('Comments UI signal emits typing event multiple times while typing', async () => {
		const spy = vi.spyOn(commentUIBus, 'emit').mockImplementation((action) => ({ action }));

		const controller = newCommentControllerFactory()(props, emit);

		controller.commentContent.value = 'foo';
		await expect(controller.commentContent.value).toBe('foo');

		controller.commentContent.value = 'bar';
		await expect(controller.commentContent.value).toBe('bar');

		expect(spy).toHaveBeenCalledWith(CommentUISignals.TYPING);
		expect(spy).toHaveBeenCalledTimes(2);
	});

	it('emits Commenting event', async () => {
		const controller = newCommentControllerFactory()(props, emit);

		controller.commentContent.value = 'bar';

		await expect(controller.commentContent.value).toBe('bar');
		expect(emit).toHaveBeenCalledWith(NewCommentEvents.Commenting);
	});

	it('emits Added event when the comment is created', async () => {
		const response = {
			comment: '<div>text</div>',
			content: 'abcd',
			files: [],
			relativeTime: 'relTime',
			slug: 'sLuG',
			user: 'Author Name',
			userId: 77,
			id: 3,
		};

		Vue.prototype.$http = {
			post: () => Promise.resolve({ data: Object.assign({}, response) }),
		};

		const controller = newCommentControllerFactory()(props, emit);

		controller.files.value = [{ id: 8, name: 'foo_file' }];

		await controller.createComment();

		expect(controller.commentContent.value).toBe('');
		expect(controller.files.value).toStrictEqual([]);
		expect(emit).toHaveBeenCalledWith(NewCommentEvents.Added, {
			comment: 'abcd',
			content: 'abcd',
			files: [
				{
					id: 8,
					name: 'foo_file',
				},
			],
			id: response.id,
			relativeTime: response.relativeTime,
			slug: response.slug,
			user: response.user,
			userId: response.userId,
		});
	});

	it('should save the comment when CommentUISignals.SAVE_COMMENT is emitted', async () => {
		Vue.prototype.$http = {
			post: () => Promise.resolve({ data: Object.assign({}, { comment: 'foo' }, comment) }),
		};

		const controller = newCommentControllerFactory()(props, emit);

		controller.commentContent.value = 'foo';

		await commentUIBus.emit(CommentUISignals.SAVE_COMMENT);

		expect(emit).toHaveBeenCalledWith(NewCommentEvents.Commenting);

		expect(emit).toHaveBeenCalledWith(NewCommentEvents.Added, {
			comment: 'A comment content',
			id: 9,
			slug: 'aCoMmEnT',
			name: 'A comment',
			content: 'A comment content',
			userId: 77,
			temp: false,
			files: [],
		});
	});

	it('internal should be false by default', () => {
		const controller = newCommentControllerFactory()(props, emit);

		expect(controller.internal.value).toBe(false);
	});

	it('should show save button if there is some content or files', async () => {
		const controller = newCommentControllerFactory()(props, emit);

		// Initially, the button should be hidden
		expect(controller.showSaveCommentButton.value).toBe(false);

		controller.commentContent.value = 'Typing a comment';
		await Vue.nextTick();
		expect(controller.showSaveCommentButton.value).toBe(true);

		// When there are no files and content is empty
		controller.commentContent.value = '';
		controller.showSaveCommentButton.value = false;
		await Vue.nextTick();

		// When there is at least one file
		controller.files.value = [{ id: 1, name: 'image.jpg' }];
		controller.commentContent.value = 'triggering the value change';
		controller.commentContent.value = '';
		await Vue.nextTick();
		expect(controller.showSaveCommentButton.value).toBe(true);
	});
});
