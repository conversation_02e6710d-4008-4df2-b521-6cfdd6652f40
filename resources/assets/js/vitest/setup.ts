import Base64 from '@/lib/base64';
import { vi } from 'vitest';
import { VueDataProvider } from '@/domain/services/VueDataProvider';

const obfuscate = (d: unknown) => Base64.encode(JSON.stringify(d));

VueDataProvider.bootGlobalData({
	defaultLanguage: 'en_GB',
	supportedLanguages: ['en_GB', 'en_US'],
	obfuscatedData: obfuscate({
		language: {
			locale: 'en_GB',
			fallback: 'en_GB',
		},
	}),
});

vi.mock('@/domain/utils/Activity', () => ({
	wasActiveIn: vi.fn(),
}));
