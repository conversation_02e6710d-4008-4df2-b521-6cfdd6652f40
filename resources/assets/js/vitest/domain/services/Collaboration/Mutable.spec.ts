import { isChanged } from '@/domain/utils/Cache';
import { lockableServiceFactory } from '@/domain/services/Collaboration/Lockable';
import { mutableServiceFactory } from '@/domain/services/Collaboration/Mutable';
import { UpdateFieldEndpoint } from '@/modules/entry-form/Collaboration/services/Api';
import { valueServiceFactory } from '@/domain/services/Collaboration/MutableValueService';
import { beforeEach, describe, expect, it, Mock, vi } from 'vitest';

vi.mock('@/domain/services/Collaboration/Document');

vi.mock('@/domain/services/Collaboration/Lockable', () => ({
	lockableServiceFactory: vi.fn(),
}));

vi.mock('@/domain/services/Collaboration/MutableValueService', () => ({
	valueServiceFactory: vi.fn(),
}));

vi.mock('@/domain/utils/Cache', () => ({
	isChanged: vi.fn(),
}));

describe('Mutable', () => {
	const locksFactory = vi.fn();
	const valuesFactory = vi.fn();
	const setValue = vi.fn();

	let locks = {
		set: vi.fn(),
	};

	let values = {
		set: setValue,
	};

	beforeEach(() => {
		vi.resetAllMocks();

		locks = {
			set: vi.fn(),
		};

		values = {
			set: setValue,
		};

		(lockableServiceFactory as unknown as Mock).mockReturnValue(locksFactory);
		(valueServiceFactory as unknown as Mock).mockReturnValue(valuesFactory);
		(locksFactory as unknown as Mock).mockReturnValue(locks);
		(valuesFactory as unknown as Mock).mockReturnValue(values);
		(isChanged as unknown as Mock).mockReturnValue(vi.fn(() => false));
	});

	it('should make a service', () => {
		const mutable = mutableServiceFactory('submittableSlug', 'formSlug')(
			'mutable-id',
			'endpoint' as unknown as UpdateFieldEndpoint
		);

		expect(lockableServiceFactory).toHaveBeenCalledWith('submittableSlug', 'formSlug');
		expect(locksFactory).toHaveBeenCalledWith('mutable-id');
		expect(valueServiceFactory).toHaveBeenCalledWith('submittableSlug', 'formSlug');
		expect(valuesFactory).toHaveBeenCalledWith('mutable-id');

		expect(mutable).toEqual({
			locks: locks,
			values: values,
			endpoint: 'endpoint',
			destroy: expect.any(Function),
		});
	});

	it('should run mutator on set', () => {
		const mutator = (data: unknown) => ({ data, foo: 'bar' });
		const mutable = mutableServiceFactory('submittableSlug', 'formSlug')('mutable-id', null, mutator);

		mutable.values.set('value');

		expect(setValue).toHaveBeenCalledWith({ data: 'value', foo: 'bar' });
	});

	it('should pass a callback to service if endpoint exists', () => {
		const mutator = (data: unknown) => ({ data, foo: 'bar' });
		const mutable = mutableServiceFactory('submittableSlug', 'formSlug')(
			'mutable-id',
			'endpoint' as unknown as UpdateFieldEndpoint,
			mutator
		);

		mutable.values.set('value', true);
		// expect(setValue).toHaveBeenCalledWith({ data: 'value', foo: 'bar' });
		expect(setValue).toHaveBeenCalledWith({ data: { data: 'value', foo: 'bar' }, callback: expect.any(Function) });
	});

	it('should run mutator on init', () => {
		const mutator = vi.fn();
		const mutable = mutableServiceFactory('submittableSlug', 'formSlug')('mutable-id', null, mutator);

		mutable.values.init('value');

		expect(mutator).toHaveBeenCalledWith('value');
	});

	it('should call the endpoint once', () => {
		(valueServiceFactory as unknown as Mock).mockReturnValue(
			vi.fn().mockReturnValue({
				set: (payload) => {
					payload.callback();
					payload.callback();
				},
			})
		);

		const endpoint = vi.fn().mockResolvedValue({});
		const mutable = mutableServiceFactory('submittableSlug', 'formSlug')('mutable-id', endpoint, vi.fn());

		mutable.locks.set(false);

		expect(endpoint).toHaveBeenCalledOnce();
	});
});
