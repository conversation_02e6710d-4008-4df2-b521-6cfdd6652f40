import Base64 from '@/lib/base64';
import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';
import { clarify, VueDataProvider } from '@/domain/services/VueDataProvider';

const obfuscate = (d: unknown) => Base64.encode(JSON.stringify(d));
describe('VueDataProvider', () => {
	beforeEach(() => {
		vi.resetAllMocks();
	});

	afterEach(() => {
		vi.clearAllMocks();
	});

	it('gets and decodes obfuscated data', () => {
		const obfuscatedData = {
			bar: 'foo',
			num: 123,
			arr: [1, 'abc', { foo: 'bar' }],
		};

		VueDataProvider.bootGlobalData({
			foo: 'bar',
			obfuscatedData: obfuscate(obfuscatedData),
		});

		expect(VueDataProvider.getAllGlobalData()).toStrictEqual(obfuscatedData);
	});

	it('gets and sets global data', () => {
		VueDataProvider.bootGlobalData({
			foo: 'bar-raw',
			obfuscatedData: obfuscate({
				foo: 'bar-obfuscated',
			}),
		});

		expect(VueDataProvider.getGlobalData('foo')).toBe('bar-obfuscated');
		expect(VueDataProvider.getGlobal('foo')).toBe('bar-raw');
	});

	it('gets obfuscated global data', () => {
		VueDataProvider.bootGlobalData({
			foo: obfuscate({
				email: '<EMAIL>',
			}),
		});

		expect(VueDataProvider.getGlobal('foo', true)).toStrictEqual({
			email: '<EMAIL>',
		});
	});

	it('gets obfuscated and encoded global data', () => {
		VueDataProvider.bootGlobalData({
			foo: obfuscate({
				email1: encodeURIComponent('<EMAIL>'),
				email2: '<EMAIL>',
			}),
		});

		expect(VueDataProvider.getGlobal('foo', true, true)).toStrictEqual({
			email1: '<EMAIL>',
			email2: '<EMAIL>',
		});
	});

	it('should clarify obfuscated data', () => {
		const obfuscatedData = {
			bar: 'foo',
			num: 123,
			arr: [1, 'abc', { foo: 'bar' }],
		};
		const clarified = clarify(obfuscate(obfuscatedData));
		expect(clarified).toStrictEqual(obfuscatedData);
	});
});
