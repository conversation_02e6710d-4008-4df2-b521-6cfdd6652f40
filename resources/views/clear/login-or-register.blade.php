@extends('layouts.splash')

@section('title')
    {!! HTML::pageTitle(trans('home.login.heading')) !!}
@endsection

@section('main')
    <div class="container">
        @include('partials.errors.summary')
        @include('partials.errors.message')

        <div class="row">
            <div class="col-xs-12 col-sm-6 col-sm-offset-3 col-lg-4 col-lg-offset-4">
                @if($showSaml)
                <simple-widget widget-class="simple-widget-form">
                    <template slot="content">
                        <a class="btn btn-primary btn-lg ignore" href="{{ route('saml.login') }}">{{ trans('home.login.saml') }}</a>
                    </template>
                </simple-widget>
                @endif
                <login-or-register
                    :login="@js(old('registerEmail'))"
                    country="{{ detect_country() }}"
                    :phone-with-leading-zeros="@js($countriesWithLeadingZeros)"
                    form-action="{{ route('auth.initiate') }}"
                    role-slug="{{ $requestedRole }}"
                    :social-providers="@js($socialProviders)"
                >
                </login-or-register>
            </div>
        </div>
    </div>
@endsection
