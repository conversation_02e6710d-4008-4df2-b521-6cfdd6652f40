<?php $searching = query_parameters([]); ?>

@section('title')
    {!! HTML::pageTitle(trans('organisations.titles.main')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    <organisation-index inline-template>
        <organisation-list id="organisations-list" :ids="{{ json_encode($organisationsIds) }}" inline-template>
            <div class="searchResults">
                <div class="selectors island">
                    <div class="selector-title">
                        <div class="mrx">
                            <h1>{!! trans('organisations.titles.main') !!}</h1>
                            @include('partials.holocron.feature-intro-revealer')
                        </div>
                        <div class="inline-block mtm -mlm">
                            {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                        </div>

                        @if (Consumer::can('create', 'Organisations'))
                            <div class="selector-buttons">
                                {!! Button::link(route('organisation.new'), trans('organisations.titles.new'), ['type' => 'primary']) !!}
                            </div>
                        @endif

                    </div>
                    @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area'))
                        @slot('actions')
                            {!! HTML::exportAction('organisations.export') !!}
                        @endslot
                    @endcomponent
                </div>

                @include('partials.errors.display',['html' => true])
                @include('partials.errors.message')

                <div class="row mtm">
                    <div class="col-xs-12 col-lg-6">
                        @include('organisation.partials.action-overflow')
                    </div>

                    <div class="col-xs-12 col-lg-6">
                        <div class="search-info">
                            @include('partials.page.active-filters', ['filters' => request()->all(), 'context' => ['resource' => 'Organisations']])
                            @include('partials.page.pagination-info', ['paginator' => $organisations])
                        </div>
                    </div>
                </div>

                <div>
                    @if ($organisations->count())
                        @include('search.datatable', ['columnator' => $columnator, 'results' => $organisations->items()])
                        <div class="row">
                            <div class="col-xs-12">
                                @include('partials.page.pagination', ['paginator' => $organisations])
                            </div>
                        </div>
                    @else
                        <div>
                            <p>@lang('organisations.table.empty')</p>
                        </div>
                    @endif

                </div>

            </div>
        </organisation-list>
    </organisation-index>
@stop
