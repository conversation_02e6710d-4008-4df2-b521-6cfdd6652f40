{{-- General --}}
@if (!empty($css['app-header-background']))
#wrapper-page,
.no-cover-bar, #navigation.navbar {
    background: #{{ $css['app-header-background'] }};
}
.context-menu-sidebar-wrapper,
.account-switcher-button,
#navigation .navbar-item {
    background-color: #{{ $css['app-header-background'] }};
}
@endif
@if (!empty($css['app-header-background-text']))
.navbar-default .navbar-brand,
#accountName,
#accountName:hover,
.account-switcher-button,
#navigation .navbar-item {
    color: #{{ $css['app-header-background-text'] }};
}
#navigation.navbar .navbar-toggle .icon-bar {
    background: #{{ $css['app-header-background-text'] }};
}
@endif
@if (!empty($css['text']))
h1, .h1,
h2, .h2,
h3, .h3,
h4, .h4,
h5, .h5,
h6, .h6 {
    color: #{{ $css['text'] }};
}
.labels .label-tag {
    background-color: #{{ $css['text'] }};
}
.account-details h1,
.account-details h2,
.account-details h3 {
    color: #{{ $css['text'] }};
}
@endif
@if (!empty($css['link']))
h1 a, .h1 a,
h2 a, .h2 a,
h3 a, .h3 a {
    color: #{{ $css['link'] }};
}
@endif
@if (!empty($css['link-hover']))
h1 a:hover, .h1 a:hover,
h2 a:hover, .h2 a:hover,
h3 a:hover, .h3 a:hover,
h1 a:focus, .h1 a:focus,
h2 a:focus, .h2 a:focus,
h3 a:focus, .h3 a:focus {
    color: #{{ $css['link-hover'] }};
}
@endif
@if (!empty($css['text']))
body {
    color: #{{ $css['text'] }};
}
@endif
@if (!empty($css['link']))
a,
.judging-comments .judging-comments-button {
    color: #{{ $css['link'] }}
}
@endif
@if (!empty($css['link-hover']))
a:hover, a:focus {
    color: #{{ $css['link-hover'] }};
    text-decoration: solid underline #{{ $css['link-hover'] }} 2px;
}
@endif
@if (!empty($css['link-active']))
a:active,
.judging-comments .judging-comments-button:hover,
.judging-comments .judging-comments-button:focus,
.judging-comments .judging-comments-button:active {
    color: #{{ $css['link-active'] }};
}
@endif

@if (!empty($css['alert-box-warning']))
    .comment-container.internal .comment-text,
    .comment-container.internal .editor-toolbar,
    .comment-container.internal .CodeMirror,
    .comment-container.internal .ck-content,
    .comment-container.internal .ck-toolbar,
    .comment-new.internal .comment-text,
    .comment-new.internal .editor-toolbar,
    .comment-new.internal .CodeMirror,
    .comment-new.internal .ck-content,
    .comment-new.internal .ck-toolbar {
        background-color: rgba({{ hex_to_rgb($css['alert-box-warning']) }}, 0.2) !important;
    }

    .badge.badge-warning {
        background-color: #{{ $css['alert-box-warning'] }};
    }
@endif

@if(!empty($css['home-background']))
body, body.splash-no-bg {
    background-color: #{{ $css['home-background']  }};
}
@endif

@if (empty($css['home-background-image']) && !empty($css['text']))
.splash h1.home-heading, .splash h2.home-heading {
    color: #{{ $css['text'] }};
}
@endif

{{-- Footer --}}
@if (!empty($css['brand-footer-background']))
footer {
    background-color: {{ $css['brand-footer-background'] }} !important;
}
@endif

{{-- Header --}}
@if (!empty($css['brand-header-background']))
.app #header {
    background-color: {{ $css['brand-header-background'] }};
}
@endif

{{-- Forms --}}
@if (!empty($css['text']))
input[type="text"],
input[type="password"],
input[type="color"],
input[type="date"],
input[type="datetime"],
input[type="datetime-local"],
input[type="email"],
input[type="month"],
input[type="number"],
input[type="range"],
input[type="search"],
input[type="tel"],
input[type="time"],
input[type="url"],
input[type="week"],
textarea,
keygen,
select {
    color: #{{ $css['text'] }} !important;
}
.control-label .optional-field,
.control-label .optional-field {
  color: #{{ $css['text'] }} !important;
}
@endif

{{-- Buttons --}}
@if (!empty($css['primary-button']) || !empty($css['primary-button-text']))
.btn-primary,
a.btn-primary,
input.btn-primary {
    @if (!empty($css['primary-button']))background: #{{ $css['primary-button'] }};@endif
    @if (!empty($css['primary-button-text']))color: #{{ $css['primary-button-text'] }};@endif
}
.cube1 {
    @if (!empty($css['primary-button']))background: #{{ $css['primary-button'] }} !important;@endif
}
.manager-actions .btn-sm:hover,
.manager-actions .btn-sm:focus {
    @if (!empty($css['primary-button']))background-color: #{{ $css['primary-button'] }};@endif
    @if (!empty($css['primary-button']))border-color: #{{ $css['primary-button'] }};@endif
    @if (!empty($css['primary-button-text']))color: #{{ $css['primary-button-text'] }};@endif
}
.manager-actions .btn-sm:hover .caret,
.manager-actions .btn-sm:focus .caret {
    @if (!empty($css['primary-button-text']))color: #{{ $css['primary-button-text'] }};@endif
}
.advanced-search:hover,
.advanced-search:focus,
.advanced-search:active,
.advanced-search.active {
    @if (!empty($css['primary-button']))background-color: #{{ $css['primary-button'] }};@endif
    @if (!empty($css['primary-button']))border-color: #{{ $css['primary-button'] }} !important;@endif
    @if (!empty($css['primary-button-text']))color: #{{ $css['primary-button-text'] }} !important;@endif
}
@endif
@if (!empty($css['primary-button-hover']) || !empty($css['primary-button-hover-text']))
.btn-primary:hover, .btn-primary:active, .btn-primary:focus, .btn-primary:focus-visible,
a.btn-primary:hover, a.btn-primary:active, a.btn-primary:focus, a.btn-primary:focus-visible,
input.btn-primary:hover, input.btn-primary:active, input.btn-primary:focus {
    @if (!empty($css['primary-button-hover']))background: #{{ $css['primary-button-hover'] }};@endif
    @if (!empty($css['primary-button-hover-text']))color: #{{ $css['primary-button-hover-text'] }};@endif
}

.btn-primary:active, .btn-primary:focus, .btn-primary:focus-visible,
a.btn-primary:active, a.btn-primary:focus, a.btn-primary:focus-visible,
input.btn-primary:active, input.btn-primary:focus {
    @if (!empty($css['primary-button-hover']))outline-color: #{{ $css['primary-button-hover'] }}; @endif
}
@endif
@if (!empty($css['secondary-button']) || !empty($css['secondary-button']))
.btn-secondary,
a.btn-secondary,
input.btn-secondary,
#quick-selector-category .quick-selector-content__tabs-list li .badge {
    @if (!empty($css['secondary-button']))background: #{{ $css['secondary-button'] }};@endif
    @if (!empty($css['secondary-button-text']))color: #{{ $css['secondary-button-text'] }};@endif
}
.pagination > .active > a, .pagination > .active > a:hover   {
    @if (!empty($css['secondary-button']))background-color: #{{ $css['secondary-button'] }};@endif
    @if (!empty($css['secondary-button']))border-color: #{{ $css['secondary-button'] }};@endif
    @if (!empty($css['secondary-button-text'])) color: #{{ $css['secondary-button-text'] }};@endif
}
.cube2 {
    @if (!empty($css['secondary-button']))background: #{{ $css['secondary-button'] }} !important;@endif
}
.radio.styled label:after {
    @if (!empty($css['secondary-button']))background-color: #{{ $css['secondary-button'] }};@endif
}

.vue-multiselect .multiselect-option .radio-btn-wrapper .checkmark:after {
    @if (!empty($css['secondary-button']))background-color: #{{ $css['secondary-button'] }} !important;@endif
}

.steps .segment,
.steps .button.completed:not(.active) .bubble,
.steps .button.active .bubble::before,
.steps .button.remaining .bubble::before,
.steps-progress .segment {
    @if (!empty($css['secondary-button']))background-color: #{{ $css['secondary-button'] }};@endif
}
.steps .button.active .bubble {
    @if (!empty($css['secondary-button']))border-color: #{{ $css['secondary-button'] }};@endif
}
@endif
@if (!empty($css['secondary-button-hover']) || !empty($css['secondary-button-hover-text']))
.btn-secondary:hover, .btn-secondary:active, .btn-secondary:focus, .btn-secondary:focus-visible,
a.btn-secondary:hover, a.btn-secondary:active, a.btn-secondary:focus, a.btn-secondary:focus-visible,
input.btn-secondary:hover, input.btn-secondary:active, input.btn-secondary:focus {
    @if (!empty($css['secondary-button-hover']))background-color: #{{ $css['secondary-button-hover'] }} !important;@endif
    @if (!empty($css['secondary-button-hover']))outline-color: #{{ $css['secondary-button-hover'] }} !important;@endif
    @if (!empty($css['secondary-button-hover-text']))color: #{{ $css['secondary-button-hover-text'] }} !important;@endif
}
@endif
@if (!empty($css['tertiary-button']) || !empty($css['tertiary-button-text']))
.btn-tertiary,
a.btn-tertiary,
input.btn-tertiary {
    @if (!empty($css['tertiary-button']))background: #{{ $css['tertiary-button'] }} !important;@endif
    @if (!empty($css['tertiary-button-text']))color: #{{ $css['tertiary-button-text'] }} !important;@endif
}
@endif
@if (!empty($css['tertiary-button-hover']) || !empty($css['tertiary-button-hover-text']))
.btn-tertiary:hover, .btn-tertiary:active, .btn-tertiary:focus, btn-tertiary:focus-visible,
a.btn-tertiary:hover, a.btn-tertiary:active, a.btn-tertiary:focus, a.btn-tertiary:focus-visible,
input.btn-tertiary:hover, input.btn-tertiary:active, input.btn-tertiary:focus {
    @if (!empty($css['tertiary-button-hover']))background: #{{ $css['tertiary-button-hover'] }} !important;@endif
    @if (!empty($css['tertiary-button-hover']))outline-color: #{{ $css['tertiary-button-hover'] }}; @endif
    @if (!empty($css['tertiary-button-hover-text']))color: #{{ $css['tertiary-button-hover-text'] }} !important;@endif
}
@endif

@if (!empty($css['secondary-button']))
.tray-content .tray-buttons a:not(.action-button) {
    @if (!empty($css['secondary-button-text']))
        color: #{{ $css['secondary-button-text'] }};
    @endif
}
@endif

@if (!empty($css['link']))
.btn-link {
    color: #{{ $css['link'] }}
}
@endif
@if (!empty($css['link-hover']))
.btn-link:hover,
.btn-link:focus {
    color: #{{ $css['link-hover'] }};
}

.btn-link:not(.open-info-box):not(.dropdown-menu-item):focus,
.btn-link:not(.open-info-box):not(.dropdown-menu-item):focus-visible,
.btn-link:not(.open-info-box):not(.dropdown-menu-item):focus-within {
    outline-color:  #{{ $css['link-hover'] }}!important;
}
@endif
@if (!empty($css['link-active']))
.btn-link:active {
    color: #{{ $css['link-active'] }};
}
@endif
@if (!empty($css['link']))
.btn.btn-light,
.btn.btn-light * {
    color: #{{ $css['link'] }};
}
@endif
@if (!empty($css['primary-button']) || !empty($css['primary-button-text']))
.btn.btn-light:hover,
.btn.btn-light:focus,
.btn.btn-light:active {
    @if (!empty($css['primary-button']))
    background-color: #{{ $css['primary-button'] }};
    border-color: #{{ $css['primary-button'] }};
    @endif
    @if (!empty($css['primary-button-text']))
    color: #{{ $css['primary-button-text'] }};
    @endif
}
@endif
@if (!empty($css['primary-button-text']))
.btn.btn-light:hover *,
.btn.btn-light:focus *,
.btn.btn-light:active * {
    color: #{{ $css['primary-button-text'] }};
}
@endif
.button-pill {
    @if (!empty($css['secondary-button'])) background: #{{ $css['secondary-button'] }};@endif
    @if (!empty($css['secondary-button-text'])) color: #{{ $css['secondary-button-text'] }};@endif
}
.button-pill-close a{
    @if (!empty($css['secondary-button-text'])) color: #{{ $css['secondary-button-text'] }};@endif
}

@if (!empty($css['link']))
.button-pill-close.standalone a {
    color: #{{ $css['link'] }};
}
@endif
@if (!empty($css['link-hover']))
.button-pill-close.standalone a:hover {
    color: #{{ $css['link-hover'] }};
}
@endif
@if (!empty($css['link-active']))
.button-pill-close.standalone a:active {
    color: #{{ $css['link-active'] }};
}
@endif

@if (!empty($css['secondary-button-hover']) || !empty($css['secondary-button-hover-text']))
#navigation .navbar-user .dropdown-menu > li > a:hover span.af-icons {
    @if (!empty($css['secondary-button-hover-text']))color: #{{ $css['secondary-button-hover-text'] }} !important;@endif
}
@endif

@if (!empty($css['link']))
.dropdown-menu,
.action-dropdown .dropdown-menu {
    border-color: #{{ $css['link'] }};
}

.dropdown-menu > li > a:hover:first-child,
.dropdown-menu > li > a:focus:first-child,
.dropdown-menu > li > a:focus-visible:first-child,
.dropdown-menu > li > a:focus-within:first-child,
.action-list .action-group:hover,
.action-list .action-group:focus,
.action-list .action-group:focus-visible,
.action-list .action-group:focus-within {
    color: #fff;
    background-color: #{{ $css['link'] }};
}

.icon-button {
    color: #{{ $css['link'] }};
}
.icon-button:hover {
    background-color: #{{ $css['link'] }};
}
.icon-button:focus,
.icon-button:focus-visible,
.icon-button:focus-within {
    background-color: #{{ $css['link'] }}!important;
    outline-color: #{{ $css['link'] }}!important;
}
.action-button,
.action-button,
.dropdown-light .link-selector {
    color: #{{ $css['link'] }};
    border-color: #{{ $css['link'] }};
    outline-color: #{{ $css['link'] }};
}

.action-button:not(.action-button-disabled):hover,
.action-button:not(.action-button-disabled):focus,
.action-button:not(.action-button-disabled):focus-visible,
.action-button:not(.action-button-disabled):focus-within,
.action-button:not(.action-button-disabled):focus-visible,
.action-button:not(.action-button-disabled):active,
.action-overflow:hover .dropdown-toggle,
.action-overflow:focus .dropdown-toggle,
.action-overflow:focus-visible .dropdown-toggle,
.action-overflow:focus-within .dropdown-toggle,
.dropdown-menu-item:not([disabled]):hover,
.dropdown-menu-item:not([disabled]):focus,
.dropdown-menu-item:focus-visible,
.dropdown-menu-item:focus-within,
.action-dropdown ul .dropdown-menu-item:focus-within {
    color: #fff;
    background-color: #{{ $css['link'] }};
    border-color: #{{ $css['link'] }};
    outline-color: #{{ $css['link'] }};
}

.action-dropdown .dropdown-toggle {
    color: #{{ $css['link'] }};
    background-color: #fff;
    border-color: #{{ $css['link'] }};
    outline-color: #{{ $css['link'] }};
}

.action-dropdown .dropdown-toggle .caret,
.action-overflow .dropdown-toggle .af-icons-action-overflow,
.action-list .action-group .title a:focus-within .af-icons-action-overflow,
.dropdown-light .dropdown-menu {
    color: #{{ $css['link'] }};
}

.action-dropdown .dropdown-toggle:hover,
.action-dropdown .dropdown-toggle:focus,
.dropdown-light .link-selector:hover {
    color: #fff;
    background-color: #{{ $css['link'] }};
    border-color: #{{ $css['link'] }};
}

.action-dropdown .dropdown-toggle:hover .caret,
.action-dropdown .dropdown-toggle:hover span,
.action-dropdown .dropdown-toggle:focus .caret,
.action-dropdown .dropdown-toggle:active .caret,
.action-dropdown .dropdown-toggle:focus-visible .caret,
.action-dropdown .dropdown-toggle:focus-within .caret,
.action-dropdown.open .dropdown-toggle:hover span,
.action-dropdown.open .dropdown-toggle:hover .caret,
.action-overflow:hover .dropdown-toggle.af-icons-action-overflow,
.action-overflow:focus .dropdown-toggle.af-icons-action-overflow,
.action-overflow:focus-visible .dropdown-toggle.af-icons-action-overflow,
.action-overflow:focus-within .dropdown-toggle.af-icons-action-overflow,
.action-dropdown.open .dropdown-toggle {
    color: #fff !important;
}

.action-dropdown .dropdown-menu .divider {
    background-color: #{{ $css['link'] }};
}

.action-dropdown .dropdown-toggle:active,
.action-dropdown .dropdown-toggle:focus-visible,
.action-dropdown .dropdown-toggle:focus-within,
.dropdown-light .link-selector:active,
.dropdown-light .link-selector:focus-visible,
.dropdown-light .link-selector:focus-within,
.action-dropdown.open .dropdown-toggle,
.dropdown-light.open .link-selector,
.action-overflow.open .dropdown-toggle,
.action-overflow hr {
    color: #fff;
    background-color: #{{ $css['link'] }};
    border-color: #{{ $css['link'] }};
    outline-color: #{{ $css['link'] }};
}

.action-dropdown .dropdown-menu.action-dropdown-primary {
    border-color: #{{ $css['primary-button-hover'] }} !important;
}
.action-dropdown .dropdown-menu.action-dropdown-primary .caption a {
    color: #{{ $css['primary-button'] }};
}
.action-dropdown .dropdown-menu.action-dropdown-primary .caption a:hover {
    color: #{{ $css['primary-button-hover'] }};
}
.action-dropdown .dropdown-menu.action-dropdown-primary .divider {
    background-color: #{{ $css['primary-button-hover'] }};
}
.action-dropdown .dropdown-menu.action-dropdown-primary .dropdown-menu-item:hover,
.action-dropdown .dropdown-menu.action-dropdown-primary .dropdown-menu-item:focus {
    color: #fff;
    background-color: #{{ $css['primary-button-hover'] }};
    outline-color: #{{ $css['primary-button-hover'] }};
}

@endif

@if(!empty($css['link-active']))
.popover-wrap.help-icon:hover {
    color: #{{ $css['link-active'] }};
}
.popover-wrap.help-icon:focus,
.popover-wrap.help-icon:focus-visible,
.popover-wrap.help-icon:focus-within {
    outline-color: #{{ $css['link-active'] }}!important;
    color: #{{ $css['link-active'] }};
}
@endif

{{-- Tabs --}}
@if (!empty($css['primary-button']) || !empty($css['primary-button-text']))
ul.tabs li > a:hover,
ul.tabs li > a:active,
.tabs button:hover,
.tabs button:active {
    @if (!empty($css['primary-button']))
    background-color: #{{ $css['primary-button'] }} !important;
    border-color: #{{ $css['primary-button'] }};
    @endif
    @if (!empty($css['primary-button-text']))
    color: #{{ $css['primary-button-text'] }} !important;
    @endif
}
@endif
@if (!empty($css['tab']) || !empty($css['tab-text']))
ul.tabs li > a,
.tabs button {
    @if (!empty($css['tab']))background: #{{ $css['tab'] }} !important; @endif
    @if (!empty($css['tab-text']))color: #{{ $css['tab-text'] }} !important; @endif
}
@endif
@if (!empty($css['tab-active']) || !empty($css['tab-active-text']))
ul.tabs li.active > a,
.tabs button.active {
    @if (!empty($css['tab-active']))background: #{{ $css['tab-active'] }} !important; @endif
    @if (!empty($css['tab-active-text']))color: #{{ $css['tab-active-text'] }} !important; @endif
}
@endif
@if (!empty($css['tab-hover']) || !empty($css['tab-hover-text']))
ul.tabs li > a:hover,
.tabs button:hover {
    @if (!empty($css['tab-hover']))background: #{{ $css['tab-hover'] }} !important; @endif
    @if (!empty($css['tab-hover-text']))color: #{{ $css['tab-hover-text'] }} !important; @endif
}
@endif

{{-- Button - onoff switch --}}
@if (!empty($css['primary-button']))
.onoffswitch-inner:before {
    background-color: #{{ $css['primary-button'] }};
}
@endif

{{-- Info boxes --}}
@if (!empty($css['info-box-background']) || !empty($css['info-box-text']))
.entry-content-block {
    @if (!empty($css['info-box-background']))background: #{{ $css['info-box-background'] }}; @endif
    @if (!empty($css['info-box-text']))color: #{{ $css['info-box-text'] }}; @endif
}
@endif
@if (!empty($css['info-box-text']))
.info-box h1, .entry-content-block h1, .info-box .h1, .entry-content-block .h1
.info-box h2, .entry-content-block h2, .info-box .h2, .entry-content-block .h2
.info-box h3, .entry-content-block h3, .info-box .h3, .entry-content-block .h3 {
    color: #{{ $css['info-box-text'] }};
}
@endif
@if (!empty($css['info-box-heading']))
#infoBoxExpander,
.info-box .heading,
.entry-content-block .title,
.entry-content-block .title a,
.info-box .title button.open-info-box {
    color: #{{ $css['info-box-heading'] }};
}
@endif
@if (!empty($css['info-box-icon']))
.info-box .af-icons {
    color: #{{ $css['info-box-icon'] }};
}
@endif

@if (!empty($css['link']))
    .info-box .body a {
    color: #{{ $css['link'] }};
    }
@endif

@if(!empty($css['link-hover']))
    .info-box .body a:hover {
    background-color: #{{ $css['link-hover'] }};
    }
@endif

@if(!empty($css['link-active']))
    .info-box .body a:focus-visible {
    color: #{{ $css['link-active'] }};
    outline: 2px solid #{{ $css['link-active'] }};
    }
@endif

{{-- Alert boxes --}}
@if (!empty($css['alert-box-text']))
div[class^="alert-"],
div[class^="alert-"] a,
div[class^="alert-"] h1,
div[class^="alert-"] h2,
div[class^="alert-"] h3 {
    color: #{{ $css['alert-box-text'] }};
}
div[class^="alert-"] a:hover,
div[class^="alert-"] a:focus {
    text-decoration: solid underline #{{ $css['alert-box-text'] }} 2px;
}
@endif
@if (!empty($css['alert-box-success']))
.alert-success,
.alert-success.sticky {
    background: #{{ $css['alert-box-success'] }};
}
@endif
@if (!empty($css['alert-box-warning']))
.alert-warning,
.alert-warning.sticky {
    background: #{{ $css['alert-box-warning'] }};
}
@endif
@if (!empty($css['alert-box-info']))
.alert-info,
.alert-info.sticky {
    background: #{{ $css['alert-box-info'] }};
}
@endif
@if (!empty($css['alert-box-error']))
.alert-error,
.alert-error.sticky {
    background: #{{ $css['alert-box-error'] }};
}

.alert-error.sticky.inline,
.form-group.error,
.form-group.error .field-label,
.form-group.error th,
.entrant-content .error{
    color: #{{ $css['alert-box-error'] }} !important;
}

.form-group.error input, .form-group.error select, .form-group.error textarea, li.invalid > a {
    border-color: #{{ $css['alert-box-error'] }} !important;
}
@endif

{{-- Focus boxes (a.k.a tips box) --}}
@if (!empty($css['focus-box-text']))
.tips {
    color: #{{ $css['focus-box-text'] }};
}
.tips h1,
.tips h2,
.tips h3 {
    color: #{{ $css['focus-box-text'] }};
}
.tips-body ol > li:before {
    background: #{{ $css['focus-box-text'] }};
}
.tips-body a {
    color: #{{ $css['focus-box-text'] }};
}
@endif
@if (!empty($css['focus-box-background']))
.tips {
    background: #{{ $css['focus-box-background'] }};
}
.tips-body ol > li:before {
    color: #{{ $css['focus-box-background'] }};
}
@endif
@if (!empty($css['focus-box-background']) && empty($css['focus-box-text']))
.tips-body ol > li:before {
    background: #FFFFFF;
}
@endif

{{-- Form boxes --}}
@if (!empty($css['form-box-background']))
div.alert-neutral.sticky,
.filtertron .extended-container,
.quick-selector,
.simple-widget.simple-widget-form,
.well {
    background: #{{ $css['form-box-background'] }};
}
@endif
@if (!empty($css['form-box-text']))
div.alert-neutral,
div.alert-neutral a:not(.btn),
div.alert-neutral h1, div.alert-neutral h2, div.alert-neutral h3,
.filtertron .extended-container,
.filtertron .extended-container a:not(.btn),
.filtertron .extended-container h1, .filtertron .extended-container h2, .filtertron .extended-container h3,
.well,
.well a:not(.btn),
.well .help-text,
.well h1, .well h2, .well h3 {
    color: #{{ $css['form-box-text'] }};
}
.simple-widget.simple-widget-form,
.simple-widget.simple-widget-form h1,
.simple-widget.simple-widget-form h2,
.simple-widget.simple-widget-form h3,
.simple-widget.simple-widget-form .help-text,
.simple-widget.simple-widget-form a:not(.btn):not(.datepicker-control) {
    color: #{{ $css['form-box-text'] }};
}
@endif
@if (!empty($css['text']))
.multiselect .multiselect-counter,
body.app .vue-multiselect .multiselect-counter {
    background: #{{ $css['text'] }};
}
@endif

{{-- Select2 --}}
@if (!empty($css['text']))
.select2-container .select2-choice,
.select2-drop {
    color: #{{ $css['text'] }} !important;
}
@endif

{{-- Tables --}}
@if (!empty($css['text']))
table thead tr {
    color: #{{ $css['text'] }};
}
@endif
@if (!empty($css['link']))
table thead a:not(.cart table tbody td a) {
    color: #{{ $css['link'] }};
}
table tbody td a:not(.cart table tbody td a) {
    color: #{{ $css['link'] }};
}
@endif

@if (!empty($css['link-hover']))
table thead a:not(.cart table tbody td a):hover {
    color: #{{ $css['link-hover'] }};
}
table tbody td a:not(.cart table tbody td a):hover {
    color: #{{ $css['link-hover'] }};
}
@endif

@if (!empty($css['link-active']))
table thead a:not(.cart table tbody td a):active {
    color: #{{ $css['link-active'] }};
}
table tbody a:not(.cart table tbody td a):focus,
table tbody a:not(.cart table tbody td a):active {
    color: #{{ $css['link-active'] }};
    outline: 2px solid #{{ $css['link-active'] }};
}
@endif

{{-- Navigation --}}
@if (!empty($css['menu-background']))
.main-menu .primary-menu,
.main-menu .primary-menu a,
.main-menu .primary-menu button,
.simple-menu ul li a,
.simple-menu ul li button,
.menu-widget {
    background: #{{ $css['menu-background'] }};
}
@endif
@if (!empty($css['menu-text']))
.main-menu .primary-menu a,
.main-menu .primary-menu button,
.simple-menu ul li a,
.simple-menu ul li button {
    color: #{{ $css['menu-text'] }};
}
.main-menu .primary-menu > ul li.menu-bottom-fixed {
    border-top: 1px solid rgba({{ hex_to_rgb($css['menu-text']) }}, 0.5);
}
.main-menu .primary-menu > ul > li.menu-bottom-fixed.box-shadow {
    border-top: none;
    box-shadow: 0 -3px 5px -3px rgba({{ hex_to_rgb($css['menu-text']) }}, 0.25);
}

@endif
@if (!empty($css['text']))
.secondary-menu-submenu .toggle {
    color: #{{ $css['text'] }};
}
@endif
@if (!empty($css['menu-hover-background']) || !empty($css['menu-hover-text']))
.main-menu .primary-menu a:hover,
.main-menu .primary-menu a:focus,
.main-menu .primary-menu a:active,
.main-menu .primary-menu button:hover,
.main-menu .primary-menu button:focus,
.main-menu .primary-menu button:active,
.main-menu .secondary-menu-submenu .toggle:hover,
.main-menu .secondary-menu-submenu .toggle:focus,
.submenu a:hover,
.submenu a:focus,
.submenu a:active,
.simple-menu ul li a:hover,
.simple-menu ul li a:focus,
.simple-menu ul li button:hover,
.simple-menu ul li button:focus {
    @if (!empty($css['menu-hover-background']))background: #{{ $css['menu-hover-background'] }} !important; @endif
    @if (!empty($css['menu-hover-text']))color: #{{ $css['menu-hover-text'] }} !important; @endif
}
.main-menu .primary-menu a:hover .af-icons,
.main-menu .primary-menu a:focus .af-icons,
.main-menu .primary-menu a:active .af-icons,
.main-menu .primary-menu button:hover .af-icons,
.main-menu .primary-menu button:focus .af-icons,
.main-menu .primary-menu button:active .af-icons,
.simple-menu ul li a:hover .af-icons,
.simple-menu ul li a:focus .af-icons,
.simple-menu ul li button:hover .af-icons,
.simple-menu ul li button:focus .af-icons {
    @if (!empty($css['menu-hover-text']))color: #{{ $css['menu-hover-text'] }} !important; @endif
}
@endif
@if (!empty($css['menu-active-background']) || !empty($css['menu-active-text']))
.main-menu .primary-menu a.active,
.main-menu .primary-menu button.active,
.submenu a.active,
.simple-menu ul li a.active,
.simple-menu ul li button.active{
    @if (!empty($css['menu-active-background']))background: #{{ $css['menu-active-background'] }} !important; @endif
    @if (!empty($css['menu-active-text']))color: #{{ $css['menu-active-text'] }} !important; @endif
}
.main-menu .primary-menu a.active .af-icons,
.main-menu .primary-menu button.active .af-icons {
    @if (!empty($css['menu-active-text']))color: #{{ $css['menu-active-text'] }} !important; @endif
}
.context-menu ul li.selected .highlight,
.context-menu ul li a.context-menu-link:hover,
.context-menu ul li a.context-menu-link:focus-visible {
    background-color: #{{ $css['menu-active-background'] }};
}
.context-menu ul li a.context-menu-link:hover .context-menu-text,
.context-menu ul li a.context-menu-link:focus-visible .context-menu-text{
    color: #{{ $css['menu-active-text'] }};
}
.account-switcher-button:hover,
.account-switcher-button:focus,
#navigation .navbar-item:hover,
#navigation .navbar-item:focus,
#navigation .navbar-item:focus-visible {
    background-color: #{{ $css['menu-active-background'] }};
    color: #{{ $css['menu-active-text'] }};
}
@endif
@if (!empty($css['app-header-background']))
.tray .tray-header {
    background-color: #{{ $css['app-header-background'] }};
}
.form-card.widget {
    border-top: 5px solid #{{ $css['app-header-background'] }};
}
@endif
@if (!empty($css['app-header-background-text']))
.tray .tray-header,
.tray .tray-close .af-icons-cross,
.tray .tray-close .af-icons-cross:hover {
    color: #{{ $css['app-header-background-text'] }};
}
.context-menu {
    color: #{{ $css['app-header-background-text'] }};
    background-color: #{{ $css['app-header-background'] }};
}
.context-menu ul li button{
    color: #{{ $css['app-header-background-text'] }};
}
.context-menu ul li a.context-menu-link .context-menu-text{
    color: #{{ $css['app-header-background-text'] }};
}
.context-menu ul li.selected .highlight .vertical-line {
    background-color: #{{ $css['app-header-background-text'] }};
}
.context-menu-sidebar-wrapper .context-menu-sidebar-container .context-menu-divider {
    background-color: #{{ $css['app-header-background-text'] }};
}
@endif

{{-- Tray --}}
@if (!empty($css['tray-background']))
.tray,
.tray .tray-content .tray-buttons,
.main-menu .secondary-menu,
.main-menu .secondary-menu .secondary-menu-submenu .toggle,
.main-menu .secondary-menu .secondary-menu-close {
    background: #{{ $css['tray-background'] }};
}
@endif
@if (!empty($css['tray-text']))
.tray,
.tray h1,
.tray h2,
.tray h3,
.tray .tray-section .tray-section-heading a,
.main-menu .secondary-menu h1,
.main-menu .secondary-menu h2,
.main-menu .secondary-menu h3,
.main-menu .secondary-menu .secondary-menu-submenu .toggle,
.main-menu .secondary-menu .secondary-menu-close {
    color: #{{ $css['tray-text'] }};
}
@endif
@if (!empty($css['tray-link']))
.tray a:not(.action-button),
.main-menu .secondary-menu .secondary-menu-submenu .submenu > li > a:not(.action-button) {
    color: #{{ $css['tray-link'] }};
}
@endif
@if (!empty($css['tray-link-hover']))
.tray a:hover {
    color: #{{ $css['tray-link-hover'] }};
}
@endif
@if (!empty($css['tray-link-active']))
.tray a:active {
    color: #{{ $css['tray-link-active'] }};
}
@endif

{{-- Audio --}}
@if (!empty($css['secondary-button']))
.jp-audio-stream .jp-progress .jp-seek-bar .jp-play-bar {
    background: #{{ $css['secondary-button'] }};
}
@endif

{{-- Top Pick --}}
.top-pick-preference {
@if (!empty($css['tertiary-button']))
    background-color: #{{ $css['tertiary-button'] }};
@endif
@if (!empty($css['tertiary-button-text']))
    color: #{{ $css['tertiary-button-text'] }};
@endif
}

.top-pick-preference:hover:not(.active) {
@if (!empty($css['primary-button']))
    background-color: #{{ $css['primary-button'] }};
@endif
@if (!empty($css['primary-button-text']))
    color: #{{ $css['primary-button-text'] }};
@endif
}

.top-pick-preference.active {
@if (!empty($css['secondary-button']))
    background-color: #{{ $css['secondary-button'] }};
@endif
@if (!empty($css['secondary-button-text']));
    color: #{{ $css['secondary-button-text'] }};
@endif
}

{{-- Voting --}}
@if (!empty($css['primary-button']))
.voting-controls .vote {
    background-color: #{{ $css['primary-button'] }};
}
@endif
@if (!empty($css['primary-button-hover']))
.voting-controls .vote:hover {
    background-color: #{{ $css['primary-button-hover'] }};
}
@endif
@if (!empty($css['secondary-button']))
.voting-controls .voting,
.voting-controls .voted {
    background-color: #{{ $css['secondary-button'] }};
}
@endif
@if (!empty($css['secondary-button-hover']))
.voting-controls .voted:hover {
    background-color: #{{ $css['secondary-button-hover'] }};
}
@endif
@if (!empty($css['tertiary-button']))
.voting-controls .vote-count {
    background-color: #{{ $css['tertiary-button'] }};
}
@endif

{{-- Scoring criteria slider --}}
@if (!empty($css['primary-button']))
.slider-handle {
    background-color: #{{$css['primary-button']}};
    background-image: -moz-linear-gradient(top, #{{$css['primary-button']}}, #{{$css['primary-button']}});
    background-image: -webkit-gradient(linear, 0 0, 0 100%, from(#{{$css['primary-button']}}), to(#{{$css['primary-button']}}));
    background-image: -webkit-linear-gradient(top, #{{$css['primary-button']}}, #{{$css['primary-button']}});
    background-image: -o-linear-gradient(top, #{{$css['primary-button']}}, #{{$css['primary-button']}});
    background-image: linear-gradient(to bottom, #{{$css['primary-button']}}, #{{$css['primary-button']}});
    background-repeat: repeat-x;
    filter: progid:DXImageTransform.Microsoft.gradient(startColorstr='#{ {{$css['primary-button']}} }', endColorstr='#{ {{$css['primary-button']}} }',GradientType=0);
}
@endif

{{-- Date picker --}}
@if (!empty($css['primary-button']) || !empty($css['primary-button-text']))
.bootstrap-datetimepicker-widget table td.active,
.bootstrap-datetimepicker-widget table td.active:hover {
    @if (!empty($css['primary-button']))background: #{{ $css['primary-button'] }} !important;@endif
    @if (!empty($css['primary-button-text']))color: #{{ $css['primary-button-text'] }} !important;@endif
}

.vue-datetimepicker-widget table button.active,
.vue-datetimepicker-widget table button.active:hover,
.vue-datetimepicker-widget button.selected,
.vue-datetimepicker-widget button.selected:hover {
    @if (!empty($css['primary-button']))background: #{{ $css['primary-button'] }} !important;@endif
    @if (!empty($css['primary-button-text']))color: #{{ $css['primary-button-text'] }} !important;@endif
}
@endif
@if (!empty($css['link-hover']))
.bootstrap-datetimepicker-widget a[data-action]:hover {
    color: #{{ $css['link-hover'] }} !important;
}
.vue-datetimepicker-widget .control-button-navigation:hover,
.vue-datetimepicker-widget .datepicker-control:hover {
    color: #{{ $css['link-hover'] }} !important;
}
@endif

@if (!empty($css['link']))
.bootstrap-datetimepicker-widget a[data-action] {
    color: #{{ $css['link'] }} !important;
}
.vue-datetimepicker-widget .control-button-navigation,
.vue-datetimepicker-widget .datepicker-control {
    color: #{{ $css['link'] }} !important;
}
@endif

{{-- Account switcher --}}
@if (!empty($css['link']))
.account-switcher-result-item:hover,
.account-switcher-result-item:focus,
.account-switcher-add-account button:hover,
.account-switcher-add-account button:focus {
background-color: #{{ $css['link'] }};
}
.account-switcher-result,
.roles .matrix-role .matrix-table thead .btn-form-mode,
.roles .matrix-role .matrix-table thead .btn-form-mode:focus:not(:hover) {
    color: #{{ $css['link'] }};
}

.account-switcher-dropdown,
.account-switcher-searcher,
.account-switcher-add-account {
    border-color: #{{ $css['link'] }};
}
@endif
@if (!empty($css['link-hover']))
.account-switcher-result:hover,
.roles .matrix-role .matrix-table thead .btn-form-mode:hover {
    color: #{{ $css['link-hover'] }};
}
@endif
@if (!empty($css['link-active']))
.account-switcher-result:active {
    color: #{{ $css['link-active'] }};
}
@endif

{{-- Open rounds --}}
@if (!empty($css['text']))
.open-rounds-container .popover {
    color: #{{ $css['text'] }};
}
@endif

{{-- Dashboard--}}
@if (!empty($css['text']))
.round-activity-widget-container a.focusable,
.round-activity-widget-container a,
.round-activity-widget-container a:hover,
.round-activity-widget-container a:focus,
.round-activity-widget-container a:active,
.widget-score-set a,
.widget-score-set a:hover,
.widget-score-set a:focus,
.widget-score-set a:active {
    color: #{{ $css['text'] }};
}
@endif

{{-- Toastr --}}
@if (!empty($css['alert-box-text']))
#toast-container > div,
#toast-container > .toast:before {
    color: #{{ $css['alert-box-text'] }};
}
@endif
@if (!empty($css['alert-box-success']))
.toast-success {
    background: #{{ $css['alert-box-success'] }};
}
@endif
@if (!empty($css['alert-box-warning']))
.toast-warning {
    background: #{{ $css['alert-box-warning'] }};
}
@endif
@if (!empty($css['alert-box-info']))
.toast-info {
    background: #{{ $css['alert-box-info'] }};
}
@endif
@if (!empty($css['alert-box-error']))
.toast-error {
    background: #{{ $css['alert-box-error'] }};
}
@endif

{{-- Configuration mode --}}
@if (!empty($css['secondary-button']))
.configuring,
.app .field-container .field-content.configuring {
    border: 1px solid #{{ $css['secondary-button'] }};
    background: rgba({{ hex_to_rgb($css['secondary-button']) }}, 0.1);
}
@endif

{{-- Holocron --}}
@if (!empty($css['text']))
.guide,
.tour{
    color: #{{ $css['text'] }};
}
@endif

{{-- Generic colours --}}
.primary-button-color {
@if (!empty($css['primary-button']))
    background-color: #{{ $css['primary-button'] }} !important;
    border-color: #{{ $css['primary-button'] }};
@endif
@if (!empty($css['primary-button-text']))
    color: #{{ $css['primary-button-text'] }} !important;
@endif
}

.secondary-button-color {
@if (!empty($css['secondary-button']))
    background-color: #{{ $css['secondary-button'] }} !important;
    border-color: #{{ $css['secondary-button'] }};
@endif
@if (!empty($css['secondary-button-text']))
    color: #{{ $css['secondary-button-text'] }} !important;
@endif
}

.tertiary-button-color {
@if (!empty($css['tertiary-button']))
    background-color: #{{ $css['tertiary-button'] }} !important;
    border-color: #{{ $css['tertiary-button'] }};
@endif
@if (!empty($css['tertiary-button-text']))
    color: #{{ $css['tertiary-button-text'] }} !important;
@endif
}

{{-- Feature Intro --}}
@if (!empty($css['primary-button-hover']))
button.icon.feature-intro-revealer:hover {
    color: #{{ $css['primary-button-hover'] }};
}

button.icon.feature-intro-revealer:focus,
button.icon.feature-intro-revealer:active {
    color: #{{ $css['primary-button-hover'] }};
    outline: 3px solid #{{ $css['primary-button-hover'] }};
}
@endif

{{-- Judging --}}
@if (!empty($css['menu-hover-background']))
.judging-shortcut:hover {
    background-color: #{{ $css['menu-hover-background'] }};
}
@endif

@if (!empty($css['menu-hover-text']))
.judging-shortcut:hover a.shortcut-link {
    color: #{{ $css['menu-hover-text'] }};
}
@endif

{{-- PDF Viewer Toolbar --}}
@if (!empty($css['text']))
.pdf .pdf__toolbar .pdf__toolbar-button {
    color: #{{ $css['text'] }};
}
@endif

@if (!empty($css['link-active']))
.pdf .pdf__toolbar .pdf__toolbar-button:focus {
    color: #{{ $css['link-active'] }};
    outline: 2px solid #{{ $css['link-active'] }};
}
@endif

@if (!empty($css['link-hover']))
.pdf .pdf__toolbar .pdf__toolbar-button:hover {
    color: #FFF;
    background-color: #{{ $css['link-hover'] }};
}
@endif

@if (!empty($css['secondary-button']))
.pdf .pdf__toolbar .pdf__toolbar-button:active {
    background-color: rgba({{ hex_to_rgb($css['secondary-button']) }}, 0.2);
}
@endif
{{-- END PDF Viewer Toolbar --}}

{{-- CKEditor style --}}
:root {
    @if (!empty($css['text']))--ck-color-text: #{{ $css['text'] }};@endif
    @if (!empty($css['text']))--ck-content-font-color: #{{ $css['text'] }};@endif
    @if (!empty($css['text']))--ck-color-base-text: #{{ $css['text'] }};@endif
    @if (!empty($css['text']))--ck-color-base-border: #{{ $css['text'] }};@endif
    @if (!empty($css['text']))--ck-color-button-on: #{{ $css['text'] }};@endif
    @if (!empty($css['link-hover']))--ck-color-button-default-hover-background: #{{ $css['link-hover'] }};@endif

    @if (!empty($css['secondary-button']))--ck-color-button-default-active-background: rgba({{ hex_to_rgb($css['secondary-button']) }}, 0.2);@endif
    @if (!empty($css['secondary-button']))--ck-color-button-on-background: rgba({{ hex_to_rgb($css['secondary-button']) }}, 0.2);@endif
    @if (!empty($css['secondary-button']))--ck-color-button-on-hover-background: rgba({{ hex_to_rgb($css['secondary-button']) }}, 0.2);@endif
    @if (!empty($css['secondary-button']))--ck-color-button-on-active-background: rgba({{ hex_to_rgb($css['secondary-button']) }}, 0.2);@endif

    @if (!empty($css['text']))--ck-color-button-on-color: #{{ $css['text'] }};@endif
    @if (!empty($css['primary-button']))--ck-color-button-action-background: #{{ $css['primary-button'] }};@endif
    @if (!empty($css['primary-button-hover']))--ck-color-button-action-hover-background: #{{ $css['primary-button-hover'] }};@endif
    @if (!empty($css['primary-button-hover']))--ck-color-button-action-active-background: #{{ $css['primary-button-hover'] }};@endif

    @if (!empty($css['link']))--ck-color-link-default: #{{ $css['link'] }}@endif
}

{{-- Toolbar --}}
@if (!empty($css['link-active']))
.ck.ck-toolbar .ck.ck-button:active,
.ck.ck-toolbar .ck.ck-button:focus {
    outline: 2px solid #{{ $css['link-active'] }};
}
@endif

{{-- Dropdown typography --}}
@if (!empty($css['link-hover']))
.ck.ck-toolbar .ck.ck-button:hover:not(:active):not(a):not(.ck-disabled),
.ck.ck-toolbar .ck.ck-dropdown .ck.ck-list .ck-list-item-button:focus,
.ck.ck-toolbar .ck.ck-dropdown .ck.ck-list .ck-list-item-button:active,
.ck.ck-toolbar .ck.ck-dropdown .ck.ck-list .ck-list-item-button:hover {
    background-color: #{{ $css['link-hover'] }};
}

{{-- Modals --}}
.ck-dialog .ck-form__row .ck-button:focus:not(a):not(.ck-disabled),
.ck-dialog .ck-form__row .ck-button:active:not(a):not(.ck-disabled),
.ck-dialog .ck-dialog__actions .ck-button:focus:not(a):not(.ck-disabled),
.ck-dialog .ck-dialog__actions .ck-button:active:not(a):not(.ck-disabled),
.ck-balloon-panel .ck-form__row .ck-button:focus:not(a):not(.ck-disabled),
.ck-balloon-panel .ck-form__row .ck-button:active:not(a):not(.ck-disabled),
.ck-balloon-panel .ck-dialog__actions .ck-button:focus:not(a):not(.ck-disabled),
.ck-balloon-panel .ck-dialog__actions .ck-button:active:not(a):not(.ck-disabled) {
    background-color: #{{ $css['link-hover'] }};
    outline: 2px solid #{{ $css['link-hover'] }};
}
@endif


{{-- Primary button --}}
@if (!empty($css['primary-button-hover']))
.ck-dialog .ck-form__row .ck-button-action:focus:not(a):not(.ck-disabled),
.ck-dialog .ck-form__row .ck-button-action:active:not(a):not(.ck-disabled),
.ck-dialog .ck-dialog__actions .ck-button-action:focus:not(a):not(.ck-disabled),
.ck-dialog .ck-dialog__actions .ck-button-action:active:not(a):not(.ck-disabled),
.ck-balloon-panel .ck-form__row .ck-button-action:focus:not(a):not(.ck-disabled),
.ck-balloon-panel .ck-form__row .ck-button-action:active:not(a):not(.ck-disabled),
.ck-balloon-panel .ck-dialog__actions .ck-button-action:focus:not(a):not(.ck-disabled),
.ck-balloon-panel .ck-dialog__actions .ck-button-action:active:not(a):not(.ck-disabled) {
    background-color: #{{ $css['primary-button-hover'] }};
    outline: 2px solid #{{ $css['primary-button-hover'] }};
}
@endif
{{-- End CKEditor style --}}
