<?php $columnsWithHtml = ['allocation.allocated', 'marker', 'action-overflow', 'grant_status', 'star', 'carts.items']; ?>

<tr class="row-{{ empty($record['inactive']) ? 'active' : 'inactive' }}">
    @foreach ($columns as $column)
        @if ($column->visible())
            <td class="{{ method_exists($column, 'styles') ? $column->styles() : '' }}" {{ in_array($column->name(), $columnsWithHtml) ? '' : ' v-pre' }}>
                {{ $column->html($record) }}
            </td>
        @endif
    @endforeach
</tr>
