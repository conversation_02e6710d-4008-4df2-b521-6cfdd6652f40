@section('title')
    {!! HTML::pageTitle(trans('setting.tabs.registration')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <div class="row island">
        <div class="col-sm-12 col-md-6">
            <div class="title">
                <h1>{{ trans('setting.tabs.registration') }}</h1>
                @include('partials.holocron.feature-intro-revealer')
            </div>
        </div>
    </div>

    @include('partials.errors.display')
    @include('partials.errors.message')

    <div class="form-settings">
        {!! html()->form('put', route('setting.registration.update'))->id('settings-form')->open() !!}

            <div class="row">
                <div class="col-xs-12 col-md-6">
                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="panel-title">
                                <h4>@lang('setting.form.cookies.header')</h4>
                            </div>

                            <div class="form-group">
                                <div class="checkbox styled">
                                    {!! html()->hidden('setting[request-consent-to-cookies]', 0) !!}
                                    {!! html()->checkbox('setting[request-consent-to-cookies]', array_get($settings, 'request-consent-to-cookies'), 1)->attributes(['id' => 'request-consent-to-cookies']) !!}
                                    <label for="request-consent-to-cookies">
                                        {{ trans('setting.form.request_consent_to_cookies.label') }}
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="panel-title">
                                <h4>@lang('setting.form.email_required.header')</h4>
                            </div>

                            <div class="form-group">
                                <div class="checkbox styled">
                                    {!! html()->hidden('setting[app-site-registration-open]', 0) !!}
                                    {!! html()->checkbox('setting[app-site-registration-open]', array_get($settings, 'app-site-registration-open'), 1)->attributes(['id' => 'app-site-registration-open']) !!}
                                    <label for="app-site-registration-open">
                                        {{ trans('setting.form.registration_open.label') }}
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <div class="checkbox styled">
                                    {!! html()->hidden('setting[enable-3rd-party-authentication]', 0) !!}
                                    {!! html()->checkbox('setting[enable-3rd-party-authentication]', array_get($settings, 'enable-3rd-party-authentication'), 1)->attributes(['id' => 'enable-3rd-party-authentication']) !!}
                                    <label for="enable-3rd-party-authentication">
                                        {{ trans('setting.form.enable_3rd_party_authentication.label') }}
                                    </label>
                                </div>
                            </div>
                            <p>@lang('setting.form.can_register_with')</p>
                            <email-mobile-registration-settings
                                :enable-mobile-registration="{{ boolean_string_value(array_get($settings, 'enable-mobile-registrations', false)) }}"
                                :email-required="{{ boolean_string_value(array_get($settings, 'email-required', false)) }}"
                                :mobile-required="{{ boolean_string_value(array_get($settings, 'mobile-required', false)) }}"
                            >
                            </email-mobile-registration-settings>
                            <div class="form-group">
                                {!! html()->label(trans('setting.form.restrict_email_domain.label'), 'restrict-email-domain') !!}
                                <help-icon content="{{ trans('setting.form.restrict_email_domain.help') }}"></help-icon>
                                {!! html()->textarea('setting[restrict-email-domain]', array_get($settings, 'restrict-email-domain'))->attributes(['rows' => 4, 'class' => 'form-control']) !!}
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="panel-title">
                                <h4>@lang('setting.form.3rd_party_authentication.label')</h4>
                            </div>

                            <div class="form-group">
                                @foreach(config('awardforce.social-auth.settings') as $provider)
                                    @if($provider === 'wordpress')
                                        <div class="form-group">
                                            @if(feature_enabled('api'))
                                                <div class="checkbox styled">
                                                    {!! html()->checkbox('setting[social-authentication][]', in_array($provider, $socialProviders), $provider)->attributes(['id' => "{$provider}-authentication", 'class' => 'social-authentication']) !!}
                                            @else
                                                <div class="checkbox styled" id="wordpress-disabled">
                                                    {!! html()->checkbox('setting[social-authentication][]', 0, $provider)->attributes(['id' => "{$provider}-authentication", 'class' => 'social-authentication', 'disabled' => 'disabled']) !!}
                                            @endif
                                                    <label for="{{ $provider }}-authentication">
                                                        {{ trans("setting.form.social_authentication.{$provider}") }}
                                                    </label>
                                                     <help-icon preserve-html content="{{ trans('setting.form.3rd_party_authentication.wordpress.help') }}"></help-icon>
                                                </div>
                                        </div>
                                    @else
                                        <div class="form-group">
                                            <div class="checkbox styled">
                                                {!! html()->checkbox('setting[social-authentication][]', in_array($provider, $socialProviders), $provider)->attributes(['id' => "{$provider}-authentication", 'class' => 'social-authentication']) !!}
                                                <label for="{{ $provider }}-authentication">
                                                    <span class="sr-only">@lang('home.social.'.$provider)</span>
                                                    {{ trans("setting.form.social_authentication.{$provider}") }}
                                                </label>
                                            </div>
                                        </div>
                                    @endif
                                    @if($provider == 'facebook')
                                        @if(feature_enabled('saml'))
                                            <div class="form-group">
                                                <div class="checkbox styled" >
                                                    {!! html()->hidden('setting[enable-saml]', 0) !!}
                                                    {!! html()->checkbox('setting[enable-saml]', array_get($settings, 'enable-saml'), 1)->attributes(['id' => 'enable-saml', 'class' => 'social-authentication']) !!}
                                                    <label for="enable-saml">
                                                        {{ trans('setting.form.enable_saml.label') }}
                                                    </label>
                                                </div>
                                            </div>

                                            <div id="saml-settings">
                                                <div class="form-group indent-option {{ FormError::classIfError('setting.saml-issuer') }}">
                                                    {!! html()->label(trans('setting.form.saml_issuer.label'), 'saml-issuer') !!}
                                                    {!! html()->text('setting[saml-issuer]', array_get($settings, 'saml-issuer'))->attributes(['id' => 'saml-issuer', 'class' => 'form-control']) !!}
                                                </div>

                                                <div class="form-group indent-option {{ FormError::classIfError('setting.saml-sso-service') }}">
                                                    {!! html()->label(trans('setting.form.saml_sso_service.label'), 'saml-sso-service') !!}
                                                    <help-icon content="{{ trans('setting.form.saml_sso_service.help') }}"></help-icon>
                                                    {!! html()->text('setting[saml-sso-service]', array_get($settings, 'saml-sso-service'))->attributes(['id' => 'saml-sso-service', 'class' => 'form-control']) !!}
                                                </div>

                                                <div class="form-group indent-option {{ FormError::classIfError('setting.saml-certificate') }}">
                                                    {!! html()->label(trans('setting.form.saml_certificate.label'), 'saml-certificate') !!}
                                                    {!! html()->textarea('setting[saml-certificate]', array_get($settings, 'saml-certificate'))->attributes(['id' => 'saml-certificate', 'rows' => 4, 'class' => 'form-control']) !!}
                                                </div>

                                                <div class="form-group indent-option">
                                                    <div class="checkbox styled" >
                                                        {!! html()->hidden('setting[saml-certificate-encrypt]', 0) !!}
                                                        {!! html()->checkbox('setting[saml-certificate-encrypt]', array_get($settings, 'saml-certificate-encrypt'), 1)->attributes(['id' => 'enable-saml-certificate-encrypt', 'class' => 'social-authentication']) !!}
                                                        <label for="saml-certificate-encrypt">
                                                            {{ trans('setting.form.saml_certificate_encrypt.label') }}
                                                        </label>
                                                    </div>
                                                </div>

                                                <div id="saml-certificate-public" class="form-group indent-option {{ FormError::classIfError('setting.saml-certificate-public') }}">
                                                    {!! html()->label(trans('setting.form.saml_certificate_public.label'), 'saml-certificate-public') !!}
                                                    {!! html()->textarea('setting[saml-certificate-public]', array_get($settings, 'saml-certificate-public'))->attributes(['id' => 'saml-certificate-public', 'rows' => 4, 'class' => 'form-control']) !!}
                                                </div>

                                                <div id="saml-certificate-private-key" class="form-group indent-option {{ FormError::classIfError('setting.saml-certificate-private-key') }}">
                                                    {!! html()->label(trans('setting.form.saml_certificate_private_key.label'), 'saml-certificate-private-key') !!}
                                                    {!! html()->textarea('setting[saml-certificate-private-key]', array_get($settings, 'saml-certificate-private-key'))->attributes(['id' => 'saml-certificate-private-key', 'rows' => 4, 'class' => 'form-control']) !!}
                                                </div>

                                                @if (array_get($settings, 'enable-saml'))
                                                    <div class="form-group indent-option">
                                                        <p>
                                                            @lang('setting.form.saml_metadata.label')<br>
                                                            <a href="{{ route('saml.metadata') }}" target="_blank" rel="noopener noreferrer">{{ route('saml.metadata') }}</a>
                                                        </p>
                                                    </div>
                                                @endif
                                            </div>
                                        @else
                                            <div class="form-group">
                                                <div class="checkbox styled" id="saml-disabled" >
                                                    {!! html()->checkbox('', false, null)->id('saml-feature')->disabled(true) !!}
                                                    <label for="saml-feature">
                                                        {{ trans('setting.form.enable_saml.label') }}
                                                    </label>
                                                </div>
                                            </div>
                                        @endif
                                    @endif
                                @endforeach
                                <hr />
                                <div class="form-group">
                                    <div class="checkbox styled">
                                        {!! html()->hidden('setting[disable-login-form]', 0) !!}
                                        {!! html()->checkbox('setting[disable-login-form]', array_get($settings, 'disable-login-form'), 1)->attributes(['id' => 'disable-login-form']) !!}
                                        <label for="disable-login-form">
                                            {{ trans('setting.form.disable_login_form.label') }}
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="panel panel-default">
                        <div class="panel-body">
                            <div class="panel-title">
                                <h4>@lang('setting.form.consent.header')</h4>
                            </div>

                            <div class="checkbox styled">
                                {!! html()->hidden('setting[require-agreement-to-terms]', 0) !!}
                                {!! html()->checkbox('setting[require-agreement-to-terms]', array_get($settings, 'require-agreement-to-terms'), 1)->attributes(['id' => 'require-agreement-to-terms']) !!}
                                <label for="require-agreement-to-terms">
                                    {{ trans('setting.form.require_agreement_to_terms.label') }}
                                </label>
                            </div>

                            <agreement-text
                                inline-template
                                :default-agreement-texts="@js($defaultTexts['agreementToTerms'])"
                                :has-custom-agreement-text="{{ boolean_string_value($hasCustomAgreementText) }}"
                                :multilingual-resource="@js($agreementResource)"
                            >
                                <div id="agreement-to-terms">
                                    <div class="form-group form-group-subordinate">
                                        {!! html()->label(trans('setting.form.agreement_to_terms.label'), 'agreementToTerms') !!}

                                        <div v-if="!shouldHide" class="default-text">
                                            @include('html.multilingual', [
                                                'field' => 'agreementToTerms',
                                                'resourceRaw' => 'multilingualResource',
                                                'disabledRaw' => '!(isEditingAgreement || hasCustomAgreementText)',
                                                'toolbar' => (new \AwardForce\Library\Editor\Toolbar(['undo', 'redo', '|', 'bold', 'italic', 'link']))->toArray()
                                            ])
                                        </div>

                                        <input type="hidden" name="setting[custom-agreement-to-terms]" class="customized" :value="usesCustomAgreementText" />

                                        <a v-if="showResetButton" class="modify-text" @click="onResetAgreement">@lang('setting.form.modify_text.reset')</a>
                                        <a v-else class="modify-text" @click="onModifyAgreement">@lang('setting.form.modify_text.modify')</a>
                                    </div>
                                </div>
                            </agreement-text>

                            <div class="checkbox styled">
                                {!! html()->hidden('setting[require-consent-to-notifications-and-broadcasts]', 0) !!}
                                {!! html()->checkbox('setting[require-consent-to-notifications-and-broadcasts]', array_get($settings, 'require-consent-to-notifications-and-broadcasts'), 1)->attributes(['id' => 'require-consent-to-notifications-and-broadcasts']) !!}
                                <label for="require-consent-to-notifications-and-broadcasts">
                                    {{ trans('setting.form.require_consent_to_notifications_and_broadcasts.label') }}
                                </label>
                            </div>

                            <consent-text
                                inline-template
                                :default-consent-texts="@js($defaultTexts['consentToNotificationsAndBroadcasts'])"
                                :has-custom-consent-text="{{ boolean_string_value($hasCustomConsentText) }}"
                                :multilingual-resource="@js($consentResource)"
                            >
                                <div id="consent-to-notifications-and-broadcasts">
                                    <div class="form-group form-group-subordinate">
                                        {!! html()->label(trans('setting.form.consent_to_notifications_and_broadcasts.label'), 'consentToNotificationsAndBroadcasts') !!}
                                        <div v-if="!shouldHide" class="default-text">
                                            @include('html.multilingual', [
                                                'field' => 'consentToNotificationsAndBroadcasts',
                                                'resourceRaw' => 'multilingualResource',
                                                'disabledRaw' => '!(isEditingConsent || hasCustomConsentText)',
                                                'toolbar' => (new \AwardForce\Library\Editor\Toolbar(['undo', 'redo', '|', 'bold', 'italic', 'link']))->toArray()
                                            ])
                                        </div>

                                        <input type="hidden" name="setting[custom-consent-to-notifications-and-broadcasts]" class="customized" :value="usesCustomConsentText" />

                                        <a v-if="showResetButton" class="modify-text" @click="onResetConsent">@lang('setting.form.modify_text.reset')</span></a>
                                        <a v-else class="modify-text" @click="onModifyConsent">@lang('setting.form.modify_text.modify')</span></a>
                                    </div>
                                </div>
                            </consent-text>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-xs-12">
                    <div class="form-actions">
                        @include('html.buttons.save')
                    </div>
                </div>
            </div>
        {!! html()->form()->close() !!}
    </div>
@stop
