<?php
$filtertron = app(\Platform\Search\Filtertron::class);

$none = $filtertron->with('archived', 'none')->with('trashed', 'none')->with('page', 1)->url();
$onlyArchived = $filtertron->with('archived', 'only')->with('trashed', 'none')->with('page', 1)->url();
$onlyTrashed = $filtertron->with('archived', 'none')->with('trashed', 'only')->with('page', 1)->url();

if (Request::get('archived') == 'only') {
    $selected = $onlyArchived;
} else if (Request::get('trashed') == 'only') {
    $selected = $onlyTrashed;
} else {
    $selected = null;
}

if (!isset($options)) {
    $options = array_filter([
        $none => trans('miscellaneous.state.current'),
        $onlyArchived => (!empty($archived) ? trans('miscellaneous.state.archived') : null),
        $onlyTrashed => (!empty($deleted) ? trans('miscellaneous.trashed.only_deleted') : null)
    ]);
}
?>

<state-selector
    id="state-selector"
    :links="@js(key_value_pairs($options, 'link', 'name'))"
    active-link="{{ $selected }}">
</state-selector>
