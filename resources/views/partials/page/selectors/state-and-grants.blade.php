<?php
$filtertron = app(\Platform\Search\Filtertron::class);

$showGrants = $filtertron->with('showGrants', '1')->without('archived')->without('trashed')->with('page', 1)->url();

$filtertron->without('grant_status');
$filtertron->without('showGrants');

$none = $filtertron->with('archived', 'none')->with('trashed', 'none')->with('page', 1)->without('showGrants')->url();
$onlyArchived = $filtertron->with('archived', 'only')->with('trashed', 'none')->with('page', 1)->without('showGrants')->url();
$onlyTrashed = $filtertron->with('archived', 'none')->with('trashed', 'only')->with('page', 1)->without('showGrants')->url();

$selected = match (true) {
    Request::get('archived') === 'only' => $onlyArchived,
    Request::get('trashed') === 'only' => $onlyTrashed,
    Request::get('showGrants') === '1' => $showGrants,
    default => null
};
if (! isset($options)) {
    $options = array_filter([
        $none => trans('miscellaneous.state.current'),
        $onlyArchived => trans('miscellaneous.state.archived'),
        $onlyTrashed => trans('miscellaneous.trashed.only_deleted'),
        $showGrants => trans(':grant+'),
    ]);
}
?>

<state-selector
    id="state-selector"
    :links="@js(key_value_pairs($options, 'link', 'name'))"
    active-link="{{ $selected }}">
</state-selector>
