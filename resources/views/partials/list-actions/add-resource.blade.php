<div class="selector-buttons">
    @if($canCreate)
        @if($isMultiform)
            <add-action-dropdown :revealed-action="reveal" label="{{ strip_tags($label) }} " action="{{$selectedFormSlug ? $route.'/?formSlug='.$selectedFormSlug : ''}}" v-cloak>
                @if(isset($extraOptions) && is_array($extraOptions))
                    @foreach($extraOptions as $extraOption)
                        <a href="{{ $extraOption['route'] }}" class="dropdown-menu-item">{{ $extraOption['name'] }}</a>
                        <div class="divider"></div>
                    @endforeach
                @endif
                <div class="caption">@lang('form.actions.choose_form')</div>
                <ul class="action-list indent-list">
                    @foreach($availableForms as $formSlug => $formName)
                        <a href="{{ "{$route}/?formSlug={$formSlug}" }}" class="dropdown-menu-item">{{ $formName }}</a>
                    @endforeach
                </ul>
            </add-action-dropdown>
        @else
            {!! Button::link($route, $label, ['type' => 'primary', 'size' => 'lg']) !!}
        @endif
    @endif
</div>
