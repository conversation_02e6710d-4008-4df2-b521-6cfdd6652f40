@if ($isMultiform && count($availableForms) > 1)
    <list-action-dropdown dropdown-id="add-resource-action" :revealed-action="reveal" label="{{ strip_tags($label) }}" v-cloak>
        <ul class="action-list">
            @foreach($availableForms as $formSlug => $formName)
            <li>
                <a class="dropdown-menu-item" style="text-overflow: ellipsis" href="{{ "{$route}/?formSlug={$formSlug}" }}">
                    {{$formName}}
                </a>
            </li>
            @endforeach
        </ul>
    </list-action-dropdown>
@else
    {!! Button::link($route, $label, ['class' => 'dropdown-toggle', 'size' => 'sm', 'type' => 'light']) !!}
@endif
