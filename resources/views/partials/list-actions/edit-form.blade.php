@if($canUpdateForms && !empty($availableForms))
    @if($isMultiform)
        <list-action-dropdown :revealed-action="reveal" :dropdown-id="'edit-form-dropdown'" label="{{ trans('entries.titles.edit_form') }}" v-cloak>
            <ul class="action-list">
                @foreach($availableForms as $formSlug => $formName)
                <li>
                    <a class="dropdown-menu-item" style="text-overflow: ellipsis" href="{{route('forms.edit', ['form' => $formSlug])}}">
                        {{$formName}}
                    </a>
                </li>
                @endforeach
            </ul>
        </list-action-dropdown>
    @elseif($form = FormSelector::get())
        <div class="dropdown action-dropdown">
            {!! Button::link(route($route, ['configuration' => 'true', 'formSlug' => (string) $form->slug]), trans('entries.titles.edit_form'), ['class' => 'dropdown-toggle', 'size' => 'sm', 'type' => 'light']) !!}
        </div>
    @endif
@elseif(in_array('report', $allowedTypes))
    <div class="dropdown action-dropdown">
        {!! Button::link(route('forms.new', ['type' => 'report']), trans('form.buttons.create'), ['class' => 'dropdown-toggle', 'size' => 'sm', 'type' => 'light']) !!}
    </div>
@endif
