<script>
	window.announcekit = (window.announcekit || {
		queue: [], on: function (n, x) {
			window.announcekit.queue.push([n, x]);
		}, push: function (x) {window.announcekit.queue.push(x); },
	});
	window.announcekit.push(@json($payload));

	window.announcekit.on('widget-open', function({ widget }) {
		const iframe = document.querySelector('.announcekit-widget iframe');
		if (iframe) {
			iframe.setAttribute('title', @json(trans('miscellaneous.updates')));
		}
    });
</script>
<script async src="https://cdn.announcekit.app/widget-v2.js"></script>
