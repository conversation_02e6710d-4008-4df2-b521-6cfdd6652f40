@if (Auth::check() && ($emailNotConfirmed || $mobileNotConfirmed))
    <div class="alert-info sticky island alert-verification" role="alert">
        <div class="icon">
            <div class="af-icons-md af-icons-md-alert-info"></div>
        </div>
        <div class="message">
            @if ($emailNotConfirmed)
                <div class="{{ ($emailNotConfirmed && $mobileNotConfirmed) ? 'column' : '' }}">
                    <h3 class="first">{{ trans('entries.confirmation_required.header', ['email' => Auth::user()->email]) }}</h3>
                    <p>{{ trans('entries.confirmation_required.text', ['email' => $whitelistEmail]) }}</p>
                    <p>
                        <a href="{{ route('profile.resend-token', ['channel' => 'email', 'user' => Auth::user()]) }}">
                            {{ trans('entries.confirmation_required.resend_email') }}
                        </a>
                    </p>
                </div>
            @endif

            @if ($mobileNotConfirmed)
                <div class="{{ ($emailNotConfirmed && $mobileNotConfirmed) ? 'column' : '' }}">
                    <h3 class="first">{{ trans('entries.confirmation_required_mobile.header', ['mobile' => Auth::user()->mobile]) }}</h3>
                    @if($canResendToken)
                        <p>
                            <a href="{{ route('profile.resend-token', ['channel' => 'mobile', 'user' => Auth::user()]) }}">
                                {{ trans('entries.confirmation_required_mobile.resend_code') }}
                            </a>
                        </p>
                    @endif
                </div>
            @endif
        </div>
    </div>

    @if ($mobileNotConfirmed || $emailNotConfirmed)
        <div id="six-digit-code" class="island alert-verification">
                @if($emailNotConfirmed)
                    <six-digit-code
                        route="{{ isset($redirect) ? route('auth.verify.confirm', ['redirect' => $redirect]) : route('auth.verify.confirm', ['method' => 'post']) }}"
                        recipient="{{ Auth::user()->email }}"
                        token="{{ $emailToken }}"
                        resend-route="{{ route('profile.resend-token', ['channel' => 'mobile', 'user' => Auth::user()]) }}"
                        :centered="false"
                        :show-cancel="false"
                        :show-alerts="false"
                        submit-label="{{ trans('entries.confirmation_required_mobile.verify') }}"
                        :auto-focus="false"
                    ></six-digit-code>
                @endif
                @if($mobileNotConfirmed)
                    <six-digit-code
                        route="{{ isset($redirect) ? route('auth.verify.confirm', ['redirect' => $redirect]) : route('auth.verify.confirm', ['method' => 'post']) }}"
                        recipient="{{ Auth::user()->mobile }}"
                        token="{{ $mobileToken }}"
                        @if($canResendToken)
                            resend-route="{{ route('profile.resend-token', ['channel' => 'mobile', 'user' => Auth::user()]) }}"
                        @endif
                        :centered="false"
                        :show-cancel="false"
                        :show-alerts="false"
                        submit-label="{{ trans('entries.confirmation_required_mobile.verify') }}"
                        :auto-focus="false"
                    ></six-digit-code>
                @endif
        </div>
    @endif
@endif

@if(Auth::check() && $notifyChangePassword = session('notify_change_password'))
    <div class="alert-info sticky island alert-verification" role="alert">
        <div class="icon">
            <div class="af-icons-md af-icons-md-alert-info"></div>
        </div>
        <div class="message">
            <p>{!! $notifyChangePassword !!}</p>
        </div>
    </div>
    @php session()->forget('notify_change_password'); @endphp
@endif
