<?php $searching = query_parameters(['chapter', 'category', 'entrant', 'status', 'moderation', 'tag', 'review_status', 'price', 'payment_status', 'plagiarism_scan_status']); ?>

@section('title')
    {!! HTML::pageTitle(trans('grant_reports.titles.manager')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    @include('users.confirm')

    <!-- TODO The export action and form filter only work in this page when the elements are included twice. Remove when the issue is solved -->
    <div class="hidden">
        {!! html()->formFilter(['report']) !!}
        {!! HTML::exportAction(['manage_grant_reports.export']) !!}
    </div>
    <!-- -->

    <entries-list id="entries-list" :ids="@js($grantReportIds)" :translations="@js($translations)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ trans('grant_reports.titles.manager') }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        @include('partials.page.selectors.season')
                        {!! html()->formFilter(['report']) !!}
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>
                </div>

                @component('search.filtertron.filtertron-search', compact('searching', 'columnator', 'area', 'tooltipText'))
                    @slot('actions')
                        {!! HTML::broadcast('broadcast.new', 'grant-report-recipients') !!}
                        {!! HTML::exportAction(['manage_grant_reports.export']) !!}
                    @endslot
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                    area="{{ $area }}"
                    title="{{ trans('search.shortcuts-bar.title') }}"
                    :saved-views="@js($savedViews)"
                    id="saved-views-shortcuts-bar"
                    class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <portal-target name="tagger" v-if="taggerRevealed"></portal-target>
            <portal-target name="untagger" v-if="untaggerRevealed"></portal-target>
            <portal-target name="schedule-grant-report" v-if="scheduleGrantReportRevealed" multiple></portal-target>
            <portal-target name="create-document" v-if="createDocumentRevealed" multiple></portal-target>

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                            <li>@include('partials.list-actions.delete', ['resource' => 'grant-report'])</li>

                            @if(feature_enabled('grant_reports') && Consumer::can('create', 'Grants'))
                                <li>@include('partials.list-actions.schedule-report', ['showReportSelector' => false])</li>
                            @endif

                            <li>@include('partials.list-actions.download', ['resource' => 'grant-report.manager'])</li>

                            @if (Consumer::can('create', 'Tags'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.tag', ['resource' => 'grant-report.manager', 'labels' => ['button' => trans('buttons.tag')]])</li>
                                <li>@include('partials.list-actions.untag', ['resource' => 'grant-report.manager', 'labels' => ['button' => trans('buttons.remove_tag')]])</li>
                            @endif

                            @if (!trashed_filter_active() && feature_enabled('documents') && Consumer::can('create', 'Documents'))
                                <li class="divider"></li>
                                <li>@include('partials.list-actions.create-document', ['resource' => 'grant-report'])</li>
                            @endif
                        </ul>
                    </list-action-dropdown>
                    <div class="dropdown action-dropdown">
                        @include('partials.list-actions.edit-form', ['allowedTypes' => ['report']])
                    </div>
                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info">
                        @include('partials.page.active-filters', ['filters' => request()->all()])
                        @include('partials.page.pagination-info', ['paginator' => $grantReports])
                    </div>
                </div>
            </div>

            <div>
                @if ($grantReports->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $grantReports->items(), 'class' => 'entries-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $grantReports])
                        </div>
                    </div>
                @else
                    <div>
                        <p>@lang('grant_reports.table.empty')</p>
                    </div>
                @endif
            </div>
            <collaborators-list-modal></collaborators-list-modal>
            <collaborators-invite-modal></collaborators-invite-modal>
        </div>
    </entries-list>
@stop
