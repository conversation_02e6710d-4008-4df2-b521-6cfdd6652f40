<?php
$name = (isset($name) ? $name : 'values').'['.$field->slug.']';
$for = str_slug($name);
$toolbar = ($field->entryField() && $field->form->settings->enableEditorInMultilineTextFields ? 'undefined' : Js::from([]));
?>
<div class="form-group {{ form_field_id($field->slug).' '.FormError::classIfError($name) }}">
    @include('html.field.form.counters')
    <text-editor
        name="{{ $name }}"
        value="{{ Output::html($field->value) }}"
        :allows-inline-images="true"
        :toolbar="{{ $toolbar }}"
        :maximum-characters="{{ Js::from((int)$field->maximumCharacters) }}"
        :maximum-words="{{ Js::from((int)$field->maximumWords) }}"
    ></text-editor>
    {!! FormError::message($name) !!}
</div>
