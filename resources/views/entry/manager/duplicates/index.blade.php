@section('title')
    {!! HTML::pageTitle(trans('entries.duplicates.title')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')

    <div class="row island">
        <div class="col-xs-12">
            <div class="title">
                @include('partials.header.breadcrumbs', ['crumbs' => [
                    [trans('entries.titles.manager'), route('entry.manager.index')],
                    [trans('entries.duplicates.title')],
                ]])
                @include('partials.holocron.feature-intro-revealer')
            </div>
        </div>
    </div>

    @include('partials.errors.message')

    <div id="duplicates-table">
        @if ($primaries->count())
            <table class="table datatable" width="100%" data-area="{{ $area }}">
                <thead>
                    @include('search.head', ['columns' => $columns = $columnator->searchColumns()])
                    @php($fixed = count($columns->fixed()))
                </thead>
                <tbody>
                @foreach ($primaries as $primary)
                    @include('search.row', ['columns' => $columns, 'record' => $primary])

                    @foreach ($primary->duplicates as $duplicate)
                        @include('search.row', ['columns' => $columns, 'record' => $duplicate])
                    @endforeach

                    <tr class="{{ !$loop->last ? 'not-last' : '' }}">
                        @foreach(range(0, $fixed - 2) as $cell)
                            <td></td>
                        @endforeach
                        <td>
                            @if($primary->canArchive)
                                {!! html()->form('put', route('entry.duplicates.confirm-group', [$primary->entry->slug]))->open() !!}
                                    {!! html()->submit(trans('entries.duplicates.buttons.confirm_multiple'))->attributes(['class' => 'btn btn-secondary no-wrap']) !!}
                                {!! html()->form()->close() !!}
                            @else
                                <div class="no-wrap">
                                    <button class="btn btn-secondary disabled no-wrap">@lang('entries.duplicates.buttons.confirm_multiple')</button>

                                    <tooltip-icon content="@lang('entries.duplicates.buttons.actions_disabled')"></tooltip-icon>
                                </div>
                            @endif
                        </td>
                        @foreach(range($fixed + 1, count($columns)) as $cell)
                            <td></td>
                        @endforeach
                    </tr>
                @endforeach
                </tbody>
            </table>
            <div class="row">
                <div class="col-xs-12">
                    @include('partials.page.pagination', ['paginator' => $primaries, 'changeable' => false])
                </div>
            </div>
        @else
            <div>
                <p>@lang('entries.duplicates.table.empty')</p>
            </div>
        @endif
    </div>

    {{ link_to_route('entry.duplicates.scan', trans('entries.duplicates.buttons.scan'), [])->attributes(['class' => 'btn btn-primary']) }}
    {{ $lastCompleted ? trans('entries.duplicates.last_scan', ['diff' => $lastCompleted->diffForHumans()]) : '' }}

    @if($unscanned || $outOfDate)
        {{ trans_choice('entries.duplicates.unscanned', $unscanned, ['count' => $unscanned]) . (!$outOfDate ? '.' : ' ') }}
        @if ($outOfDate)
            <span class="error">{{ strtolower(trans_choice('entries.duplicates.out_of_date', $outOfDate, ['count' => $outOfDate])) }}</span>
        @endif
    @endif
@stop
