<div class="dropdown action-overflow">
    <a href="javascript:;" class="dropdown-toggle" data-toggle="dropdown" role="button" tabindex="0">
        <i class="af-icons af-icons-action-overflow"></i>
        <span class="sr-only">{{ trans('buttons.action_overflow', ['resource' => $entry->resourceLabel()]) }}</span>
    </a>
    <ul class="dropdown-menu dropdown-menu-left">
        @if (is_null($entry->deleted_at))
            <li>
                <a href="{{ route('entry.manager.view', [$entry->slug, 'vtab' => config('tabs.entry.manage.entry')]) }}" class="dropdown-menu-item">
                    @lang('entries.actions.preview_entry.label')
                </a>
            </li>
            @if (Consumer::can('update', 'EntriesAll'))
                <li>
                    <a href="{{ route('entry.manager.edit', [$entry->slug]) }}" class="dropdown-menu-item">
                        @lang('entries.actions.edit_entry.label')
                    </a>
                </li>
                <li>
                    @include('partials.list-actions.manage-collaborators', ['submittable' => $entry->collaborationData()])
                </li>
            @endif
        @endif

        @if (Consumer::can('create', 'EntriesAll') && !trashed_filter_active())
            @include('partials.list-actions.form', ['route' => 'entry.manager.copy', 'method' => 'post', 'model' => $entry, 'label' => 'buttons.copy'])
        @endif

        @if (Consumer::can('delete', 'EntriesAll'))
            @if (trashed_filter_active())
                @include('partials.list-actions.form', ['route' => 'entry.manager.undelete', 'method' => 'put', 'model' => $entry, 'label' => 'buttons.undelete'])
            @else
                <li>
                    <button class="view-simple-modal dropdown-menu-item"
                            data-content="{{ obfuscate(view('entry.partials.delete-modal', ['entry' => $entry, 'route' => route('entry.manager.delete', $entry)])->render()) }}">
                        {{ trans('buttons.delete') }}
                    </button>
                </li>
            @endif

            @if (archived_filter_active())
                @include('partials.list-actions.form', ['route' => 'entry.manager.unarchive', 'method' => 'put', 'model' => $entry, 'label' => 'buttons.unarchive'])
            @elseif(!trashed_filter_active())
                <li>
                    <button class="view-simple-modal dropdown-menu-item" data-content="{{ obfuscate(view(
                        'entry.partials.archive-modal', compact('entry'))->render()) }}">
                        {{ trans('buttons.archive') }}
                    </button>
                </li>
            @endif
        @endif

        @if(!trashed_filter_active())
            @include('partials.list-actions.form', ['route' => 'entry.manager.download', 'method' => 'post', 'model' => $entry, 'label' => 'buttons.download'])

            @if ($canModerate)
                <hr>
                @include('partials.list-actions.form', ['route' => 'entry.manager.moderate', 'method' => 'post', 'model' => $entry, 'label' => 'buttons.approve', 'moderationStatus' => 'approved'])
                @include('partials.list-actions.form', ['route' => 'entry.manager.moderate', 'method' => 'post', 'model' => $entry, 'label' => 'buttons.undecide', 'moderationStatus' => 'undecided'])
                @include('partials.list-actions.form', ['route' => 'entry.manager.moderate', 'method' => 'post', 'model' => $entry, 'label' => 'buttons.reject', 'moderationStatus' => 'rejected'])
            @endif

            @if (is_null($entry->deleted_at))
                <hr>
                @if(Consumer::can('update', 'EntriesAll'))
                    @include('entry.manager.resubmission-actions')
                @endif

                @if ($plagiarismDetection)
                    <li>
                        {{ link_to_route('entry-plagiarism-scan.start', trans('entries.actions.plagiarism_scan.label'), [$plagiarismDetection->slug, $entry->slug])->attributes(['class' => 'dropdown-menu-item']) }}
                    </li>
                @endif
            @endif

            @if(!archived_filter_active() && selected_season_is_active() && Consumer::can('create', 'EntriesAll') && $selectedForm)
                    @if (feature_enabled('review_flow'))
                        <li>
                            @include('partials.list-actions.review', ['resource' => 'entry.manager', 'selected' => $entry->id])
                        </li>
                    @else
                        <li>
                            <a href="{{ route('feature.disabled', ['review_flow']) }}">
                                {{ trans_elliptic('buttons.initiate_review_stage') }}
                            </a>
                        </li>
                    @endif
                @endif
            @endif

            @if (Consumer::can('create', 'Users'))
                <li>
                    @include('partials.list-actions.form-invitation', $invitationConfig)
                </li>
            @endif

            @if (!trashed_filter_active() && !archived_filter_active() && Consumer::can('create', 'EntriesAll'))
                @if (feature_enabled("manage_duplicates"))
                    <li>
                        @include('partials.list-actions.duplicate-archive', ['resource' => 'entry.duplicates.duplicate.archive', 'selected' => $entry->id])
                    </li>
                @else
                    <li>
                        <a href="{{ route('feature.disabled', ['manage_duplicates']) }}">
                            @lang('entries.duplicates.buttons.confirm')
                        </a>
                    </li>
                @endif

                @if ($selectedForm)
                    <li>
                        @include('partials.list-actions.assign-judges', ['resource' => 'assignment.assign', 'selected' => $entry->id])
                    </li>
                @endif
            @endif

            @if (!trashed_filter_active() && feature_enabled('contracts') && Consumer::can('create', 'EntriesAll'))
                <li>@include('partials.list-actions.add-contract', ['resource' => 'entry.manager', 'selected' => $entry->id])</li>
            @endif

            @if (!trashed_filter_active() && feature_enabled('documents') && Consumer::can('create', 'Documents'))
                <li>@include('partials.list-actions.create-document-singular', ['selected' => $entry->id, 'resource' => 'entry', 'userName' => $entry->entrant->fullname(), 'entryTitle' => $entry->title, 'allocation' => $allocationTotals])</li>
            @endif

            @if(!trashed_filter_active() && feature_enabled('fund_management') && Consumer::can('create', 'Funding'))
                <li>@include('partials.list-actions.fund-allocations', ['selected' => $entry->id])</li>
            @endif
            @if(feature_enabled('grants') && Consumer::can('create', 'Grants'))
                <li>@include('partials.list-actions.grant-status-selector', ['selected' => $entry->id])</li>
            @endif
            @if ($canScheduleGrantReports)
                <li>@include('partials.list-actions.schedule-report', ['selected' => $entry->id, 'showReportSelector' => true])</li>
            @endif
    </ul>
</div>
