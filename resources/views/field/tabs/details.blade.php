<div class="row">
    <div class="col-xs-12 col-md-4">
        <div class="panel panel-default">
            <div class="panel-body pbn">
                <div class="form-group" id="labelContainer">
                    <div class="panel-title">
                        <h4>
                            <label for="multilingual-textarea">{{ trans('fields.form.label.label') }}</label>
                            <help-icon content="{{ trans('fields.form.label.help') }}"></help-icon>
                        </h4>
                    </div>
                    @include('html.multilingual', [
                        'resource' => ['translated' => htmlify_translations($field)],
                        'field' => 'label',
                        'disabled' => !empty($readOnly),
                        'class' => 'multilingual-field-label',
                        'markdownMode' => 'single-line',
                        'toolbar' => $toolbar,
                        'allowsInlineImages' => $field->isContent()
                    ])
                </div>

                <div class="form-group form-group-subordinate">
                    {!! html()->label(trans('fields.form.title.label'), 'title') !!}
                    <help-icon content="{{ trans('fields.form.title.help') }}"></help-icon>
                    {!! Multilingual::text('title', $field, ['maxlength' => 32, 'class' => 'form-control multilingual-title', 'disabled' => !empty($readOnly)]) !!}
                </div>

                @if($field->resource !== 'Attachments')
                    <div class="form-group form-group-subordinate hidden" id="hintTextContainer">
                        {!! html()->label(trans('fields.form.hint_text.label'), 'hintText') !!}
                        <help-icon content="{{ trans('fields.form.hint_text.help') }}"></help-icon>
                        @include('html.multilingual', [
                           'field' => 'hintText',
                           'resource' => ['translated' => htmlify_translations($field)],
                           'disabled' => !empty($readOnly),
                           'class' => 'multilingual-field-label',
                           'allowsInlineImages' => true,
                           'markdownMode' => 'full',
                           'toolbar' => \AwardForce\Library\Editor\Toolbar::full()->toArray(),
                       ])
                    </div>
                @endif

                <div class="form-group form-group-subordinate hidden" id="helpTextContainer">
                    {!! html()->label( trans('fields.form.help_text.label'), 'helpText') !!}
                    <help-icon content="{{ trans('fields.form.help_text.help') }}"></help-icon>
                    {!! Multilingual::textarea('helpText', $field, ['rows' => 3, 'class' => 'form-control', 'disabled' => !empty($readOnly)]) !!}
                </div>
            </div>
        </div>
        <div class="panel panel-default">
            <div class="panel-body pbn">
                <div id="tabSelectionContainer" class="hidden">
                    <div class="form-group">
                        <div class="panel-title">
                            <h4>
                                <label for="tabId">{{ trans('fields.form.tab.label') }}</label>
                                <help-icon content="{{ trans('fields.form.tab.help') }}"></help-icon>
                            </h4>
                        </div>
                        {!! html()->select('tabId', $resourceTabs, null)->attributes(['id' => 'tabId', 'class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                    </div>

                    @if($allowsCategorySelection)
                        <div id="categoriesContainer">
                            <div class="form-group">
                                @lang('fields.form.categories.text')
                                <div class="radio styled">
                                    {!! html()->radio('categoryOption', ($field->categoryOption == 'all' || empty($field->categoryOption) ) ? '1':'0', 'all')->attributes(['id' => 'allCategories', (!empty($readOnly) ? 'disabled' : '')]) !!}
                                    <label for="allCategories">
                                        {!! trans('fields.form.categories_all.label') !!}
                                    </label>
                                </div>
                                <div class="radio styled">
                                    {!! html()->radio('categoryOption', ($field->categoryOption == 'select')?'1':'0', 'select')->attributes(['id' => 'selectCategories', (!empty($readOnly) ? 'disabled' : '')]) !!}
                                    <label for="selectCategories">
                                        {!! trans('fields.form.categories_select.label') !!}
                                    </label>
                                </div>
                            </div>
                            <div id="categorySelectionContainer" class="hidden">
                                <div class="form-group">
                                    <multiselect
                                        id="category-selector"
                                        name="categories[]"
                                        :options="@js($categories)"
                                        :selected-options="@js(old('categories', $categoryIds))"
                                        placeholder="{{ trans('fields.form.categories.placeholder') }}"
                                        select-all-label="{{ trans('multiselect.select_all') }}">
                                    </multiselect>
                                </div>
                            </div>
                        </div>
                    @endif
                </div>
                <div class="form-group">
                    {!! html()->label(trans('fields.form.order.label'), 'order') !!}
                    <help-icon content="{{ trans('fields.form.order.help') }}"></help-icon>
                    {!! html()->number('order', Request::get('order', $fieldOrderPrefilled ?? null))->attributes(['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                </div>
            </div>
        </div>
    </div>

    <div class="col-xs-12 col-md-4">
        {!! html()->hidden('seasonId', $field->season->id)->attributes(['id' => 'seasonId']) !!}
        {!! html()->hidden('resource', $field->resource)->attributes(['id' => 'resource']) !!}

        @if($hasForm)
            {!! html()->formSelector() !!}
        @endif

        {{-- field type --}}
        <div class="panel panel-default">
            <div class="panel-body pbn">
                <div class="form-group">
                    @if (count($compatibleTypes) > 1)
                        <div class="panel-title">
                            <h4>{{ trans('fields.form.type.label') }}</h4>
                            @include('field.partials.about-field-types')
                        </div>
                        {!! html()->select('type', $compatibleTypes, null)->attributes( ['id' => 'type', 'placeholder' => '', 'class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                    @else
                        <div class="panel-title">
                            <h4>
                                <label for="typeLabel">
                                    <span class="af-icons af-icons-lock"></span>
                                    @lang('fields.form.type.label')
                                </label>
                            </h4>
                        </div>
                        <input class="form-control" id="typeLabel" value="@lang('fields.types.'.$field->type)" disabled>
                        {!! html()->hidden('type', $field->type)->attributes(['id' => 'type']) !!}
                    @endif
                </div>

                <div class="formula-field-configurator-container hidden" id="formulaFieldConfiguratorContainer">
                    <formula-field-configurator-wrapper :field-titles="@js($fieldTitles)"
                                                        :configuration="@jsObject(old('configuration', $configuration))"
                                                        :translations="@js($draggableOptionsTranslations)"
                                                        field-name="formulaConfiguration"/>
                </div>

                <div id="dateOptionsContainer" class="hidden">
                    <div class="form-group">
                        @lang('fields.form.timezone.text')
                        <div class="checkbox styled">
                            {!! html()->checkbox('includeTimezone', null, true)->attributes(['id' => 'field-specify-timezone']) !!}
                            <label for="field-specify-timezone">
                                {!! trans('fields.form.timezone.label') !!}
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <div class="checkbox styled">
                            {!! html()->checkbox('preselectCurrentDate', isset($field->preselectCurrentDate) ? $field->preselectCurrentDate : true, true)->attributes(['id' => 'field-select-current-date']) !!}
                            <label for="field-select-current-date">
                                {!! trans('fields.form.preselect_current_date.label') !!}
                            </label>
                        </div>
                    </div>
                </div>

                <div id="checkboxConfiguratorContainer" class="hidden">
                    <checkbox-configurator-wrapper
                        :field="@js($translatedField)"
                            id="checkbox-configurator"
                        :supported-languages="@js(current_account()->supportedLanguageCodes())"
                        :translations="@js($draggableOptionsTranslations)"
                            :read-only="{{ boolean_string_value($readOnly) }}"
                            form-selector="#{{ $formSelector }}"
                    ></checkbox-configurator-wrapper>
                </div>

                {{-- Options container - values and translations --}}
                <div id="optionsContainer" class="hidden">
                    <draggable-options-wrapper
                        :initial-field="@js($translatedField)"
                            id="draggable-options"
                        :supported-languages="@js(current_account()->supportedLanguageCodes())"
                        :translations="@js($draggableOptionsTranslations)"
                            :read-only="{{ boolean_string_value($readOnly) }}"
                            form-selector="#{{ $formSelector }}"
                    >
                    </draggable-options-wrapper>
                </div>
                <div id="currenciesContainer" class="hidden">
                    <div class="form-group">
                        {!! html()->label(trans('payments.currencies.form.currency.label'), 'currency') !!}
                        {!! html()->select('currency', for_select_sorted($currencies, ['code', 'name'], true), $field->currency ?? country_currency(detect_country()))->class('form-control') !!}
                    </div>
                </div>
                <div id="minMaxLimitsContainer" class="hidden">
                    <div class="form-group form-group-subordinate">
                        {!! html()->label(trans('fields.form.maximum_words.label'), 'maximumWords') !!}
                        <help-icon content="{{ trans('fields.form.maximum_words.help') }}"></help-icon>
                        {!! html()->text('maximumWords', null)->attributes( ['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                    </div>

                    <div class="form-group form-group-subordinate">
                        {!! html()->label(trans('fields.form.minimum_words.label'), 'minimumWords') !!}
                        <help-icon content="{{ trans('fields.form.minimum_words.help') }}"></help-icon>
                        {!! html()->text('minimumWords', null)->attributes(['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                    </div>

                    <div class="form-group form-group-subordinate">
                        {!! html()->label(trans('fields.form.maximum_characters.label'), 'maximumCharacters') !!}
                        <help-icon content="{{ trans('fields.form.maximum_characters.help') }}"></help-icon>
                        {!! html()->text('maximumCharacters', null)->attributes(['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                    </div>

                    <div class="form-group form-group-subordinate">
                        {!! html()->label(trans('fields.form.minimum_characters.label'), 'minimumCharacters') !!}
                        <help-icon content="{{ trans('fields.form.minimum_characters.help') }}"></help-icon>
                        {!! html()->text('minimumCharacters', null)->attributes(['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                    </div>
                </div>

                <div class="form-group form-group-subordinate hidden" id="maxFileSizeContainer">
                    {!! html()->label(trans('fields.form.max-file-size.label'), 'maxFileSize') !!}
                    {!! html()->text('maxFileSize', null)->attributes(['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                </div>

                @if($plagiarismDetection)
                    <div class="form-group form-group-subordinate hidden" id="plagiarismDetection">
                        <div class="form-group">
                            <div class="checkbox styled">
                                {!! html()->checkbox('plagiarismDetection', !$field->exists || $field->plagiarismDetection, true)->id('field-scan-plagiarism') !!}
                                <label for="field-scan-plagiarism">
                                    {!! trans('fields.form.plagiarism_detection.label') !!}
                                </label>
                            </div>
                        </div>
                    </div>
                @else
                    {!! html()->hidden('plagiarismDetection', true) !!}
                @endif
            </div>
        </div>

        @if (!$showEntrantAccessSettings)
            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="form-group">
                        <div class="panel-title">
                            <h4>@lang('fields.form.season.label')</h4>
                        </div>
                        <div class="radio styled">
                            {!! html()->radio('seasonal', !$field->exists() || !$field->seasonal, false)->attributes(['id' => 'persists', (!empty($readOnly) ? 'disabled' : '')]) !!}
                            <label for="persists">
                                {!! trans('fields.form.season.persists') !!}
                            </label>
                        </div>
                        <div class="radio styled">
                            {!! html()->radio('seasonal', $field->seasonal, true)->attributes(['id' => 'seasonal', (!empty($readOnly) ? 'disabled' : '')]) !!}
                            <label for="seasonal">
                                {!! trans('fields.form.season.seasonal', ['season' => lang($season, 'name') ]) !!}
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            {{-- access --}}
            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="form-group">
                        <div class="panel-title">
                            <h4>@lang('fields.form.roles.text')</h4>
                        </div>
                        <div class="radio styled">
                            {!! html()->radio('roleOption', $field->roleOption == 'all' || empty($field->roleOption), 'all')->attributes(['id' => 'allRoles', (!empty($readOnly) ? 'disabled' : '')]) !!}
                            <label for="allRoles">
                                {!! trans('fields.form.roles_all.label') !!}
                            </label>
                        </div>
                        <div class="radio styled">
                            {!! html()->radio('roleOption', $field->roleOption == 'select', 'select')->attributes(['id' => 'selectRoles', (!empty($readOnly) ? 'disabled' : '')]) !!}
                            <label for="selectRoles">
                                {!! trans('fields.form.roles_select.label') !!}
                            </label>
                        </div>
                    </div>
                    <div class="form-group hidden" id="roleSelector">
                        @include('field.partials.role-access-settings', compact('roles', 'roleAccessSettings'))
                    </div>
                    <div id="accessSettingsContainer" class="hidden">
                        <div class="panel-title">
                            <h4>@lang('fields.form.access.label')</h4>
                        </div>
                        <div class="checkbox styled">
                            {!! html()->checkbox('required', null, true)->attributes(['id' => 'required', (!empty($readOnly) ? 'disabled' : '')]) !!}
                            <label for="required">
                                @lang('fields.form.access.required')
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        @else
            {{-- access --}}
            <div id="fieldAccessContainer" class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="form-group">
                        <div class="panel-title">
                            <h4>@lang('fields.form.access.label')</h4>
                        </div>
                        @include('field.partials.entrant-access-settings', ['field' => $field, 'readOnly' => $readOnly])
                    </div>
                </div>
            </div>
        @endif

        {{-- autocomplete --}}
        <div id="autocompleteFieldContainer" class="panel panel-default hidden">
            <div class="panel-body pbn">
                <div class="form-group">
                    <div class="panel-title">
                        <h4>@lang('fields.form.autocomplete.text')</h4>
                    </div>
                    <div class="checkbox styled">
                        {!! html()->checkbox('autocomplete', null, true)->attributes(['id' => 'autocomplete', (!empty($readOnly) ? 'disabled' : '')]) !!}
                        <label for="autocomplete">
                            {!! trans('fields.form.autocomplete.label') !!}
                        </label>
                    </div>
                </div>
            </div>
        </div>

        {{-- conditional --}}
        @if ($showConditionalSettings)
            <div class="panel panel-default">
                <div class="panel-body pbn">
                    <div class="form-group">
                        <div class="panel-title">
                            <h4>@lang('fields.form.conditional.text')</h4>
                        </div>
                        <div class="checkbox styled">
                            {!! html()->checkbox('conditional', null, true)->attributes(['id' => 'conditional', (!empty($readOnly) ? 'disabled' : '')]) !!}
                            <label for="conditional">
                                {!! trans('fields.form.conditional.label') !!}
                            </label>
                        </div>
                    </div>
                    <div id="conditionalContainer" class="hidden">
                        <div class="form-group">
                            {!! html()->select('conditionalVisibility', ['hide' => trans('fields.form.conditional.visibility.hide'), 'show' => trans('fields.form.conditional.visibility.show')], null)->attributes(['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                        </div>
                        <div class="form-group">
                            {!! html()->label( trans('fields.form.conditional.when'), 'conditionalField') !!}
                        </div>
                        <div class="form-group">
                            {!! html()->select('conditionalField', [], null)->attributes( ['id' => 'conditionalField', 'class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                        </div>
                        <div class="form-group">
                            {!! html()->select('conditionalPattern', $conditionalOptions, null)->attributes(['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                        </div>
                        <div class="form-group">
                            {!! html()->text('conditionalValue', null)->attributes(['class' => 'form-control', (!empty($readOnly) ? 'disabled' : '')]) !!}
                        </div>
                    </div>
                </div>
            </div>
        @endif

        {{-- protection --}}
        <div id="protectionContainer" class="panel panel-default hidden">
            <div class="panel-body pbn">
                <div class="form-group">
                    <div class="panel-title">
                        <h4>@lang('fields.form.protection.label')</h4>
                    </div>
                    <span class="pull-right">
                        <a href="{{ $protectionGuide = config('links.'.current_account_brand().'.protection_guide') }}"
                           data-redirector="{{ external_url($protectionGuide) }}">@lang('fields.form.protection.about')</a>
                    </span>
                    @foreach (\AwardForce\Modules\Forms\Fields\Database\Entities\Field::PROTECTION_TYPES as $type)
                        <div class="radio styled">
                            {{ html()->radio('protection', null, $type)->disabled($field->isRecalculating && !empty($readOnly))->attributes( ['id' => 'protection-'.$type, 'class' => $field->isRecalculating ? "enable-on-field-" . $field->slug : '']) }}
                            <label for="protection-{{ $type }}">
                                @lang('fields.form.protection.'.$type)
                            </label>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        {{-- searchable --}}
        <div id="searchableFieldContainer" class="panel panel-default hidden">
            <div class="panel-body pbn">
                <div class="form-group">
                    <div class="panel-title">
                        <h4>@lang('fields.form.searchable.text')</h4>
                    </div>
                    <div class="checkbox styled">
                        @if($field->isRecalculating)
                            {!! html()->checkbox(null, null, true)->attributes(['disabled' => true, 'class' => ["hide-on-field-" . $field->slug]]) !!}
                            {!! html()->checkbox('searchable', null, true)->attributes(['id' => 'searchable', 'class' =>["show-on-field-" . $field->slug, 'hidden', (!empty($readOnly) ? 'disabled' : '')]]) !!}
                        @else
                            {!! html()->checkbox('searchable', null, true)->id('searchable')->disabled(!empty($readOnly)) !!}
                        @endif
                        <label for="searchable">
                            {!! trans('fields.form.searchable.label') !!}
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xs-12 col-md-4">
        <div class="component-preview island">
            <h2 class="first"><i class="af-icons af-icons-preview"></i> @lang('fields.preview.heading')</h2>
            <div id="fieldPreviewContainer"></div>
            <div id="fieldPreviewNotAvailable" style="display:none">{{ trans('fields.preview.error') }}</div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-xs-12">
        <div class="table-field-configurator-container hidden" id="tableFieldConfiguratorContainer">
            <table-field-configurator
                :configuration="@jsObject(old('configuration', $configuration))"
                :configuration-translated="@jsObject(old('translatedConfiguration') ? $oldTranslatedConfiguration : $translatedConfiguration)"
                :labels="@js($tableFieldConfiguratorLabels)"
                :input-types="@js($tableFieldInputTypes)"
                :supported-languages="@js($supportedLanguages)"
                    language="{{ $preferredLanguage }}"
                    :table-field-calculation-functions="@js($tableFieldCalculationFunctions)"
                    :has-entries={{ boolean_string_value($hasEntries) }}
            >
            </table-field-configurator>
        </div>
    </div>
</div>

<script type="text/javascript">
  var conditionalFields = '{{ obfuscate($conditionalFields) }}';
  var searchableFields = '{{ obfuscate(config('awardforce.fields.searchable')) }}';
  var selectedConditional = '{{ obfuscate($field->conditionalFieldId) }}';
  var existingField = {{ $field->exists ? 'true' : 'false' }};
  var tabId = {{ Request::get('tabId', $field->tabId ?: 'null') }};
    var currenciesForVue = '{{ obfuscate(currencies_for_vue()) }}'
</script>

@include('field.partials.allowed-file-types', [
    'selectedFileTypes' => $field->fileTypes ?? [],
    'name' => 'fileTypes',
    'grouped' => false,
    'minVideoLength' => $field->minVideoLength,
    'minVideoLengthFieldName' => 'minVideoLength',
    'maxVideoLength' => $field->maxVideoLength,
    'maxVideoLengthFieldName' => 'maxVideoLength',
    'maxImageWidthFieldName' => 'imageDimensionConstraints[maxWidth]',
    'minImageWidthFieldName' => 'imageDimensionConstraints[minWidth]',
    'maxImageHeightFieldName' => 'imageDimensionConstraints[maxHeight]',
    'minImageHeightFieldName' => 'imageDimensionConstraints[minHeight]',
    'imageDimensionConstraints' => $field->imageDimensionConstraints,
])
