@section('title')
    {!! HTML::pageTitle(sentence_case(trans('dashboard.titles.dashboard'))) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    @include('partials.errors.message')
    @include('users.confirm')

    <div id="dashboard" {{ $hasCustomDashboards ? '' : 'class=mtm' }}>
        @if ($hasCustomDashboards)
            <div class="row-selectors">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>Dashboards</h1>
                    </div>
                    <link-selector 
                        :links="@js($dashboardLinks)"
                        active-link="{{ $activeDashboardLink }}"
                        base-url="/dashboard-new/"
                    ></link-selector>
                </div>
            </div>
        @endif

        <x-dynamic-component :$component :$dashboardId />
    </div>
@stop
