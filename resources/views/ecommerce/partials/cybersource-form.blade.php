<script src="{{ $jsUrl }}"></script>

{!! html()->form(action: route($checkoutFormRoute))->id('payment')->style($checkoutFormStyle)->open() !!}
    @include('ecommerce.partials.standard-form')
    <input type="hidden" id="JWTContainer" value="{{ $jwt }}" />
    <input type="hidden" id="orderNumber" value="{{ $order }}" />
{!! html()->form()->close() !!}

<script src="{{ app_asset_url('js/jquery/jquery-1.12.4.min.js') }}"></script>
<script src="{{ app_asset_url('js/toastr/toastr.min.js') }}"></script>

<script type="text/javascript">
    const paymentError = "Payment error"
    let checkCca = document.getElementById('checkCca');
    checkCca.onclick = function () {
        checkCca.setAttribute('disabled', 'disabled');
        startCca();
    };

    // Step 3.  Configure Songbird
    Cardinal.configure({
        logging: {
            level: "on"
        }
    });

    // Step 4.  Listen for Events
    Cardinal.on('payments.setupComplete', function(){
        console.log("setup completed");
    });

    Cardinal.on("payments.validated", function (data, jwt) {
        let error = data.ErrorNumber !== 0;

        switch(data.ActionCode){
            case "NOACTION":
            case "SUCCESS":
                if (error) {
                    handleError(paymentError);
                    break;
                }

                let form = document.getElementById("payment");
                form.action = @js(route('cart.process'));

                var input = document.createElement('input');
                input.setAttribute('name', 'jwt');
                input.setAttribute('value', jwt);
                input.setAttribute('type', 'hidden');
                form.appendChild(input);
                form.submit();
                break;
            case "FAILURE":
            case "ERROR":
                handleError(data.Payment?.ExtendedData?.CardHolderInfo || paymentError);
                break;
        }

    });

    // Step 5.  Initialze Songbird
    Cardinal.setup("init", {
        jwt: document.getElementById("JWTContainer").value
    });

    function handleError(message)
    {
        toastr.error(
            message,
            'Error',
            {
                fadeOut: 1000,
                onHidden: function () {
                    window.location.reload();
                }
            }
        );
    }

    function getOrderObject()   {
        return {
            OrderDetails: {
                OrderNumber: document.getElementById('orderNumber').value,
                Amount: '{{ $cart->total()->toCents() }}',
                CurrencyCode: '{{ $cart->currencyCode() }}',
                OrderChannel: '5',
            },
            Consumer: {
                Account: {
                    AccountNumber: document.getElementById('cardNumber').value,
                    ExpirationMonth: document.getElementById('expiryMonth').value.slice(0,2),
                    ExpirationYear: 20 + document.getElementById('expiryYear').value.slice(-2),
                }
            }
        };
    }

    function startCca() {
        var data = getOrderObject();
        Cardinal.start("cca", data);
    }
</script>
