@if($cart->has3DS2Challenge())
    <div id="payment-in-progress" style="display: flex; flex-direction: column;">
        <div style="display: flex; justify-content: center; left: 25px; position:relative; padding-bottom:20px">{{trans('ecommerce.cart.info.payment_in_progress')}}</div>
        <div style="display: flex; justify-content: center">
            <div class="spinner" id="spinner" style="position:relative">
                <div class="cube1"></div>
                <div class="cube2"></div>
            </div>
        </div>
    </div>
    <iframe id="3ds-v2-challenge-iframe" name="3ds-v2-challenge-iframe" style="width: 500px; height: 500px; visibility:hidden;"></iframe>
    <script src="{{ app_asset_url('js/jquery/jquery-1.12.4.min.js') }}"></script>
    <script src="{{ app_asset_url('js/toastr/toastr.min.js') }}"></script>
@endif

@if($cart->paymentTestMode())
    <script id="sp-threeds-js" src="https://test.api.securepay.com.au/threeds-js/securepay-threeds.js"></script>
@else
    <script id="sp-threeds-js" src="https://api.securepay.com.au/threeds-js/securepay-threeds.js"></script>
@endif

{!! html()->form(action: route($checkoutFormRoute))->id('payment')->style($checkoutFormStyle)->open() !!}
    @include('ecommerce.partials.standard-form')
{!! html()->form()->close() !!}

@if($cart->has3DS2Challenge())
    <script defer>
		const paymentInProgressSpinner = document.getElementById("payment-in-progress");
		const iframe = document.getElementById("3ds-v2-challenge-iframe");

		iframe.onload = function() {
		    if(iframe.style.visibility === 'visible') {
			    paymentInProgressSpinner.style.display = "none";
            }
        };
        const onRequestInputDataCallback = () => {
            return {
                cardholderInfo: {
                    cardholderName: @js($cart->cardholderName()),
                    cardNumber: @js($cart->cardNumber()),
                    cardExpiryMonth: @js($cart->cardExpiryMonth()),
                    cardExpiryYear: @js($cart->cardExpiryYear()),
                },
                accountData: {
                    @if($cart->get('email'))
                    emailAddress: @js($cart->get('email')),
                    @endif
                        @if(Consumer::user()->mobile)
                    mobilePhone: {
                        cc: @js('+'.phone(Consumer::user()->mobile)?->toLibPhoneObject()?->getCountryCode()),
                        subscriber: @js(phone(Consumer::user()->mobile)?->toLibPhoneObject()?->getNationalNumber()),
                    },
                    @endif
                },
                billingAddress: {
                    streetAddress: @js($cart->get('streetAddress')),
                    city: @js($cart->get('city')),
                    state: @js($cart->get('state')),
                    country: @js($cart->get('country')),
                    zipCode: @js($cart->get('postcode')),
                },
                threeDSInfo: {
                    threeDSReqAuthMethodInd: @js($threeDSReqAuthMethodInd),
                },
                merchantRiskData: {
                    deliveryTimeframeType: @js($deliveryTimeframeType),
                    reOrderType: @js($reOrderType),
                    shippingMethodType: @js($shippingMethodType),
                    deliveryEmailAddress: @js($cart->get('email')),
                }
            }
        };

        const onThreeDSResultsResponseCallback = () => {
            let form = document.querySelectorAll("form#payment")[0];
            form.action = @js(route('cart.process'));
            form.submit();
        };

        const onThreeDSErrorCallback = (data) => {
            console.error(data.errors);
            toastr.error(
                '',
                data.errors[0].detail,
                {
                    timeOut: 5000,
                    fadeOut: 1000,
                    onHidden: function () {
                        // window.location.reload();
                    }
                }
            );
        };

        let sp3dsConfig = {
            clientId: @js(session('gateway.securepay.order.threedSecure.providerClientId')),
            iframe: document.getElementById('3ds-v2-challenge-iframe'),
            token: @js(session('gateway.securepay.order.orderToken')),
            simpleToken: @js(session('gateway.securepay.order.simpleToken')),
            threeDSSessionId: @js(session('gateway.securepay.order.threedSecure.sessionId')),
            onRequestInputData: onRequestInputDataCallback,
            onThreeDSResultsResponse: onThreeDSResultsResponseCallback,
            onThreeDSError: onThreeDSErrorCallback
        };

        setTimeout(function() {
            let securePayThreedsUI = new window.SecurePayThreedsUI();
            window.securePayThreedsUI = securePayThreedsUI;
            securePayThreedsUI.initAndStartThreeDS(sp3dsConfig);
        }, 100);
    </script>
@endif
