@section('title')
    {!! HTML::pageTitle($title = trans_merge('grants.titles.status')) !!}
@stop

@section('main')
    @include('partials.holocron.feature-intro')
    <grant-status-list id="grant-status-list" :ids="@js($grantStatusIds)" inline-template>
        <div id="searchResults">
            <div class="selectors island">
                <div class="selector-title">
                    <div class="mrx">
                        <h1>{{ $title }}</h1>
                        @include('partials.holocron.feature-intro-revealer')
                    </div>
                    <div class="inline-block mtm -mlm">
                        {!! html()->trashedSelect('partials.page.selectors.delete') !!}
                    </div>

                    <div class="selector-buttons">
                        <div class="selector-buttons buttons-spacer island">
                            @if (Consumer::can('create', 'Grants'))
                                {!! Button::link(route('grant.status.new'), trans_merge('shared.new', 'grants.titles.status'), ['type' => 'primary']) !!}
                            @endif
                        </div>
                    </div>
                </div>

                @component('search.filtertron.filtertron-search', ['columnator' => $columnator, 'area' => $area, 'searching' => false, 'disableAdvanced' => true])
                @endcomponent
            </div>

            <saved-views-shortcuts-bar
                    area="{{ $area }}"
                    title="{{ trans('search.shortcuts-bar.title') }}"
                    :saved-views="@js($savedViews)"
                    id="saved-views-shortcuts-bar"
                    class="shortcuts-bar-space-above"
            ></saved-views-shortcuts-bar>

            @include('partials.errors.display')
            @include('partials.errors.message')

            <div class="row mtm">
                <div class="col-xs-12 col-lg-6">
                    <list-action-dropdown :revealed-action="reveal" label="{{ trans('buttons.action') }}" v-cloak>
                        <ul class="action-list">
                           @if(Consumer::can('delete', 'Grants'))
                               <li>@include('partials.list-actions.delete', ['resource' => 'grant.status'])</li>
                           @endif
                        </ul>
                    </list-action-dropdown>

                </div>
                <div class="col-xs-12 col-lg-6">
                    <div class="search-info" v-pre>
                        @include('partials.page.active-filters', ['filters' => request()->all(), 'resource' => 'assignments', 'hide' => ['score-set']])
                        @include('partials.page.pagination-info', ['paginator' => $grantStatuses])
                    </div>
                </div>
            </div>
            <div>
                @if ($grantStatuses->count())
                    @include('search.datatable', ['columnator' => $columnator, 'results' => $grantStatuses->items(), 'class' => 'table markers-table'])

                    <div class="row">
                        <div class="col-xs-12">
                            @include('partials.page.pagination', ['paginator' => $grantStatuses])
                        </div>
                    </div>
                @else
                    <div>
                        <p>{{ trans_merge(['shared.table.empty_singular', ['resource' => trans('grants.titles.status')]]) }}</p>
                    </div>
                @endif
            </div>
        </div>
    </grant-status-list>
@stop
