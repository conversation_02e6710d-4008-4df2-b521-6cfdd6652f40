{"private": true, "scripts": {"dev": "npm run development", "development": "mix", "eslint": "eslint --fix resources/assets/js/**/*.vue resources/assets/js/src/lib/store/**/*.js", "eslint-all": "eslint --fix resources/assets/js/**/*.{js,ts,vue}", "firebase": ". ~/.nvm/nvm.sh && nvm use v18 && npx firebase emulators:start --project af4 --log-verbosity DEBUG", "nibble": "npx eslint-nibble resources/assets/js/**/*.vue resources/assets/js/src/lib/store/**/*.js", "nibble-all": "npx eslint-nibble resources/assets/js/**/*.{js,ts,vue}", "pentest": "npm run production", "prod": "npm run production", "production": "mix --production", "slint": "node linting-bot/base.mjs", "slint-w": "node linting-bot/feature.mjs", "stage2": "npm run staging", "staging": "mix", "stylelint": "stylelint --fix 'resources/assets/sass/**/*.scss'", "test": "npm run test-karma && vitest run", "test-karma": "karma start karma.conf.js --single-run", "test-win": "node_modules\\.bin\\karma start karma.conf.js --single-run && npx tsc --noEmit && npx vue-tsc --noEmit", "tsc-watch": " npx vue-tsc --noEmit --watch", "uat": "npm run production", "watch": "mix watch", "watch-poll": "mix watch -- --watch-options-poll=1000"}, "dependencies": {"@babel/preset-typescript": "^7.24.7", "@pqina/pintura": "^8.91.1", "@pqina/vue-pintura": "^9.0.1", "@prismatic-io/embedded": "^2.11.0", "@types/pdfjs-dist": "^2.10.377", "@types/plupload": "^2.0.14", "@vimeo/player": "^2.20.1", "autosize": "^5.0.1", "axios": "^1.8.4", "babel-polyfill": "^6.26.0", "bootstrap": "^3.4.1", "bootstrap-slider": "^10.6.2", "chargebee-typescript": "^2.16.0", "chart.js": "^2.9.4", "ckeditor5": "^46.0.0", "copy-webpack-plugin": "^13.0.0", "cross-env": "^7.0.2", "crypto-js": "^4.2.0", "css-loader": "^5.2.7", "darkreader": "4.9.27", "datatables.net": "^1.10.15", "datatables.net-colreorder": "^1.5.1", "datatables.net-responsive": "^2.5.0", "dompurify": "^3.2.4", "domready": "~1.0.7", "eonasdan-bootstrap-datetimepicker": "^4.17.49", "exports-loader": "^1.1.0", "firebase": "^9.23.0", "firebase-tools": "^13.13.3", "fork-ts-checker-webpack-plugin": "^8.0.0", "fp-ts": "^2.13.1", "hyperformula": "^2.3.0", "imagesloaded": "~3.2.0", "imports-loader": "^3.1.1", "intl-tel-input": "^17.0.0", "isotope-layout": "3.0.5", "jquery": "^3.7.1", "lang.js": "^2.0.0-beta.1", "laravel-mix": "^6.0.49", "moment": "^2.29.4", "moment-timezone": "^0.5.45", "object-hash": "^3.0.0", "pdfjs-dist": "^5.2.133", "pinia": "^2.0.33", "portal-vue": "^1.5.0", "postcss": "^8.4.32", "postcss-loader": "^4.3.0", "prettier": "^2.8.4", "pusher-js": "7.0.0", "raw-loader": "^4.0.2", "readline-sync": "^1.4.10", "resolve-typescript-plugin": "^2.0.1", "rtlcss": "^2.5.0", "sass": "1.69.5", "sass-loader": "^13.3.2", "screenfull": "^4.1.0", "signature_pad": "3.0.0-beta.3", "sinon": "^19.0.2", "sortablejs": "^1.10.2", "swipejs": "^2.2.3", "toastr": "^2.1.4", "ts-loader": "^9.4.3", "tslib": "^2.2.0", "turndown": "^7.1.1", "typescript": "^5.0.4", "underscore": "^1.13.6", "uuid": "^11.0.5", "video.js": "^8.20.0", "videojs-contrib-quality-menu": "^1.0.3", "vue": "^2.7.16", "vue-bootstrap": "github:tectonic/vue-bootstrap#1.3.125", "vue-color": "^2.8.1", "vue-currency-input": "1.22.6", "vue-draggable-resizable": "^2.3.0", "vue-loader": "^15.10.1", "vue-loading-overlay": "^3.3.2", "vue-outside-events": "^1.1.3", "vue-template-compiler": "~2.7.16", "vue-textarea-autogrow-directive": "^0.2.0", "vue-tsc": "^1.8.5", "vue-youtube-embed": "^2.2.2", "vuedraggable": "^2.20.0", "vuex": "^3.0.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/eslint-parser": "^7.5.4", "@babel/preset-env": "^7.25.4", "@types/jquery": "^3.5.20", "@typescript-eslint/eslint-plugin": "^5.59.0", "@typescript-eslint/parser": "^5.59.0", "@vue/eslint-config-typescript": "^11.0.2", "@vue/test-utils": "^1.3.6", "babel-eslint": "^10.1.0", "chai": "^4.3.10", "eslint": "^8.36.0", "eslint-config-prettier": "^8.7.0", "eslint-config-standard": "^12.0.0", "eslint-nibble": "^8.1.0", "eslint-plugin-autofix": "^1.1.0", "eslint-plugin-chai-friendly": "^0.7.2", "eslint-plugin-import": "^2.30.0", "eslint-plugin-node": "^8.0.1", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^9.9.0", "karma": "^6.4.1", "karma-chrome-launcher": "^3.1.0", "karma-mocha": "^2.0.1", "karma-mocha-reporter": "^2.2.5", "karma-webpack": "^5.0.0", "mocha": "^10.2.0", "prettier": "^2.8.4", "stylelint": "^15.11.0", "vite": "^5.4.8", "vitest": "^0.30.1", "vue-eslint-parser": "^9.1.0", "vue-tsc": "^1.8.5"}, "engines": {"node": "^22.13.1", "npm": "^10.9.2"}}