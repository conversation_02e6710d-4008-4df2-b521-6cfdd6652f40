<?php

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Accounts\Models\GlobalAccount;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Models\Season;
use Faker\Factory as Faker;
use League\FactoryMuffin\FactoryMuffin;
use Platform\Language\LanguageRepository;
use Ramsey\Uuid\Uuid;

/** @var FactoryMuffin $this */
$this->define(Account::class)
    ->setDefinitions(
        [
            'user_id' => 'factory|'.User::class,
            'chapter_quantity_limit' => Faker::create()->numberBetween(100, 200),
            'form_quantity_limit' => Faker::create()->numberBetween(100, 200),
            'region' => 'au',
            'product' => function () {
                $products = [
                    'usd-a-starter-1',
                    'usd-a-plus-1',
                    'usd-a-professional-1',
                    'usd-a-enterprise-1',
                ];

                return $products[mt_rand(0, 3)];
            },
            'subscriptionId' => 'number',
            'subscriptionProvider' => 'awardforce',
            'globalId' => function () {
                return Uuid::uuid4();
            },
            'hsd_deal' => '{}',
            'footer_link' => '',
            'footer_link_text' => '',
        ]
    )->setCallback(
        function (Account $account, $saved) {
            $language = app(LanguageRepository::class)->getByCode('en_GB');

            $account->addLanguage($language, true);
            $account->saveTranslation($language->code, 'name', Faker::create()->company(), $account->id);

            $globalAccount = new GlobalAccount;
            $globalAccount->id = $account->globalId;
            $globalAccount->database = env('DB_TESTING_CONNECTION');
            $globalAccount->region = $account->region;
            $account->setRelation('globalAccount', $globalAccount);

            // Account needs an Active Season
            $this->create(Season::class, [
                'account_id' => $account->id,
                'status' => Season::STATUS_ACTIVE,
            ]);
        }
    );
