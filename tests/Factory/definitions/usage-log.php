<?php

use AwardForce\Modules\Billing\Data\UsageLog;
use AwardForce\Modules\Billing\Enums\LoggableType;
use AwardForce\Modules\Billing\Enums\Status;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Identity\Users\Models\User;
use League\FactoryMuffin\FactoryMuffin;

/** @var FactoryMuffin $this */
$this->define(UsageLog::class)
    ->setDefinitions([
        'event' => 'ai_tokens_consumed',
        'status' => Status::Ready,
        'metrics' => [
            'promptTokens' => random_int(100, 10000),
            'completionTokens' => random_int(100, 10000),
        ],
        'metadata' => [
            'source' => 'test',
        ],
        'loggable_type' => LoggableType::Entry,
        'loggable_id' => 'factory|'.Entry::class,
        'account_id' => fn() => current_account_id(),
        'user_id' => 'factory|'.User::class,
        'created_at' => $now = now()->getTimestampMs(),
        'updated_at' => $now,
    ]);
