<?php

namespace Tests\Console\Commands;

use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use AwardForce\Modules\Seasons\Models\Season;
use Illuminate\Support\Facades\Artisan;
use Tests\BaseTestCase;
use Tests\Concerns\Database;
use Tests\Concerns\Laravel;

final class ResetSeasonDuplicatesTest extends BaseTestCase
{
    use Database;
    use Laravel;

    public function testRemovesAllDuplicateRecordsInSeason(): void
    {
        $entryA = $this->muffin(Entry::class);
        $entryB = $this->muffin(Entry::class);

        Duplicate::getForEntry($entryB)->markAsDuplicate(Duplicate::getForEntry($entryA));

        $this->assertCount(2, app(DuplicateRepository::class)->getBy('season_id', $this->season->id));
        $this->assertEquals($entryA->id, $entryB->duplicateOf()->id);

        Artisan::call('duplicates:reset', ['--season' => $this->season->slug]);

        $this->assertEmpty(app(DuplicateRepository::class)->getBy('season_id', $this->season->id));
        $this->assertTrue(Duplicate::getForEntry($entryA)->primary());
        $this->assertTrue(Duplicate::getForEntry($entryB)->primary());
    }

    public function testDoesNotRemoveDuplicatesInOtherSeasons(): void
    {
        Duplicate::getForEntry($entryA = $this->muffin(Entry::class));
        Duplicate::getForEntry($entryB = $this->muffin(Entry::class, ['season_id' => $this->muffin(Season::class)->id]));

        $this->assertCount(1, app(DuplicateRepository::class)->getBy('season_id', $entryA->seasonId));
        $this->assertCount(1, app(DuplicateRepository::class)->getBy('season_id', $entryB->seasonId));

        Artisan::call('duplicates:reset', ['--season' => $entryA->season->slug]);

        $this->assertEmpty(app(DuplicateRepository::class)->getBy('season_id', $entryA->seasonId));
        $this->assertCount(1, app(DuplicateRepository::class)->getBy('season_id', $entryB->seasonId));
    }
}
