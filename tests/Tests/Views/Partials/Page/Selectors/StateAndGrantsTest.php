<?php

namespace Tests\Views\Partials\Page\Selectors;

use Platform\Search\Filtertron;
use Tests\BaseTestCase;
use Tests\Concerns\Laravel;

class StateAndGrantsTest extends BaseTestCase
{
    use Laravel;

    public function testStateSelectGrantsUnsetGrantStatusParam(): void
    {
        $filterTron = app(Filtertron::class);
        $filterTron->with('grant_status', 'test');
        app()->instance(Filtertron::class, $filterTron);

        $view = view('partials.page.selectors.state-and-grants')->render();

        $this->assertEquals(1, substr_count($view, 'grant_status=test'));
        $this->assertStringContainsString('?grant_status=test', $view);
        $this->assertStringContainsString('showGrants=1', $view);
    }

    public function testStateSelectGrantsNoGrantStatusParam(): void
    {
        $filterTron = app(Filtertron::class);
        app()->instance(Filtertron::class, $filterTron);

        $view = view('partials.page.selectors.state-and-grants')->render();

        $this->assertEquals(0, substr_count($view, 'grant_status=test'));
    }
}
