<?php

namespace Tests\Http\Middleware;

use AwardForce\Http\Middleware\IsTempConsumer;
use AwardForce\Library\Authorization\NullConsumer;
use AwardForce\Library\Authorization\TempConsumer;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Mo<PERSON>y as m;
use Tests\IntegratedTestCase;

final class IsTempConsumerTest extends IntegratedTestCase
{
    public function testIsTemp(): void
    {
        \Consumer::set(new TempConsumer);

        $middleware = app(IsTempConsumer::class);

        $result = $middleware->handle(new Request, fn() => 'I am temp');

        $this->assertEquals('I am temp', $result);
    }

    public function testIsNot(): void
    {
        \Consumer::set(new NullConsumer());
        $request = m::mock(Request::class);
        app()->instance(Request::class, $request);
        $middleware = app(IsTempConsumer::class);

        $request->shouldReceive('session')->andReturn(session());

        $result = $middleware->handle($request, fn() => 'I am temp');

        $this->assertInstanceOf(RedirectResponse::class, $result);
    }
}
