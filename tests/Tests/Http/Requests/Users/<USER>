<?php

namespace Tests\Http\Requests\Users;

use AwardForce\Http\Requests\User\RegisterUserRequest;
use AwardForce\Library\Authorization\TempConsumer;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Tests\IntegratedTestCase;
use Tests\Support\MockGlobals;

final class RegisterUserRequestTest extends IntegratedTestCase
{
    use MockGlobals;

    private Role $defaultRole;

    public function init()
    {
        $this->initMockGlobals();
    }

    protected function setupRoles()
    {
        $this->defaultRole = $this->muffin(Role::class);

        $roleRepository = $this->mock(RoleRepository::class);
        $roleRepository->shouldReceive('getRegistrationSlugs')->once()->andReturn([(string) $this->defaultRole->slug]);
        $roleRepository->shouldReceive('getDefault')->atLeast()->once()->andReturn($this->defaultRole);
    }

    public function testValidationDoesNotIgnoreRequiredFileField(): void
    {
        $this->globalUsers->shouldReceive('getByEmail')
            ->with('<EMAIL>')
            ->andReturn(null);

        $this->setupRoles();

        $this->request = new RegisterUserRequest;
        app()->instance(Request::class, $this->request);

        $textField = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'type' => 'text',
            'role_option' => 'all',
            'required' => true,
            'registration' => 'step2',
        ]);

        $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'type' => fn() => 'file',
            'role_option' => 'all',
            'required' => true,
            'registration' => 'step2',
        ]);

        $this->request->replace([
            'firstName' => 'firstName',
            'lastName' => 'lastName',
            'password' => 'Password12345?',
            'registerEmail' => '<EMAIL>',
            'values' => [
                (string) $textField->slug => 'test',
            ],
        ]);

        $validator = Validator::make($this->request->all(), $this->request->rules());

        $this->assertFalse($validator->passes());
    }

    public function testMergeBeforeValidation(): void
    {
        $this->globalUsers->shouldReceive('getByEmail')
            ->once()
            ->andReturn(null);
        $this->globalUsers->shouldReceive('getByEmailOrMobile')
            ->once()
            ->andReturn(null);
        \Consumer::set(TempConsumer::fromLogin('<EMAIL>'));

        $request = new RegisterUserRequest;
        $request->setContainer(app());
        $request->merge([
            'firstName' => 'firstName',
            'lastName' => 'lastName',
            'password' => 'Password12345?',
            'registerEmail' => '<EMAIL>',
        ]);
        $request->validateResolved();
        $this->assertEquals('<EMAIL>', $request->registerEmail);
    }

    public function testNotRequiredFieldsArePartOfValidation(): void
    {
        $this->globalUsers->shouldReceive('getByEmail')->andReturn(null);

        $this->setupRoles();

        $this->request = new RegisterUserRequest;

        $textFields = $this->muffins(2, Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'type' => 'text',
            'role_option' => 'all',
            'required' => false,
            'registration' => 'step1',
        ]);

        $this->request->replace([
            'firstName' => 'firstName',
            'lastName' => 'lastName',
            'password' => 'Password12345?',
            'registerEmail' => '<EMAIL>',
            'values' => [(string) $textFields[0]->slug => 'test'],
        ]);

        $validator = Validator::make($this->request->all(), $this->request->rules());

        $this->assertArrayHasKey('values', $valuesValidated = $validator->validated());
        $this->assertSame('test', $valuesValidated['values'][(string) $textFields[0]->slug]);
        $this->assertArrayNotHasKey((string) $textFields[1]->slug, $valuesValidated['values']);
    }

    public function testOptionalFieldWithExtraRulesDoesntFailWhenEmpty()
    {
        $this->globalUsers->shouldReceive('getByEmail')->andReturn(null);

        $this->setupRoles();

        $nullableFieldWithMinimumCharacters = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'type' => fn() => 'text',
            'role_option' => 'all',
            'required' => false,
            'registration' => 'step1',
            'minimum_characters' => 1,
        ]);

        $nullableEmailField = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'type' => fn() => 'email',
            'role_option' => 'all',
            'required' => false,
            'registration' => 'step1',
        ]);

        $slugForFieldWithMinimumCharacters = (string) $nullableFieldWithMinimumCharacters->slug;
        $slugForEmailField = (string) $nullableEmailField->slug;
        $request = new RegisterUserRequest;
        $request->replace([
            'firstName' => 'firstName',
            'lastName' => 'lastName',
            'password' => 'Password12345?',
            'registerEmail' => '<EMAIL>',
            'values' => [
                $slugForFieldWithMinimumCharacters => '',
                $slugForEmailField => '',
            ],
        ]);

        $rules = $request->rules();
        $validator = Validator::make($request->all(), $rules);

        // assert that rules for the field were added
        $keyForFieldWithMinimumCharacters = 'values.'.$slugForFieldWithMinimumCharacters;
        $keyForEmailField = 'values.'.$slugForEmailField;

        $this->assertArrayHasKey($keyForFieldWithMinimumCharacters, $rules);
        $this->assertArrayHasKey($keyForEmailField, $rules);

        // assert that nullable is one of those rules
        $this->assertContains('nullable', $rules[$keyForFieldWithMinimumCharacters]);
        $this->assertContains('nullable', $rules[$keyForEmailField]);

        // assert that request validation passes
        $this->assertTrue($validator->passes());
    }

    public function testDefaultRoleFieldIsValidatedWhenNoRoleDefined(): void
    {
        $this->globalUsers->shouldReceive('getByEmail')->andReturn(null);

        $this->setupRoles();

        $fieldForDefaultRole = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_USERS,
            'type' => fn() => 'text',
            'role_option' => 'select',
            'required' => false,
            'registration' => 'step1',
            'minimum_characters' => 1,
        ]);

        $fieldForDefaultRole->roles()->attach($this->defaultRole->id, [
            'read_access' => 1,
            'write_access' => 1,
            'required' => 1,
        ]);

        $fieldSlug = (string) $fieldForDefaultRole->slug;
        $request = new RegisterUserRequest;
        $request->replace([
            'firstName' => 'firstName',
            'lastName' => 'lastName',
            'password' => 'Password12345?',
            'registerEmail' => '<EMAIL>',
            'values' => [
                $fieldSlug => '',
            ],
        ]);

        $validator = Validator::make($request->all(), $request->rules());

        $errors = $validator->errors()->toArray();
        $this->assertFalse($validator->passes());
        $this->assertArrayHasKey('values.'.$fieldSlug, $errors);
        $this->assertCount(1, $errors['values.'.$fieldSlug]);
    }
}
