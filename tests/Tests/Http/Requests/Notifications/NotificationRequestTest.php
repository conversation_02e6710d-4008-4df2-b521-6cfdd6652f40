<?php

namespace Tests\Http\Requests\Notifications;

use AwardForce\Http\Requests\Notifications\NotificationRequest;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Validator;
use Tests\UnitTestCase;

class NotificationRequestTest extends UnitTestCase
{
    use WithFaker;

    private NotificationRequest $request;

    public function init()
    {
        $this->request = new NotificationRequest();
    }

    public function testSmsBodyCannotBeSavedWhenExceedsLength()
    {
        $smsBodyRule = array_intersect_key($this->request->rules(), array_flip(['translated.smsBody.*']));
        $this->request->replace([
            'translated' => [
                'smsBody' => [
                    'en_GB' => str_repeat('a', config('notifications.sms.max_characters') + 1),
                ],
            ],
        ]);

        $this->assertFalse(Validator::make($this->request->all(), $smsBodyRule)->passes());
    }

    public function testSmsBodyCanBeSaved()
    {
        $smsBodyRule = array_intersect_key($this->request->rules(), array_flip(['translated.smsBody.*']));
        $this->request->replace([
            'translated' => [
                'smsBody' => [
                    'en_GB' => str_repeat('a', config('notifications.sms.max_characters')),
                ],
            ],
        ]
        );

        $this->assertTrue(Validator::make($this->request->all(), $smsBodyRule)->passes());
    }

    public function testSmsBodyCanBeSavedWithNewLines()
    {
        $newLine = "\r\n";
        $smsBody = str_repeat('a', config('notifications.sms.max_characters') - 10).$newLine.$newLine.str_repeat('a', 8);
        $smsBodyRule = array_intersect_key($this->request->rules(), array_flip(['translated.smsBody.*']));
        $this->request->replace([
            'translated' => [
                'smsBody' => [
                    'en_GB' => $smsBody,
                ],
            ],
        ]
        );

        $this->assertTrue(Validator::make($this->request->all(), $smsBodyRule)->passes());
    }

    public function testSmsBodyCannotBeSavedWithNewLinesExceedingMaxLength()
    {
        $newLine = "\r\n";
        $smsBody = str_repeat('a', config('notifications.sms.max_characters') - 10).$newLine.$newLine.str_repeat('a', 9);
        $smsBodyRule = array_intersect_key($this->request->rules(), array_flip(['translated.smsBody.*']));
        $this->request->replace([
            'translated' => [
                'smsBody' => [
                    'en_GB' => $smsBody,
                ],
            ],
        ]
        );

        $this->assertFalse(Validator::make($this->request->all(), $smsBodyRule)->passes());
    }

    public function testCreateNotificationRequestShouldFailValidationWithoutSendTimeUnitSetForBeforeDueTime()
    {
        $this->request->replace([
            'translated' => [
                'subject' => ['en_GB' => 'Subject'],
                'body' => ['en_GB' => 'Body'],
            ],
            'validation' => '',
            'trigger' => 'payment.due.date',
            'sendTimeOption' => 'before_due_time',
            'sendTimeOffset' => 10,
        ]);

        $validator = Validator::make($this->request->all(), $this->request->rules(), $this->request->messages());

        $this->assertTrue($validator->fails());
        $this->assertEquals(
            'The send time unit field is required.',
            $validator->errors()->first('sendTimeUnit')
        );
    }

    public function testCreateNotificationRequestShouldFailValidationWithoutSendTimeUnitSetForAfterDueTime()
    {
        $this->request->replace([
            'translated' => [
                'subject' => ['en_GB' => 'Subject'],
                'body' => ['en_GB' => 'Body'],
            ],
            'validation' => '',
            'trigger' => 'payment.due.date',
            'sendTimeOption' => 'after_due_time',
            'sendTimeOffset' => 10,
        ]);

        $validator = Validator::make($this->request->all(), $this->request->rules(), $this->request->messages());

        $this->assertTrue($validator->fails());
        $this->assertEquals(
            'The send time unit field is required.',
            $validator->errors()->first('sendTimeUnit')
        );
    }
}
