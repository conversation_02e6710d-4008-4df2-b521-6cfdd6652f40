<?php

namespace Tests\Modules\Entries\Services\Duplicates;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Comparers\SameCategorySimilarTitle;
use AwardForce\Modules\Entries\Services\Duplicates\DuplicateFinder;
use Tests\IntegratedTestCase;

final class DuplicateFinderTest extends IntegratedTestCase
{
    public function testSetsFirstEntryAsPrimary(): void
    {
        $entry1 = $this->muffin(Entry::class);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertTrue($entry1->primary());
    }

    public function testSetsNextEntryAsPrimaryIfInDifferentCategory(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertTrue($entry1->primary());
        $this->assertTrue($entry2->primary());
    }

    public function testSetsNextEntryAsPrimaryIfBelowThreshold(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb', 'category_id' => $entry1->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertTrue($entry1->primary());
        $this->assertTrue($entry2->primary());
    }

    public function testSetsNextEntryAsDuplicateIfAboveThreshold(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaab', 'category_id' => $entry1->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertFalse($entry2->primary());
        $this->assertEquals($entry1->id, $entry2->duplicateOf()->id);
    }

    public function testSetsEntryAsDuplicateOfClosestMatch(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaab', 'category_id' => $entry1->categoryId]);
        $entry3 = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb', 'category_id' => $entry1->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertEquals($entry1->id, $entry2->duplicateOf()->id);
        $this->assertEquals($entry2->id, $entry3->duplicateOf()->id);

        $this->assertCount(2, $descendants = $entry1->duplicate->descendants()->pluck('entry_id'));
        $this->assertContains($entry2->id, $descendants);
        $this->assertContains($entry3->id, $descendants);
    }

    public function testConfirmedNotDuplicateIsNotReconsidered(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaab', 'category_id' => $entry1->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertEquals($entry1->id, $entry2->duplicateOf()->id);

        $entry2->duplicate->confirmNotDuplicate();
        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertTrue($entry2->primary());
    }

    public function testUnconfirmedPrimaryCanBeDuplicateAtLowerThreshold(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb', 'category_id' => $entry1->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertTrue($entry2->primary());

        (new DuplicateFinder(new SameCategorySimilarTitle(80)))->findInSeason($this->season->id);

        $this->assertEquals($entry1->id, $entry2->fresh()->duplicateOf()->id);
    }

    public function testUnconfirmedDuplicateCanBePrimaryAtHigherThreshold(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaab', 'category_id' => $entry1->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertEquals($entry1->id, $entry2->duplicateOf()->id);

        (new DuplicateFinder(new SameCategorySimilarTitle(95)))->findInSeason($this->season->id);

        $this->assertTrue($entry2->fresh()->primary());
        $this->assertNull($entry2->fresh()->duplicateOf());
    }

    public function testForcedPrimaryIsRememberedAfterRescan(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaab', 'category_id' => $entry1->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertEquals($entry1->id, $entry2->duplicateOf()->id);

        $entry2->duplicate->forcePrimary();
        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertTrue($entry2->primary());
        $this->assertEquals($entry2->id, $entry1->duplicateOf()->id);
    }

    public function testDeletedAndArchivedEntriesAreNotConsidered(): void
    {
        [$title, $categoryId] = ['aaaaaaaaaa', $this->muffin(Category::class)->id];

        $toBeDeleted = $this->muffin(Entry::class, ['title' => $title, 'category_id' => $categoryId]);
        $toBeArchived = $this->muffin(Entry::class, ['title' => $title, 'category_id' => $categoryId]);

        $entry1 = $this->muffin(Entry::class, ['title' => $title, 'category_id' => $categoryId]);
        $entry2 = $this->muffin(Entry::class, ['title' => $title, 'category_id' => $categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertEquals($toBeDeleted->id, $entry1->duplicateOf()->id);
        $this->assertEquals($toBeDeleted->id, $entry2->duplicateOf()->id);

        $toBeDeleted->delete();
        $toBeArchived->archive();

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertEquals($entry1->id, $entry2->fresh()->duplicateOf()->id);
    }
}
