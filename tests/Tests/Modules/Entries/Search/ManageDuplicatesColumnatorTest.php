<?php

namespace Tests\Modules\Entries\Search;

use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Modules\Entries\Contracts\DuplicateRepository;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Comparers\SameCategorySimilarTitle;
use AwardForce\Modules\Entries\Services\Duplicates\DuplicateFinder;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use Platform\Events\EventDispatcher;
use Tests\IntegratedTestCase;
use Tests\Library\Search\ColumnatorTestHelper;

final class ManageDuplicatesColumnatorTest extends IntegratedTestCase
{
    use ColumnatorTestHelper;
    use EventDispatcher;

    protected function area()
    {
        return 'manage_duplicates.search';
    }

    public function testShowsPrimaryEntriesWithUnconfirmedDuplicates(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaab', 'category_id' => $entry1->categoryId]);
        $entry3 = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb', 'category_id' => $entry1->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertCount(1, $primaries = $this->search());
        $this->assertEquals($entry1->duplicate->id, $primaries->first()->id);
        $this->assertCount(2, app(DuplicateRepository::class)->unconfirmedDuplicates($entry1));

        $entry2->duplicate->confirmAndArchive($entry1);

        $this->assertCount(1, $primaries = $this->search());
        $this->assertEquals($entry1->duplicate->id, $primaries->first()->id);
        $this->assertCount(1, app(DuplicateRepository::class)->unconfirmedDuplicates($entry1));

        $entry3->duplicate->confirmAndArchive($entry1);

        $this->assertEmpty($this->search());
    }

    public function testHidesOutOfDateDuplicates(): void
    {
        $this->spy(Database::class);
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa', 'local_id' => 1]);
        $entry2 = $this->muffin(Entry::class, ['title' => 'aaaaaaaaab', 'category_id' => $entry1->categoryId, 'local_id' => 2]);
        $entry3 = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb', 'category_id' => $entry1->categoryId, 'local_id' => 3]);

        (new DuplicateFinder(new SameCategorySimilarTitle(80)))->findInSeason($this->season->id);

        $this->assertCount(1, $primaries = $this->search());
        $this->assertEquals($entry1->duplicate->id, $primaries->first()->id);
        $this->assertCount(2, app(DuplicateRepository::class)->unconfirmedDuplicates($entry1));

        $entry3->edit($entry3->userId, $entry3->chapterId, $entry3->categoryId, 'cccccccccc')->save();
        $this->dispatch($entry3->releaseEvents());

        $this->assertCount(1, $primaries = $this->search());
        $this->assertEquals($entry1->duplicate->id, $primaries->first()->id);
        $this->assertCount(1, $duplicates = app(DuplicateRepository::class)->unconfirmedDuplicates($entry1));
        $this->assertEquals($entry2->id, $duplicates->first()->entryId);
    }

    public function testHidesDeletedOrArchivedEntries(): void
    {
        $primary = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']);
        $duplicate = $this->muffin(Entry::class, ['title' => 'aaaaaaaaab', 'category_id' => $primary->categoryId]);
        $archivedDuplicate = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb', 'category_id' => $primary->categoryId]);
        $deletedDuplicate = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb', 'category_id' => $primary->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        $this->assertCount(1, $primaries = $this->search());
        $this->assertEquals($primary->duplicate->id, $primaries->first()->id);
        $this->assertCount(3, app(DuplicateRepository::class)->unconfirmedDuplicates($primary));

        $archivedDuplicate->archive();
        $deletedDuplicate->delete();

        $this->assertCount(1, $primaries = $this->search());
        $this->assertEquals($primary->duplicate->id, $primaries->first()->id);
        $this->assertCount(1, app(DuplicateRepository::class)->unconfirmedDuplicates($primary));

        $primary->archive();

        $this->assertEmpty($this->search());
    }

    public function testItDoesntRetrieveEntriesFromDeletedForms()
    {
        Feature::shouldReceive('enabled')->with('multiform')->twice()->andReturnTrue(); // One for ActiveSettings, and one for the FormFilter
        $mainEntryOne = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa', 'form_id' => ($deletedForm = $this->muffin(Form::class))->id]);
        $duplicateEntryOne = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa', 'category_id' => $mainEntryOne->categoryId, 'form_id' => $deletedForm->id]);
        $mainEntryTwo = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb']);
        $duplicateEntryTwo = $this->muffin(Entry::class, ['title' => 'aaaaaaaabb', 'category_id' => $mainEntryTwo->categoryId]);

        (new DuplicateFinder(new SameCategorySimilarTitle(85)))->findInSeason($this->season->id);

        // delete form after duplicates have been found
        // if we delete the form before, the duplicates will not be found and therefore no need to filter them out
        $deletedForm->delete();

        $this->assertCount(1, $primaries = $this->search());
        $this->assertCount(1, $duplicates = $primaries->first()->duplicates);
        $this->assertEquals($mainEntryTwo->id, $primaries->first()->entry->id);
        $this->assertEquals($duplicateEntryTwo->id, $duplicates->first()->entry->id);
    }
}
