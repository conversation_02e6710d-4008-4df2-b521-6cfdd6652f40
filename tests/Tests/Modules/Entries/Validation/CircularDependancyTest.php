<?php

namespace Tests\Modules\Entires\Validation;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use AwardForce\Modules\Entries\Validation\CircularDependancyValidator as Validator;
use Tests\IntegratedTestCase;

final class CircularDependancyTest extends IntegratedTestCase
{
    public function testPassesWhenPrimaryDuplicateRecordDoesNotExist(): void
    {
        $primaryEntry = $this->muffin(Entry::class);

        $this->assertTrue((new Validator([]))->passes('primary', $primaryEntry->id));
    }

    public function testPassesWhenPrimaryNotDescendantOfChildAndNotInSelectedEntries(): void
    {
        $primaryEntry = $this->muffin(Entry::class);
        $primary = Duplicate::getForEntry($primaryEntry);
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicateAndArchive($primary);
        $selected = ['100000', '100001', '100002'];

        $this->assertTrue((new Validator($selected))->passes('primary', $primaryEntry->id));
    }

    public function testFailsWhenPrimaryIsAncestorOfChild(): void
    {
        $childEntry = $this->muffin(Entry::class);
        $childDuplicate = Duplicate::getForEntry($childEntry);
        $primaryEntry = $this->muffin(Entry::class);
        $primaryDuplicate = Duplicate::getForEntry($primaryEntry);
        $primaryDuplicate->markAsDuplicate($childDuplicate);

        $selected = [$childEntry->id];

        $this->assertFalse((new Validator($selected))->passes('primary', $primaryEntry->id));
    }

    public function testFailsWhenPrimaryIsInSelectedEntries(): void
    {
        $primaryEntry = $this->muffin(Entry::class);
        $primary = Duplicate::getForEntry($primaryEntry);
        $duplicate = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate->markAsDuplicateAndArchive($primary);
        $selected = ['1000', '1001', $primaryEntry->id];

        $this->assertFalse((new Validator($selected))->passes('primary', $primaryEntry->id));
    }

    public function testFailsWhenPrimaryIsSelectedAndHasNoDuplicateModel(): void
    {
        $primaryEntry = $this->muffin(Entry::class);
        $selected = [$primaryEntry->id];

        $this->assertFalse((new Validator($selected))->passes('primary', $primaryEntry->id));
    }
}
