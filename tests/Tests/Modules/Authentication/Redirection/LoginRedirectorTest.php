<?php

namespace Tests\Library\Http;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\Manager;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Assignments\Models\AssignmentFactory;
use AwardForce\Modules\Assignments\Models\AssignmentRepository;
use AwardForce\Modules\Assignments\Services\CurrentAssignments;
use AwardForce\Modules\Authentication\Redirection\LoginRedirector;
use AwardForce\Modules\Authentication\Redirection\Redirectors\AssignmentRedirector;
use AwardForce\Modules\Authentication\Redirection\Redirectors\GalleryRedirector;
use AwardForce\Modules\Identity\Roles\Contracts\PermissionRepository;
use AwardForce\Modules\Identity\Roles\Models\Permission;
use AwardForce\Modules\Identity\Roles\Models\PermissionCollection;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Roles\ValueObjects\Mode;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Judging\Services\Dashboard\ContextJudging;
use AwardForce\Modules\Rounds\Models\RoundCollection;
use AwardForce\Modules\Rounds\Repositories\RoundRepository;
use AwardForce\Modules\Rounds\Services\RoundStatus;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoreSets\Models\ScoreSetRepository;
use AwardForce\Modules\Seasons\Models\Season;
use Carbon\Carbon;
use Eloquence\Behaviours\Slug;
use Illuminate\Http\Request;
use Illuminate\Routing\UrlGenerator;
use Mockery as m;
use Tests\IntegratedTestCase;

final class LoginRedirectorTest extends IntegratedTestCase
{
    /**
     * @var m\MockInterface
     */
    public $consumer;

    /**
     * @var m\MockInterface
     */
    public $assignments;

    /**
     * @var m\MockInterface
     */
    public $request;

    /**
     * @var RoundRepository
     */
    public $rounds;

    /**
     * @var m\MockInterface
     */
    public $url;

    /**
     * @var LoginRedirector
     */
    public $redirector;

    public function init()
    {
        $this->assignments = m::mock(CurrentAssignments::class);
        $this->assignments->shouldReceive('lastUpdatedNonGalleryAssignment')->andReturnNull()->byDefault();
        $this->instance(CurrentAssignments::class, $this->assignments);

        $this->consumer = m::mock(Manager::class);
        $this->consumer->shouldReceive('isGuest')->andReturn(false)->byDefault();
        $this->consumer->shouldReceive('isProgramManager')->andReturn(false)->byDefault();
        $this->consumer->shouldReceive('can')->with('view', 'EntriesAll')->andReturn(false)->byDefault();
        $this->consumer->shouldReceive('isJudge')->andReturn(false)->byDefault();
        $this->consumer->shouldReceive('isEntrant')->andReturn(false)->byDefault();
        $this->consumer->shouldReceive('jediId')->andReturn('')->byDefault();
        $this->consumer->shouldReceive('get')->andReturn(1)->byDefault();
        $this->consumer->shouldReceive('user')->andReturn($this->muffin(User::class))->byDefault();
        $this->consumer->shouldReceive('languageCode')->andReturn('en_GB')->byDefault();
        $this->instance(Manager::class, $this->consumer);

        $this->request = m::mock(Request::class);
        $this->request->shouldReceive('session->has')->with('intended')->andReturn(false)->byDefault();
        $this->request->shouldReceive('get')->andReturn(null)->byDefault();
        $this->instance(Request::class, $this->request);

        $this->rounds = m::mock(RoundRepository::class);
        $this->rounds->shouldReceive('getAllActive->count')->andReturn(0)->byDefault();
        $this->rounds->shouldReceive('getScoreSetRounds')->andReturn(new RoundCollection())->byDefault();
        $this->instance(RoundRepository::class, $this->rounds);

        $this->url = m::mock(UrlGenerator::class);
        $this->instance('url', $this->url);
        $this->url->shouldReceive('route')->with('judge.index', [], true)->andReturn('redirect.mock')->byDefault();

        $this->redirector = app(LoginRedirector::class);

        \Auth::setUser($this->muffin(User::class));
    }

    public function testRedirectsToIntendedUrl(): void
    {
        $this->request->shouldReceive('session->has')->with('intended')->andReturn(true);
        $this->request->shouldReceive('session->get')->with('intended')->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectsGuestsToHome(): void
    {
        $this->consumer->shouldReceive('isGuest')->andReturn(true);
        $this->url->shouldReceive('route')->with('home', [], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectsProgramManagersToDashboardIfAccountHasEntries(): void
    {
        $season = m::mock(Season::class);
        $season->shouldReceive('hasEntries')->andReturn(true);

        CurrentAccount::shouldReceive('get');
        CurrentAccount::shouldReceive('activeSeason')->andReturn($season);

        $this->consumer->shouldReceive('isProgramManager')->andReturn(true);
        $this->url->shouldReceive('route')->with('dashboard.index', [], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectsProgramManagersToSetupGuideIfAccountHasNoEntries(): void
    {
        $season = m::mock(Season::class);
        $season->shouldReceive('hasEntries')->andReturn(false);

        CurrentAccount::shouldReceive('get');
        CurrentAccount::shouldReceive('activeSeason')->andReturn($season);

        $this->consumer->shouldReceive('isProgramManager')->andReturn(true);
        $this->url->shouldReceive('route')->with('guides-and-tours.index', [], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectsUsersWithEntriesAllToManageEntries(): void
    {
        $this->consumer->shouldReceive('can')->with('view', 'EntriesAll')->andReturn(true);
        $this->url->shouldReceive('route')->with('entry.manager.index', [], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectJudgeWithoutAssignment(): void
    {
        $this->consumer->shouldReceive('isJudge')->andReturn(true);
        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn(null);
        $this->assignments->shouldReceive('lastUpdatedNonGalleryAssignment')->andReturn(null);
        $this->url->shouldReceive('route')->with('judge.index', [], true)->andReturn('redirect');
        $this->rounds->shouldReceive('getAllActive->count')->andReturn(1);

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testJudgeIsRedirectedToJudgingDashboardIfTheyHaveAssignmentsToMultipleScoreSets(): void
    {
        $this->user = $this->setupUserWithRole('Judge', true);

        $scoreSets = collect($this->muffins(3, ScoreSet::class, ['mode' => ScoreSet::MODE_QUALIFYING]));

        $contextJudging = m::mock(ContextJudging::class);
        $this->instance(ContextJudging::class, $contextJudging);
        $contextJudging->shouldReceive('getScoreSetsAndRounds')->andReturn([$scoreSets, collect()]);

        $assignments = $scoreSets->map(fn(ScoreSet $scoreSet) => $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSet->getKey(),
            'judge_id' => $this->user->getKey(),
        ]));

        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn(null);
        $this->assignments->shouldReceive('lastUpdatedNonGalleryAssignment')->andReturn($assignments->first());

        $this->consumer->shouldReceive('get')->andReturn($this->user);
        $this->consumer->shouldReceive('isGuest')->andReturn(false);
        $this->consumer->shouldReceive('isJudge')->andReturn(true);

        $this->url->shouldReceive('route')->once()->with('judge.index', [], true)->andReturn('redirect');

        $this->rounds->shouldReceive('getAllActive->count')->andReturn(true);

        $this->redirector->route();
    }

    public function testJudgeIsRedirectedToJudgingDashboardIfTheyHaveAssignmentsToMultipleScoreSetsAndOpenGallery(): void
    {
        $this->user = $this->setupUserWithRole('Judge', true);

        $scoreSets = collect($this->muffins(3, ScoreSet::class, ['mode' => ScoreSet::MODE_QUALIFYING]));

        $contextJudging = m::mock(ContextJudging::class);
        $this->instance(ContextJudging::class, $contextJudging);
        $contextJudging->shouldReceive('getScoreSetsAndRounds')->andReturn([$scoreSets, collect()]);

        $assignments = $scoreSets->map(fn(ScoreSet $scoreSet) => $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSet->getKey(),
            'judge_id' => $this->user->getKey(),
        ]));
        $gallery = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_GALLERY, 'updated_at' => now()]);
        $assignmentGallery = $this->muffin(Assignment::class, ['score_set_id' => $gallery->getKey(), 'judge_id' => $this->user->getKey()]);

        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn($assignmentGallery);
        $this->assignments->shouldReceive('lastUpdatedNonGalleryAssignment')->andReturn($assignments->first());

        $this->consumer->shouldReceive('get')->andReturn($this->user);
        $this->consumer->shouldReceive('isGuest')->andReturn(false);
        $this->consumer->shouldReceive('isJudge')->andReturn(true);

        $this->url->shouldReceive('route')->once()->with('judge.index', [], true)->andReturn('redirect');

        $this->rounds->shouldReceive('getAllActive->count')->andReturn(true);

        $this->redirector->route();
    }

    public function testJudgeIsRedirectedToScoreSetAssignmentIfTheyHaveAssignmentsToASingleScoreSet(): void
    {
        $this->user = $this->setupUserWithRole('Judge', true);

        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_QUALIFYING]);

        $contextJudging = m::mock(ContextJudging::class);
        $this->instance(ContextJudging::class, $contextJudging);
        $contextJudging->shouldReceive('getScoreSetsAndRounds')->andReturn([collect([$scoreSet]), collect()]);

        $assignment = $this->muffin(Assignment::class, [
            'score_set_id' => $scoreSet->getKey(),
            'judge_id' => $this->user->getKey(),
        ]);

        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn(null);
        $this->assignments->shouldReceive('lastUpdatedNonGalleryAssignment')->andReturn($assignment);

        $this->consumer->shouldReceive('get')->andReturn($this->user);
        $this->consumer->shouldReceive('isGuest')->andReturn(false);
        $this->consumer->shouldReceive('isJudge')->andReturn(true);

        $this->url->shouldReceive('route')->once()->with('qualifying.index', ['score-set' => $assignment->scoreSet->id], true)->andReturn('redirect');

        $this->rounds->shouldReceive('getAllActive->count')->andReturn(true);

        $this->redirector->route();
    }

    public function testRedirectEntrantWithAssignmentAndNoOpenRound(): void
    {
        $scoreSet = new ScoreSet;
        $scoreSet->mode = ScoreSet::MODE_VOTING;

        $assignment = new Assignment;
        $assignment->setRelation('scoreSet', $scoreSet);

        $this->consumer->shouldReceive('isJudge')->andReturn(true);
        $this->consumer->shouldReceive('isEntrant')->andReturn(true);

        $this->assignments->shouldReceive('lastUpdatedNonGalleryAssignment')->andReturn($assignment);
        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn(null);
        $this->rounds->shouldReceive('getAllActive->count')->andReturn(0);

        $this->url->shouldReceive('route')->with('entry.entrant.index', [], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectEntrantWithAssignmentAndOpenRound(): void
    {
        $scoreSet = new ScoreSet;
        $scoreSet->mode = ScoreSet::MODE_VOTING;
        $scoreSet->slug = new Slug('slug');

        $assignment = new Assignment;
        $assignment->setRelation('scoreSet', $scoreSet);

        $contextJudging = m::mock(ContextJudging::class);
        $this->instance(ContextJudging::class, $contextJudging);
        $contextJudging->shouldReceive('getScoreSetsAndRounds')->andReturn([collect([$scoreSet]), collect()]);

        $this->consumer->shouldReceive('isJudge')->andReturn(true);
        $this->consumer->shouldReceive('isEntrant')->andReturn(true);

        $this->assignments->shouldReceive('lastUpdatedNonGalleryAssignment')->andReturn($assignment);
        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn(null);
        $this->rounds->shouldReceive('getAllActive->count')->andReturn(1);

        $this->url->shouldReceive('route')->with('voting.index', m::any(), true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectToEntry(): void
    {
        $this->consumer->shouldReceive('isEntrant')->andReturn(true);
        $this->url->shouldReceive('route')->with('entry.entrant.index', [], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectToFirstMenuLink(): void
    {
        $user = $this->muffin(User::class);
        $permissions = m::mock(PermissionRepository::class);
        $permissions->shouldReceive('getByAccountAndUserId')->with(current_account()->id, $user->id)
            ->andReturn(new PermissionCollection([
                new Permission(['resource' => 'Users', 'action' => 'view', 'mode' => new Mode('allow')]),
            ]));
        $this->app->instance(PermissionRepository::class, $permissions);
        Consumer::set(new UserConsumer($user, $permissions));
        $this->url->shouldReceive('route')->with('leaderboard.progress', [], true)->andReturn('redirect');

        //Expect to redirect to 'leaderboard.progress' route name
        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectsToGalleryWhenOpen(): void
    {
        $scoreSet = new ScoreSet;
        $scoreSet->mode = ScoreSet::MODE_GALLERY;
        $scoreSet->slug = new Slug('slug');
        $scoreSet->galleryEndAt(Carbon::tomorrow()->format('Y-m-d H:i'), 'UTC');

        $assignment = new Assignment;
        $assignment->setRelation('scoreSet', $scoreSet);

        $this->consumer->shouldReceive('isJudge')->andReturn(true);
        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn($assignment);

        $this->url->shouldReceive('route')
            ->with('gallery.index', ['scoreSet' => $scoreSet->slug], true)
            ->andReturn('to-gallery');

        $this->assertEquals('to-gallery', $this->redirector->route());
    }

    public function testDoesNotRedirectToGalleryWhenClosed(): void
    {
        $scoreSet = new ScoreSet;
        $scoreSet->mode = ScoreSet::MODE_GALLERY;
        $scoreSet->slug = new Slug('slug');
        $scoreSet->galleryEndAt(Carbon::yesterday()->format('Y-m-d H:i'), 'UTC');

        $assignment = new Assignment;
        $assignment->setRelation('scoreSet', $scoreSet);

        $this->consumer->shouldReceive('isJudge')->andReturn(true);
        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn($assignment);

        $this->url->shouldReceive('route')
            ->with('judging.index', [], true)
            ->andReturn('to-judging');

        $this->assertEquals('to-judging', $this->redirector->route());
    }

    public function testItRedirectsToJudgingDashboardInsteadOfGalleryWhenClosedAndMoreThanOneScoreSet(): void
    {
        $this->user = $this->setupUserWithRole('Judge', true);

        $nonGallery = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_QUALIFYING]);
        $gallery = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_GALLERY]);
        $gallery->galleryEndAt(Carbon::yesterday()->format('Y-m-d H:i'), 'UTC');
        $gallery->save();

        $contextJudging = m::mock(ContextJudging::class);
        $this->instance(ContextJudging::class, $contextJudging);
        $contextJudging->shouldReceive('getScoreSetsAndRounds')->andReturn([collect([$nonGallery]), collect()]);

        $assignmentGallery = $this->muffin(Assignment::class, ['score_set_id' => $gallery->getKey(), 'judge_id' => $this->user->getKey()]);
        $assignmentNonGallery = $this->muffin(Assignment::class, ['score_set_id' => $nonGallery->getKey(), 'judge_id' => $this->user->getKey()]);

        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn($assignmentGallery);
        $this->assignments->shouldReceive('lastUpdatedNonGalleryAssignment')->andReturn($assignmentNonGallery);
        $this->consumer->shouldReceive('get')->andReturn($this->user);
        $this->consumer->shouldReceive('isGuest')->andReturn(false);
        $this->consumer->shouldReceive('isJudge')->andReturn(true);

        $this->rounds->shouldReceive('getAllActive->count')->andReturn(1);

        $this->url->shouldReceive('route')->once()->with('qualifying.index', ['score-set' => $nonGallery->getKey()], true)->andReturn('redirect');

        $this->redirector->route();
    }

    public function testRedirectToProfile(): void
    {
        $this->url->shouldReceive('route')->with('judge.index', [], true)->andReturn('redirect');
        $this->url->shouldReceive('route')->with('profile.show', [], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->route());
    }

    public function testRedirectsToGalleryFromPastSeason(): void
    {
        $pastSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $currentSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ACTIVE]);

        $gallery = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_GALLERY, 'season_id' => $pastSeason->id, 'updated_at' => Carbon::now(),
        ]);
        $nonGallery = $this->muffin(ScoreSet::class, [
            'mode' => ScoreSet::MODE_TOP_PICK, 'season_id' => $pastSeason->id, 'updated_at' => Carbon::yesterday(),
        ]);

        $user = $this->user = $this->setupUserWithRole('Judge', true);
        $assignmentGallery = $this->muffin(Assignment::class, [
            'score_set_id' => $gallery->id, 'judge_id' => $this->user->id, 'season_id' => $gallery->seasonId,
        ]);
        $assignmentNonGallery = $this->muffin(Assignment::class, [
            'score_set_id' => $nonGallery->id, 'judge_id' => $this->user->id, 'season_id' => $nonGallery->seasonId,
        ]);

        $this->instance(RoundRepository::class, null);
        $currentAssignments = new CurrentAssignments(
            app(AssignmentRepository::class),
            app(RoundStatus::class),
            app(AssignmentFactory::class),
            $scoreSets = app(ScoreSetRepository::class)
        );
        $this->instance(CurrentAssignments::class, $currentAssignments);
        $this->rounds->shouldReceive('getAllActive->count')->andReturn(0);

        Consumer::shouldReceive('get')->andReturn($this->user);
        Consumer::shouldReceive('isGuest')->andReturn(false);
        Consumer::shouldReceive('user')->andReturn($user)->byDefault();
        Consumer::shouldReceive('id')->andReturn($user->getKey())->byDefault();

        $this->consumer->shouldReceive('isJudge')->andReturn(true);
        $this->assignments->shouldReceive('lastUpdatedGalleryAssignment')->andReturn($assignmentGallery);
        $this->url->shouldReceive('route')
            ->with('gallery.index', ['scoreSet' => $assignmentGallery->scoreSet->slug], true)
            ->andReturn('redirect-to-gallery');

        $redirector = new LoginRedirector($this->consumer);
        $redirector->registerRedirector(new AssignmentRedirector($this->rounds));
        $redirector->registerRedirector(new GalleryRedirector($currentAssignments, $this->rounds));

        $this->assertEquals('redirect-to-gallery', $redirector->route());
    }

    public function testAfterRoleRegistrationContentBlock(): void
    {
        $role = new Role;
        $role->slug = new Slug('slug');
        $role->completedContentId = 1;

        $this->url->shouldReceive('route')->with('register.role.complete', [$role->slug], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->afterRoleRegistration($role));
    }

    public function testAfterRoleRegistrationScoreSet(): void
    {
        $scoreSet = new ScoreSet;
        $scoreSet->mode = ScoreSet::MODE_VIP;

        $role = new Role;
        $role->slug = new Slug('slug');
        $role->completedScoreSetId = 1;

        $role->setRelation('completedScoreSet', $scoreSet);

        $this->url->shouldReceive('route')->with('judging.index', [], true)->andReturn('redirect');

        $this->assertEquals('redirect', $this->redirector->afterRoleRegistration($role));
    }

    public function testAfterRoleRegistrationFallback(): void
    {
        $this->url->shouldReceive('route')->with('judge.index', [], true)->andReturn('redirect');
        $this->url->shouldReceive('route')->with('profile.show', [], true)->twice()->andReturn('redirect');

        $this->assertEquals($this->redirector->route(), $this->redirector->afterRoleRegistration(new Role));
    }
}
