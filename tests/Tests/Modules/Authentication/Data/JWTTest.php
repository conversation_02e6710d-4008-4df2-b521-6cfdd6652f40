<?php

namespace Authentication\Data;

use AwardForce\Modules\Authentication\Tokens\JWT;
use Tests\IntegratedTestCase;
use Tests\Modules\Authentication\Data\PrismaticKey;

final class JWTTest extends IntegratedTestCase
{
    use PrismaticKey;

    private JWT $jwt;

    public function init()
    {
        $this->jwt = (new JWT($this->privateKey(), ['custom' => 'value']));
    }

    public function testGeneratesJWTWithCustomData(): void
    {
        $token = $this->jwt->generate();

        $this->assertIsString($token);
        $this->assertNotEmpty($token);
    }

    public function testExpires(): void
    {
        $this->assertEquals(240, $this->jwt->expires()); // 4 hours in minutes
    }
}
