<?php

namespace Tests\Modules\Authentication\Middleware;

use AwardForce\Library\Authorization\GuestConsumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Audit\Events\Auditor;
use AwardForce\Modules\Authentication\Authenticators\Adapter;
use AwardForce\Modules\Authentication\Authenticators\Authenticator as AuthenticatorManager;
use AwardForce\Modules\Authentication\Data\GlobalAuthenticator as Model;
use AwardForce\Modules\Authentication\Middleware\Authenticator;
use AwardForce\Modules\Authentication\Services\Emulator\Facades\JediEmulator;
use AwardForce\Modules\Identity\Users\Models\GlobalUser;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Identity\Users\Services\Emulation\UserEmulatorService;
use Carbon\Carbon;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Mockery as m;
use Tests\Stubs\FakeConsumerManager;
use Tests\UnitTestCase;

final class AuthenticatorTest extends UnitTestCase
{
    const RESPONSE = 'Use the force, Luke!';

    /**
     * @var User
     */
    private $user;

    /**
     * @var FakeConsumerManager
     */
    private $consumerManager;

    public function init()
    {
        AuthenticatorManager::setAdapters([FakeAdapter::class]);
        User::unguard();

        $this->user = new User(['id' => 1]);
        $this->consumerManager = new FakeConsumerManager($this->user);
    }

    public function testNextIfGuest(): void
    {
        $this->consumerManager->set(m::mock(GuestConsumer::class));

        $middleware = new Authenticator($this->consumerManager);
        $response = $middleware->handle($request = $this->request(), $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function testNextWhenNoMFA(): void
    {
        $middleware = new Authenticator($this->consumerManager);
        $response = $middleware->handle($request = $this->request(), $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function testChallengeWithMFA(): void
    {
        $middleware = new Authenticator($this->consumerManager);
        $this->enableAuthenticator();

        $response = $middleware->handle($request = $this->request(), $this->unusedNext());

        $this->assertRedirectedTo($response, 'authenticator.challenge');
        $this->assertEquals(route('dashboard.index'), Session::get('intended'));
    }

    public function testNextWhenJedi(): void
    {
        JediEmulator::shouldReceive('active')->once()->andReturn(true);
        $this->enableAuthenticator();

        $response = (new Authenticator($this->consumerManager))
            ->handle($request = $this->request(), $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function testNextOnAccountSwitcher(): void
    {
        $this->enableAuthenticator();
        Session::put(Auditor::SESSION_SWITCHER, $this->user->id);

        $response = (new Authenticator($this->consumerManager))
            ->handle($request = $this->request(), $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function testNextWhenSessionHasPriorSuccess(): void
    {
        $this->enableAuthenticator();
        Session::put(Authenticator::getChallengeSuccess(), $this->user->id);

        $response = (new Authenticator($this->consumerManager))
            ->handle($request = $this->request(), $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function testNextWhenCookieHasPriorSuccess(): void
    {
        $this->enableAuthenticator();
        $request = $this->request();
        $request->shouldReceive('cookie')->with(Authenticator::getChallengeSuccess(), '')->andReturn(json_encode([
            'id' => $this->user->id,
            'expires' => Carbon::now()->addMinutes(5)->timestamp,
        ]));

        $response = (new Authenticator($this->consumerManager))->handle($request, $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function testNextWhenExceptRoute(): void
    {
        $this->enableAuthenticator();
        $request = $this->request('profile.pusher');

        $response = (new Authenticator($this->consumerManager))->handle($request, $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function testEnforceWhenCookieIsDifferentUser(): void
    {
        $this->enableAuthenticator();
        $request = $this->request();
        $request->shouldReceive('cookie')->with(Authenticator::getChallengeSuccess(), '')->andReturn(json_encode([
            'id' => $this->user->id + 1,
            'expires' => Carbon::now()->addMinutes(5)->timestamp,
        ]));

        $response = (new Authenticator($this->consumerManager))->handle($request, $this->next($request));

        $this->assertRedirectedTo($response, 'authenticator.challenge');
        $this->assertEquals(route('dashboard.index'), Session::get('intended'));
    }

    public function testEnforceWhenCookieHasExpired(): void
    {
        $this->enableAuthenticator();
        $request = $this->request();
        $request->shouldReceive('cookie')->with(Authenticator::getChallengeSuccess(), '')->andReturn(json_encode([
            'id' => $this->user->id,
            'expires' => Carbon::now()->subMinutes(5)->timestamp,
        ]));

        $response = (new Authenticator($this->consumerManager))->handle($request, $this->next($request));

        $this->assertRedirectedTo($response, 'authenticator.challenge');
        $this->assertEquals(route('dashboard.index'), Session::get('intended'));
    }

    public function testRegisterWhenRequiredByConsumer(): void
    {
        $consumer = m::mock(UserConsumer::class);
        $consumer->shouldReceive('authenticatorRequired')->andReturn(true);

        $this->consumerManager->set($consumer);

        $response = (new Authenticator($this->consumerManager))->handle($this->request(), $this->unusedNext());

        $this->assertRedirectedTo($response, 'authenticator.index');
    }

    public function testNextOnSetupRouteWhenRequiredButNotSetup(): void
    {
        $consumer = m::mock(UserConsumer::class);
        $consumer->shouldReceive('authenticatorRequired')->andReturn(true);

        $request = $this->request('authenticator.index');

        $this->consumerManager->set($consumer);

        $response = (new Authenticator($this->consumerManager))->handle($request, $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    public function testNextWhenEmulating(): void
    {
        $this->enableAuthenticator();
        $emulator = m::mock(UserEmulatorService::class);
        app()->instance(UserEmulatorService::class, $emulator);
        $emulator->shouldReceive('active')->once()->andReturn(true);

        $response = (new Authenticator($this->consumerManager))
            ->handle($request = $this->request(), $this->next($request));

        $this->assertEquals(self::RESPONSE, $response);
    }

    private function request($route = 'dashboard.index')
    {
        return tap(m::mock(Request::class), function (m\MockInterface $mock) use ($route) {
            $mock->shouldReceive('fullUrl')->andReturn(route($route));
            $mock->shouldReceive('route->getName')->andReturn($route);
            $mock->shouldReceive('user')->andReturn($this->user);
            $mock->shouldReceive('cookie')->with(Authenticator::getChallengeSuccess(), '')->andReturn('')->byDefault();
        });
    }

    private function next($expected)
    {
        return function ($actual) use ($expected) {
            $this->assertSame($expected, $actual);

            return self::RESPONSE;
        };
    }

    private function unusedNext()
    {
        return function () {
            throw new \Exception('$next($request) was called when it should not have been!');
        };
    }

    private function assertRedirectedTo($response, $redirect)
    {
        $this->assertInstanceOf(RedirectResponse::class, $response);
        $this->assertEquals(route($redirect), $response->getTargetUrl());
    }

    private function enableAuthenticator()
    {
        $this->user->globalUser->setRelation('authenticators', collect([new Model]));
    }
}

class FakeAdapter implements Adapter
{
    public function name(): string
    {
        return 'fake';
    }

    public function primary(): bool
    {
        return true;
    }

    public function registered(GlobalUser $user): bool
    {
        return $user->relationLoaded('authenticators');
    }

    public function initiate(GlobalUser $user): array
    {
        return [];
    }

    public function settings(GlobalUser $user): array
    {
    }

    public function challenge(GlobalUser $user): void
    {
    }

    public function verify(GlobalUser $user, $payload): bool
    {
        return false;
    }

    public function enabled(): bool
    {
        return true;
    }
}
