<?php

namespace Tests\Modules\Authentication\Commands;

use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Authentication\Commands\RequestLoginLink;
use AwardForce\Modules\Authentication\Commands\RequestLoginLinkHandler;
use AwardForce\Modules\Authentication\Services\SixDigitsCode;
use AwardForce\Modules\Authentication\Tokens\RequestLoginLinkToken;
use AwardForce\Modules\Authentication\Tokens\ResetPassword;
use AwardForce\Modules\Authentication\Tokens\SixDigit;
use AwardForce\Modules\Identity\Users\Contracts\UserRepository;
use AwardForce\Modules\Notifications\Services\Client\SmsClient;
use AwardForce\Modules\Notifications\Services\Client\SmsClients;
use AwardForce\Modules\Notifications\Services\Courier;
use AwardForce\Modules\Notifications\Services\Providers\SmsChannelProvider;
use AwardForce\Modules\Notifications\Services\Recipients\Subscriptions;
use AwardForce\Modules\Notifications\Services\SmsSender;
use Mockery as m;
use Platform\Kessel\Hyperdrive;
use Platform\Tokens\TokenManager;
use Tests\Acceptance\Models\User;
use Tests\IntegratedTestCase;
use Vonage\Client;

final class RequestLoginLinkCommandTest extends IntegratedTestCase
{
    /** @var m\MockInterface */
    protected $users;

    /** @var m\MockInterface */
    protected $courier;

    /** @var m\MockInterface */
    protected $tokens;

    /** @var RequestLoginLinkHandler */
    protected $handler;

    private m\MockInterface|SixDigitsCode $sixDigitCode;

    public function init()
    {
        $this->users = m::mock(UserRepository::class);
        $this->tokens = m::mock(TokenManager::class);
        $this->courier = m::mock(Courier::class);
        $this->sms = m::mock(SmsSender::class);
        $this->sixDigitCode = m::mock(SixDigitsCode::class);
        $this->handler = new RequestLoginLinkHandler($this->tokens, $this->sixDigitCode);

        Account::unguard();
        User::unguard();
    }

    public function testSendEmail(): void
    {
        $this->mockCourier();

        $user = $this->fm->create(\AwardForce\Modules\Identity\Users\Models\User::class);

        $account = new Account(['id' => 2]);

        $this->users->shouldReceive('getByEmailOrMobile')
            ->with($user->email, $user->email)->andReturn($user);

        $this->tokens->shouldReceive('create')->with(m::type(RequestLoginLinkToken::class))->andReturn(str_random());
        $this->tokens->shouldReceive('create')->with(m::type(ResetPassword::class))->andReturn(str_random());

        $this->sixDigitCode->shouldReceive('generateToken')->once()
            ->withArgs(function ($user6dc, $channel, $redirection) use ($user) {
                $this->assertTrue($user6dc->is($user));
                $this->assertEquals($user->email, $channel);
                $this->assertStringContainsString('login/link/', $redirection);

                return true;
            })
            ->andReturn('123456');
        $this->sixDigitCode->shouldReceive('tokenValue')->once()->andReturn(new SixDigit($user->id, $account->id, 'email'));

        $command = new RequestLoginLink($account, $user->email, $user, 'en_GB', 'https://cool.awards/');

        $this->assertEquals('123456', $this->handler->handle($command));
    }

    public function testSendSms(): void
    {
        $this->mockCourier();

        $user = $this->fm->create(\AwardForce\Modules\Identity\Users\Models\User::class, ['mobile' => '+***********']);
        $account = new Account(['id' => 2]);

        $this->users->shouldReceive('getByEmailOrMobile')
            ->with($user->mobile, $user->mobile)->andReturn($user);

        $this->tokens->shouldReceive('create')->with(m::type(RequestLoginLinkToken::class))->andReturn(str_random());
        $this->tokens->shouldReceive('create')->with(m::type(ResetPassword::class))->andReturn(str_random());

        $this->sixDigitCode->shouldReceive('generateToken')->once()
            ->withArgs(function ($user6dc, $channel, $redirection) use ($user) {
                $this->assertTrue($user6dc->is($user));
                $this->assertEquals($user->mobile, $channel);
                $this->assertStringContainsString('login/link/', $redirection);

                return true;
            })
            ->andReturn('123456');
        $this->sixDigitCode->shouldReceive('tokenValue')->once()->andReturn(new SixDigit($user->id, $account->id, 'mobile'));

        $command = new RequestLoginLink($account, $user->mobile, $user, 'en_GB', 'https://cool.awards/');
        $this->sms->shouldReceive('deliver');
        $this->assertEquals('123456', $this->handler->handle($command));
    }

    public function testSendLoginLinkDoesNotIncludeUnsubscribeLink(): void
    {
        $hyperdrive = m::spy(Hyperdrive::class);
        app()->instance(Hyperdrive::class, $hyperdrive);
        $client = m::mock(SmsClient::class);
        $client->shouldReceive('send')->andReturn($m = m::mock());
        $clients = m::mock(SmsClients::class);
        $clients->shouldReceive('default')->andReturn($client);
        app()->instance(Client::class, $client);
        app()->instance(SmsChannelProvider::class, new SmsChannelProvider($clients, app(Subscriptions::class), $this->users));

        $user = $this->fm->create(\AwardForce\Modules\Identity\Users\Models\User::class, ['mobile' => '+***********']);
        $account = new Account(['id' => 2]);

        $this->users->shouldReceive('getByEmailOrMobile')
            ->with($user->mobile, $user->mobile)->andReturn($user);

        $this->tokens->shouldReceive('create')->with(m::type(RequestLoginLinkToken::class))->andReturn(str_random());
        $this->tokens->shouldReceive('create')->with(m::type(ResetPassword::class))->andReturn(str_random());

        $this->sixDigitCode->shouldReceive('generateToken')->once()->andReturn('123456');
        $this->sixDigitCode->shouldReceive('tokenValue')->once()->andReturn(new SixDigit($user->id, $account->id, 'mobile'));

        $command = new RequestLoginLink($account, $user->mobile, $user, 'en_GB', 'https://cool.awards/');

        $m->shouldReceive('send')->withArgs(function ($message) {
            $this->assertStringNotContainsString('unsubscribe', implode(',', $message));

            return true;
        });

        $this->handler->handle($command);
    }

    public function mockCourier(): void
    {
        app()->instance(Courier::class, $this->courier);
        $this->courier->shouldReceive('deliver')->once();
    }
}
