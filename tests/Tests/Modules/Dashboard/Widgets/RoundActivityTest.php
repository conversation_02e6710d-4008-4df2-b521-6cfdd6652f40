<?php

namespace Tests\Modules\Dashboard\Widgets;

use AwardForce\Modules\Dashboard\View\Widgets\RoundActivity;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Rounds\Models\Round;
use Carbon\Carbon;
use Tests\IntegratedTestCase;

class RoundActivityTest extends IntegratedTestCase
{
    public function testItReturnsRoundsDependingOnForm(): void
    {
        $form1 = $this->muffin(Form::class);
        $form2 = $this->muffin(Form::class);

        $round1 = $this->muffin(Round::class, ['enabled' => true, 'form_id' => $form1->id, 'round_type' => Round::ROUND_TYPE_ENTRY]);
        $round1->startAt(Carbon::now()->subDays(2)->format('Y-m-d H:i'), 'UTC');
        $round1->endAt(Carbon::now()->addDays(2)->format('Y-m-d H:i'), 'UTC');
        $round1->save();

        $round2 = $this->muffin(Round::class, ['enabled' => true, 'form_id' => $form2->id, 'round_type' => Round::ROUND_TYPE_ENTRY]);
        $round2->startAt(Carbon::now()->subDays(2)->format('Y-m-d H:i'), 'UTC');
        $round2->endAt(Carbon::now()->addDays(2)->format('Y-m-d H:i'), 'UTC');
        $round2->save();

        FormSelector::set($form1);

        $roundActivityWidget = app(RoundActivity::class);

        $form1Rounds = $roundActivityWidget->roundData();
        $this->assertCount(1, $form1Rounds);
        $this->assertEquals($round1->id, $form1Rounds->first()['round']->id);

        FormSelector::set($form2);

        $form2Rounds = $roundActivityWidget->roundData();
        $this->assertCount(1, $form2Rounds);
        $this->assertEquals($round2->id, $form2Rounds->first()['round']->id);

        FormSelector::shouldReceive('viewingAll')->andReturnTrue();
        FormSelector::shouldReceive('formIds')->andReturn([$form1->id, $form2->id]);

        $this->assertCount(2, $roundActivityWidget->roundData());
    }

    public function testItDoesNotReturnRoundsThatHaveDeleteFormWhenAllFormsIsSelected(): void
    {
        $form1 = $this->muffin(Form::class);
        $form2 = $this->muffin(Form::class);

        $round1 = $this->muffin(Round::class, ['enabled' => true, 'form_id' => $form1->id, 'round_type' => Round::ROUND_TYPE_ENTRY]);
        $round1->startAt(Carbon::now()->subDays(2)->format('Y-m-d H:i'), 'UTC');
        $round1->endAt(Carbon::now()->addDays(2)->format('Y-m-d H:i'), 'UTC');
        $round1->save();

        $round2 = $this->muffin(Round::class, ['enabled' => true, 'form_id' => $form2->id, 'round_type' => Round::ROUND_TYPE_ENTRY]);
        $round2->startAt(Carbon::now()->subDays(2)->format('Y-m-d H:i'), 'UTC');
        $round2->endAt(Carbon::now()->addDays(2)->format('Y-m-d H:i'), 'UTC');
        $round2->save();

        $form1->delete();

        FormSelector::shouldReceive('viewingAll')->andReturnTrue();
        FormSelector::shouldReceive('formIds')->andReturn([$form2->id]);

        $roundActivityWidget = app(RoundActivity::class);

        $this->assertCount(1, $roundActivityWidget->roundData());
    }
}
