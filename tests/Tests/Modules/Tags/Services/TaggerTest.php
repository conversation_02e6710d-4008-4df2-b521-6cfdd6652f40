<?php

namespace Tests\Modules\Tags\Services;

use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Modules\Assignments\Commands\SyncScoreSetCommandHandler;
use AwardForce\Modules\Assignments\Events\AssignmentWasCreated;
use AwardForce\Modules\Assignments\Events\StoreConsensusListener;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Assignments\Services\AssignmentUser;
use AwardForce\Modules\Assignments\Services\SyncFilter;
use AwardForce\Modules\Awards\Data\Award;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Ecommerce\Orders\Data\OrderItem;
use AwardForce\Modules\Ecommerce\Orders\Events\OrderWasCreated;
use AwardForce\Modules\Ecommerce\Orders\Events\OrderWasPaid;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Events\EntryWasSubmitted;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Duplicates\Duplicate;
use AwardForce\Modules\GrantReports\Events\GrantReportWasCreated;
use AwardForce\Modules\GrantReports\Events\GrantReportWasSubmitted;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Judging\Commands\ProcessVote;
use AwardForce\Modules\Judging\Commands\ProcessVoteHandler;
use AwardForce\Modules\Judging\Commands\SaveDecisionCommand;
use AwardForce\Modules\Judging\Commands\SaveDecisionCommandHandler;
use AwardForce\Modules\Judging\Commands\SavePreferenceCommand;
use AwardForce\Modules\Judging\Commands\SavePreferenceCommandHandler;
use AwardForce\Modules\Judging\Services\Voting\Ballot;
use AwardForce\Modules\Notifications\Events\SystemEventListener;
use AwardForce\Modules\Panels\Models\Panel;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Tags\Models\Tag;
use AwardForce\Modules\Tags\Models\TagAction;
use AwardForce\Modules\Tags\Services\TagManager;
use Platform\Events\EventDispatcher;
use Tests\IntegratedTestCase;

final class TaggerTest extends IntegratedTestCase
{
    use EventDispatcher;

    private $listener;

    public function init()
    {
        $this->listener = $this->spy(SystemEventListener::class);
        $this->spy(Database::class);
    }

    public function testActionsOnTag(): void
    {
        $tag = Tag::create($this->account->id, $this->season->id, 'SUBMITTED', [config('taggable.entry_submission.submitted')]);

        $entry = $this->muffin(Entry::class);
        $entry->submit();
        $this->dispatch($entry->releaseEvents());

        $this->assertSame('SUBMITTED', $entry->fresh()->tags->first()->tag);
    }

    public function testActionsOnEntryModeration(): void
    {
        $tag2 = $this->muffin(Tag::class, ['tag' => 'MODERATION_APPROVED']);
        TagAction::add($tag2, config('taggable.entry_moderated.moderation_approved'))->save();
        $entry = $this->muffin(Entry::class);
        $entry->moderate(Entry::MODERATION_STATUS_APPROVED);

        $this->dispatch($entry->releaseEvents());

        $tags = $entry->tags->pluck('tag');
        $this->assertNotFalse($tags->search('MODERATION_APPROVED'));
    }

    public function testActionsOnEntryModerationUndecided(): void
    {
        $tag2 = $this->muffin(Tag::class, ['tag' => 'MODERATION_UNDECIDED']);
        TagAction::add($tag2, config('taggable.entry_moderated.moderation_undecided'))->save();
        $entry = $this->muffin(Entry::class, ['moderation_status' => Entry::MODERATION_STATUS_APPROVED]);
        $entry->moderate(Entry::MODERATION_STATUS_UNDECIDED);

        $this->dispatch($entry->releaseEvents());

        $tags = $entry->tags->pluck('tag');
        $this->assertNotFalse($tags->search('MODERATION_UNDECIDED'));
    }

    public function testActionsOnEntryDuplicated(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'PRIMARY']);
        TagAction::add($tag, config('taggable.entry_duplicate.primary'))->save();
        $tag = $this->muffin(Tag::class, ['tag' => 'DUPLICATED']);
        TagAction::add($tag, config('taggable.entry_duplicate.duplicated'))->save();

        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($entry = $this->muffin(Entry::class));
        $duplicate->markAsDuplicate($primary);
        $duplicate->confirmAndArchive($primaryEntry = $primary->entry);

        $this->dispatch($duplicate->releaseEvents());

        $this->assertSame(1, $entry->fresh()->tags->count());
        $this->assertSame(1, $primaryEntry->fresh()->tags->count());
        $this->assertNotFalse($primaryEntry->tags->pluck('tag')->search('PRIMARY'));
        $this->assertNotFalse($entry->tags->pluck('tag')->search('DUPLICATED'));
    }

    public function testEntryArchived(): void
    {
        $tagArchived = $this->muffin(Tag::class, ['tag' => 'ARCHIVED']);
        TagAction::add($tagArchived, config('taggable.entry_submission.archived'))->save();
        $entry = $this->muffin(Entry::class);
        $entry->archive();
        $this->dispatch($entry->releaseEvents());

        $this->assertSame(1, $entry->fresh()->tags->count());
        $this->assertNotFalse($entry->tags->pluck('tag')->search('ARCHIVED'));
    }

    public function testEntryWasMarkedAsDuplicatedNoArchivedManually(): void
    {
        $tagArchived = $this->muffin(Tag::class, ['tag' => 'ARCHIVED']);
        TagAction::add($tagArchived, config('taggable.entry_submission.archived'))->save();
        $tag = $this->muffin(Tag::class, ['tag' => 'DUPLICATED']);
        TagAction::add($tag, config('taggable.entry_duplicate.duplicated'))->save();
        $entry = $this->muffin(Entry::class);
        $primary = Duplicate::getForEntry($this->muffin(Entry::class));
        $duplicate = Duplicate::getForEntry($entry);
        $duplicate->markAsDuplicateAndArchive($primary);
        $this->dispatch($duplicate->releaseEvents());

        $this->assertSame(1, $entry->fresh()->tags->count());
        $this->assertNotFalse($entry->tags->pluck('tag')->search('DUPLICATED'));
    }

    public function testEntryResubmitted(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'RESUBMITTED']);
        TagAction::add($tag, config('taggable.entry_submission.resubmitted'))->save();
        $entry = $this->muffin(Entry::class);
        $entry->resubmit();

        $this->dispatch($entry->releaseEvents());

        $this->assertSame('RESUBMITTED', $entry->fresh()->tags->first()->tag);
    }

    protected function createOrder(string $paymentMethod, array $entries)
    {
        $this->setupUserWithRole('ProgramManager');
        $order = new Order(['id' => rand()]);
        $order->accountId = current_account_id();
        $order->seasonId = current_account()->activeSeason()->id;
        $order->userId = $this->user->id;
        $order->paymentMethod = $paymentMethod;
        $order->save();
        foreach ($entries as $entry) {
            $item1 = new OrderItem(['order_id' => $order->id]);
            $item1->entryId = $entry->id;
            $item1->meta = [];
            $item1->save();
            $order->orderItems()->save($item1);
        }

        return $order;
    }

    public function testPaymentSuccess(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'PAID']);
        TagAction::add($tag, config('taggable.payment.success'))->save();
        $entries = [$entry1 = $this->muffin(Entry::class), $entry2 = $this->muffin(Entry::class)];

        $this->dispatch(new OrderWasCreated($order = $this->createOrder('visa', $entries)));

        $this->assertSame('PAID', $entry1->fresh()->tags->first()->tag);
        $this->assertSame('PAID', $entry2->fresh()->tags->first()->tag);
    }

    public function testPaymentPending(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'PENDING']);
        TagAction::add($tag, config('taggable.payment.pending'))->save();
        $entries = [$entry1 = $this->muffin(Entry::class), $entry2 = $this->muffin(Entry::class)];

        $this->dispatch(new OrderWasCreated($order = $this->createOrder('invoice', $entries)));

        $this->assertSame('PENDING', $entry1->fresh()->tags->first()->tag);
        $this->assertSame('PENDING', $entry2->fresh()->tags->first()->tag);
    }

    public function testEntryWasTagged(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'TAGGED']);
        TagAction::add($tag, config('taggable.entry_submission.tagged'))->save();
        $entry = $this->muffin(Entry::class);
        $manager = app(TagManager::class);
        $manager->tag($entry, 'WINNER');

        $this->dispatch($manager->releaseEvents());

        $tags = $entry->fresh()->tags->pluck('tag');
        $this->assertNotFalse($tags->search('TAGGED'));
        $this->assertNotFalse($tags->search('WINNER'));
    }

    public function testTagReviewStageStarted(): void
    {
        $entry = $this->muffin(Entry::class);
        $task = ReviewTask::generate(
            $entry->id,
            ($stage = $this->muffin(ReviewStage::class))->id,
            'manager',
            current_account_id()
        );
        $tag = $this->muffin(Tag::class, ['tag' => 'TAGGED']);
        TagAction::add($tag, TagAction::modelAction('review_stage', config('taggable.review_stage.started'), $stage->id))->save();

        $this->dispatch($task->releaseEvents());

        $this->assertSame($tag->tag, $entry->fresh()->tags->first()->tag);
    }

    public function testTagReviewStageProceeded(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'TAGGED']);
        $entry = $this->muffin(Entry::class);
        $entry2 = $this->muffin(Entry::class);

        $task = ReviewTask::generate(
            $entry->id,
            ($stage = $this->muffin(ReviewStage::class))->id,
            'manager',
            current_account_id()
        );

        $task2 = ReviewTask::generate(
            $entry2->id,
            ($stage2 = $this->muffin(ReviewStage::class))->id,
            'manager',
            current_account_id()
        );

        TagAction::add($tag, TagAction::modelAction('review_stage', config('taggable.review_stage.proceeded'), $stage->id))->save();
        $task->proceed();
        $task2->proceed();

        $this->dispatch($task->releaseEvents());
        $this->dispatch($task2->releaseEvents());

        $this->assertSame('TAGGED', $entry->fresh()->tags->first()->tag);
        $this->assertTrue($entry2->fresh()->tags->isEmpty());
    }

    public function testTagReviewStageCancelled(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'CANCELLED']);
        $entry = $this->muffin(Entry::class);
        $task = ReviewTask::generate(
            $entry->id,
            ($stage = $this->muffin(ReviewStage::class))->id,
            'manager',
            current_account_id()
        );
        TagAction::add($tag, TagAction::modelAction('review_stage', config('taggable.review_stage.stopped'), $stage->id))->save();
        $task->stop();

        $this->dispatch($task->releaseEvents());

        $this->assertSame('CANCELLED', $entry->fresh()->tags->first()->tag);
    }

    public function testTagAssignmentCompleted(): void
    {
        /** @var Assignment $assignment1 */
        $assignment1 = $this->muffin(Assignment::class, ['season_id' => $this->season->id]);
        $assignment2 = $this->muffin(Assignment::class, ['season_id' => $this->season->id]);
        $assignment1->complete();
        $assignment2->complete();
        $entry1 = $assignment1->entry;
        $entry2 = $assignment2->entry;
        $scoreSet1 = $assignment1->scoreSet;
        $scoreSet2 = $assignment2->scoreSet;
        $tag1 = $this->muffin(Tag::class, ['tag' => 'COMPLETED1']);
        $tag2 = $this->muffin(Tag::class, ['tag' => 'COMPLETED2']);
        TagAction::add($tag1, TagAction::modelAction('score_set', config('taggable.assignment.completed'), $scoreSet1->id))->save();
        TagAction::add($tag2, TagAction::modelAction('score_set', config('taggable.assignment.completed'), $scoreSet2->id))->save();
        $this->dispatch($assignment1->releaseEvents());
        $this->dispatch($assignment2->releaseEvents());

        $this->assertSame('COMPLETED1', $entry1->fresh()->tags->first()->tag);
        $this->assertSame('COMPLETED2', $entry2->fresh()->tags->first()->tag);
    }

    public function testTagAssignmentCreated(): void
    {
        $assignment = $this->muffin(Assignment::class, ['season_id' => $this->season->id]);
        $tag = $this->muffin(Tag::class, ['tag' => 'CREATED']);
        TagAction::add($tag, TagAction::modelAction('score_set', config('taggable.assignment.created'), $assignment->scoreSet->id))->save();
        event(new AssignmentWasCreated($assignment));

        $this->assertSame('CREATED', $assignment->entry->fresh()->tags->first()->tag);
    }

    public function testTagsProcessorSync(): void
    {
        $assignment = $this->muffin(Assignment::class, ['season_id' => $this->season->id]);
        $assignment->complete();
        $assignment->entry->releaseEvents();
        $entry = $assignment->entry;
        $scoreSet = $assignment->scoreSet;

        $tag = $this->muffin(Tag::class, ['tag' => 'COMPLETED1']);
        TagAction::add($tag, TagAction::modelAction('score_set', config('taggable.assignment.completed'), $scoreSet->id))->save();

        $syncScoreSet = $this->spy(SyncScoreSetCommandHandler::class);

        $this->dispatch($assignment->releaseEvents());
        $this->assertSame('COMPLETED1', $entry->fresh()->tags->first()->tag);

        $syncScoreSet->shouldHaveReceived('handle')->once();
    }

    public function testTopPickTagging(): void
    {
        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_TOP_PICK]);
        $assignment = $this->muffin(Assignment::class, ['season_id' => $this->season->id, 'score_set_id' => $scoreSet->id]);
        $entry = $assignment->entry;

        $tag = $this->muffin(Tag::class, ['tag' => 'COMPLETED1']);
        TagAction::add($tag, TagAction::modelAction('score_set', config('taggable.assignment.completed'), $scoreSet->id))->save();

        $syncScoreSet = $this->spy(SyncScoreSetCommandHandler::class);

        $savePreference = app(SavePreferenceCommandHandler::class);
        $savePreference->handle(new SavePreferenceCommand(new AssignmentUser($assignment->judge), $scoreSet, $assignment->entry, 1));

        $this->assertSame('COMPLETED1', $entry->fresh()->tags->first()->tag);

        $this->listener->shouldHaveReceived('whenEntryWasTagged')->once();
        $syncScoreSet->shouldHaveReceived('handle')->once();
    }

    public function testTagQualifyingAssignmentCompleted(): void
    {
        app()->instance(StoreConsensusListener::class, $m = $this->spy(StoreConsensusListener::class));

        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_QUALIFYING]);
        $assignment = $this->muffin(Assignment::class, ['season_id' => $this->season->id, 'score_set_id' => $scoreSet->id]);
        $assignment->complete();
        $entry = $assignment->entry;
        $tag1 = $this->muffin(Tag::class, ['tag' => 'COMPLETED']);
        TagAction::add($tag1, TagAction::modelAction('score_set', config('taggable.assignment.completed'), $scoreSet->id))->save();
        app(SaveDecisionCommandHandler::class)->handle(new SaveDecisionCommand($scoreSet, new AssignmentUser($assignment->judge), $entry, 'pass'));

        $this->assertSame('COMPLETED', $entry->fresh()->tags->first()->tag);
    }

    public function testAssignmentsToPanelWhenTagged(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'VOTED']);
        $votingScoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_VOTING]);
        $topPickScoreSet = $this->muffin(ScoreSet::class, ['mode' => ScoreSet::MODE_TOP_PICK]);
        $votingPanel = $this->muffin(Panel::class, ['score_set_id' => $votingScoreSet->id]);
        $chapter = $votingPanel->chapters->first();
        $category = $votingPanel->categories->first();
        $topPickPanel = $this->muffin(Panel::class, [
            'tag_match' => Panel::TAGS_ANY,
            'score_set_id' => $topPickScoreSet->id,
        ]);
        $topPickPanel->chapters()->attach($chapter);
        $topPickPanel->categories()->attach($category);
        $topPickPanel->tags()->attach($tag);
        $topPickPanel->save();
        $entry = $this->muffin(Entry::class, ['category_id' => $category->id, 'chapter_id' => $chapter->id]);
        $assignment = $this->muffin(Assignment::class, ['season_id' => $this->season->id, 'score_set_id' => $votingScoreSet->id, 'entry_id' => $entry->id]);

        TagAction::add($tag, TagAction::modelAction('score_set', config('taggable.assignment.completed'), $votingScoreSet->id))->save();
        $entries = app(EntryRepository::class)->forPanel($topPickPanel, new SyncFilter($topPickScoreSet));
        $this->assertEmpty($entries);
        app(ProcessVoteHandler::class)->handle(
            new ProcessVote(Ballot::create($entry, new AssignmentUser($assignment->judge), 2, '127.0.0.1', 'cookie', ''), $votingScoreSet)
        );
        $entries = app(EntryRepository::class)->forPanel($topPickPanel, new SyncFilter($topPickScoreSet));
        $this->assertSame($entry->id, $entries->first()->id);
    }

    public function testCertificateGeneratedWhenEntryWasTaggedAutomatically(): void
    {
        $tag = Tag::create($this->account->id, $this->season->id, 'SUBMITTED', [config('taggable.entry_submission.submitted')]);
        $entry = $this->muffin(Entry::class);
        $award = $this->muffin(Award::class, ['tag_match' => 'any', 'type' => Award::TYPE_CERTIFICATE, 'season_id' => $entry->seasonId]);
        $award->addTag($tag);
        $award->save();

        $entry->submit();
        $this->dispatch($entry->releaseEvents());

        $this->assertSame('SUBMITTED', $entry->fresh()->tags->first()->tag);
        $this->assertSame($award->id, $entry->awards()->first()->id);
    }

    public function testInvoicePaymentSuccess(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'PENDING']);
        TagAction::add($tag, config('taggable.payment.pending'))->save();

        $tag = $this->muffin(Tag::class, ['tag' => 'PAID']);
        TagAction::add($tag, config('taggable.payment.success'))->save();

        $entries = [$entry1 = $this->muffin(Entry::class), $entry2 = $this->muffin(Entry::class)];

        $this->dispatch(new OrderWasCreated($order = $this->createOrder('invoice', $entries)));

        $this->assertSame('PENDING', $entry1->fresh()->tags->first()->tag);
        $this->assertSame('PENDING', $entry2->fresh()->tags->first()->tag);

        $this->dispatch(new OrderWasPaid($order));

        $this->assertTrue($entry1->fresh()->tags->some(function ($tag) {
            return $tag->tag === 'PAID';
        }) &&
            $entry2->fresh()->tags->some(function ($tag) {
                return $tag->tag === 'PAID';
            }));
    }

    public function testAutoTagOnlyAppliesToIntendedSeason(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'SUBMITTED ACTIVE']);
        TagAction::add($tag, config('taggable.entry_submission.submitted'))->save();

        $draftSeason = $this->muffin(Season::class, ['status' => Season::STATUS_DRAFT]);
        $draftEntry = $this->muffin(Entry::class, ['season_id' => $draftSeason->id, 'submitted_at' => now()]);

        $this->dispatch(new EntryWasSubmitted($draftEntry));

        $this->assertEmpty($draftEntry->fresh()->tags);

        $draftTag = $this->muffin(Tag::class, ['tag' => 'SUBMITTED DRAFT', 'season_id' => $draftSeason->id]);
        TagAction::add($draftTag, config('taggable.entry_submission.submitted'))->save();

        $this->dispatch(new EntryWasSubmitted($draftEntry));

        $this->assertCount(1, $tags = $draftEntry->fresh()->tags);
        $this->assertSame('SUBMITTED DRAFT', $tags->first()->tag);
    }

    public function testAutoTagGrantReportsAndEntryWhenGrantReportIsScheduled(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'GRANT REPORT SCHEDULED']);
        TagAction::add($tag, config('taggable.grant_report.scheduled'))->save();

        $entry = $this->muffin(Entry::class);
        $grantReport = $this->muffin(GrantReport::class, ['entry_id' => $entry->id]);

        $this->assertEmpty($entry->fresh()->tags);
        $this->assertEmpty($grantReport->fresh()->tags);

        $this->dispatch(new GrantReportWasCreated($grantReport));

        $this->assertSame('GRANT REPORT SCHEDULED', $entry->fresh()->tags->first()->tag);
        $this->assertSame('GRANT REPORT SCHEDULED', $grantReport->fresh()->tags->first()->tag);
    }

    public function testAutoTagGrantReportsAndEntryWhenGrantReportIsSubmitted(): void
    {
        $tag = $this->muffin(Tag::class, ['tag' => 'GRANT REPORT SUBMITTED']);
        TagAction::add($tag, config('taggable.grant_report.submitted'))->save();

        $entry = $this->muffin(Entry::class);
        $grantReport = $this->muffin(GrantReport::class, ['entry_id' => $entry->id]);

        $this->assertEmpty($entry->fresh()->tags);
        $this->assertEmpty($grantReport->fresh()->tags);

        $this->dispatch(new GrantReportWasSubmitted($grantReport));

        $this->assertSame('GRANT REPORT SUBMITTED', $entry->fresh()->tags->first()->tag);
        $this->assertSame('GRANT REPORT SUBMITTED', $grantReport->fresh()->tags->first()->tag);
    }
}
