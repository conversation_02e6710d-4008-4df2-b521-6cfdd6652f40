<?php

namespace Tests\Modules\GrantReport\Listeners;

use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Bus\CreateForm;
use AwardForce\Modules\Forms\Forms\Bus\CreateFormHandler;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\ScheduledTasks\Models\ScheduledTask;
use AwardForce\Modules\ScheduledTasks\Services\ActionExecutors\SendNotificationGrantReport;
use AwardForce\Modules\ScheduledTasks\Services\Scheduler;
use Carbon\Carbon;
use Platform\Events\EventDispatcher;
use Tests\IntegratedTestCase;

final class GrantReportEventListenerTest extends IntegratedTestCase
{
    use EventDispatcher;

    public function testSendNotificationScheduledWhenGrantReportIsCreated(): void
    {
        $notification = $this->muffin(Notification::class, [
            'trigger' => 'grant.report',
            'send_time_option' => 'before_due_date',
            'send_time_unit' => 'minutes',
            'send_time_offset' => 10,
        ]);

        $reportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $entry = $this->muffin(Entry::class);

        $grantReport = GrantReport::add(
            $this->account->id,
            $reportForm->id,
            $this->season->id,
            $entry->id,
            (new Carbon)->addMinutes(10)->toString()
        );

        $this->assertEmpty(ScheduledTask::all());
        $this->dispatch($grantReport->releaseEvents());
        $this->assertCount(1, $tasks = ScheduledTask::all());
        $this->assertSame(['notification_id' => $notification->id, 'grant_report_id' => $grantReport->id], json_decode($tasks->first()->payload, true));
    }

    public function testUpdateDueDate(): void
    {
        $grantReport = $this->muffin(GrantReport::class);
        $grantReport->updateDueDate($tomorrow = Carbon::tomorrow());

        app()->instance(Scheduler::class, $scheduler = $this->spy(Scheduler::class));
        $this->dispatch($grantReport->releaseEvents());

        $scheduler->shouldHaveReceived('updateOrReschedule')
            ->once()
            ->withArgs(function ($action, $field, $id, $dueDate) use ($grantReport, $tomorrow) {
                $this->assertSame($action, SendNotificationGrantReport::action());
                $this->assertSame($field, 'grant_report_id');
                $this->assertSame($id, $grantReport->id);
                $this->assertTrue($tomorrow->is($dueDate));

                return true;
            });
    }

    public function testUpdateDueDateAlsoSchedulesNewNotificationsForExecutedOnce(): void
    {
        $this->muffins(2, Notification::class, [
            'trigger' => 'grant.report',
            'send_time_option' => Notification::SEND_TIME_AFTER,
            'send_time_unit' => Notification::SEND_TIME_UNIT_MINUTES,
            'send_time_offset' => 10,
        ]);

        $reportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $entry = $this->muffin(Entry::class);

        $grantReport = GrantReport::add(
            $this->account->id,
            $reportForm->id,
            $this->season->id,
            $entry->id,
            ($due = (new Carbon)->addMinutes(10))->toString()
        );

        $this->assertEmpty(ScheduledTask::all());
        $this->dispatch($grantReport->releaseEvents());

        $this->assertCount(2, ScheduledTask::all());

        $tasks = ScheduledTask::all();

        $tasks->each(function ($task) {
            $this->assertEquals(ScheduledTask::STATUS_SCHEDULED, $task->status);
        });

        $task2 = ScheduledTask::latest('id')->first();
        $task2->status = ScheduledTask::STATUS_EXECUTED;
        $task2->save();

        $grantReport->updateDueDate(($newDue = (new Carbon)->addMinutes(20))->toString());
        $this->dispatch($grantReport->releaseEvents());

        $this->assertCount(3, ScheduledTask::all());

        $task1 = ScheduledTask::first();
        $task3 = ScheduledTask::latest('id')->first();

        $this->assertEquals(ScheduledTask::STATUS_SCHEDULED, $task1->fresh()->status);
        $this->assertEquals(ScheduledTask::STATUS_EXECUTED, $task2->fresh()->status);
        $this->assertEquals(ScheduledTask::STATUS_SCHEDULED, $task3->fresh()->status);
        $this->assertEquals(Carbon::parse($newDue)->addMinutes(10)->toString(), $task1->fresh()->dueDate->toString());
        $this->assertEquals(Carbon::parse($due)->addMinutes(10)->toString(), $task2->fresh()->dueDate->toString());
        $this->assertEquals(Carbon::parse($newDue)->addMinutes(10)->toString(), $task3->fresh()->dueDate->toString());
    }

    public function testCreateDefaultCategoryAndChapterWhenReportFormWasCreated(): void
    {
        $this->muffins(2, Chapter::class);

        $form = app(CreateFormHandler::class)->handle(
            new CreateForm(Form::FORM_TYPE_REPORT, ['name' => ['en_GB' => $name = 'Report form']], [])
        );

        $this->assertCount(1, $form->categories);
        $this->assertSame($name, translate($category = $form->categories->first())->name);
        $this->assertSame(2, $category->chapters->count());
    }

    public function testItReschedulesExecutedTasksOnlyForFutureDueDates(): void
    {
        $this->muffin(Notification::class, [
            'trigger' => 'grant.report',
            'send_time_option' => 'before_due_date',
            'send_time_unit' => 'days',
            'send_time_offset' => 1,
        ]);

        $reportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $entry = $this->muffin(Entry::class);

        $grantReport = GrantReport::add(
            $this->account->id,
            $reportForm->id,
            $this->season->id,
            $entry->id,
            (new Carbon)->addDays(10)->toString()
        );

        $this->assertEmpty(ScheduledTask::all());
        $this->dispatch($grantReport->releaseEvents());

        $oldGrantReport = GrantReport::add(
            $this->account->id,
            $reportForm->id,
            $this->season->id,
            $entry->id,
            (new Carbon)->subDays(10)->toString()
        );

        $this->dispatch($oldGrantReport->releaseEvents());
        $this->assertCount(2, $tasks = ScheduledTask::orderBy('due_date')->get());

        $this->assertTrue($tasks->first()->dueDate->lessThan(now()));
        $this->assertTrue($tasks->last()->dueDate->greaterThan(now()));
        $tasks->first()->markAsExecuted();
        $tasks->last()->markAsExecuted();

        $oldGrantReport->updateDueDate((new Carbon)->subDays(2)->toString());
        $this->dispatch($oldGrantReport->releaseEvents());

        $grantReport->updateDueDate((new Carbon)->addDays(20)->toString());
        $this->dispatch($grantReport->releaseEvents());

        $this->assertCount(3, $tasks = ScheduledTask::all());

        $scheduled = $tasks->filter(fn($task) => $task->status === ScheduledTask::STATUS_SCHEDULED);
        $this->assertCount(1, $scheduled);
        $this->assertTrue($scheduled->first()->dueDate->greaterThan(now()));
        $this->assertEquals($grantReport->id, json_decode($scheduled->first()->payload, true)['grant_report_id']);
    }
}
