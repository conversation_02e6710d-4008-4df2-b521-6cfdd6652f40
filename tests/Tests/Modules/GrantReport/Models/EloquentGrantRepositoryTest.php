<?php

namespace Tests\Modules\GrantReport\Models;

use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use AwardForce\Modules\Identity\Users\Models\User;
use Tests\IntegratedTestCase;

final class EloquentGrantRepositoryTest extends IntegratedTestCase
{
    /**
     * @var GrantReportRepository
     */
    protected $repository;

    /**
     * Initiate the unit tests
     */
    public function init()
    {
        $this->repository = app(GrantReportRepository::class);
    }

    public function testItFetchesGrantReportsCollectionWithCategoryAndWithoutInvitedEntries(): void
    {
        $entry1 = $this->muffin(Entry::class, [
            'invited_at' => null,
        ]);

        $entry2 = $this->muffin(Entry::class, [
            'invited_at' => now(),
        ]);

        $grantReport1 = $this->muffin(GrantReport::class, [
            'entry_id' => $entry1->id,
        ]);

        $grantReport2 = $this->muffin(GrantReport::class, [
            'entry_id' => $entry2->id,
        ]);

        $ids = [$grantReport1->id, $grantReport2->id];

        $results = $this->repository->getIdsWithCategoriesExcludingInvited($ids);

        $this->assertCount(1, $results);
        $this->assertNotNull($results->first()?->entry?->category);
    }

    public function testItReturnsIdsOfGrantReportsFoGivenEntryIds(): void
    {
        $entry1 = $this->muffin(Entry::class);
        $entry2 = $this->muffin(Entry::class);
        $this->muffin(Entry::class);

        $entryIds = [$entry1->id, $entry2->id];

        $this->muffin(GrantReport::class, ['entry_id' => $entry1->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $entry2->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $entry2->id]);
        $this->muffin(GrantReport::class);

        $returnedIds = $this->repository->getIdsByEntries($entryIds);

        $this->assertCount(3, $returnedIds);
    }

    public function testItReturnsEmptyArrayWheNoGrantReportsExistForGivenEntryIds(): void
    {
        $returnedIds = $this->repository->getIdsByEntries([1, 2, 3]);

        $this->assertEmpty($returnedIds);
    }

    public function testUser(): void
    {
        $user = $this->muffin(User::class);
        $entry1 = $this->muffin(Entry::class, ['user_id' => $user->id]);
        $this->muffins(2, GrantReport::class, ['entry_id' => $entry1->id]);
        $this->muffin(GrantReport::class);
        $entry2 = $this->muffin(Entry::class, ['user_id' => $this->muffin(User::class)->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $entry2->id]);

        $this->assertEquals(2, $this->repository->user($user->id)->count());
    }

    public function testItCanFilterByUserOrCollaborator(): void
    {
        Feature::shouldReceive('enabled')->once()->with('collaboration')->andReturnTrue();
        $this->muffin(GrantReport::class, ['entry_id' => $this->muffin(Entry::class, ['user_id' => consumer_id()])->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $this->muffin(Entry::class, ['user_id' => $this->muffin(User::class)->id])->id]);
        $grantReport = $this->muffin(GrantReport::class, ['entry_id' => $this->muffin(Entry::class, ['user_id' => $this->muffin(User::class)->id])->id]);
        $grantReport->form->settings = FormSettings::create(['collaborative' => true]);
        $grantReport->form->save();

        $this->muffin(Collaborator::class, ['user_id' => consumer_id(), 'submittable_id' => $grantReport->id, 'submittable_type' => GrantReport::class]);

        $results = $this->repository->userOrCollaborator(consumer_id())->count();

        $this->assertSame(2, $results);
    }

    public function testItFiltersByEntryId(): void
    {
        $entry = $this->muffin(Entry::class);
        $grantReport = $this->muffin(GrantReport::class, ['entry_id' => $entry->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $this->muffin(Entry::class)->id]);

        $results = $this->repository->fields(['grant_reports.id'])->entry($entry->id)->get();

        $this->assertCount(1, $results);
        $this->assertEquals($grantReport->id, $results->first()->id);
    }
}
