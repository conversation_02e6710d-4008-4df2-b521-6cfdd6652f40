<?php

namespace Tests\Modules\GrantReport\Search;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Search\Search;
use Carbon\Carbon;
use Faker\Factory as Faker;
use Tests\IntegratedTestCase;
use Tests\Library\Search\ColumnatorExportTestHelper;
use Tests\Library\Search\ColumnatorTestHelper;

final class ManageGrantReportsColumnatorTest extends IntegratedTestCase
{
    use ColumnatorExportTestHelper, ColumnatorTestHelper {
        ColumnatorTestHelper::search insteadof ColumnatorExportTestHelper;
        ColumnatorExportTestHelper::search as export;
    }

    protected function area()
    {
        return 'manage_grant_reports.search';
    }

    public function testCanSeeGrantReportsFromAllUsers(): void
    {
        $user1 = $this->muffin(User::class, ['last_name' => Faker::create()->lastName()]);
        $user2 = $this->muffin(User::class, ['last_name' => Faker::create()->lastName()]);
        $user3 = $this->muffin(User::class, ['last_name' => Faker::create()->lastName()]);

        $entry1 = $this->muffin(Entry::class, ['user_id' => $user1->id]);
        $entry2 = $this->muffin(Entry::class, ['user_id' => $user2->id]);
        $entry3 = $this->muffin(Entry::class, ['user_id' => $user3->id]);
        $entry4 = $this->muffin(Entry::class, ['user_id' => consumer_id()]);

        $this->muffin(GrantReport::class, ['entry_id' => $entry1->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $entry2->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $entry3->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $entry4->id]);

        $grantReports = $this->search();

        $this->assertCount(4, $grantReports);
        $lastNames = $grantReports->pluck('lastName');

        $this->assertNotFalse($lastNames->search($user1->lastName));
        $this->assertNotFalse($lastNames->search($user2->lastName));
        $this->assertNotFalse($lastNames->search($user3->lastName));
        $this->assertNotFalse($lastNames->search(consumer()->get()->user()->lastName));
    }

    public function testCanFilterByCategory(): void
    {
        $category = $this->muffin(Category::class);
        $entry = $this->muffin(Entry::class, ['category_id' => $category->id]);
        $grantReport = $this->muffin(GrantReport::class, ['entry_id' => $entry->id]);

        $grantReports = $this->search([], ['category' => $category->id]);

        $this->assertCount(1, $grantReports);
        $this->assertEquals($grantReport->id, $grantReports->first()->id);
    }

    public function testCanOrderByEntryTitle(): void
    {
        $entry1 = $this->muffin(Entry::class, ['title' => 'aaa']);
        $entry2 = $this->muffin(Entry::class, ['title' => 'bbb']);
        $entry3 = $this->muffin(Entry::class, ['title' => 'ccc']);
        $grantReport1 = $this->muffin(GrantReport::class, ['entry_id' => $entry1->id]);
        $grantReport2 = $this->muffin(GrantReport::class, ['entry_id' => $entry2->id]);
        $grantReport3 = $this->muffin(GrantReport::class, ['entry_id' => $entry3->id]);

        $grantReports = $this->search([], ['order' => 'my_grant_reports.entry_title']);

        $this->assertCount(3, $grantReports);
        $this->assertSame((string) $grantReport1->slug, (string) $grantReports[2]->slug);
        $this->assertSame((string) $grantReport2->slug, (string) $grantReports[1]->slug);
        $this->assertSame((string) $grantReport3->slug, (string) $grantReports[0]->slug);
    }

    public function testCanFilterByChapter(): void
    {
        $chapter1 = $this->muffin(Chapter::class);
        $chapter2 = $this->muffin(Chapter::class);
        $entry1 = $this->muffin(Entry::class, ['chapter_id' => $chapter1->id]);
        $entry2 = $this->muffin(Entry::class, ['chapter_id' => $chapter2->id]);
        $grantReport1 = $this->muffin(GrantReport::class, ['entry_id' => $entry1->id]);
        $grantReport2 = $this->muffin(GrantReport::class, ['entry_id' => $entry2->id]);

        $grantReports = $this->search([], ['chapter' => $chapter2->id]);

        $this->assertCount(1, $grantReports);
        $this->assertEquals($grantReport2->id, $grantReports->first()->id);
    }

    public function testCanFilterByEntryStatus(): void
    {
        $entry1 = $this->muffin(Entry::class, ['submitted_at' => Carbon::now()]);
        $entry2 = $this->muffin(Entry::class, ['submitted_at' => null]);
        $grantReport1 = $this->muffin(GrantReport::class, ['entry_id' => $entry1->id]);
        $grantReport2 = $this->muffin(GrantReport::class, ['entry_id' => $entry2->id]);

        $grantReports = $this->search([], ['entry_status' => 'submitted']);

        $this->assertCount(1, $grantReports);
        $this->assertEquals($grantReport1->id, $grantReports->first()->id);
    }

    public function testCanFilterByReportStatus(): void
    {
        $submittedGrantReport = $this->muffin(GrantReport::class, ['submitted_at' => Carbon::now()]);
        $scheduledGrantReport = $this->muffin(GrantReport::class, ['submitted_at' => null, 'due_date' => Carbon::now()->addDays(), 'values' => null]);
        $inProgressGrantReport = $this->muffin(GrantReport::class, ['submitted_at' => null, 'values' => '{"someKey": "someValue"}', 'due_date' => Carbon::now()->addDays()]);
        $overdueGrantReport = $this->muffin(GrantReport::class, ['submitted_at' => null, 'due_date' => Carbon::now()->subDays(1)]);

        $submittedGrantReportSearch = $this->search([], ['status' => 'submitted']);
        $scheduledGrantReportSearch = $this->search([], ['status' => 'scheduled']);
        $inProgressGrantReportSearch = $this->search([], ['status' => 'in_progress']);
        $overdueGrantReportSearch = $this->search([], ['status' => 'overdue']);
        $allGrantReports = $this->search();

        $this->assertCount(1, $submittedGrantReportSearch);
        $this->assertEquals($submittedGrantReport->id, $submittedGrantReportSearch->first()->id);

        $this->assertCount(1, $scheduledGrantReportSearch);
        $this->assertEquals($scheduledGrantReport->id, $scheduledGrantReportSearch->first()->id);

        $this->assertCount(1, $inProgressGrantReportSearch);
        $this->assertEquals($inProgressGrantReport->id, $inProgressGrantReportSearch->first()->id);

        $this->assertCount(1, $overdueGrantReportSearch);
        $this->assertEquals($overdueGrantReport->id, $overdueGrantReportSearch->first()->id);

        $this->assertCount(4, $allGrantReports);
    }

    public function testSearchWithGrantStatusColumnAndWithoutDueDateColumn(): void
    {
        $grantReport = $this->muffin(GrantReport::class, [
            'entry_id' => ($this->muffin(Entry::class, ['category_id' => ($this->muffin(Category::class))->id]))->id,
            'due_date' => Carbon::now()->addDays(),
        ]);

        $grantReports = $this->search(['status']);

        $this->assertEquals($grantReport->dueDate, $grantReports->first()->dueDate);
    }

    public function testSearchWithEntryIdSlugCategoryShortcodeColumns(): void
    {
        $grantReport = $this->muffin(GrantReport::class, [
            'entry_id' => ($this->muffin(Entry::class, ['category_id' => ($this->muffin(Category::class))->id]))->id,
            'due_date' => Carbon::now()->addDays(),
        ]);

        $grantReports = $this->search(['grant-reports.entry-id', 'grant-reports.entry-slug', 'grant-reports.category-shortcode']);

        $entryModel = $grantReport->entry;
        $entryFromSearch = $grantReports->first()->entry;

        $this->assertEquals($entryModel->localId, $entryFromSearch->localId);
        $this->assertEquals($entryModel->slug, $entryFromSearch->slug);
        $this->assertEquals($entryModel->category->shortcode, $entryFromSearch->category->shortcode);
    }

    public function testExportGrantReportWithoutDueDate(): void
    {
        $grantReport = $this->muffin(GrantReport::class, [
            'entry_id' => ($this->muffin(Entry::class, ['category_id' => ($this->muffin(Category::class))->id]))->id,
            'due_date' => Carbon::now()->addDays(),
        ]);

        $grantReportExport = $this->export(['status']);

        $this->assertEquals($grantReport->dueDate, $grantReportExport->first()->dueDate);
    }

    public function testExportContainsEntryTitleEntrantSubmittedAtAndDueDate(): void
    {
        $grantReports = collect($this->muffins(3, GrantReport::class))
            ->each(fn(GrantReport $grantReport) => $grantReport->submit()->save())
            ->groupBy('id');

        $export = $this->export();

        $export = $export->groupBy('id');

        foreach ($export as $id => $row) {
            $row = $row->first();
            $grantReport = $grantReports->get($id)->first();

            $this->assertSame($row->title, $grantReport->entry->title);
            $this->assertSame($row->first_name, $grantReport->entry->entrant->firstName);
            $this->assertSame($row->due_date->toDateString(), $grantReport->dueDate->toDateString());
            $this->assertEquals($row->submitted_at, $grantReport->submitted_at->toDateTimeString());
        }
    }

    public function testExportContainsOnlyGrantReportFieldsFromTheSpecifiedForm(): void
    {
        $entryForms = $this->muffins(3, Form::class);
        $grantReportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $otherGrantReportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);

        $entries = [];
        $entryFields = collect();
        foreach ($entryForms as $entryForm) {
            $entryFields->push($this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'form_id' => $entryForm->id]));
            $entries[] = $this->muffin(Entry::class, ['form_id' => $entryForm->id]);
        }

        $grantReportFields = collect($this->muffins(3, Field::class, ['resource' => Field::RESOURCE_FORMS, 'form_id' => $grantReportForm->id]));
        $otherGrantReportFields = collect($this->muffins(3, Field::class, ['resource' => Field::RESOURCE_FORMS, 'form_id' => $otherGrantReportForm->id]));
        $userFields = collect($this->muffins(2, Field::class, ['resource' => Field::RESOURCE_USERS]));

        $this->muffins(1, GrantReport::class, ['form_id' => $grantReportForm->id, 'entry_id' => $entries[0]->id]);
        $this->muffins(1, GrantReport::class, ['form_id' => $grantReportForm->id, 'entry_id' => $entries[1]->id]);
        $this->muffins(1, GrantReport::class, ['form_id' => $otherGrantReportForm->id, 'entry_id' => $entries[2]->id]);

        // Only the $grantReportForm: 3 grant report fields, 2 entry fields related through the entries forms + 2 user fields
        $export = $this->export(input: ['form' => $grantReportForm->id])->first();
        $this->assertCount(7, $export->values);

        $this->assertEqualsCanonicalizing(
            $grantReportFields
                ->merge($entryFields->filter(fn($field) => in_array($field->form_id, [$entryForms[0]->id, $entryForms[1]->id])))
                ->merge($userFields)
                ->map(fn($field) => (string) $field->slug)
                ->all(),
            array_keys($export->values)
        );

        // All forms: 6 total grant report fields, 3 entry fields related through the entries forms + 2 user fields
        $export = $this->export(input: ['form' => 'all'])->first();

        $this->assertCount(11, $export->values);
        $this->assertEqualsCanonicalizing(
            $grantReportFields
                ->merge($otherGrantReportFields)
                ->merge($entryFields)
                ->merge($userFields)
                ->map(fn($field) => (string) $field->slug)
                ->all(),
            array_keys($export->values)
        );
    }

    public function testEntryTableFieldsAreLoadedCorrectly()
    {
        $form = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $grantReport = $this->muffin(GrantReport::class, ['form_id' => $form->id]);
        $field = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS, 'type' => Field::TYPE_TABLE, 'form_id' => $grantReport->entry->formId]);
        $this->muffin(Search::class, ['area' => 'manage_grant_reports.export', 'columns' => [(string) $field->slug]]);

        app(ValuesService::class)->setValuesForObject([
            ((string) $field->slug) => $json = '{"a": "this", "b": "is", "c": "for table field"}',
        ], $grantReport->entry);

        $export = $this->export([$field->slug]);

        $this->assertEquals($json, $export->first()->entry->values[(string) $field->slug]);
    }

    public function testItLoadsUserFields()
    {
        $this->user = $this->setupUserWithRole('Entrant');
        $userField = $this->muffin(Field::class, ['resource' => Field::RESOURCE_USERS]);
        app(ValuesService::class)->setValuesForObject([
            ((string) $userField->slug) => '<EMAIL>',
        ], $this->user->currentMembership);
        $entry = $this->muffin(Entry::class, ['user_id' => $this->user->id]);
        $grantReport = $this->muffin(GrantReport::class, ['entry_id' => $entry->id]);

        $search = $this->search();

        $this->assertEquals('<EMAIL>', $search->first()->values[(string) $userField->slug]);
    }

    public function testItFiltersOutReportsFromSoftDeletedEntries()
    {
        $entryActive = $this->muffin(Entry::class);
        $entryDeleted = $this->muffin(Entry::class, ['deleted_at' => now()]);
        $reportActive = $this->muffin(GrantReport::class, ['entry_id' => $entryActive->id]);
        $reportDeleted = $this->muffin(GrantReport::class, ['entry_id' => $entryDeleted->id]);

        $grantReports = $this->search();
        $grantReportsIds = $grantReports->pluck('id');

        $this->assertCount(1, $grantReportsIds);
        $this->assertContains($reportActive->id, $grantReportsIds);
        $this->assertNotContains($reportDeleted->id, $grantReportsIds);
    }

    public function testItFiltersOutReportsFromSoftDeletedEntryForms()
    {
        $formActive = $this->muffin(Form::class);
        $formDeleted = $this->muffin(Form::class, ['deleted_at' => now()]);
        $entryActive = $this->muffin(Entry::class, ['form_id' => $formActive->id]);
        $entryDeleted = $this->muffin(Entry::class, ['form_id' => $formDeleted->id]);
        $reportActive = $this->muffin(GrantReport::class, ['entry_id' => $entryActive->id]);
        $reportDeleted = $this->muffin(GrantReport::class, ['entry_id' => $entryDeleted->id]);

        $grantReports = $this->search();
        $grantReportsIds = $grantReports->pluck('id');

        $this->assertCount(1, $grantReportsIds);
        $this->assertContains($reportActive->id, $grantReportsIds);
        $this->assertNotContains($reportDeleted->id, $grantReportsIds);
    }
}
