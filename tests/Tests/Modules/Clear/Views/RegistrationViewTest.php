<?php

namespace Tests\Modules\Clear\Views;

use AwardForce\Library\Authorization\GuestConsumer;
use AwardForce\Library\Authorization\NullConsumer;
use AwardForce\Library\Authorization\TempConsumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Html\VueData;
use AwardForce\Modules\Clear\Views\RegistrationView;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Identity\Roles\Contracts\RoleRepository;
use AwardForce\Modules\Identity\Roles\Models\Role;
use AwardForce\Modules\Identity\Users\Models\GlobalUser;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Settings\Contracts\SettingRepository;
use Tests\IntegratedTestCase;
use Tests\Support\MockGlobals;

final class RegistrationViewTest extends IntegratedTestCase
{
    use FieldsSetup;
    use MockGlobals;

    private RegistrationView $view;

    public function init()
    {
        $this->initMockGlobals();
        $this->view = app(RegistrationView::class);
    }

    public function testRegistersTranslations(): void
    {
        $this->assertArrayHasKey('en_GB.home', VueData::getTranslations());
    }

    public function testDoesNotHaveTipsContentBlock(): void
    {
        $this->assertEmpty($this->view->tipsContentBlock);
    }

    public function testTipsContentBlock(): void
    {
        $contentBlock = $this->muffin(ContentBlock::class);
        $role = $this->muffin(Role::class, ['form_content_id' => $contentBlock->id]);
        session()->put('role.requested', (string) $role->slug);

        $this->assertEquals($contentBlock->id, $this->view->tipsContentBlock['id']);
    }

    public function testDefaultRole(): void
    {
        $this->assertNull($this->view->defaultRole);
        $role = $this->muffin(Role::class, ['default' => 1]);
        $this->assertEquals($role->id, $this->view->defaultRole->id);
    }

    public function testRequestedRoleSlug(): void
    {
        $role = $this->muffin(Role::class);
        session()->put('role.requested', (string) $role->slug);

        $this->assertEquals((string) $role->slug, $this->view->roleSlug);
    }

    public function testIsRoleRegistration(): void
    {
        session()->forget('role.requested');
        $this->assertFalse($this->view->isRoleRegistration);

        session()->put('role.requested', 'roleSlug');
        $this->assertTrue($this->view->isRoleRegistration());
    }

    public function testThirdPartyAuthentication(): void
    {
        $this->assertFalse($this->view->thirdPartyAuthentication);

        app(SettingRepository::class)->saveSetting('enable-3rd-party-authentication', true);
        app(SettingRepository::class)->saveSetting('social-authentication', 'twitter');

        $this->assertTrue($this->view->thirdPartyAuthentication());
    }

    public function testAgreements(): void
    {
        app(SettingRepository::class)->saveSetting('require-consent-to-notifications-and-broadcasts', false);
        app(SettingRepository::class)->saveSetting('require-agreement-to-terms', false);

        // User agreements and consent not required
        $this->setupUserWithRole('Entrant', true);
        $this->assertEmpty($this->view->agreements());

        \Consumer::set(new GuestConsumer(new User));
        $this->assertEmpty($this->view->agreements());

        // User agreements not required consent
        app(SettingRepository::class)->saveSetting('require-consent-to-notifications-and-broadcasts', true);
        $this->assertCount(1, $this->view->agreements());

        \Consumer::set(new GuestConsumer(new User));
        $this->assertCount(1, $this->view->agreements());

        $this->setupUserWithRole('Entrant', true);
        // User agreements and consent required
        app(SettingRepository::class)->saveSetting('require-agreement-to-terms', true);

        \Consumer::set(new UserConsumer($this->user));
        $this->assertCount(2, $this->view->agreements());

        \Consumer::set(new GuestConsumer(new User));
        $this->assertCount(2, $this->view->agreements());
    }

    public function testUser(): void
    {
        $this->assertEquals(\Consumer::user(), $this->view->user);
    }

    public function testInvitationToken(): void
    {
        $this->assertNull($this->view->invitationToken);
    }

    public function testRegistrationIsOpen(): void
    {
        $userConsumer = \Consumer::get();
        \Consumer::set(new NullConsumer);
        app(SettingRepository::class)->saveSetting('app-site-registration-open', 0);
        $this->assertFalse($this->view->registrationIsOpen());

        app(SettingRepository::class)->saveSetting('app-site-registration-open', 1);
        $this->assertTrue($this->view->registrationIsOpen());

        // If consumer is user, we consider registration open regardless of the setting for this view (additional details).
        app(SettingRepository::class)->saveSetting('app-site-registration-open', 0);
        \Consumer::set($userConsumer);
        $this->assertTrue($this->view->registrationIsOpen());
    }

    public function testFormAction(): void
    {
        // Default
        $this->globalUsers->shouldReceive('getByEmailOrMobile')
            ->with('<EMAIL>', '<EMAIL>')
            ->andReturn(null);
        \Consumer::set(TempConsumer::fromLogin('<EMAIL>'));
        $this->assertEquals(route('auth.register'), $this->view->formAction());

        $user = $this->setupUserWithRole('Entrant', true);
        $role = $this->muffin(Role::class, ['registration' => 1]);
        session()->put('role.requested', (string) $role->slug);
        // User and role
        $this->assertEquals(route('registration.complete.role', (string) $role->slug), $this->view->formAction());

        // User default role
        session()->forget('role.requested');
        $this->assertEquals(route('registration.complete'), $this->view->formAction());

        // QuickRegister
        $this->globalUsers->shouldReceive('getByEmailOrMobile')
            ->with($user->preferredContact(), $user->preferredContact())
            ->andReturn(new GlobalUser(['id' => $user->globalId]));
        \Consumer::set(TempConsumer::fromLogin($user->preferredContact()));

        $this->assertEquals(route('quick-register'), $this->view->formAction());
    }

    public function testDefaultFields(): void
    {
        $this->setUpFields();

        $this->globalUsers->shouldReceive('getByEmailOrMobile')
            ->with('<EMAIL>', '<EMAIL>')
            ->andReturn(null);
        \Consumer::set(TempConsumer::fromLogin('<EMAIL>'));

        // 3 Default fields (file field included) + 1 field for all roles + 2 optional fields
        $this->assertCount(6, $this->view->fields());
    }

    public function testRoleFields(): void
    {
        $roles = $this->muffins(2, Role::class);
        $this->setUpFields($roles);

        $this->globalUsers->shouldReceive('getByEmailOrMobile')
            ->with('<EMAIL>', '<EMAIL>')
            ->andReturn(null);
        \Consumer::set(TempConsumer::fromLogin('<EMAIL>', (string) $roles[0]->slug));

        // 4 Role fields + 1 field for all roles + file field + 2 optional fields
        $this->assertCount(8, $this->view->fields());
    }

    public function testDefaultUserMissingDefaultFields(): void
    {
        $this->setUpFields();
        $user = $this->muffin(User::class);
        $user->registerMembership(current_account(), 'en_GB');
        $user->syncRoles([app(RoleRepository::class)->getDefault()->id]);

        \Consumer::set(new UserConsumer($user->fresh()));

        $this->session(['_token' => 'aaa']);
        // All fields (including file field) except other role specific fields
        $this->assertCount(6, $fields = $this->view->fields());

        app(ValuesService::class)->syncValuesForObject([
            $fields[0]['slug'] => 1,
            $fields[1]['slug'] => 1,
        ], $user->currentMembership);

        \Consumer::set(new UserConsumer($user->fresh()));

        // Only missing fields
        $this->assertCount(4, $this->view->fields());
    }

    public function testRoleUserMissingFields(): void
    {
        $roles = $this->muffins(2, Role::class);
        $this->setUpFields($roles);
        $user = $this->muffin(User::class);
        $user->registerMembership(current_account(), 'en_GB');
        $user->syncRoles([$roles[0]->id, $roles[1]->id, app(RoleRepository::class)->getDefault()->id]);

        \Consumer::set(new UserConsumer($user->fresh()));

        $this->session(['_token' => 'aaa']);
        // All fields (including file field)
        $this->assertCount(10, $fields = $this->view->fields());

        app(ValuesService::class)->syncValuesForObject([
            $fields[0]['slug'] => 1,
            $fields[1]['slug'] => 1,
        ], $user->currentMembership);

        \Consumer::set(new UserConsumer($user->fresh()));

        // Only missing fields
        $this->assertCount(8, $this->view->fields());
    }

    public function testFieldsHaveWriteAccessAttributes(): void
    {
        $this->setUpFields();
        $user = $this->muffin(User::class);
        $user->registerMembership(current_account(), 'en_GB');
        $user->syncRoles([app(RoleRepository::class)->getDefault()->id]);

        \Consumer::set(new UserConsumer($user->fresh()));

        $this->session(['_token' => 'aaa']);

        $this->view->fields()->each(fn($field) => $this->assertNotNull($field['writeAccess']));
    }
}
