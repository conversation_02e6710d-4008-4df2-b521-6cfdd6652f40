<?php

namespace Tests\Modules\ReviewFlow\Search;

use AwardForce\Modules\Chapters\Contracts\ChapterRepository;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTask;
use AwardForce\Modules\Search\Search;
use AwardForce\Modules\Search\Services\ActiveSettings;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Platform\Search\ColumnatorSearch;
use Tests\IntegratedTestCase;

final class ManageReviewTasksColumnatorTest extends IntegratedTestCase
{
    private $columns = [
        'manage_review_tasks.entry_local_id',
        'manage_review_tasks.entry',
        'manage_review_tasks.review_stage',
        'manage_review_tasks.started',
        'manage_review_tasks.reviewer',
        'manage_review_tasks.decision',
    ];

    private function search(array $columns, array $input)
    {
        $search = Search::configureForSearch(
            $this->account->id,
            $this->account->activeSeason()->id,
            consumer_id(),
            $this->muffin(Form::class)->id,
            'manage_review_tasks.search',
            $columns
        );

        app(ActiveSettings::class)->setActive('manage_review_tasks.search', $search->id);

        $columnator = app(ColumnatorFactory::class)->forArea('manage_review_tasks.search', $input);

        return (new ColumnatorSearch($columnator))->search();
    }

    public function testCanSearchReviewTasks(): void
    {
        $this->muffins(3, ReviewTask::class);

        $items = $this->search($this->columns, []);

        $this->assertCount(3, $items);
    }

    public function testChapterManagerCanSearchReviewTasks(): void
    {
        $this->setupUserWithRole('ChapterManager', true);

        $this->muffin(ReviewTask::class);

        $reviewTask = $this->muffin(ReviewTask::class);
        app(ChapterRepository::class)->syncManagers($reviewTask->entry->chapter, [$this->user->id]);

        $items = $this->search($this->columns, []);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $reviewTask->id);
    }

    public function testExcludeReviewTasksForDeletedEntries(): void
    {
        $reviewTask = $this->muffin(ReviewTask::class);

        $entry = $reviewTask->entry;
        $entry->deletedAt = Carbon::now();
        $entry->save();

        $this->assertEmpty($this->search($this->columns, []));
    }

    public function testCanSearchByReviewStage(): void
    {
        $this->makeReviewTask();

        [$reviewTask, $reviewStage] = $this->makeReviewTask();

        $items = $this->search($this->columns, ['review-stage' => $reviewStage->id]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $reviewTask->id);
    }

    public function testCanSearchByReviewer(): void
    {
        $this->makeReviewTask();

        [$reviewTask, $reviewStage] = $this->makeReviewTask();
        $assignee = $this->muffin(User::class, ['first_name' => 'John', 'last_name' => 'Foo']);
        $reviewTask->assignees()->sync($assignee);

        $items = $this->search($this->columns, ['reviewer' => 'Foo']);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $reviewTask->id);
    }

    public function testCanSearchByDecision(): void
    {
        $this->makeReviewTask(ReviewTask::ACTION_STOP);

        [$reviewTask, $reviewStage] = $this->makeReviewTask(ReviewTask::ACTION_PROCEED);

        $items = $this->search($this->columns, ['decision' => ReviewTask::ACTION_PROCEED]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $reviewTask->id);
    }

    private function makeReviewTask($decision = 'proceed')
    {
        $reviewStage = $this->muffin(ReviewStage::class);

        $reviewTask = $this->muffin(ReviewTask::class, [
            'review_stage_id' => $reviewStage->id,
            'action_taken' => $decision,
        ]);

        return [$reviewTask, $reviewStage];
    }

    public function testCanSearchByLocalIdKeywords(): void
    {
        $reviewTask = $this->muffin(ReviewTask::class);
        $entry = $reviewTask->entry;
        $localId = local_id($entry);
        $entryLocalId = $entry->localId;

        $itemsByEntryLocalId = $this->search($this->columns, ['keywords' => $localId]);

        $this->assertCount(1, $itemsByEntryLocalId);
        $this->assertArrayHasKey('local_id', $itemsByEntryLocalId->first());
        $this->assertEquals($entryLocalId, Arr::get($itemsByEntryLocalId->first(), 'local_id'));
    }

    public function testCanSearchByCategoryShortcodeKeywords(): void
    {
        $reviewTask = $this->muffin(ReviewTask::class);
        $entry = $reviewTask->entry;
        $localId = local_id($entry);
        $entryLocalId = $entry->localId;

        $itemsByCategoryShortCode = $this->search($this->columns, ['keywords' => $localId]);

        $this->assertCount(1, $itemsByCategoryShortCode);
        $this->assertArrayHasKey('local_id', $itemsByCategoryShortCode->first());
        $this->assertEquals($entryLocalId, Arr::get($itemsByCategoryShortCode->first(), 'local_id'));
    }

    public function testCanSearchByReviewStageNameKeywords(): void
    {
        [$reviewTask, $reviewStage] = $this->makeReviewTask();
        $reviewStage = translate($reviewStage);

        $itemsByReviewStageName = $this->search($this->columns, ['keywords' => $reviewStage->name]);
        $itemsByWrongReviewStageName = $this->search($this->columns, ['keywords' => 'wrong'.$reviewStage->name]);

        $this->assertCount(1, $itemsByReviewStageName);
        $this->assertCount(0, $itemsByWrongReviewStageName);
        $this->assertArrayHasKey('review_stage_id', $itemsByReviewStageName->first());
        $this->assertEquals($reviewStage->id, Arr::get($itemsByReviewStageName->first(), 'review_stage_id'));
    }

    public function testCanSearchByCategoryShortcodeOnlyKeywords(): void
    {
        $reviewTask = $this->muffin(ReviewTask::class);
        $entry = $reviewTask->entry;
        $category = translate($entry->category);
        $entryLocalId = $entry->localId;

        $itemsByCategoryShortcode = $this->search($this->columns, ['keywords' => $category->shortcode]);
        $itemsByCategoryShortcodeWrong = $this->search($this->columns, ['keywords' => $category->shortcode.'wrong']);

        $this->assertCount(1, $itemsByCategoryShortcode);
        $this->assertEmpty($itemsByCategoryShortcodeWrong);
        $this->assertArrayHasKey('local_id', $itemsByCategoryShortcode->first());
        $this->assertEquals($entryLocalId, Arr::get($itemsByCategoryShortcode->first(), 'local_id'));
        $this->assertEquals($category->id, Arr::get($itemsByCategoryShortcode->first(), 'category_id'));
    }

    public function testCanSearchByToken(): void
    {
        $reviewTask = $this->muffins(3, ReviewTask::class);
        $token = $reviewTask[2]->token;

        $items = $this->search($this->columns, ['token' => $token]);

        $this->assertCount(1, $items);
        $this->assertArrayHasKey('token', $items->first());
        $this->assertEquals($token, $items->first()->token);
    }
}
