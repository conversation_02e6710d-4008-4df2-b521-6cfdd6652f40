<?php

namespace Tests\Modules\Categories\Search;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Search\Search;
use AwardForce\Modules\Search\Services\ActiveSettings;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Carbon\Carbon;
use Platform\Search\ColumnatorSearch;
use Tectonic\LaravelLocalisation\Database\TranslationService;
use Tests\IntegratedTestCase;

final class CategoryColumnatorTest extends IntegratedTestCase
{
    private $columns = [
        'categories.name',
        'categories.parent_id',
        'categories.slug',
        'categories.season',
        'categories.shortcode',
        'categories.entry_count',
        'categories.status',
        'categories.divisions',
        'categories.chapters',
        'categories.updated_at',
        'categories.promoted',
        'categories.parent',
    ];

    protected function search(array $columns, array $input)
    {
        $search = Search::configureForSearch(
            $this->account->id,
            $this->account->activeSeason()->id,
            consumer_id(),
            $this->muffin(Form::class)->id,
            'categories.search',
            $columns
        );

        app(ActiveSettings::class)->setActive('categories.search', $search->id);

        $columnator = app(ColumnatorFactory::class)->forArea('categories.search', $input);

        return (new ColumnatorSearch($columnator))->search();
    }

    public function testCanSearchCategories(): void
    {
        $this->muffin(Category::class);
        $this->muffin(Category::class);

        $categories = $this->search($this->columns, []);

        $this->assertCount(2, $categories);
    }

    public function testCanCountEntriesPerCategory(): void
    {
        $categoryA = $this->muffin(Category::class);
        $categoryB = $this->muffin(Category::class);
        $this->muffin(Entry::class, ['category_id' => $categoryA->id]);
        $this->muffins(2, Entry::class, ['category_id' => $categoryB->id]);

        $categories = $this->search($this->columns, []);

        $this->assertCount(2, $categories);
        $this->assertEquals(1, $categories->first(function ($category) use ($categoryA) {
            return $category->id == $categoryA->id;
        })->entryCount);
        $this->assertEquals(2, $categories->first(function ($category) use ($categoryB) {
            return $category->id == $categoryB->id;
        })->entryCount);
    }

    public function testCanFilterByCategoryStatus(): void
    {
        $active = $this->muffin(Category::class, ['active' => true]);
        $inactive = $this->muffin(Category::class, ['active' => false]);

        $categories = $this->search($this->columns, ['status' => 'active']);
        $this->assertCount(1, $categories);
        $this->assertEquals($active->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['status' => 'inactive']);
        $this->assertCount(1, $categories);
        $this->assertEquals($inactive->id, $categories->first()->id);
    }

    public function testCanFilterByChapter(): void
    {
        $chapterA = $this->muffin(Chapter::class);
        $chapterB = $this->muffin(Chapter::class);

        $categoryA = $this->muffin(Category::class);
        $categoryA->chapters()->sync([$chapterA->id]);

        $categoryB = $this->muffin(Category::class);
        $categoryB->chapters()->sync([$chapterB->id]);

        $categories = $this->search($this->columns, ['chapter' => $chapterA->id]);
        $this->assertCount(1, $categories);
        $this->assertEquals($categoryA->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['chapter' => $chapterA->slug]);
        $this->assertCount(1, $categories);
        $this->assertEquals($categoryA->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['chapter' => $chapterB->id]);
        $this->assertCount(1, $categories);
        $this->assertEquals($categoryB->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['chapter' => $chapterB->slug]);
        $this->assertCount(1, $categories);
        $this->assertEquals($categoryB->id, $categories->first()->id);
    }

    public function testCanFilterDeletedCategories(): void
    {
        $existingCategory = $this->muffin(Category::class);
        $deletedCategory = $this->muffin(Category::class, ['deleted_at' => Carbon::now()]);

        $categories = $this->search($this->columns, ['trashed' => 'none']);
        $this->assertCount(1, $categories);
        $this->assertEquals($existingCategory->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['deleted' => 'none']);
        $this->assertCount(1, $categories);
        $this->assertEquals($existingCategory->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['trashed' => 'only']);
        $this->assertCount(1, $categories);
        $this->assertEquals($deletedCategory->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['deleted' => 'only']);
        $this->assertCount(1, $categories);
        $this->assertEquals($deletedCategory->id, $categories->first()->id);
    }

    public function testCanFilterBySeason(): void
    {
        $this->muffin(Category::class);
        $pastSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        $pastSeasonCategory = $this->muffin(Category::class, ['season_id' => $pastSeason->id]);

        SeasonFilter::set(SeasonFilterService::FILTER_ALL);
        FormSelector::set(FormSelector::FILTER_ALL);
        $categories = $this->search($this->columns, []);
        $this->assertCount(2, $categories);

        SeasonFilter::set($pastSeason);
        FormSelector::set(FormSelector::FILTER_ALL);
        $categories = $this->search($this->columns, []);
        $this->assertCount(1, $categories);
        $this->assertEquals($pastSeasonCategory->id, $categories->first()->id);
    }

    public function testCanSearchByKeyword(): void
    {
        $category = $this->muffin(Category::class);
        $category->saveTranslation(default_language_code(), 'name', 'foo', $category->accountId);
        $category->saveTranslation(default_language_code(), 'shortcode', 'bar', $category->accountId);

        $this->muffin(Category::class); // Additional category

        $categories = $this->search($this->columns, ['keywords' => 'foo']);
        $this->assertCount(1, $categories);
        $this->assertEquals($category->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['keywords' => 'bar']);
        $this->assertCount(1, $categories);
        $this->assertEquals($category->id, $categories->first()->id);
    }

    public function testCanFilterByParentCategory(): void
    {
        $parentA = $this->muffin(Category::class);
        $childA = $this->muffin(Category::class, ['parent_id' => $parentA->id]);

        $parentB = $this->muffin(Category::class);
        $childB = $this->muffin(Category::class, ['parent_id' => $parentB->id]);

        $categories = $this->search($this->columns, ['parent' => $parentA->id]);
        $this->assertCount(1, $categories);
        $this->assertEquals($childA->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['parent' => $parentA->slug]);
        $this->assertCount(1, $categories);
        $this->assertEquals($childA->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['parent' => $parentB->id]);
        $this->assertCount(1, $categories);
        $this->assertEquals($childB->id, $categories->first()->id);

        $categories = $this->search($this->columns, ['parent' => $parentB->slug]);
        $this->assertCount(1, $categories);
        $this->assertEquals($childB->id, $categories->first()->id);
    }

    public function testCanSortByEntryCount(): void
    {
        $category0 = $this->muffin(Category::class);
        $category1 = $this->muffin(Category::class);
        $category2 = $this->muffin(Category::class);

        $this->muffins(1, Entry::class, ['category_id' => $category1->id]);
        $this->muffins(2, Entry::class, ['category_id' => $category2->id]);

        $categories = $this->search(['categories.entry_count'], ['order' => 'categories.entry_count', 'dir' => 'desc']);

        $this->assertEquals($category2->id, $categories[0]->id);
        $this->assertEquals($category1->id, $categories[1]->id);
        $this->assertEquals($category0->id, $categories[2]->id);
    }

    public function testCategoriesWithJustDeletedEntriesAreNotHidden(): void
    {
        $category = $this->muffin(Category::class);

        $this->muffin(Entry::class, ['category_id' => $category->id, 'deleted_at' => Carbon::now()]);

        $categories = $this->search($this->columns, []);

        $this->assertCount(1, $categories);
        $this->assertEquals($category->id, $categories->first()->id);
    }

    public function testCanFilterByCategorySlug(): void
    {
        $category = $this->muffin(Category::class);
        $categories = $this->search($this->columns, ['slug' => $category->slug]);

        $this->assertCount(1, $categories);
    }

    public function testParentCategoryColumnOrdersCorrectly(): void
    {
        $parent1 = $this->muffin(Category::class);
        $category1 = $this->muffin(Category::class, ['parent_id' => $parent1->id]);
        app(TranslationService::class)->sync($parent1, [
            'name' => [
                'en_GB' => 'AAA',
            ],
        ]);

        $parent2 = $this->muffin(Category::class);
        $category2 = $this->muffin(Category::class, ['parent_id' => $parent2->id]);
        app(TranslationService::class)->sync($parent2, [
            'name' => [
                'en_GB' => 'ZZZ',
            ],
        ]);

        $categories = translate($this->search($this->columns, ['order' => 'categories.parent', 'dir' => 'asc'])
            ->filter(fn($category) => $category->parent));

        $this->assertCount(2, $categories);
        $this->assertEquals($category1->id, $categories->first()->id);
        $this->assertEquals($category2->id, $categories->last()->id);

        $categories = translate($this->search($this->columns, ['order' => 'categories.parent', 'dir' => 'desc'])
            ->filter(fn($category) => $category->parent)->values());

        $this->assertCount(2, $categories);
        $this->assertEquals($category2->id, $categories[0]->id);
        $this->assertEquals($category1->id, $categories[1]->id);
    }

    public function testItDoesntShowGrantReportCategoriesWithMultiformDisabled()
    {
        $features = Feature::spy();
        $features->shouldReceive('enabled')->with('multiform')->andReturnFalse();

        $entryForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_ENTRY]);
        $grantReportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        \FormSelector::set(FormSelector::FILTER_ALL);

        $entryCategory = $this->muffin(Category::class, ['form_id' => $entryForm->id]);
        $this->muffin(Category::class, ['form_id' => $grantReportForm->id]);

        $categories = $this->search($this->columns, []);

        $this->assertCount(1, $categories);
        $this->assertEquals($categories->first()->id, $entryCategory->id);
    }

    public function testItDoesntShowGrantReportCategoriesWithMultiformEnabled()
    {
        $features = Feature::spy();
        $features->shouldReceive('enabled')->with('multiform')->andReturnTrue();

        $entryForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_ENTRY]);
        $grantReportForm = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        \FormSelector::set(FormSelector::FILTER_ALL);

        $entryCategory = $this->muffin(Category::class, ['form_id' => $entryForm->id]);
        $this->muffin(Category::class, ['form_id' => $grantReportForm->id]);

        $categories = $this->search($this->columns, []);

        $this->assertCount(1, $categories);
        $this->assertEquals($categories->first()->id, $entryCategory->id);
    }
}
