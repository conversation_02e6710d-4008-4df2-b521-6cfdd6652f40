<?php

namespace Tests\Modules\Categories\Repositories;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Categories\Repositories\EloquentCategoryRepository;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\GrantReports\Models\GrantReport;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Models\Season;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Tests\IntegratedTestCase;

final class EloquentCategoryRepositoryTest extends IntegratedTestCase
{
    /**
     * @var EloquentCategoryRepository
     */
    protected $repository;

    /**
     * Initiate the unit tests
     */
    public function init()
    {
        $this->repository = app(EloquentCategoryRepository::class);
    }

    public function testGetActiveFromChapter(): void
    {
        $chapterA = $this->muffin(Chapter::class);
        $chapterA->categories()->attach($this->muffin(Category::class));
        $chapterA->categories()->attach($this->muffin(Category::class, ['active' => false]));

        $chapterB = $this->muffin(Chapter::class);
        $chapterB->categories()->attach($this->muffin(Category::class));

        $result = $this->repository->getActiveFromChapter($chapterA);

        $this->assertCount(1, $result->all());
    }

    public function testGetForChapter(): void
    {
        $chapterA = $this->muffin(Chapter::class);
        $chapterA->categories()->attach($this->muffin(Category::class));
        $chapterA->categories()->attach($this->muffin(Category::class, ['active' => false]));
        $chapterA->categories()->attach($this->muffin(Category::class, ['deleted_at' => Carbon::now()]));

        $chapterB = $this->muffin(Chapter::class);
        $chapterB->categories()->attach($this->muffin(Category::class));

        $result = $this->repository->getForChapter($chapterA);

        $this->assertCount(2, $result->all());
    }

    public function testForceDeleteAllFromSeason(): void
    {
        $deleting = $this->muffin(Category::class);
        $safe = $this->muffin(Category::class, ['season_id' => $this->muffin(Season::class)->id]);

        $this->repository->forceDeleteAllFromSeason($deleting->seasonId);

        $this->assertNull(Category::withTrashed()->find($deleting->id));
        $this->assertNotNull(Category::find($safe->id));
    }

    public function testGetWithAllChildren(): void
    {
        $categories = [
            '0' => $this->muffin(Category::class),
            '01' => $this->muffin(Category::class),
            '02' => $this->muffin(Category::class),
            '03' => $this->muffin(Category::class),
            '04' => $this->muffin(Category::class),
            '011' => $this->muffin(Category::class),
            '012' => $this->muffin(Category::class),
            '013' => $this->muffin(Category::class),
            '031' => $this->muffin(Category::class),
            '032' => $this->muffin(Category::class, ['active' => false]),
            '033' => $this->muffin(Category::class, ['deleted_at' => Carbon::now()]),
            '041' => $this->muffin(Category::class),
            '042' => $this->muffin(Category::class),
        ];

        $categories['01']->makeChildOf($categories['0']);
        $categories['02']->makeChildOf($categories['0']);
        $categories['03']->makeChildOf($categories['0']);
        $categories['04']->makeChildOf($categories['0']);

        $categories['011']->makeChildOf($categories['01']);
        $categories['012']->makeChildOf($categories['01']);

        $categories['031']->makeChildOf($categories['03']);
        $categories['032']->makeChildOf($categories['03']);
        $categories['033']->makeChildOf($categories['03']);

        $categories['041']->makeChildOf($categories['04']);
        $categories['042']->makeChildOf($categories['04']);

        $chapterA = $this->muffin(Chapter::class);
        $chapterB = $this->muffin(Chapter::class);
        foreach ($categories as $key => $category) {
            (in_array($key, ['04', '042']) ? $chapterB : $chapterA)->categories()->attach($category);
            $category->refresh();
        }

        $result = $this->repository->getWithAllChildren($categories['0'], $chapterA->id, $chapterA->seasonId, true, false)->pluck('id')->toArray();
        $expected = $this->getExpectedCategories($categories, ['0', '01', '02', '03', '011', '012', '031', '041']); // Search: ChapterA and Category 0. Child of 0: 01, 02, 03. Child of 01: 011, 012. Child of 03: 031. Child of 04: 041

        $this->assertEqualsCanonicalizing($result, $expected);

        $result = $this->repository->getWithAllChildren($categories['01'], $chapterA->id, $chapterA->seasonId, true, false)->pluck('id')->toArray();

        $expected = $this->getExpectedCategories($categories, ['01', '011', '012']);

        $this->assertEqualsCanonicalizing($result, $expected);

        $result = $this->repository->getWithAllChildren($categories['03'], $chapterA->id, $chapterA->seasonId, true, false)->pluck('id')->toArray();

        $expected = $this->getExpectedCategories($categories, ['03', '031']);

        $this->assertEqualsCanonicalizing($result, $expected);

        $result = $this->repository->getWithAllChildren($categories['03'], $chapterA->id, $chapterA->seasonId, false, false)->pluck('id')->toArray();

        $expected = $this->getExpectedCategories($categories, ['03', '031', '032']);

        $this->assertEqualsCanonicalizing($result, $expected);
    }

    public function testGetAllByTitleReturnsCategoriesFromSeason(): void
    {
        $categories = [
            '00' => $this->muffin(Category::class, ['season_id' => 1]),
            '01' => $this->muffin(Category::class, ['season_id' => 2]),
            '02' => $this->muffin(Category::class, ['season_id' => 1]),
            '03' => $this->muffin(Category::class, ['season_id' => 1]),
        ];

        $this->assertEquals(3, $this->repository->getAllByTitle(1, '')->count());
        $this->assertEquals(1, $this->repository->getAllByTitle(2, '')->count());
    }

    public function testGetBySeasonWithChapterIds(): void
    {
        $categories = $this->muffins(3, Category::class);
        $categories[0]->chapters()->detach($categories[0]->chapters->pluck('id')->toArray());
        $categories[1]->chapters()->sync([$this->muffin(Chapter::class)->id]);
        $categories[2]->chapters()->sync(collect($this->muffins(2, Chapter::class))->pluck('id')->toArray());

        $results = $this->repository->getAllForSeasonWithChapterIds($categories[0]->season->id)->sortBy('id');

        $this->assertCount(3, $results);
        foreach ($results as $i => $category) {
            $this->assertCount($i, array_filter(json_decode($category->chapterIds)));
        }
    }

    private function getExpectedCategories(array $categories, array $keys): array
    {
        $expected = [];
        foreach ($keys as $key) {
            $expected[$key] = $categories[$key];
        }

        $expected = collect($expected)->pluck('id')->toArray();
        sort($expected);

        return $expected;
    }

    public function testGetAllByForm(): void
    {
        $categoryFromCurrentFormSeasonOne = $this->muffin(Category::class, [
            'account_id' => CurrentAccount::id(),
            'season_id' => $this->season->id,
            'form_id' => \FormSelector::getId(),
        ]);
        $categoryFromCurrentFormSeasonTwo = $this->muffin(Category::class, [
            'account_id' => CurrentAccount::id(),
            'season_id' => $this->season->id,
            'form_id' => \FormSelector::getId(),
        ]);

        $form = $this->muffin(Form::class);
        $categoryFromOtherForm = $this->muffin(Category::class, [
            'account_id' => CurrentAccount::id(),
            'season_id' => $this->season->id,
            'form_id' => $form->id,
        ]);

        $results = $this->repository->getAllForForm(\FormSelector::getId());
        $this->assertCount(2, $results);
        $this->assertCount(1, $results->where('id', $categoryFromCurrentFormSeasonOne->id));
        $this->assertCount(1, $results->where('id', $categoryFromCurrentFormSeasonTwo->id));
        $this->assertCount(0, $results->where('id', $categoryFromOtherForm->id));

        $results = $this->repository->getAllForForm($form->id);
        $this->assertCount(1, $results);
        $this->assertEquals($categoryFromOtherForm->id, $results[0]->id);
    }

    public function testGetAllCategoriesFromSpecificFormType(): void
    {
        $form1 = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $form2 = $this->muffin(Form::class, ['type' => fn() => Form::FORM_TYPE_REPORT]);
        $category1 = $this->muffin(Category::class, ['form_id' => $form1->id]);
        $category2 = $this->muffin(Category::class, ['form_id' => $form1->id]);
        $category3 = $this->muffin(Category::class, ['form_id' => $form2->id]);

        $result = $this->repository->getAllCategoriesForFormTypeAndSeason(Form::FORM_TYPE_ENTRY, $form1->season);

        $this->assertCount(2, $result);
        $this->assertTrue($result->filter(fn(Category $category) => $category->id === $category1->id)->isNotEmpty());
        $this->assertTrue($result->filter(fn(Category $category) => $category->id === $category2->id)->isNotEmpty());
        $this->assertFalse($result->filter(fn(Category $category) => $category->id === $category3->id)->isNotEmpty());
    }

    public function testGetAllCategoriesWithParent(): void
    {
        $form1 = $this->muffin(Form::class, ['type' => Form::FORM_TYPE_ENTRY]);
        $category1 = $this->muffin(Category::class, ['form_id' => $form1->id, 'parent_id' => null]);
        $category2 = $this->muffin(Category::class, ['form_id' => $form1->id, 'parent_id' => $category1->id]);

        $result = $this->repository->getAllCategoriesForFormTypeAndSeason(Form::FORM_TYPE_ENTRY, $form1->season);

        $this->assertCount(2, $result);
        $this->assertCount(1, $result->pluck('parent')->filter());
    }

    public function testGetAllForEntriesInExistingGrantReports(): void
    {
        $users = $this->muffins(2, User::class);
        $category1 = $this->muffin(Category::class);
        $category2 = $this->muffin(Category::class);
        $entry1 = $this->muffin(Entry::class, ['category_id' => $category1->id, 'user_id' => $users[0]->id]);
        $entry2 = $this->muffin(Entry::class, ['category_id' => $category2->id, 'user_id' => $users[1]->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $entry1->id]);
        $this->muffin(GrantReport::class, ['entry_id' => $entry2->id]);

        $categories = $this->repository->getAllForEntriesInExistingGrantReports($users[0]->id);

        $this->assertCount(1, $categories);
        $this->assertSame($category1->id, $categories->first()->id);
    }

    public function testPromotedForSeason(): void
    {
        $parentCategoryPromoted = $this->muffin(Category::class, ['promoted' => true]);
        $parentCategoryNotPromoted = $this->muffin(Category::class);
        $this->muffin(Category::class, ['promoted' => true]);
        $this->muffin(Category::class);

        foreach ($this->muffins(10, Category::class) as $i => $child) {
            $child->makeChildOf($i & 1 ? $parentCategoryPromoted : $parentCategoryNotPromoted);
        }

        $results = $this->repository->promotedForSeason($parentCategoryPromoted->seasonId);
        // Promoted parent + 5 children + a single promoted category
        $this->assertCount(7, $results);
    }

    public function testItGetEntrySummariesByCategoryForParentCategories(): void
    {
        $form = $this->muffin(Form::class);

        $category = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);

        $category2 = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);
        $category2->makeChildOf($category);

        $category3 = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);
        $category3->makeChildOf($category);

        FormSelector::shouldReceive('getId')->andReturn($form->id);
        FormSelector::shouldReceive('formIds')->andReturn([$form->id]);

        $chapter = $category->chapters->first();

        $this->muffins(2, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $this->muffins(3, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category2->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $this->muffins(4, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category3->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $results = $this->repository->getEntrySummariesByCategory(current_account()->activeSeason()->id, FormSelector::getId());

        $categoryResults = $results->firstWhere('id', $category->id);
        $category2Results = $results->firstWhere('id', $category2->id);
        $category3Results = $results->firstWhere('id', $category3->id);

        $this->assertNull(Arr::get($categoryResults, 'inProgress'));
        $this->assertEquals(3, Arr::get($category2Results, 'inProgress'));
        $this->assertEquals(4, Arr::get($category3Results, 'inProgress'));
    }

    public function testItDoesNotGetEntrySummariesByCategoryIfChapterIsNull(): void
    {
        $form = $this->muffin(Form::class);
        $category = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);
        $category2 = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);
        $chapter = $category->chapters->first();
        $chapter2 = $category->chapters->first();
        $chapter2->delete();

        $this->muffins(3, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $chapter2 = $category2->chapters->first();
        $this->muffins(4, Entry::class, [
            'chapter_id' => $chapter2->id,
            'category_id' => $category2->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $results = $this->repository->getEntrySummariesByCategory(current_account()->activeSeason()->id, $form->id);

        $categoryResults = $results->firstWhere('id', $category->id);
        $category2Results = $results->firstWhere('id', $category2->id);

        $this->assertEquals(0, Arr::get($categoryResults, 'inProgress'));
        $this->assertEquals(4, Arr::get($category2Results, 'inProgress'));
    }

    public function testItGetEntrySummariesByCategoryForCurrentSeason(): void
    {
        $oldSeason = $this->muffin(Season::class, [
            'status' => Season::STATUS_ARCHIVED,
        ]);

        $form1 = $this->muffin(Form::class, ['season_id' => $oldSeason->id]);
        $category1 = $this->muffin(Category::class, ['active' => true, 'form_id' => $form1->id]);
        $chapter1 = $category1->chapters->first();

        $form2 = $this->muffin(Form::class);
        $category2 = $this->muffin(Category::class, ['active' => true, 'form_id' => $form2->id]);
        $chapter2 = $category2->chapters->first();

        FormSelector::shouldReceive('getId')->andReturn($form2->id);
        FormSelector::shouldReceive('formIds')->andReturn([$form2->id]);

        // Category 1
        // Submitted entries
        $this->muffins(2, Entry::class, [
            'season_id' => $oldSeason->id,
            'chapter_id' => $chapter1->id,
            'category_id' => $category1->id,
            'form_id' => $form1->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        // In progress entries
        $this->muffins(2, Entry::class, [
            'season_id' => $oldSeason->id,
            'chapter_id' => $chapter1->id,
            'category_id' => $category1->id,
            'form_id' => $form1->id,
            'deleted_at' => null,
            'submitted_at' => now(),
        ]);

        // Category 2
        // Submitted entries
        $this->muffins(2, Entry::class, [
            'season_id' => current_account()->activeSeason()->id,
            'chapter_id' => $chapter2->id,
            'category_id' => $category2->id,
            'form_id' => $form2->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        // In progress entries
        $this->muffins(4, Entry::class, [
            'season_id' => current_account()->activeSeason()->id,
            'chapter_id' => $chapter2->id,
            'category_id' => $category2->id,
            'form_id' => $form2->id,
            'deleted_at' => null,
            'submitted_at' => now(),
        ]);

        $results = $this->repository->getEntrySummariesByCategory(current_account()->activeSeason()->id, FormSelector::getId());

        $category1Result = $results->firstWhere('id', $category1->id);

        $this->assertEquals(0, Arr::get($category1Result, 'submitted'));
        $this->assertEquals(0, Arr::get($category1Result, 'inProgress'));

        $category2Result = $results->firstWhere('id', $category2->id);
        $this->assertEquals(4, Arr::get($category2Result, 'submitted'));
        $this->assertEquals(2, Arr::get($category2Result, 'inProgress'));
    }

    public function testItHidesParentCategoriesForEntrySummariesByCategory(): void
    {
        $form = $this->muffin(Form::class);

        $category = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);

        $category2 = $this->muffin(Category::class, ['active' => true, 'form_id' => $form->id]);
        $category2->makeChildOf($category);

        FormSelector::shouldReceive('getId')->andReturn($form->id);
        FormSelector::shouldReceive('formIds')->andReturn([$form->id]);

        $chapter = $category->chapters->first();

        $this->muffins(2, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $this->muffins(3, Entry::class, [
            'chapter_id' => $chapter->id,
            'category_id' => $category2->id,
            'form_id' => $form->id,
            'deleted_at' => null,
            'submitted_at' => null,
        ]);

        $results = $this->repository->getEntrySummariesByCategory(current_account()->activeSeason()->id, FormSelector::getId());

        $categoryResults = $results->firstWhere('id', $category->id);
        $category2Results = $results->firstWhere('id', $category2->id);

        $this->assertNull(Arr::get($categoryResults, 'inProgress'));
        $this->assertEquals(3, Arr::get($category2Results, 'inProgress'));
    }

    public function testForChaptersMethod()
    {
        $category = $this->muffin(Category::class);
        $chapter1 = $this->muffin(Chapter::class);
        $chapter1->categories()->attach($category);
        $chapter1->save();

        $chapter2 = $this->muffin(Chapter::class);
        $chapter3 = $this->muffin(Chapter::class);
        $chapter3->categories()->attach($category);
        $chapter3->save();

        $result = $this->repository->chapters([$chapter1->id])->fields(['categories.id'])->get();
        $this->assertCount(1, $result);
        $this->assertEquals($category->id, $result->first()->id);

        $result = $this->repository->chapters([$chapter2->id])->fields(['categories.id'])->get();
        $this->assertCount(0, $result);

        $result = $this->repository->chapters([$chapter3->id])->fields(['categories.id'])->get();
        $this->assertCount(1, $result);
        $this->assertEquals($category->id, $result->first()->id);
    }

    public function testActiveMethod()
    {
        $category1 = $this->muffin(Category::class, ['active' => true]);
        $category2 = $this->muffin(Category::class, ['active' => false]);
        $category3 = $this->muffin(Category::class, ['active' => true]);

        $result = $this->repository->active()->fields(['id'])->get();

        $this->assertCount(2, $result);
        $this->assertTrue($result->contains('id', $category1->id));
        $this->assertTrue($result->contains('id', $category3->id));
    }

    public function testIncludeChapterIds()
    {
        $category = $this->muffin(Category::class);
        $chapters = $this->muffins(3, Chapter::class);
        foreach ($chapters as $chapter) {
            $chapter->categories()->attach($category);
            $chapter->save();
        }
        $chapter = $this->muffin(Chapter::class);

        $result = $this->repository->season($category->seasonId)->listChapterIds()->fields(['categories.id'])->get();

        $this->assertCount(1, $result);
        $chapterIds = explode(',', $result->first()->listChapterIds);
        $this->assertCount(4, $chapterIds); // the first chapter is created by default with the category
        $this->assertTrue(in_array($chapters[0]->id, $chapterIds));
        $this->assertTrue(in_array($chapters[1]->id, $chapterIds));
        $this->assertTrue(in_array($chapters[2]->id, $chapterIds));
        $this->assertFalse(in_array($chapter->id, $chapterIds));
    }

    public function testLeavesMethod()
    {
        $parentCategory = $this->muffin(Category::class);
        $leaveCategory = $this->muffin(Category::class, ['parent_id' => $parentCategory->id]);

        $result = $this->repository->leaves()->fields(['id'])->get();

        $this->assertCount(1, $result);
        $this->assertEquals($leaveCategory->id, $result->first()->id);
    }
}
