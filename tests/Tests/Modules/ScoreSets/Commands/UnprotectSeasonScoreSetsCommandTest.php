<?php

namespace Tests\Modules\ScoreSets\Commands;

use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\ScoreSets\Commands\UnprotectSeasonScoreSetsCommand;
use AwardForce\Modules\ScoreSets\Commands\UnprotectSeasonScoreSetsCommandHandler;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Seasons\Models\Season;
use Tests\IntegratedTestCase;

final class UnprotectSeasonScoreSetsCommandTest extends IntegratedTestCase
{
    public function testItIncludesDeletedScoreSetsAsWellAsScoreSetsOfDeletedForms(): void
    {
        $season = $this->muffin(Season::class);
        $deletedForm = $this->muffin(Form::class, ['deleted_at' => now()]);
        $this->muffin(ScoreSet::class, ['season_id' => $season->id, 'protected' => 1]);
        $this->muffin(ScoreSet::class, ['season_id' => $season->id, 'protected' => 1, 'deleted_at' => now()]);
        $this->muffin(ScoreSet::class, ['season_id' => $season->id, 'protected' => 1, 'form_id' => $deletedForm]);

        $handler = app(UnprotectSeasonScoreSetsCommandHandler::class);

        $handler->handle(new UnprotectSeasonScoreSetsCommand($season));

        $this->assertCount(3, ScoreSet::where('protected', 0)->withTrashed()->get());
    }
}
