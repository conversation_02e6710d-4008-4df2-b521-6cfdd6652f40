<?php

namespace Tests\Modules\ScoreSets\Commands;

use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\ScoreSets\Commands\ProtectSeasonScoreSetsCommand;
use AwardForce\Modules\ScoreSets\Commands\ProtectSeasonScoreSetsCommandHandler;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Seasons\Models\Season;
use Tests\IntegratedTestCase;

final class ProtectSeasonScoreSetsCommandTest extends IntegratedTestCase
{
    public function testItIncludesDeletedScoreSetsAsWellAsScoreSetsOfDeletedForms(): void
    {
        $season = $this->muffin(Season::class);
        $deletedForm = $this->muffin(Form::class, ['deleted_at' => now()]);
        $this->muffin(ScoreSet::class, ['season_id' => $season->id, 'protected' => 0]);
        $this->muffin(ScoreSet::class, ['season_id' => $season->id, 'protected' => 0, 'deleted_at' => now()]);
        $this->muffin(ScoreSet::class, ['season_id' => $season->id, 'protected' => 0, 'form_id' => $deletedForm]);

        $handler = app(ProtectSeasonScoreSetsCommandHandler::class);

        $handler->handle(new ProtectSeasonScoreSetsCommand($season));

        $this->assertCount(3, ScoreSet::where('protected', 1)->withTrashed()->get());
    }
}
