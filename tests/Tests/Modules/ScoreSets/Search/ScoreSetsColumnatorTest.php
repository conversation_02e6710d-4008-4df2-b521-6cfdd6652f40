<?php

namespace Tests\Modules\ScoreSets\Search;

use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\Search\Search;
use AwardForce\Modules\Search\Services\ActiveSettings;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use Carbon\Carbon;
use Platform\Search\ColumnatorSearch;
use Tests\IntegratedTestCase;

final class ScoreSetsColumnatorTest extends IntegratedTestCase
{
    private $columns = [
        'score-set.name',
        'score-set.mode',
        'score-set.url',
        'score-set.order',
        'score-set.season',
        'score_sets.updated',
        'score_sets.created',
        'score_sets.slug',
        'score-set.agreement',
        'score-set.registration-form',
        'score-set.fields',
        'score-set.responses-quantity',
        'score-set.percentage-to-qualify',
        'score-set.allow-unsure-decisions',
        'score-set.top-pick-mode',
        'score-set.winners-quantity',
        'score-set.preferences-quantity',
        'score-set.lock-scores',
        'score-set.result-calculation',
        'score-set.content-block',
        'score-set.vote-limits',
        'score-set.user-votes',
        'score-set.application-votes',
        'score-set.category-votes',
        'score-set.votes-revokable',
        'score-set.votes-visible',
        'score-set.gallery-start-date',
        'score-set.gallery-start-timezone',
        'score-set.gallery-end-date',
        'score-set.gallery-end-timezone',
    ];

    private function search(array $columns, array $input)
    {
        $search = Search::configureForSearch(
            $this->account->id,
            $this->account->activeSeason()->id,
            consumer_id(),
            $this->muffin(Form::class)->id,
            'score_sets.search',
            $columns
        );

        app(ActiveSettings::class)->setActive('score_sets.search', $search->id);

        $columnator = app(ColumnatorFactory::class)->forArea('score_sets.search', $input);

        return (new ColumnatorSearch($columnator))->search();
    }

    public function testCanSearchScoreSets(): void
    {
        $this->muffins(3, ScoreSet::class);

        $items = $this->search($this->columns, []);

        $this->assertCount(3, $items);
    }

    public function testCanSearchScoreSetsByKeyword(): void
    {
        $fooScoreSet = $this->muffin(ScoreSet::class);
        $fooScoreSet->saveTranslation(default_language_code(), 'name', 'foo', $fooScoreSet->accountId);

        $barScoreSet = $this->muffin(ScoreSet::class);
        $barScoreSet->saveTranslation(default_language_code(), 'name', 'bar', $barScoreSet->accountId);

        $items = $this->search($this->columns, ['keywords' => 'foo']);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $fooScoreSet->id);
    }

    public function testCanSearchScoreSetsByMode(): void
    {
        $this->muffin(ScoreSet::class, ['mode' => 'qualifying']);

        $scoreSet = $this->muffin(ScoreSet::class, ['mode' => 'top_pick']);

        $items = $this->search($this->columns, ['mode' => 'top_pick']);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $scoreSet->id);
    }

    public function testCanSearchScoreSetsByMultipleFilters(): void
    {
        $this->muffin(ScoreSet::class);

        $scoreSet = $this->muffin(ScoreSet::class, [
            'mode' => 'top_pick',
        ]);

        $scoreSet->saveTranslation(default_language_code(), 'name', 'Niun Niggung', $scoreSet->accountId);

        $items = $this->search($this->columns, [
            'mode' => 'top_pick',
            'keywords' => 'niggung',
        ]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $scoreSet->id);
    }

    public function testCanFilterByArchived(): void
    {
        $scoreSet1 = $this->muffin(ScoreSet::class);
        $scoreSet2 = $this->muffin(ScoreSet::class, ['archived_at' => Carbon::now()]);

        $items = $this->search($this->columns, ['archived' => 'only']);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $scoreSet2->id);
    }
}
