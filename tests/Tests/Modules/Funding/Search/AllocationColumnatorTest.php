<?php

namespace Tests\Modules\Funding\Search;

use Arr;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Funding\Data\AllocationRepository;
use AwardForce\Modules\Funding\Data\Fund;
use AwardForce\Modules\Funding\Search\Actions\CreateDocumentAction;
use AwardForce\Modules\Search\Search;
use AwardForce\Modules\Search\Services\ActiveSettings;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use AwardForce\Modules\Tags\Models\Tag;
use Platform\Search\ColumnatorSearch;
use Tests\IntegratedTestCase;

final class AllocationColumnatorTest extends IntegratedTestCase
{
    private $columns = [
        'allocation.allocated',
        'allocation.currency',
        'allocation.entrant-contact',
        'allocation.entrant-name',
        'allocation.entry-id',
        'allocation.entry-slug',
        'allocation.entry-title',
        'allocation.fund',
        'allocation.season',
        'allocation.category',
        'allocation.chapter',
        'funds.updated',
        'funds.created',
        'allocation.paid',
        'fund_allocations.updated',
    ];

    private function search(array $columns, array $input)
    {
        $search = Search::configureForSearch(
            $this->account->id,
            $this->account->activeSeason()->id,
            consumer_id(),
            $this->muffin(Form::class)->id,
            'allocations.search',
            $columns
        );

        app(ActiveSettings::class)->setActive('allocations.search', $search->id);

        $columnator = app(ColumnatorFactory::class)->forArea('allocations.search', $input);
        $repository = app(AllocationRepository::class);

        return (new ColumnatorSearch($columnator, $repository))->search();
    }

    public function testCanSearchAllocations(): void
    {
        $this->muffins(3, Allocation::class);

        $items = $this->search($this->columns, []);

        $this->assertCount(3, $items);
    }

    public function testCanSearchAllocationsByCategory(): void
    {
        $this->muffin(Allocation::class);

        $category = $this->muffin(Category::class);
        $entry = $this->muffin(Entry::class, ['category_id' => $category->id]);
        $allocation = $this->muffin(Allocation::class, ['entry_id' => $entry->id]);

        $items = $this->search($this->columns, ['category' => $category->id]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $allocation->id);
    }

    public function testCanSearchAllocationsByChapter(): void
    {
        $this->muffin(Allocation::class);

        $chapter = $this->muffin(Chapter::class);
        $entry = $this->muffin(Entry::class, ['chapter_id' => $chapter->id]);
        $allocation = $this->muffin(Allocation::class, ['entry_id' => $entry->id]);

        $items = $this->search($this->columns, ['chapter' => $chapter->id]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $allocation->id);
    }

    public function testCanSearchAllocationsByFund(): void
    {
        $this->muffin(Allocation::class);

        $fund = $this->muffin(Fund::class);
        $allocation = $this->muffin(Allocation::class, ['fund_id' => $fund->id]);

        $items = $this->search($this->columns, ['fund' => $fund->id]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $allocation->id);
    }

    public function testCanSearchAllocationsByTag(): void
    {
        $this->muffin(Allocation::class);

        $tag = $this->muffin(Tag::class);
        $entry = $this->muffin(Entry::class);
        $entry->tags()->attach($tag->id);

        $allocation = $this->muffin(Allocation::class, ['entry_id' => $entry->id]);

        $items = $this->search($this->columns, ['tag' => $tag->id]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $allocation->id);
    }

    public function testCanSearchAllocationsByMultipleFilters(): void
    {
        $this->muffin(Allocation::class);

        $entryTag = $this->muffin(Tag::class);
        $chapter = $this->muffin(Chapter::class);
        $category = $this->muffin(Category::class);
        $entry = $this->muffin(Entry::class, [
            'category_id' => $category->id,
            'chapter_id' => $chapter->id,
        ]);
        $entry->tags()->attach($entryTag->id);

        $fund = $this->muffin(Fund::class);

        $allocation = $this->muffin(Allocation::class, [
            'entry_id' => $entry->id,
            'fund_id' => $fund->id,
        ]);

        $items = $this->search($this->columns, [
            'category' => $category->id,
            'chapter' => $chapter->id,
            'fund' => $fund->id,
            'tag' => $entryTag->id,
        ]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $allocation->id);
    }

    public function testCanSearchByAllocationAndEntryTags(): void
    {
        $this->muffin(Allocation::class);

        $tag = $this->muffin(Tag::class);
        $entry = $this->muffin(Entry::class);

        $allocation = $this->muffin(Allocation::class, ['entry_id' => $entry->id]);
        $allocation->tags()->attach($tag->id);

        $items = $this->search($this->columns, ['tag' => $tag->id]);

        $this->assertCount(1, $items);
        $this->assertEquals($items->first()->id, $allocation->id);
    }

    public function testCreateDocumentActionCanSeeAmountWhenAllocatedColumnIsNotPresent()
    {
        $action = new CreateDocumentAction('allocation', 'Funding');
        $allocation = $this->muffin(Allocation::class);

        $items = $this->search(['marker'], []);

        $data = $action->viewData($items->first());
        $this->assertEquals($allocation->amount->formattedPrice(), Arr::get($data, 'allocation'));
    }
}
