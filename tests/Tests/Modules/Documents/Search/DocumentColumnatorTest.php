<?php

namespace Tests\Modules\Documents\Search;

use AwardForce\Modules\Documents\Models\Document;
use AwardForce\Modules\DocumentTemplates\Models\DocumentTemplate;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Funding\Data\Allocation;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Search\Search;
use AwardForce\Modules\Search\Services\ActiveSettings;
use AwardForce\Modules\Search\Services\ColumnatorFactory;
use Consumer;
use Platform\Search\ColumnatorSearch;
use Tests\IntegratedTestCase;

final class DocumentColumnatorTest extends IntegratedTestCase
{
    private $columns = [
        'documents.id',
        'documents.user_id',
        'documents.document_template_id',
        'documents.associatable_type',
        'documents.associatable_id',
        'documents.slug',
        'documents.file_type',
        'documents.deleted_at',
        'documents.created_at',
        'documents.updated_at',
        'document.allocation',
    ];

    private function search(array $columns, array $input)
    {
        $area = 'document.search';

        $search = Search::configureForSearch(
            $this->account->id,
            $this->account->activeSeason()->id,
            consumer_id(),
            $this->muffin(Form::class)->id,
            $area,
            $columns
        );

        app(ActiveSettings::class)->setActive($area, $search->id);

        $columnator = app(ColumnatorFactory::class)->forArea($area, $input);

        return (new ColumnatorSearch($columnator))->search();
    }

    public function testCanSearchDocuments(): void
    {
        $this->muffins($count = random_int(2, 5), Document::class);

        $documents = $this->search($this->columns, []);

        $this->assertCount($count, $documents);
    }

    public function testItShowsDocumentTemplate(): void
    {
        $document = $this->muffin(Document::class);

        $documents = $this->search($this->columns, []);

        $this->assertInstanceOf(DocumentTemplate::class, $documents->first()->documentTemplate);
        $this->assertEquals($document->documentTemplate->id, $documents->first()->documentTemplate->id);
    }

    public function testItShowsUser(): void
    {
        $document = $this->muffin(Document::class);

        $documents = $this->search($this->columns, []);

        $this->assertInstanceOf(User::class, $documents->first()->user);
        $this->assertEquals($document->user->id, $documents->first()->user->id);
    }

    public function testItShowsEntry(): void
    {
        $document = $this->muffin(Document::class);

        $documents = $this->search($this->columns, []);

        $this->assertInstanceOf(Entry::class, $documents->first()->associatable);
        $this->assertEquals($document->associatable->id, $documents->first()->associatable->id);
    }

    public function testItShowsAllocation(): void
    {
        $document = $this->muffin(Document::class, [
            'associatable_id' => 'factory|'.Allocation::class,
            'associatable_type' => Allocation::class,
        ]);

        $documents = $this->search($this->columns, []);

        $this->assertInstanceOf(Allocation::class, $documents->first()->associatable);
        $this->assertEquals($document->associatable->id, $documents->first()->associatable->id);
    }

    public function testItShowsFileType(): void
    {
        $document = $this->muffin(Document::class);

        $documents = $this->search($this->columns, []);

        $this->assertEquals($document->fileType, $documents->first()->fileType);
    }

    public function testItShowsSlug(): void
    {
        $document = $this->muffin(Document::class);

        $documents = $this->search($this->columns, []);

        $this->assertEquals((string) $document->slug, (string) $documents->first()->slug);
    }

    public function testItShowsCreatedAt(): void
    {
        $document = $this->muffin(Document::class);

        $documents = $this->search($this->columns, []);

        $this->assertEquals($document->created_at, $documents->first()->created_at);
    }

    public function testItShowsUpdatedAt(): void
    {
        $document = $this->muffin(Document::class);

        $documents = $this->search($this->columns, []);

        $this->assertEquals($document->updated_at, $documents->first()->updated_at);
    }

    public function testItCanFilterByEntrant(): void
    {
        $this->muffins(random_int(1, 5), Document::class);
        $user = $this->muffin(User::class);
        $document = $this->muffin(Document::class, ['user_id' => $user->id]);

        $documents = $this->search($this->columns, ['entrant' => (string) $user->slug]);

        $this->assertCount(1, $documents);
        $this->assertEquals($document->id, $documents->first()->id);
    }

    public function testItReturnsAnAmountForSoftDeletedFund(): void
    {
        $allocation = $this->muffin(Allocation::class);
        $this->muffin(Document::class, [
            'user_id' => Consumer::id(),
            'associatable_type' => Allocation::class,
            'associatable_id' => $allocation->id,
        ]);
        $amount = $allocation->amount;
        $allocation->fund->delete();

        $documents = $this->search($this->columns, [
            'entrant' => Consumer::id(),
        ]);

        $this->assertEquals(
            $amount,
            $documents->first()->associatable->amount,
        );
    }
}
