<?php

namespace Tests\Modules\Notifications\Services\Recipients\Resolvers;

use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Notifications\Services\Recipients\Recipients;
use AwardForce\Modules\Notifications\Services\Recipients\Resolvers\RecipientsResolver;
use Tests\UnitTestCase;

final class RecipientsResolverTest extends UnitTestCase
{
    private $resolver;

    public function init()
    {
        $this->resolver = new RecipientsResolver;
    }

    public function testValidResolver(): void
    {
        $notification = new Notification;
        $notification->recipients = '<EMAIL>';

        $this->assertTrue($this->resolver->canResolve($notification));
    }

    public function testInvalidResolver(): void
    {
        $notification1 = new Notification;
        $notification1->fieldId = 1;

        $this->assertFalse($this->resolver->canResolve($notification1));
    }

    public function testRecipients(): void
    {
        $notification = new Notification;
        $notification->recipients = '<EMAIL>,<EMAIL>';

        $recipients = $this->resolver->recipients($notification);

        $this->assertInstanceOf(Recipients::class, $recipients);
        $this->assertCount(2, $recipients);
        $this->assertEquals($notification->recipients, $recipients->asString());
    }

    public function testRecipientsIgnoreEmpty(): void
    {
        $notification = new Notification;
        $notification->recipients = '<EMAIL>, ,<EMAIL>,';

        $recipients = $this->resolver->recipients($notification);

        $this->assertInstanceOf(Recipients::class, $recipients);
        $this->assertCount(2, $recipients);
        $this->assertEquals('<EMAIL>,<EMAIL>', $recipients->asString());
    }
}
