<?php

namespace Tests\Modules\Assignments\View;

use AwardForce\Modules\Assignments\View\SearchAssignments;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Holocron\Services\FeatureIntros;
use Illuminate\Support\Facades\View;
use Mockery as m;
use Tests\IntegratedTestCase;

final class AssignmentsTest extends IntegratedTestCase
{
    private SearchAssignments $view;

    public function init(): void
    {
        $featureIntrosMock = m::mock(FeatureIntros::class);
        $featureIntrosMock->shouldReceive('forRoute')->andReturn(null);
        app()->instance(FeatureIntros::class, $featureIntrosMock);

        Feature::spy();

        $this->view = app(SearchAssignments::class);
    }

    public function testAssignmentTestPageContainsDropdownForNewAssignmentsOnMultiformEnabledAccounts(): void
    {
        Feature::shouldReceive('enabled')->with('multiform')->andReturn(true);

        $content = View::make('assignment.index', $this->view)->renderSections();

        $this->assertMatchesRegularExpression(
            '/<add-action-dropdown :revealed-action="reveal" label="'.trans('assignments.titles.new').'/',
            $content['main']
        );
        $this->assertMatchesRegularExpression(
            '/<add-action-dropdown :revealed-action="reveal" label="'.trans('assignments.titles.new_random').'/',
            $content['main']
        );
        $this->assertDoesNotMatchRegularExpression(
            '/<span>'.trans('assignments.titles.new').'<\/span>/',
            $content['main']
        );
        $this->assertDoesNotMatchRegularExpression(
            '/<span>'.trans('assignments.titles.new_random').'<\/span>/',
            $content['main']
        );
    }

    public function testAssignmentTestPageContainsButtonsForNewAssignmentsOnMultiformDisabledAccounts(): void
    {
        Feature::shouldReceive('enabled')->with('multiform')->andReturn(false);

        $content = View::make('assignment.index', $this->view)->renderSections();

        $this->assertDoesNotMatchRegularExpression(
            '/<add-action-dropdown :revealed-action="reveal" label="'.trans('assignments.titles.new').'/',
            $content['main']
        );
        $this->assertDoesNotMatchRegularExpression(
            '/<add-action-dropdown :revealed-action="reveal" label="'.trans('assignments.titles.new_random').'/',
            $content['main']
        );
        $this->assertMatchesRegularExpression(
            '/<span>'.trans('assignments.titles.new').'<\/span>/',
            $content['main']
        );
        $this->assertMatchesRegularExpression(
            '/<span>'.trans('assignments.titles.new_random').'<\/span>/',
            $content['main']
        );
    }
}
