<?php

namespace Tests\Modules\Forms\Collaboration\Middleware;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Forms\Collaboration\Middleware\Collaborator;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator as CollaboratorModel;
use AwardForce\Modules\Forms\Collaboration\ValueObjects\Privilege;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Identity\Users\Models\User;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Tests\IntegratedTestCase;

class CollaboratorTest extends IntegratedTestCase
{
    public function testItAccessPermittedWhenConsumerIsACollaborator()
    {
        $form = $this->muffin(Form::class, ['settings' => FormSettings::create(['collaborative' => true])]);
        $submittable = $this->muffin(Entry::class, ['form_id' => $form->id]);

        $this->muffin(Membership::class, ['user_id' => ($user = $this->muffin(User::class))->id]);
        CollaboratorModel::forSubmittable(
            $submittable,
            $user,
            Privilege::viewer()
        );
        Consumer::set(new UserConsumer($user));

        $response = (new Collaborator())->handle($this->getSubmittableRequest($submittable), fn() => 'next');

        $this->assertEquals('next', $response);
    }

    public function testItCantAccessWhenFormIsNotCollaborative()
    {
        $form = $this->muffin(Form::class, ['collaborative' => false]);
        $submittable = $this->muffin(Entry::class, ['form_id' => $form->id]);

        $this->muffin(Membership::class, ['user_id' => ($user = Consumer::user())->id]);
        Consumer::set(new UserConsumer($user));

        $this->muffin(CollaboratorModel::class, [
            'submittable_id' => $submittable->id,
            'submittable_type' => $submittable->getMorphClass(),
            'user_id' => $user->id,
        ]);

        $this->expectException(NotFoundHttpException::class);

        app(Collaborator::class)->handle($this->getSubmittableRequest($submittable), function ($request) {
            return 'next';
        });
    }

    public function getSubmittableRequest(Submittable $submittable): Request
    {
        return (new Request())
            ->setRouteResolver(fn() => $this->mock('router')
                ->shouldReceive('parameters')
                ->andReturn([$submittable])
                ->getMock());
    }
}
