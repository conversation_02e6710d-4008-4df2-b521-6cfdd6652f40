<?php

namespace Tests\Modules\Files\Listeners;

use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Features\Facades\Feature;
use AwardForce\Modules\Files\Commands\BroadcastTranscodingStatusUpdate;
use AwardForce\Modules\Files\Events\FileProcessed;
use AwardForce\Modules\Files\Events\TranscodeStatusWasUpdated;
use AwardForce\Modules\Files\Events\TranscodeWasLogged;
use AwardForce\Modules\Files\Listeners\TranscodingListener;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Files\Models\Transcode;
use AwardForce\Modules\Identity\Users\Models\User;
use Illuminate\Contracts\Bus\Dispatcher as CommandDispatcher;
use Illuminate\Contracts\Events\Dispatcher as EventDispatcher;
use Illuminate\Translation\Translator;
use Illuminate\View\Factory as ViewFactory;
use Mockery as m;
use Platform\Database\Eloquent\Collection;
use Tests\UnitTestCase;

final class TranscodingListenerTest extends UnitTestCase
{
    /** @var TranscodingListener */
    private $listener;

    private $commands;
    private $events;

    public function init()
    {
        $this->commands = m::spy(CommandDispatcher::class);
        $this->events = m::spy(EventDispatcher::class);
        $this->translator = m::spy(Translator::class);
        $this->view = m::mock(ViewFactory::class);

        $this->listener = new TranscodingListener($this->commands, $this->events, $this->translator, $this->view);

        Feature::shouldReceive('enabled')->with('transcoding')->andReturn(true);
        Feature::shouldReceive('enabled')->with('image_optimisation')->andReturn(true);
        CurrentAccount::shouldReceive('set');
        CurrentAccount::shouldReceive('attribute')->with('region')->andReturn('us')->byDefault();
    }

    public function testFileWasStoredListenerWorksForVideoFilesOnly(): void
    {
        $file1 = new File;
        $file1->id = 1;
        $file1->status = File::STATUS_OK;
        $file1->mime = 'video/mp4';
        $file1->setRelation('user', new User);
        $file1->setRelation('account', new Account);

        $file2 = new File;
        $file2->mime = 'image';
        $file2->status = File::STATUS_OK;

        $this->listener->whenFileProcessed(new FileProcessed($file1));
        $this->listener->whenFileProcessed(new FileProcessed($file2));

        $this->commands->shouldHaveReceived('dispatch')->once();
    }

    public function testSetupOfQueuedFilesWhenANewTranscodeJobIsLogged(): void
    {
        $files = new Collection([new File, new File]);

        $transcode = new Transcode;
        $transcode->setRelation('user', new User);
        $transcode->setRelation('files', $files);

        $this->listener->whenTranscodeWasLogged(new TranscodeWasLogged($transcode));

        $this->commands->shouldHaveReceived('dispatch')->twice();
        $this->translator->shouldHaveReceived('get')->once();
        $this->events->shouldHaveReceived('fire')->once();
    }

    public function testTranscodingStatusUpdates(): void
    {
        $files = new Collection([new File, new File]);

        $transcode = new Transcode;
        $transcode->setRelation('user', new User);
        $transcode->setRelation('files', $files);

        $file = new File;
        $file->transcodingStatus = 'completed';
        $file->original = 'some.thing.mp4';

        $this->view->shouldReceive('make')->andReturn($this->view);
        $this->view->shouldReceive('render')->andReturn('contents');
        CurrentAccount::shouldReceive('get')->andReturn($this->account);

        $this->listener->whenTranscodeStatusWasUpdated(new TranscodeStatusWasUpdated($transcode, $file));

        $this->events->shouldHaveReceived('fire')->once();
    }

    public function testTranscodingPayloadHasLimitedFiles(): void
    {
        // 110x files
        $files = Collection::times(110, function () {
            $file = new File;

            return $file;
        });

        $transcode = new Transcode;
        $transcode->setRelation('user', new User);
        $transcode->setRelation('files', $files);

        $file = new File;
        $file->transcodingStatus = 'completed';
        $file->original = 'some.thing.mp4';

        $this->view->shouldReceive('make')->andReturn($this->view);
        $this->view->shouldReceive('render')->andReturn('contents');
        CurrentAccount::shouldReceive('get')->andReturn($this->account);

        $this->listener->whenTranscodeStatusWasUpdated(new TranscodeStatusWasUpdated($transcode, $file));

        $this->events->shouldHaveReceived('fire')->with(m::on(function (BroadcastTranscodingStatusUpdate $event) {
            $this->assertCount(100, $event->broadcastWith()['data']['transcodedFiles']);

            return true;
        }))->once();
    }

    public function testTranscodePayloadHasVideoHeight(): void
    {
        $file = new File;
        $file->transcodingStatus = 'completed';
        $file->original = 'some.thing.mp4';
        $file->mime = 'video/mp4';

        $transcode = new Transcode;
        $transcode->setRelation('user', new User);
        $transcode->setRelation('files', new Collection([$file]));

        $this->view->shouldReceive('make')->andReturn($this->view);
        $this->view->shouldReceive('render')->andReturn('contents');
        $this->account->videoPlayerHeight = 450;
        CurrentAccount::shouldReceive('get')->andReturn($this->account);

        $this->listener->whenTranscodeStatusWasUpdated(new TranscodeStatusWasUpdated($transcode, $file));

        $this->events->shouldHaveReceived('fire')->with(m::on(function (BroadcastTranscodingStatusUpdate $event) {
            $this->assertEquals(450, $event->broadcastWith()['data']['transcodedFiles'][0]['videoHeight']);

            return true;
        }))->once();
    }

    public function testTranscodePayloadHasCaptionFile()
    {
        $this->view->shouldReceive('make')->andReturn($this->view);
        $this->view->shouldReceive('render')->andReturn('contents');
        CurrentAccount::shouldReceive('get')->andReturn($this->account);

        $file = new File;
        $file->transcodingStatus = 'completed';
        $file->original = 'some.thing.mp4';
        $file->mime = 'video/mp4';

        $caption = new File;
        $caption->id = 332;
        $caption->file = 'caption.vtt';

        $file->setRelation('caption', $caption);

        $transcode = new Transcode;
        $transcode->setRelation('user', new User);
        $transcode->setRelation('files', new Collection([$file]));

        $this->listener->whenTranscodeStatusWasUpdated(new TranscodeStatusWasUpdated($transcode, $file));

        $this->events->shouldHaveReceived('fire')->with(m::on(function (BroadcastTranscodingStatusUpdate $event) use ($caption) {
            $this->assertArrayHasKey('caption', $event->broadcastWith()['data']['transcodedFiles'][0]);
            $this->assertEquals($caption->id, $event->broadcastWith()['data']['transcodedFiles'][0]['caption']['id']);

            return true;
        }))->once();
    }
}
