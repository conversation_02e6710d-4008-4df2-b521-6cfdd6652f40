<?php

namespace Tests\Modules\Content\InterfaceText\Services;

use AwardForce\Modules\Accounts\Models\SupportedLanguage;
use AwardForce\Modules\Accounts\Models\SupportedLanguages;
use AwardForce\Modules\Content\InterfaceText\Contracts\InterfaceTextRepository;
use AwardForce\Modules\Content\InterfaceText\Services\InterfaceTextService;
use AwardForce\Modules\Content\InterfaceText\Services\NoTooltipInterfaceTextService;
use AwardForce\Modules\Content\Terms\Facades\Term;
use AwardForce\Modules\Features\Facades\Feature;
use Illuminate\Support\Facades\Config;
use Mockery as m;
use Platform\Localisation\Translation;
use Tests\IntegratedTestCase;

final class InterfaceTextServiceTest extends IntegratedTestCase
{
    private InterfaceTextRepository $repository;
    private InterfaceTextService $service;

    public function init()
    {
        $this->repository = app(InterfaceTextRepository::class);
        $this->service = new InterfaceTextService($this->repository);
    }

    public function testItReturnsDefaultOverride(): void
    {
        $override = $this->service->getOverride('category.titles.new', collect());

        $this->assertNotNull($override);
        $this->assertEquals('category@titles@new', $override->field);
        $this->assertNotEmpty($override->translated['en_GB']['default']);
        $this->assertEquals($override->translated['en_GB']['value'], $override->translated['en_GB']['default']);
    }

    public function testItReturnsCurrentOverride(): void
    {
        $existingOverrides = $this->repository->getExistingOverrides()->groupBy('field');
        $override = $this->service->getOverride('category.titles.new', $existingOverrides);

        $this->assertNotNull($override);
        $this->assertEquals('New category', $override->translated['en_GB']['value']);

        $this->createOverride('en_GB', 'category.titles.new', 'OVERRIDDEN');
        $existingOverrides = $this->repository->getExistingOverrides()->groupBy('field');
        $override = $this->service->getOverride('category.titles.new', $existingOverrides);

        $this->assertNotNull($override);
        $this->assertEquals('OVERRIDDEN', $override->translated['en_GB']['value']);
    }

    public function testOverridablesGrouped(): void
    {
        $grouped = $this->service->overridablesGrouped();

        $this->assertNotEmpty($grouped);
        $this->assertIsString($firstHeader = array_keys($grouped)[0]);
        $this->assertGreaterThan(0, strlen($firstHeader));
        $this->assertGreaterThan(0, count($grouped[$firstHeader]));
    }

    public function testOverridableFields(): void
    {
        $this->assertGreaterThan(0, count($this->service->overridableFields()));
    }

    public function testUsesDescriptionFallback(): void
    {
        current_account()->setRelation('languages', new SupportedLanguages([
            new SupportedLanguage(['code' => 'en_GB']),
            new SupportedLanguage(['code' => 'el_GR']),
        ]));

        $existingOverrides = $this->repository->getExistingOverrides()->groupBy('field');
        $override = $this->service->getOverride('entries.form.title.help', $existingOverrides);
        $this->assertEquals('Entry name field help text', $override->translated['en_GB']['default']);
        $this->assertStringStartsWith('Συμμετοχή', $override->translated['el_GR']['default']);

        current_account()->vertical = 'grants';
        Term::clearCache();
        $override = $this->service->getOverride('entries.form.title.help', $existingOverrides);
        $this->assertEquals('Application name field help text', $override->translated['en_GB']['default']);
        $this->assertStringStartsWith('Αίτηση', $override->translated['el_GR']['default']);
        current_account()->vertical = 'awards';
    }

    public function testResolvesDescriptiveKey(): void
    {
        $override = $this->service->getOverride('entries.titles.entrant', $existingOverrides = $this->repository->getExistingOverrides()->groupBy('field'));

        $this->assertEquals('Entries titles entrant', $override->descriptiveKey);

        current_account()->vertical = 'grants';
        Term::clearCache();
        $override = $this->service->getOverride('entries.titles.entrant', $existingOverrides);

        $this->assertEquals('Applications titles applicant', $override->descriptiveKey);

        current_account()->vertical = 'awards';
    }

    public function testCleansHtml(): void
    {
        $this->createOverride('en_GB', 'auth.request_login_link.email_sent', '<p>Override &quot; &lt;script&gt;alert(&quot;xss&quot;)&lt;/&gt;</p>');
        $override = $this->service->getOverride('auth.request_login_link.email_sent', $existingOverrides = $this->repository->getExistingOverrides()->groupBy('field'));

        $this->assertStringContainsString("<br />\n", trans('auth.request_login_link.email_sent'));
        $this->assertEquals('Override " alert("xss")', $override->translated['en_GB']['value']);
        $this->assertStringNotContainsString('<p>', $override->translated['en_GB']['value']);
    }

    public function testItCanTemporarilyDisableDisplayingTooltips(): void
    {
        $this->repository = app(InterfaceTextRepository::class);
        $this->service = new FakeInterfaceTextService($this->repository);

        $this->assertTrue($this->service->displayEditTextTooltip('fake.key'));

        $this->service = m::spy(NoTooltipInterfaceTextService::class);

        $this->assertFalse($this->service->displayEditTextTooltip('fake.key'));

        $this->service->shouldNotHaveReceived('getDisplayTooltipsSetting');
    }

    public function testKeyShouldNotBeWrapWithTooltipDisplay(): void
    {
        $this->repository = app(InterfaceTextRepository::class);
        $this->service = new FakeInterfaceTextService($this->repository);
        $text = $this->service->translationOrEditText('key1', 'editInterfaceText');

        $this->assertStringStartsNotWith('<edit-interface-text', $text);
    }

    private function createOverride(string $lang, string $key, string $value)
    {
        $trans = new Translation([
            'language' => $lang,
            'resource' => 'ui',
            'field' => $key,
            'value' => $value,
        ]);
        $trans->accountId = current_account_id();
        $trans->save();
    }

    public function testTextReturnsMarkdownsString(): void
    {
        $this->createOverride('en_GB', 'buttons.add_more_entries', '[Markdown Text](link)');
        $text = $this->service->translationOrEditText('buttons.add_more_entries', '[Markdown Text](link)');
        $this->assertNotEquals('No Markdown Text', $text);
        $this->assertStringStartsWith('<a href', $text);
    }

    public function testTextReturnsNoMarkdownString(): void
    {
        $text = $this->service->translationOrEditText('buttons.add_more_entries', 'No Markdown Text');
        $this->assertEquals('No Markdown Text', $text);

        $text = $this->service->translationOrEditText('notInterfaceText', '[No Markdown Text](link)');
        $this->assertStringStartsNotWith('<a href', $text);
    }

    public function testTextReturnsEditInterfaceText(): void
    {
        $this->repository = app(InterfaceTextRepository::class);
        $this->service = new FakeInterfaceTextService($this->repository);
        $text = $this->service->translationOrEditText('fake.edit_interface_text', 'editInterfaceText');
        $this->assertStringStartsWith('<edit-interface-text', $text);
    }

    public function testItFiltersSectionsDisabled(): void
    {
        $this->service = new FakeInterfaceTextService($this->repository);
        Config::spy();
        Config::shouldReceive('get')->with('interface-text.overridable_keys')->andReturn(
            [
                'key1' => ['value1', 'value2'],
                'key2' => ['disabled1', 'disabled2'],
            ]
        );
        Config::shouldReceive('get')->with('interface-text.depends-on-features', null)->andReturn(['key2' => 'key2']);
        Config::shouldReceive('get')->with('awardforce.translation-replacements', null)->andReturn([]);
        Feature::shouldReceive('enabled')->with('key1')->andReturn(true);
        Feature::shouldReceive('enabled')->with('key2')->andReturn(false);

        $result = $this->service->overridablesGrouped();

        $this->assertCount(1, $result);
        $this->assertStringContainsString('key1', array_key_first($result));
        $this->assertStringNotContainsString('key2', array_key_first($result));
    }

    public function testEditInterfaceTextTagIsNotDuplicated(): void
    {
        $this->repository = app(InterfaceTextRepository::class);
        $this->service = new FakeInterfaceTextService($this->repository);
        $text = $this->service->wrapStringWithTooltip('fake.key', 'fake text');
        $text = $this->service->wrapStringWithTooltip('fake.key', $text);
        // assert it contains only one <edit-interface-text> tag
        $this->assertEquals(1, substr_count($text, '<edit-interface-text'));
    }
}

class FakeInterfaceTextService extends InterfaceTextService
{
    protected function getDisplayTooltipsSetting(): bool
    {
        return true;
    }

    public function isOverridableKey(string $key): bool
    {
        return true;
    }

    protected function isProgramManager(): bool
    {
        return true;
    }

    protected function keysExcludedForToolTip(): array
    {
        return ['key1', 'key2'];
    }
}
