<?php

namespace Tests\Library\Bus\Pipeline;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Authorization\UserConsumer;
use AwardForce\Library\Bus\JediAware;
use AwardForce\Library\Bus\JediAwareness;
use AwardForce\Library\Bus\Pipeline\QueueContext;
use AwardForce\Library\Context\Context;
use AwardForce\Library\Context\Contextual;
use AwardForce\Library\Context\ContextualQueue;
use AwardForce\Modules\Accounts\Facades\CurrentAccount;
use AwardForce\Modules\Accounts\Models\Account;
use AwardForce\Modules\Authentication\Services\Emulator\Facades\JediEmulator;
use AwardForce\Modules\Authentication\Services\Emulator\Values\Jedi;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\URL;
use Platform\Bus\Pipeline\Accountable;
use Tests\IntegratedTestCase;

final class QueueContextTest extends IntegratedTestCase
{
    public function init()
    {
        config(['queue.default' => 'redis']);
    }

    public function testRestoreFromContext(): void
    {
        $context = $this->buildContext(
            $account = $this->muffin(Account::class),
            $season = $this->muffin(Season::class, ['account_id' => $account->id]),
            $form = $this->muffin(Form::class, ['account_id' => $account->id, 'season_id' => $season->id])
        );

        // Switch session
        session()->regenerate(true);
        $this->assertNotEquals($context->sessionId, session()->id());

        $command = new class($context = unserialize(serialize($context))) implements Accountable, Contextual, JediAwareness, ShouldQueue
        {
            use ContextualQueue;
            use JediAware;

            public function __construct(Context $context)
            {
                $this->context = $context;
                $this->setPreferredEmail(Consumer::user()->email);
            }
        };

        JediEmulator::setJedi(new Jedi(10, '<EMAIL>'));

        $result = (new QueueContext)->handle($command, function ($actualCommand) use ($command, $context, $account, $season, $form) {
            $snapshot = Context::snapshot();
            $this->assertSame($command, $actualCommand);
            $this->assertContextEquals($context, $snapshot);
            $this->assertEquals($context->account->id, $account->id);
            $this->assertEquals($context->account->url(), URL::current());
            $this->assertEquals($context->consumer->id(), Consumer::id());
            $this->assertEquals('<EMAIL>', $command->getPreferredEmail());
            $this->assertEquals($season->id, SeasonFilter::getId());
            $this->assertFalse(SeasonFilter::viewingAll());
            $this->assertEquals($form->id, FormSelector::getId());
            $this->assertFalse(FormSelector::viewingAll());
            $this->assertEquals($context->account->name, $account->name);
            $this->assertEquals($context->sessionId, session()->id());

            return 'This is another result.';
        });

        $this->assertEquals('This is another result.', $result);

        $this->assertEquals(1, JediEmulator::jedi()->id());
        $this->assertEquals('<EMAIL>', JediEmulator::jedi()->email());
    }

    public function testItHonoursAllSeasonsAndAllFormsSelectionAfterRestore()
    {
        $context = $this->buildContext(
            current_account(),
            $season = $this->muffin(Season::class),
            $this->muffin(Form::class, ['season_id' => $season->id]),
            true,
            true
        );

        $command = new class($context) implements Accountable, Contextual, ShouldQueue
        {
            use ContextualQueue;

            public function __construct(public $context)
            {
            }
        };

        $result = (new QueueContext)->handle($command, function ($actualCommand) {
            $this->assertTrue(SeasonFilter::viewingAll());
            $this->assertTrue(FormSelector::viewingAll());

            return 'This is another result.';
        });

        $this->assertEquals('This is another result.', $result);

    }

    protected function buildContext(
        Account $account,
        ?Season $season = null,
        ?Form $form = null,
        $showAllSeasons = false,
        $showAllForms = false
    ): Context {
        $account->domains()->create(['domain' => 'context.test']);
        $user = $this->muffin(User::class, ['email' => '<EMAIL>']);

        $this->emulateJedi($user->globalId, 1, '<EMAIL>');

        CurrentAccount::set($account);
        Consumer::set(new UserConsumer($user));
        SeasonFilter::set($showAllSeasons ? SeasonFilterService::FILTER_ALL : ($season ?: $this->muffin(Season::class)));
        FormSelector::set($showAllForms ? FormSelector::FILTER_ALL : ($form ?: $this->muffin(Form::class)));

        return Context::snapshot();
    }

    protected function assertContextEquals(Context $expected, Context $actual)
    {
        $expected = $expected->toArray();

        $this->assertEquals($expected, $actual->toArray());
    }
}
