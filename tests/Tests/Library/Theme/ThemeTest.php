<?php

namespace Tests\Library\Theme;

use AwardForce\Library\Theme\Theme;
use AwardForce\Modules\Files\Contracts\FileRepository;
use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Theme\Models\Theme as Model;
use AwardForce\Modules\Theme\Repositories\ThemeRepository;
use Mockery as m;
use Tests\IntegratedTestCase;

final class ThemeTest extends IntegratedTestCase
{
    private $themes;
    private $files;
    private $theme;

    public function init()
    {
        $this->themes = m::mock(ThemeRepository::class);
        $this->files = m::mock(FileRepository::class);

        $this->theme = new Theme($this->themes, $this->files);
    }

    public function testPrimaryButton(): void
    {
        $themeSetting = new Model(['value' => '987234']);

        $this->themes->shouldReceive('getByKey')->once()->with('primary-button')->andReturn($themeSetting);

        $this->assertEquals('#987234', $this->theme->primaryButton());
    }

    public function testSecondaryButton(): void
    {
        $this->themes->shouldReceive('getByKey')->once()->with('secondary-button')->andReturn(null);

        $this->assertEquals('#18AECF', $this->theme->secondaryButton());
    }

    public function testHex2RgbConversion(): void
    {
        $this->assertSame('rgba(255, 255, 255, 1)', $this->theme->hex2rgb('#FFF'));
        $this->assertSame('rgba(255, 255, 255, 1)', $this->theme->hex2rgb('#FFFFFF'));
    }

    public function testMagicThemeValueRetrieval(): void
    {
        $themeSetting = new Model(['value' => 'setting value']);
        $this->themes->shouldReceive('getByKey')->once()->with('some-key')->andReturn($themeSetting);

        $this->assertSame('setting value', $this->theme->someKey);
    }

    public function testFileRetrievalViaSetting(): void
    {
        $file = new File(['file' => '/path/to/file.jpg']);
        $themeSetting = new Model(['value' => 1]);

        $this->themes->shouldReceive('getByKey')->with('header-image')->andReturn($themeSetting);
        $this->files->shouldReceive('getById')->once()->with(1)->andReturn($file);

        $this->assertSame($file, $this->theme->file('header-image'));
    }

    public function testInvalidThemeKeyForFile(): void
    {
        $this->themes->shouldReceive('getByKey')->with('missing-key')->andReturn(null);

        $this->assertNull($this->theme->file('missing-key'));
    }

    public function testInvalidFileRecordForFileRetrieval(): void
    {
        $themeSetting = new Model(['value' => 42]);

        $this->themes->shouldReceive('getByKey')->with('valid-key')->andReturn($themeSetting);
        $this->files->shouldReceive('getById')->once()->with(42)->andReturn(null);

        $this->assertNull($this->theme->file('valid-key'));
    }
}
