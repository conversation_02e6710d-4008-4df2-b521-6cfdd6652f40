<?php

namespace Tests\Library\Authorization;

use AwardForce\Library\Authorization\Bouncer;
use AwardForce\Modules\Identity\Roles\Contracts\AuthoriserInterface;
use Mockery as m;
use Tests\UnitTestCase;

final class BouncerTest extends UnitTestCase
{
    private $consumer;

    public function init()
    {
        $this->consumer = m::mock(AuthoriserInterface::class);
    }

    public function testGetRequestPermissionChecks(): void
    {
        $bouncer = new Bouncer('get', 'Namespace\UserController');

        $this->consumer->shouldReceive('can')->with('view', 'Users')->once()->andReturn(true);

        $this->assertTrue($bouncer->permits($this->consumer));
    }

    public function testPostRequestPermissionChecks(): void
    {
        $bouncer = new Bouncer('post', 'Namespace\UserController');

        $this->consumer->shouldReceive('can')->with('create', 'Users')->once()->andReturn(true);

        $this->assertTrue($bouncer->permits($this->consumer));
    }

    public function testPutRequestPermissionChecks(): void
    {
        $bouncer = new Bouncer('PUT', 'Namespace\UserController');

        $this->consumer->shouldReceive('can')->with('update', 'Users')->once()->andReturn(true);

        $this->assertTrue($bouncer->permits($this->consumer));
    }

    public function testDeleteRequestPermissionChecks(): void
    {
        $bouncer = new Bouncer('delete', 'Namespace\UserController');

        $this->consumer->shouldReceive('can')->with('delete', 'Users')->once()->andReturn(true);

        $this->assertTrue($bouncer->permits($this->consumer));
    }
}
