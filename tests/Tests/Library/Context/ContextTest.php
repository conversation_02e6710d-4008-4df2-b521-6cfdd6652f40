<?php

namespace Tests\Library\Context;

use AwardForce\Library\Authorization\Consumer;
use AwardForce\Library\Context\Context;
use AwardForce\Modules\Authentication\Services\Emulator\Facades\JediEmulator;
use AwardForce\Modules\Authentication\Services\Emulator\Values\Jedi;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Seasons\Services\SeasonFilterService;
use Facades\Platform\Strings\Output;
use Tests\IntegratedTestCase;

final class ContextTest extends IntegratedTestCase
{
    public function testKnowsAccount(): void
    {
        $context = Context::snapshot();

        $this->assertSame($this->account, $context->account);
        $this->assertSame($this->account->id, $context->account->id);
    }

    public function testInstanceWithRequestId(): void
    {
        $originalId = Context::snapshot()->request->id();
        $secondId = Context::snapshot()->request->id();

        $this->assertSame($originalId, $secondId);
    }

    public function testKnowsConsumer(): void
    {
        $this->setupUserWithRole('Entrant', true);
        $context = Context::snapshot();

        $this->assertSame($consumer = Consumer::get(), $context->consumer);
        $this->assertSame($consumer->user(), $context->consumer->user());
        $this->assertSame($consumer->id(), $context->userId);
    }

    public function testKnowsActiveSeason(): void
    {
        $context = Context::snapshot();

        $this->assertFalse($context->showAllSeasons);
        $this->assertEquals($this->season->id, $context->seasonId);
    }

    public function testHonourFilteredSeason(): void
    {
        SeasonFilter::set($season = $this->muffin(Season::class));
        $context = Context::snapshot();

        $this->assertFalse($context->showAllSeasons);
        $this->assertEquals($season->id, $context->seasonId);
    }

    public function testHonourAllSeasonsFilter(): void
    {
        SeasonFilter::set(SeasonFilterService::FILTER_ALL);
        $context = Context::snapshot();

        $this->assertTrue($context->showAllSeasons);
        $this->assertEquals($this->season->id, $context->seasonId);
    }

    public function testKnowsRequest(): void
    {
        $context = Context::snapshot();
        $this->assertNotNull($context->request->ip());
    }

    public function testJsonable(): void
    {
        $this->setupUserWithRole('Entrant', true);
        $consumer = Consumer::get();
        SeasonFilter::set(SeasonFilterService::FILTER_ALL);
        $context = Context::snapshot();

        $this->assertEquals([
            'account' => [
                'id' => $this->account->id,
                'name' => Output::html($this->account->slug.': '.$this->account->name),
            ],
            'user' => [
                'id' => $consumer->id(),
                'consumer' => $consumer->type().': '.$consumer->user()->preferredContact(),
            ],
            'showAllSeasons' => true,
            'seasonId' => $this->season->id,
            'jediId' => null,
            'jediEmail' => null,
            'request' => $context->request->toArray(),
            'showAllForms' => false,
            'formId' => FormSelector::getIdRaw(),
            'sessionId' => session()->id(),
        ], json_decode($context->toJson(), true));
    }

    public function testKnowsAccountRegion(): void
    {
        $context = Context::snapshot();
        $this->assertEquals($this->account->region, $context->account->region);
    }

    public function testKnowsJediId(): void
    {
        $user = $this->muffin(User::class, ['email' => '<EMAIL>']);

        $this->emulateJedi($user->globalId, 1, '<EMAIL>');

        $context = Context::snapshot();
        $this->assertEquals(1, $context->jediId);
    }

    public function testRequestValues(): void
    {
        $context = Context::snapshot();

        $this->assertNotEmpty($context->request->id());
        $this->assertEquals('GET', $context->request->method());
        $this->assertEquals(config('app.url'), $context->request->url());
        $this->assertEquals(request()->ip(), $context->request->ip());
    }

    public function testFormIdReturnsDefaultFormIdOnBoot()
    {
        $context = Context::snapshot();
        $this->assertNotNull($context->formId);
        $this->assertEquals(FormSelector::getId(), $context->formId);
    }

    public function testHasProperFormIdWhenUnserializedWithFormId()
    {
        $context = Context::snapshot();

        $unserialized = unserialize(serialize($context));
        $this->assertEquals($context->formId, $unserialized->formId);
    }

    public function testCanCreateSnapshotWithCorrectValues(): void
    {
        // Mock the JediEmulator::jedi method to return a mock Jedi
        JediEmulator::shouldReceive('jedi')
            ->andReturn(new Jedi(1, '<EMAIL>'));

        $context = Context::snapshot();

        $this->assertInstanceOf(Context::class, $context);
        $this->assertEquals(current_account_id(), $context->account->id);
        $this->assertEquals(Consumer::get(), $context->consumer);
        $this->assertEquals(SeasonFilter::viewingAll(), $context->showAllSeasons);
        $this->assertEquals(SeasonFilter::getId(), $context->seasonId);
        $this->assertEquals(FormSelector::viewingAll(), $context->showAllForms);
        $this->assertEquals(1, $context->jediId);
        $this->assertEquals('<EMAIL>', $context->jediEmail);
        $this->assertEquals(FormSelector::formSessionUuid(), $context->formSessionUuid);
        $this->assertEquals(FormSelector::getId(), $context->formId);
        $this->assertEquals(session()->id(), $context->sessionId);
    }

    public function testHasSessionId(): void
    {
        $context = Context::snapshot();
        $this->assertNotNull($context->sessionId);
        $this->assertEquals(session()->id(), $context->sessionId);
    }
}
