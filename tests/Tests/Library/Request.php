<?php

namespace Tests\Library;

use Tests\UnitTestCase;
use Validator;

abstract class Request extends UnitTestCase
{
    abstract protected function getRequestClass(): string;

    protected function assertFails(array $values, ?array $failures = null)
    {
        $failures = $failures ?? array_keys($values);
        $errors = $this->validate($values)->errors();
        foreach ($failures as $key) {
            $this->assertTrue($errors->has($key));
        }
    }

    protected function assertPasses(array $values, ?array $passes = null)
    {
        $passes = $passes ?? array_keys($values);
        $errors = $this->validate($values)->errors();
        foreach ($passes as $key) {
            $this->assertFalse($errors->has($key));
        }
    }

    private function validate(array $values): \Illuminate\Validation\Validator
    {
        $className = $this->getRequestClass();
        $request = new $className($values);

        return Validator::make($request->all(), $request->rules());
    }

    protected function assertAuthorized(array $values)
    {
        $this->assertTrue($this->authorize($values));
    }

    protected function assertUnAuthorized(array $values)
    {
        $this->assertFalse($this->authorize($values));
    }

    private function authorize(array $values)
    {
        $className = $this->getRequestClass();
        $request = new $className($values);

        return $request->authorize();
    }
}
