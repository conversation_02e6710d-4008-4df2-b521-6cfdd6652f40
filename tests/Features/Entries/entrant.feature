Feature: Entrant
  In order to be an Entrant
  I need to be able to manage and view entries

  Background:
    Given I am currently logged in as an "Entrant"
    And there is an active season
    And A form exists
    And I have confirmed my account
    And there is an active entry round
    And there is a Details tab setup

  Scenario: View existing entries
    And I have an entry
    Then I should see a list of entries
    And I should see the screen reader text for the entry

  Scenario: View existing entries with pricing tags
      And I have an entry
      And price tags apply
      Then I should see a list of entries

  # ITS E.4.g
  Scenario: Start entry
    Then I should be able to start an entry
    And I should be able to save my entry

  Scenario: Entry submission with just one tab
    When I start and submit an entry
    And I should be able to see entries list
    Then I should see the entry

  # ITS E.4.d
  Sc<PERSON>rio: View specific entry
    And I have an entry
    And there are fields visible to entrants
    And there are fields hidden from entrants
    Then I should be able to view my entry
    And I should be able to see fields visible to entrants
    And I should not be able to see fields hidden from entrants

  # ITS E.4.e
  Scenario: Preview specific entry
    And I have an entry
    Then I should be able to preview my entry

  Scenario: Cannot see Entry ID column when not enabled
    And I have an entry
    And Entry IDs are not displayed to Entrants
    Then I should see a list of entries
    And I should not be able to see the Entry ID column

  Scenario: Can see Entry ID column when enabled
    And I have an entry
    And Entry IDs are displayed to Entrants
    Then I should see a list of entries
    And I should be able to see the Entry ID column

  Scenario: Undelete entry
    And there is at least one deleted entry with 'DNBRvMoy' slug
    Then I should not be able to undelete invalid entries
    And I should be able to see entries list
    And I should not see trashed entry with 'DNBRvMoy' slug
    And I should be able to undelete my trashed entry
    And I should be able to see entries list
    And I should see undeleted entry

  Scenario: Season selector
    Given I have an entry
    And there is a draft season
    And there is an active season
    Then I should see a list of entries
    And I should be able to see the active season in season selector
    And I should not be able to see the draft season in season selector

  Scenario: Ineligible with one eligibility tab
    Given there are eligibility content blocks and notifications
    And there is an eligibility tab
    Then I should be able to start an entry
    And I should be able to save my entry
    Then I should be able to submit for eligibility with 'one'
    And I should be redirected to the ineligible complete page
    And my entry should be ineligible

  Scenario: Eligible with one eligibility tab
    Given there are eligibility content blocks and notifications
    And there is an eligibility tab
    Then I should be able to start an entry
    And I should be able to save my entry
    Then I should be able to submit for eligibility with 'two'
    And I should be redirected to the eligible complete page
    And my entry should be eligible
    Then I should be able to view my entry
    And Eligibility should be true

  Scenario: Eligible with multiple eligibility tabs last tab
    Given there are eligibility content blocks and notifications
    And there is an eligibility tab
    And there is an eligibility tab
    Then I should be able to start an entry
    And I should be able to save my entry
    Then I should be able to submit for eligibility with two tabs 'two' 'two'
    And I should be redirected to the eligible complete page
    And my entry should be eligible
    And my entry should be submitted

  Scenario: Eligible with multiple eligibility tabs with more tabs after
    Given there are eligibility content blocks and notifications
    And there is an eligibility tab
    And there is an eligibility tab
    And 1 minutes have passed
    And there is tab after eligibility
    Then I should be able to start an entry
    And I should be able to save my entry
    Then I should be able to submit for eligibility with two tabs 'two' 'two'
    And I should get success content block
    And my entry should be eligible
    And my entry should not be submitted
    Then I should be able to submit for eligibility with two tabs 'two' 'two'
    And my entry should be submitted
    And my entry should be eligible with no new event dispatched

  Scenario: Eligible with multiple eligibility tabs and then ineligible on submit
    Given there are eligibility content blocks and notifications
    And there is an eligibility tab
    And there is an eligibility tab
    And 1 minutes have passed
    And there is tab after eligibility
    Then I should be able to start an entry
    And I should be able to save my entry
    Then I should be able to submit for eligibility with two tabs 'two' 'two'
    And I should get success content block
    And my entry should be eligible
    And my entry should not be submitted
    Then I should be able to submit for eligibility with two tabs 'one' 'one'
    And my entry should be ineligible

  Scenario: Eligible with multiple eligibility tabs and more eligibility tabs added after entry was eligible
    Given there are eligibility content blocks and notifications
    And there is an eligibility tab
    And there is an eligibility tab
    And 1 minutes have passed
    And there is tab after eligibility
    Then I should be able to start an entry
    And I should be able to save my entry
    Then I should be able to submit for eligibility with two tabs 'two' 'two'
    And I should get success content block
    And my entry should be eligible
    And my entry should not be submitted
    And 1 minutes have passed
    And there is an eligibility tab
    And 1 minutes have passed
    Then I should be able to submit for eligibility with three tabs 'two' 'two' 'two'
    Then I should get success content block
    And my entry should be eligible
    And my entry should not be submitted
    Then I should be able to submit for eligibility with three tabs 'two' 'two' 'two'
    And my entry should be eligible with no new event dispatched
    And my entry should be submitted

  Scenario: View signed contract links
    Given I have an entry
    And the contracts feature is enabled
    And I have a signed contract
    Then I should see a list of entries
    And I should be able to see the signed contract

  Scenario: Pay to entry with invoice
    Given pay to start entry is enabled
    And I have configured prices
    Then I should be able to start an entry
    And I should be able to save my entry
    And I should be redirected to cart
    And I should be able to view my cart
    And I paid an entry with invoice
    Then I should be redirected to edit entry with awaiting payment message
    And order has been paid
    And has submission completed content block
    And I update a paid entry
    Then I should be redirected to complete entry
    And I should see payment received message

  Scenario: Entry with deadline update
    Given I have an entry
    And it has deadline
    And the entry round is closed
    And I should be able to submit it with any chapter of the selected form

  Scenario: Entry invited for chapter and category
    Given there is an active entry round
    And I have an entry invited with chapter and category
    Then I should not be able to change the chapter
    And I should not be able to change the category
    And I should be able to submit to that chapter and category

  # Integrity test E.2
  Scenario: View feedback without judges name when feedback round is configured with anonymise judges
    Given I have an entry
    And There is an active feedback round
    And The round is configured with anonymise judges
    And The feedback round has comments enabled for my entry category
    And There is a vip_judging ScoreSet with comments on feedback view enabled
    And A judge has an assigment for my entry
    And A judge made a comment on the score set
    And There is a Scoring Criterion
    And A judge made a comment on the criterion
    Then I Should be able to view feedback without judges name

  # ITS E.3.a
  Scenario: Cannot see someone's else entry in entries entrant's view
    Given I have an entry
    And another entrant with an entry exists
    And I should be able to see entries list
    Then I should not be able to see other entrants entries in entries entrant's view

  # ITS E.3.b
  Scenario: Cannot access to edit someone's else entry
    Given I have an entry
    And another entrant with an entry exists
    Then I should not be able to view someone's else entry
    And I should be able to view my entry

  # ITS E.3.c
  Scenario: Cannot preview someone's else entry
    Given I have an entry
    And another entrant with an entry exists
    Then I should not be able to preview someone's else entry
    And I should be able to preview my entry

  # ITS E.3.d
  Scenario: Cannot PDF someone's else entry
    Given I have an entry
    And another entrant with an entry exists
    And form setting 'allowApplicationPdfDownload' is true
    Then I should not be able to PDF someone's else entry
    And I should not be able to bulk download someone's else entry

  # ITS E.3.e
  Scenario: Cannot copy someone's else entry
    Given I have an entry
    And another entrant with an entry exists
    And form setting 'enableCopy' is true
    Then I should not be able to copy someone's else entry

  Scenario: Update specific entry's field options
    Given I am currently logged in as an ProgramManager
    And I have an entry
    And there is a field with options visible to entrants
    Then I should be able to view my entry

  Scenario: Entry with formula field
    Given I have an entry
    And there are fields visible to entrants
    And there is a formula field
    Then I should be able to see the formula field

  Scenario: Should be able to submit extended deadline round
    Given I have an entry
    And The round deadline is expired
    And my entry should not be submitted
    Then I should be able to view my entry
    And I should not see the submit button
    Then The entry have an extended deadline
    Then I should be able to view my entry
    And I should see the submit button

  Scenario: Cannot require resubmission without permission
    Given another entrant with an entry exists
    Then I should not be able to require resubmission without required permission
    Given I am currently logged in as an ProgramManager
    Then I should be able to require resubmission with required permission

  Scenario: Cannot revert entry in progress without permission
    Given another entrant with an entry exists
    Then I should not be able to revert entry in progress without required permission
    Given I am currently logged in as an ProgramManager
    Then I should be able to revert entry in progress with required permission

  # ITS E.4.F
  Scenario: PDF my own entry
    Given I have an entry
    And form setting 'allowApplicationPdfDownload' is true
    Then I should be able to PDF my own entry

  # ITS E.4.I
  Scenario Outline: Not being able to access manager routes
    When I access <route>
    Then I should receive status <code>
    Examples:
      | route                     | code |
      | 'users.index'             | 403  |
      | 'users.new'               | 403  |
      | 'users.invite'            | 403  |
      | 'judge.index'             | 302  |
      | 'judging-dashboard.index' | 302  |
      | 'top-pick.index'          | 403  |
      | 'leaderboard.index'       | 403  |
      | 'leaderboard.progress'    | 403  |
      | 'notification.index'      | 403  |
      | 'notification.new'        | 403  |
      | 'panel.index'             | 403  |
      | 'panel.judges'            | 403  |
      | 'panel.new'               | 403  |
      | 'payment.general'         | 403  |
      | 'price'                   | 403  |
      | 'price.new'               | 403  |
      | 'round.index'             | 403  |
      | 'score-set.index'         | 403  |
      | 'assignment.index'        | 403  |
      | 'season.index'            | 403  |
      | 'season.new'              | 403  |
      | 'forms.index'             | 302  |
      | 'forms.new'               | 403  |

  # ITS E.4.j
  Scenario: Cannot view or edit season
    Then I should not be able to see the season
    And I should not be able to modify the season

  # ITS E.4.j
  Scenario: Cannot view or edit form
    Then I should not be able to see the form
    And I should not be able to modify the form
    And I should not be able to see the form settings
    And I should not be able to edit form settings

  Scenario: Delete attachment from entry throws a validation error for incorrect attachment id
    Given I have an entry
    And it has an attachment
    And Permission to delete entries is set to deny
    Then I should see a validation error when I try to delete an attachment with incorrect id

  Scenario: Can not see packing slips when feature is disabled
    Given I start and submit an entry
    And the entries category has packing slips enabled
    And the packing_slips feature is disabled
    And I should be able to see entries list
    Then I should not see the packing slip button "entries.packing-slip.print"

  Scenario: Can see packing slips when feature is enabled
    Given  I start and submit an entry
    And the entries category has packing slips enabled
    And the packing_slips feature is enabled
    And I should be able to see entries list
    Then I should see the packing slip button "entries.packing-slip.print"

  Scenario: Pay to submit with invoice
    Given pay to submit entry is enabled
    And has submission completed content block
    And I have configured prices
    Then I should be able to start an entry
    And I should be able to save my entry
    Then I submit my entry
    And I should be redirected to cart
    And I should be able to view my cart
    Then I paid an entry with invoice
    And I should be redirected to entry complete
    And I should see ready for payment message

  Scenario: Pay to submit with invoice after entry went back to in_progress
    Given pay to submit entry is enabled
    And has submission completed content block
    And I have configured prices
    Then I should be able to start an entry
    And I should be able to save my entry
    Then I submit my entry
    And I should be redirected to cart
    And I should be able to view my cart
    Then I paid an entry with invoice
    And I should be redirected to entry complete
    And I should see ready for payment message
    Then The entry went back to in progress
    And I submit my entry
    And I should be redirected to complete entry
    And I should see ready for payment message

  Scenario: Entrant can see and use cog icon
    And I have an entry
    Then I should see a list of entries
    And I should see the cog icon
    Then I should be able to add the grant status column
    And I should see the list of available columns

  Scenario: Entry with referees
    Given I have an entry
    And a referee tab exists
    Then I should be able to add a referee

  Scenario: Entrant can update an individual field
    And I have an entry
    And the entry has some field values
    And the entry has an attachment
    And the attachment has some field values
    And there are contributor fields
    Then I should be able to update a specific field value as an entrant
    And I should see the updated field value
    Then I should be able to update a specific attachment field value as an entrant
    And I should see the updated attachment field value
    And I should be able to create a new contributor as an entrant
    Then I should be able to update a specific contributor field value as an entrant
    And I should be able to remove the contributor as an entrant

  Scenario: Entrant can create a referee field
    Given I have an entry
    And there is a referee tab
    Then I should be able to create a new referee as an entrant

  Scenario: Entrant can update a referee field
    Given I have an entry
    And there are referee fields
    Then I should be able to update a specific referee as an entrant
    And I should be able to update a specific referee field value as an entrant

  Scenario: Entrant can remove a referee field
    Given I have an entry
    And there are referee fields
    Then I should be able to remove the referee as an entrant

  Scenario: Entrant can update individual entry attributes
    And I have an entry
    Then I should be able to update the title as an entrant
    And I should see the updated title
    Then I should be able to update the category as an entrant
    And I should see the updated category
    Then I should be able to update the chapter as an entrant
    And I should see the updated chapter

  Scenario: Delete Attachment
    Given I have an entry
    And it has an attachment
    And Permission to delete entries is set to deny
    Then I should be able to delete the attachment

  Scenario: Entrant should not be able to delete attachments from other entrants
    Given I have an entry
    And it has an attachment
    And Permission to delete entries is set to deny
    And another entrant with an entry exists
    And the other entrant has an attachment
    Then I should not be able to delete the other entrant attachment

  Scenario: Eligibility page should update submittable state data to firebase
    Given I have an entry
    Given There is ineligibility content block
    Then  it should update the submittable state to firebase in eligibility page

  Scenario: Entrant should not be able to start entry of a form if its role is not assigned to the form
    Given A form exists with selected roles
    Then I should not be able to start an entry
    And I should not be able to save my entry

  Scenario: View PDF on a collaboration entry
    Given A collaborative form exists
    And another entrant with an entry exists
    And I have been assigned as collaborator on the new entrant entry
    Then I should be able to view the PDF on the new entrant entry
