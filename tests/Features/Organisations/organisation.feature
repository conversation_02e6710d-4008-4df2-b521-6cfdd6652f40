Feature: Organisation
  I need to be able to manage and view organisations

  Background:
    Given I am currently logged in as an "Program Manager"
    And the organisations feature is enabled

  Scenario: view a list of organisation
    Given a number of organisations exist
    When I visit the organisation list view page
    Then I should see a list of organisations

  Scenario: Create an organisation
    Given I visit the organisation create page
    When I tried to create an organisation
    Then I should be returned to the list view page
    And I should see the updated organisation name

#  Pending field values implementation
#  Scenario: Use organisation fields
#    Given a organisation exist
#    And an organisation resource field exist
#    And I update the organisation resource field to the existing organisation
#    Then The organisation should have the correct resource field value
#    And I visit the organisation edit page
#    Then I should see the updated organisation resource field

  Scenario: view thumbnails on organisation list
    Given a number of organisations exist
    And I added a logo to the first organisation
    When I visit the organisation list view page
    Then I should see the organisation thumbnail

  Scenario: Update logo as administrator
    Given I am currently logged in as an "Entrant"
    And a organisation exist
    And I am the organisation administrator
    Then I should be able to update the organisation logo
