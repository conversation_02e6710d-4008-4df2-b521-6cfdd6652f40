<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Organisations\Organisations\Boundary\Projections\Organisation;
use PHPUnit\Framework\Assert;

trait OrganisationFeatures
{
    public $organisations;
    public Organisation $organisation;

    /**
     * @Given a number of organisations exist
     */
    public function aNumberOfOrganisationsExist()
    {
        $this->organisations = Organisation::factory(5)->create();
    }

    /**
     * @When I visit the organisation list view page
     */
    public function iVisitTheOrganisationListViewPage()
    {
        $this->route('GET', 'organisation.index');
    }

    /**
     * @Then I should see a list of organisations
     */
    public function iShouldSeeAListOfOrganisations()
    {
        $this->assertResponseOk();
        $this->organisations->each(fn(Organisation $organisation) => $this->assertResponseContains($organisation->name));
    }

    /**
     * @Given a organisation exist
     */
    public function aOrganisationExist()
    {
        $this->organisation = Organisation::factory()->create();
    }

    /**
     * @When I visit the organisation edit page
     */
    public function iVisitTheOrganisationEditPage()
    {
        $this->route('GET', 'organisation.edit', ['organisation' => $this->organisation->id]);
    }

    /**
     * @Then I should see the organisation details
     */
    public function iShouldSeeTheOrganisationDetails()
    {
        $this->assertResponseContains($this->organisation->name);
    }

    /**
     * @Then I tried to edit the organisation details
     */
    public function iTriedToEditTheOrganisationDetails()
    {
        $this->newOrganisationName = 'New Name';
        $this->route('PUT', 'organisation.update', ['organisation' => $this->organisation->id], [
            'name' => $this->newOrganisationName,
        ]);
    }

    /**
     * @Then I should be returned to the list view page
     */
    public function iShouldBeReturnedToTheListViewPage()
    {
        $this->assertRedirectedToRoute('organisation.index');
        $this->route('GET', 'organisation.index');
    }

    /**
     * @Then I should see the updated organisation name
     */
    public function iShouldSeeTheUpdatedOrganisationName()
    {
        $this->assertResponseContains($this->newOrganisationName);
    }

    /**
     * @Given I visit the organisation create page
     */
    public function iVisitTheOrganisationCreatePage()
    {
        $this->route('GET', 'organisation.new');
        $this->assertResponseOk();
    }

    /**
     * @Given I tried to create an organisation
     */
    public function iTriedToCreateANewOrganisation()
    {
        $this->newOrganisationName = 'New Organisation';
        $organisation = ['name' => $this->newOrganisationName, 'administrator' => ''];
        $this->route('POST', 'organisation.create', [], $organisation);
    }

    /**
     * @Given an organisation resource field exist
     */
    public function anOrganisationResourceFieldExist()
    {
        $this->organisationResourceField = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_ORGANISATIONS,
            'type' => 'email',
        ]);
    }

    /**
     * @Given I update the organisation resource field to the existing organisation
     */
    public function iUpdateTheOrganisationResourceFieldToTheExistingOrganisation()
    {
        $this->email = '<EMAIL>';
        $this->route('PUT', 'organisation.update', ['organisation' => $this->organisation->id], [
            'name' => $this->organisation->name,
            'values' => [
                (string) $this->organisationResourceField->slug => $this->email,
            ],
        ]);
        $this->assertRedirectedToRoute('organisation.index');
    }

    /**
     * @Then The organisation should have the correct resource field value
     */
    public function theOrganisationShouldHaveTheCorrectResourceFieldValue()
    {
        Assert::assertEquals([(string) $this->organisationResourceField->slug => $this->email], $this->organisation->fresh()->valuesData()->values);
    }

    /**
     * @Then I should see the updated organisation resource field
     */
    public function iShouldSeeTheUpdatedOrganisationResourceField()
    {
        $this->assertResponseContains($this->email);
    }

    /**
     * @Given I added a logo to the first organisation
     */
    public function iAddedALogoToTheFirstOrganisation()
    {
        $logo = $this->muffin(File::class, ['resource' => File::RESOURCE_ORGANISATION_LOGO]);
        $organisation = $this->organisations->first();
        $this->route('PUT', 'organisation.logo.update', ['organisation' => $organisation->id], [
            'file' => $logo->id,
        ]);
        $this->assertResponseStatus(204);
    }

    /**
     * @Given I should see the organisation thumbnail
     */
    public function iShouldSeeTheOrganisationThumnail()
    {
        $this->assertResponseContains($this->organisations->first()->refresh()->logo->thumbnail());
    }

    /**
     * @Given I am the organisation administrator
     */
    public function iAmTheOrganisationAdministrator()
    {
        $this->organisation->administrator_id = \Consumer::user()->id;
        $this->organisation->save();
    }

    /**
     * @Then I should be able to update the organisation logo
     */
    public function iShouldBeAbleToUpdateTheOrganisationLogo()
    {
        $logo = $this->muffin(File::class, ['resource' => File::RESOURCE_ORGANISATION_LOGO]);
        $this->route('PUT', 'organisation.logo.update', ['organisation' => $this->organisation->id], [
            'file' => $logo->id,
        ]);

        $this->assertResponseStatus(204);
    }
}
