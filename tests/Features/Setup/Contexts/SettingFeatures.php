<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Settings\Services\Settings;

trait SettingFeatures
{
    /**
     * @Then I should be able to see the entry settings page
     */
    public function iShouldBeAbleToSeeTheEntrySettingsPage()
    {
        $this->route('GET', 'setting.entries');
        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to update entry settings
     */
    public function iShouldBeAbleToUpdateTheEntrySettings()
    {
        $data = [
            'setting' => [
            ],
        ];

        $response = $this->route('PUT', 'setting.entries.update', [], $data);
        $response->assertSessionHasNoErrors();
    }

    /**
     * @Then I should not be able to update entry settings with invalid data
     */
    public function iShouldNotBeAbleToUpdateTheEntrySettingsWithInvalidData()
    {
        $data = [
            'setting' => [
            ],
        ];

        $response = $this->route('PUT', 'setting.entries.update', [], $data);
        $this->assertSessionHasErrors('setting.max-entries');

        $this->get($response->headers->get('Location'));
        $this->assertResponseNotContains(trans('miscellaneous.alerts.validator.message'));
        $this->assertResponseNotContains(trans('miscellaneous.alerts.validator.tabs'));
    }

    /**
     * @Given :setting setting is enabled
     */
    public function settingIsEnabled($setting)
    {
        app(Settings::class)->saveSettings([$setting => true], current_account());
    }

    /**
     * @Given form setting :setting is :status
     */
    public function formSettingIsStatus(string $setting, bool $status)
    {
        $form = FormSelector::get();
        $form->settings = FormSettings::create(array_merge($form->settings->toArray(), [$setting => $status]));
        $form->save();
    }

    /**
     * @Then /^I should be able to access the organisations settings page$/
     */
    public function iShouldBeAbleToAccessTheOrganisationsSettingsPage()
    {
        $this->route('GET', 'setting.organisations');
        $this->assertResponseOk();
    }

    /**
     * @Given /^I should be able to update organisations settings$/
     */
    public function iShouldBeAbleToUpdateOrganisationsSettings()
    {
        $data = [
            'enabled' => true,
            'joinOnRegisteredEmail' => false,
            'userSelect' => true,
            'userAdd' => true,
            'administratorInvite' => false,
            'managerAdd' => false,
        ];

        $response = $this->route('PUT', 'setting.organisations.update', [], $data);
        $response->assertJson(['success' => true]);
    }
}
