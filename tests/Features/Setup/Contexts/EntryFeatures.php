<?php

namespace Features\Setup\Contexts;

use AwardForce\Library\Database\Firebase\Database;
use AwardForce\Library\Enums\ScopeOption;
use AwardForce\Modules\Accounts\Models\Membership;
use AwardForce\Modules\Assignments\Models\Assignment;
use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Chapters\Models\Chapter;
use AwardForce\Modules\Comments\Services\CommentManager;
use AwardForce\Modules\Comments\Services\UserTags;
use AwardForce\Modules\Comments\Tags\CriterionTag;
use AwardForce\Modules\Comments\Tags\EntryTag;
use AwardForce\Modules\Comments\Tags\ScoreSetTag;
use AwardForce\Modules\Comments\Tags\StringTag;
use AwardForce\Modules\Content\Blocks\Models\ContentBlock;
use AwardForce\Modules\Ecommerce\Orders\Data\Order;
use AwardForce\Modules\Ecommerce\Orders\Data\OrderItem;
use AwardForce\Modules\Ecommerce\Orders\Services\NextInvoiceNumber;
use AwardForce\Modules\Entries\Commands\BulkDownloadEntriesCommand;
use AwardForce\Modules\Entries\Commands\GenerateEntrantEntryPDFCommand;
use AwardForce\Modules\Entries\Commands\GenerateManagerEntryPDFCommand;
use AwardForce\Modules\Entries\Contracts\EntryRepository;
use AwardForce\Modules\Entries\Events\EntryWasEligible;
use AwardForce\Modules\Entries\Events\EntryWasIneligible;
use AwardForce\Modules\Entries\Models\Attachment;
use AwardForce\Modules\Entries\Models\Contract;
use AwardForce\Modules\Entries\Models\Entry;
use AwardForce\Modules\Entries\Services\Collaboration;
use AwardForce\Modules\Entries\Services\Duplicates\Comparers\SameCategorySimilarTitle;
use AwardForce\Modules\Entries\Services\Duplicates\DuplicateFinder;
use AwardForce\Modules\Exports\Commands\ExportCommand;
use AwardForce\Modules\Exports\Services\LocksExportDownload;
use AwardForce\Modules\Forms\Collaboration\Models\Collaborator;
use AwardForce\Modules\Forms\Fields\Database\Entities\Field;
use AwardForce\Modules\Forms\Fields\Services\ValuesService;
use AwardForce\Modules\Forms\Forms\Database\Entities\Form;
use AwardForce\Modules\Forms\Forms\Database\Entities\FormSettings;
use AwardForce\Modules\Forms\Forms\Database\Entities\Submittable;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Forms\Tabs\Database\Entities\Tab;
use AwardForce\Modules\GrantReports\Models\GrantReportRepository;
use AwardForce\Modules\Identity\Roles\Models\Permission;
use AwardForce\Modules\Identity\Users\Models\User;
use AwardForce\Modules\Notifications\Data\Notification;
use AwardForce\Modules\Payments\Models\Price;
use AwardForce\Modules\Payments\Models\PriceAmount;
use AwardForce\Modules\Payments\Models\Tax;
use AwardForce\Modules\Referees\Models\Referee;
use AwardForce\Modules\ReviewFlow\Data\ReviewStage;
use AwardForce\Modules\ReviewFlow\Data\ReviewTaskRepository;
use AwardForce\Modules\Rounds\Models\Round;
use AwardForce\Modules\Rounds\Models\RoundFeedbackOption;
use AwardForce\Modules\ScoreSets\Models\ScoreSet;
use AwardForce\Modules\ScoringCriteria\Models\ScoringCriterion;
use AwardForce\Modules\Search\Search;
use AwardForce\Modules\Seasons\Facades\SeasonFilter;
use AwardForce\Modules\Seasons\Models\Season;
use AwardForce\Modules\Settings\Services\Settings;
use AwardForce\Modules\Tags\Models\Tag;
use AwardForce\Modules\Tags\Services\TagManager;
use Carbon\Carbon;
use Consumer;
use Eloquence\Behaviours\Slug;
use Facades\Platform\Strings\Output;
use Illuminate\Contracts\Filesystem\Factory;
use Mockery as m;
use PHPUnit\Framework\Assert;
use PHPUnit\Framework\Assert as PHPUnit;
use Platform\Bus\Commands\DeleteModelsCommand;
use Platform\Bus\Commands\UndeleteModelsCommand;
use Tests\Support\Storage;

/**
 * Contains the entry-specific feature context methods.
 */
trait EntryFeatures
{
    private array $eligibilityTabs = [];
    private array $eligibilityFields = [];

    /**
     * ITS E.4.g
     *
     * @Then I should be able to start an entry
     */
    public function iShouldBeAbleToStartAnEntry()
    {
        $this->route('GET', 'entry.entrant.start');
        $this->assertRedirectedToRoute('entry-form.entrant.start');

        $this->route('GET', 'entry-form.entrant.start');

        $this->assertResponseOk();
    }

    /**
     * @Given I want to start an entry
     */
    public function iWantToStartAnEntry()
    {
        $this->route('GET', 'entry-form.manager.start');

        $this->assertResponseOk();
    }

    /**
     * @Given a number of entries have been started
     */
    public function numberOfEntriesStarted(): void
    {
        $numEntries = mt_rand(1, 10);

        for ($i = 0; $i < $numEntries; $i++) {
            $this->muffin(User::class);
            $this->entries[] = $this->muffin(Entry::class, ['user_id' => $this->user->id]);
        }

        $this->entry = $this->entries[0];
    }

    /**
     * @Then I should be able to schedule grant reports for the entries
     */
    public function iShouldBeAbleToScheduleGrantReportsForTheEntries()
    {
        $this->route('POST', 'grant-report.create', [
            'reportFormId' => $this->reportForm->id,
            'selected' => $entryIds = collect($this->entries)->pluck('id')->toArray(),
            'dueDate' => (string) Carbon::now()->addWeek(),
        ]);

        $this->assertRedirectedTo('/');
        $this->assertNoErrors();

        $reports = app(GrantReportRepository::class)->getAllByEntries($entryIds);

        $this->assertTrue($reports->count() === count($entryIds));
    }

    /**
     * @Given a number of entries without a title have been started
     */
    public function numberOfentriesWithoutATitleStarted()
    {
        $numEntries = mt_rand(1, 10);

        for ($i = 0; $i < $numEntries; $i++) {
            $this->muffin(User::class);
            $this->entries[] = $this->muffin(Entry::class, ['user_id' => $this->user->id, 'title' => '']);
        }
    }

    /**
     * @Given a form slug has been set
     */
    public function formSlugHasBeenSet(): void
    {
        $this->formSlug = (string) ($this->entries[0]?->form?->slug ?? $this->muffin(Form::class)->slug);
    }

    /**
     * @Then I should be able to view a specific entry
     */
    public function iShouldBeAbleToViewASpecificEntry()
    {
        $this->route('GET', 'entry.manager.view', [$this->entries[0]->slug]);

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to see auto-score panel in empty state
     */
    public function iShouldBeAbleToSeeAutoScorePanelInEmptyState(): void
    {
        $this->route('GET', 'entry.manager.view', [$this->entries[0]->slug])
            ->assertSuccessful()
            ->assertSee(trans('fields.no_auto_score_fields'))
            ->assertSee(trans('fields.configure_link'))
            ->assertSee(trans('fields.auto_score'));
    }

    /**
     * @Given there are scorable fields
     */
    public function thereAreScorableFields(): void
    {
        $field = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'type' => 'checkbox',
            'auto_scoring' => true,
            'options' => ['One' => 1, 'Two' => 2, 'Three' => 3, 'Four' => 4],
        ]);

        app(ValuesService::class)->setValuesForObject([(string) $field->slug => str_random()], $this->entries[0]);

        $this->entries[0]->scores = [(string) $field->slug => 2];
    }

    /**
     * @Then I should be able to see auto-score panel with scores
     */
    public function iShouldBeAbleToSeeAutoScorePanelWithScores(): void
    {
        $this->route('GET', 'entry.manager.view', [$this->entries[0]->slug])
            ->assertSuccessful()
            ->assertSee(trans('fields.auto_score'))
            ->assertSee(trans('interface-text.form.field.label'))
            ->assertSee(trans('entries.table.columns.score'))
            ->assertSee(localised_number_format($this->entries[0]->fields->first()->score))
            ->assertSee(localised_number_format($this->entries[0]->fields->first()->options->maxScore()));
    }

    /**
     * @Then I should be able to preview a specific entry
     */
    public function iShouldBeAbleToPreviewASpecificEntry()
    {
        $this->route('GET', 'entry.manager.view', [$this->entries[0]->slug, 'vtab' => 'entry.manager.tabs.entry']);

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to comment on a specific entry
     */
    public function iShouldBeAbleToCommentOnASpecificEntry()
    {
        $comment = 'A new comment.';

        $postData = [
            'token' => app(UserTags::class)->getToken(new EntryTag($this->entries[0]), new StringTag('manager')),
            'comment' => $comment,
            'internal' => false,
        ];

        $this->route('POST', 'comment.create', [], $this->withInput($postData));

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to update my own comment
     */
    public function iShouldBeAbleToUpdateMyOwnComment()
    {
        $tags = [new EntryTag($this->entries[0]), new StringTag('manager')];

        $comment = app(CommentManager::class)->create($this->user->id, 'Original comment', false, ...$tags);

        $putData = [
            'comment' => 'Updated comment.',
            'token' => app(UserTags::class)->getToken(...$tags),
            'internal' => false,
        ];

        $this->route('PUT', 'comment.update', $comment->slug, $this->withInput($putData));

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to delete my own comment
     */
    public function iShouldBeAbleToDeleteMyOwnComment()
    {
        $tags = [new EntryTag($this->entries[0]), new StringTag('manager')];

        $comment = app(CommentManager::class)->create($this->user->id, 'Original comment', false, ...$tags);

        $deleteData = [
            'token' => app(UserTags::class)->getToken(...$tags),
        ];

        $this->route('DELETE', 'comment.delete', $comment->slug, $this->withInput($deleteData));

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to moderate a specific entry
     */
    public function iShouldBeAbleToModerateASpecificEntry()
    {
        $postData = [
            'moderationStatus' => 'approved',
        ];

        $this->route('POST', 'entry.manager.moderate.entry', ['entry' => $this->entries[0]->slug], $this->withInput($postData));

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to tag a specific entry
     */
    public function iShouldBeAbleToTagASpecificEntry()
    {
        $this->muffin(Tag::class, ['tag' => 'tag A']);
        $this->muffin(Tag::class, ['tag' => 'tag B']);
        $this->muffin(Tag::class, ['tag' => 'tag C']);

        $postData = [
            'tags' => ['tag A', 'tag B', 'tag C'],
        ];

        $this->route('POST', 'entry.manager.tag.entry', ['entry' => $this->entries[0]->slug], $this->withInput($postData));

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to save my entry
     */
    public function iShouldBeAbleToSaveMyEntry()
    {
        $category = $this->muffin(Category::class);
        $chapterId = $category->chapters->first()->id;

        $this->round->chapters()->attach($chapterId);

        $title = 'Some entry - '.str_random();

        $postData = [
            'formId' => FormSelector::getId(),
            'title' => $title,
            'categoryId' => $category->id,
            'chapterId' => $chapterId,
            'values' => [],
            'links' => [],
        ];

        $this->route('POST', 'entry-form.entrant.startCreate', [], $postData);

        $this->entry = app(EntryRepository::class)->getByTitle($title)->first();

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to save my entry in a draft season form
     *
     * @throws \JsonException
     */
    public function iShouldBeAbleToSaveMyEntryInADraftSeasonForm(): void
    {
        $draftSeason = $this->muffin(Season::class, ['status' => Season::STATUS_DRAFT]);
        $form = $this->muffin(Form::class, ['seasonId' => $draftSeason->id]);
        $chapter = $this->muffin(Chapter::class, ['seasonId' => $draftSeason->id]);
        $category = $this->muffin(Category::class, ['seasonId' => $draftSeason->id, 'formId' => $form->id]);
        $chapter->categories()->attach($category->id);

        SeasonFilter::set($draftSeason);
        FormSelector::set($form);

        $postData = [
            'formId' => $form->id,
            'title' => $title = 'Some entry - '.str_random(),
            'categoryId' => $category->id,
            'chapterId' => $chapter->id,
            'values' => [],
            'links' => [],
        ];

        $this->route('POST', 'entry-form.entrant.startCreate', [], $postData)
            ->assertOk()
            ->assertSessionHasNoErrors();

        $entry = Entry::where('title', $title)->first();

        Assert::assertNotNull($entry);
        Assert::assertEquals($draftSeason->id, $entry->seasonId);
    }

    /**
     * @Then I should be able to save that entry
     */
    public function iShouldBeAbleToSaveThatEntry()
    {
        $category = $this->muffin(Category::class);
        $users = $this->createUserAccounts(1);

        $title = 'Some entry - '.str_random();

        $postData = [
            'title' => $title,
            'userId' => $users[0]->id,
            'categoryId' => $category->id,
            'chapterId' => $category->chapters->first()->id,
            'seasonId' => $category->season->id,
            'formId' => $category->formId,
            'values' => [],
            'links' => [],
        ];

        $this->route('POST', 'entry-form.manager.startCreate', [], $this->withInput($postData));

        $this->seeJsonContains([
            'title' => $title,
            'categoryId' => $category->id,
            'chapterId' => $category->chapters->first()->id,
            'seasonId' => $category->season->id,
        ]);
    }

    /**
     * @Given I have an entry
     */
    public function iHaveAnEntry()
    {
        $this->entry = $this->muffin(Entry::class, ['user_id' => $this->user->id]);
    }

    /**
     * @Given I have an entry with a different form
     */
    public function iHaveAnEntryWithADifferentForm(): void
    {
        $this->entry = $this->muffin(Entry::class, ['user_id' => $this->user->id, 'form_id' => $this->muffin(Form::class)->id]);
    }

    /**
     * ITS E.4.d
     *
     * @Then I should be able to view my entry
     */
    public function iShouldBeAbleToViewMyEntry()
    {
        $this->route('GET', 'entry-form.entrant.edit', ['entry' => $this->entry->slug]);

        $this->assertResponseOk();
    }

    /**
     * ITS E.4.e
     *
     * @Then I should be able to preview my entry
     */
    public function iShouldBeAbleToPreviewMyEntry()
    {
        $this->route('GET', 'entry.entrant.preview', ['entry' => $this->entry->slug]);

        $this->assertResponseOk();
    }

    /**
     * @Then I should not be able to see fields hidden from entrants
     */
    public function iShouldNotBeAbleToSeeFieldsHiddenFromEntrants()
    {
        $this->route('GET', 'entry-form.entrant.fields', ['tab' => $this->tab->slug, 'entry' => $this->entry->slug]);

        $this->assertResponseNotContains('"slug":"'.(string) $this->hiddenField->slug.'"');
    }

    /**
     * @Then I should be able to see fields visible to entrants
     */
    public function iShouldBeAbleToSeeFieldsVisibleToEntrants()
    {
        $this->route('GET', 'entry-form.entrant.fields', ['tab' => $this->tab->slug, 'entry' => $this->entry->slug]);

        $this->assertResponseContains('"slug":"'.(string) $this->visibleField->slug.'"');
    }

    /**
     * @Then I should see a list of entries
     */
    public function iShouldSeeAListOfEntries()
    {
        $this->setGlobalCommunicationChannels();

        Consumer::isManager()
            ? $this->route('GET', 'entry.manager.index')
            : $this->route('GET', 'entry.entrant.index');

        $this->followRedirects();

        $this->assertResponseOk();
    }

    /**
     * @Then I should be able to search through a list of entries with a proper title
     */
    public function iShouldBeAbleToSearchThroughAListOfEntriesWithAProperTitle(): void
    {
        $this->route('GET', 'assignment.entry-search');

        $this->assertResponseOk();
        foreach ($this->entries as $entry) {
            $this->assertResponseContains('"title":"'.$entry->user->getName().'"');
        }
    }

    /**
     * @Then I should be able to search through a list of entries
     */
    public function iShouldBeAbleToSearchThroughAListOfEntries(): void
    {
        $this->route('GET', 'assignment.entry-search');

        $this->assertResponseOk();
        foreach ($this->entries as $entry) {
            $this->assertResponseContains('"title":'.json_encode(Output::text($entry->title)));
        }
    }

    /**
     * @Then I should be able to search through a list of entries for specific form
     */
    public function iShouldBeAbleToSearchThroughAListOfEntriesForSpecificForm(): void
    {
        $this->route('GET', 'assignment.entry-search', ['formSlug' => $this->formSlug]);

        $this->assertResponseOk();
        foreach ($this->entries as $entry) {
            if ((string) $entry?->form?->slug === $this->formSlug) {
                $this->seeJsonContains(['title' => $entry->title]);
            }
        }
    }

    /**
     * @Then I should not be able to search through a list of entries that are not for the specific form
     */
    public function iShouldNotBeAbleToSearchThroughAListOfEntriesThatAreNotForTheSpecificForm(): void
    {
        $this->route('GET', 'assignment.entry-search', ['formSlug' => $this->formSlug]);

        $this->assertResponseOk();
        foreach ($this->entries as $entry) {
            if ((string) $entry?->form?->slug !== $this->formSlug) {
                $this->dontSeeJson(['title' => $entry->title]);
            }
        }
    }

    /**
     * @Then I should be able to search through a list of entries with a proper title for specified form slug
     */
    public function iShouldBeAbleToSearchThroughAListOfEntriesWithAProperTitleForSpecifiedFormSlug(): void
    {
        $this->route('GET', 'assignment.entry-search', ['formSlug' => $this->formSlug]);

        $this->assertResponseOk();
        foreach ($this->entries as $entry) {
            $this->seeJsonContains(['title' => $entry->user->getName()]);
        }
    }

    /**
     * @Given there is an active entry round
     */
    public function thereIsAnActiveEntryRound()
    {
        $this->round = $this->muffin(Round::class, [
            'enabled' => true,
            'round_type' => Round::ROUND_TYPE_ENTRY,
        ]);
    }

    /**
     * @Given there is a Details tab setup
     */
    public function thereIsADetailsTabSetup()
    {
        $this->tab = $this->muffin(Tab::class, [
            'resource' => Tab::RESOURCE_ENTRIES,
            'type' => Tab::TYPE_DETAILS,
        ]);
    }

    /**
     * @param  Submittable  $submittable
     *
     * @Given there is a field with options visible to entrants
     */
    public function thereIsAFieldWithOptionsVisibleToEntrants()
    {
        $field = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'tab_id' => $this->tab->id,
            'type' => function () {
                return 'radio';
            },
            'entrant_read_access' => function () {
                return 1;
            },
            'entrant_write_access' => function () {
                return 1;
            },
        ]);

        app(ValuesService::class)->setValuesForObject([(string) $field->slug => str_random()], $this->entry);

        $this->visibleField = $field;
    }

    /**
     * @param  Entry  $entry
     * @param  bool  $entrantReadAccess
     * @param  bool  $entrantWriteAccess
     * @return mixed
     *
     * @throws \AwardForce\Modules\Forms\Fields\Exceptions\CannotModifyFieldValue
     */
    private function fieldForSubmittable($submittable, $entrantReadAccess, $entrantWriteAccess)
    {
        $field = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'tab_id' => $this->tab->id,
            'type' => function () {
                return 'text';
            },
            'entrant_read_access' => function () use ($entrantReadAccess) {
                return $entrantReadAccess;
            },
            'entrant_write_access' => function () use ($entrantWriteAccess) {
                return $entrantWriteAccess;
            },
        ]);

        app(ValuesService::class)->setValuesForObject([(string) $field->slug => str_random()], $submittable);

        return $field;
    }

    /**
     * @Given there are fields visible to entrants
     */
    public function thereAreFieldsVisibleToEntrants()
    {
        $this->visibleField = $this->fieldForSubmittable($this->entry, 1, 1);
    }

    /**
     * @Given there are fields hidden from entrants
     */
    public function thereAreFieldsHiddenFromEntrants()
    {
        $this->hiddenField = $this->fieldForSubmittable($this->entry, 0, 0);
    }

    /**
     * @Given there is a formula field
     */
    public function thereIsAFormulaField(): void
    {
        $this->formulaField = $this->muffin(Field::class, [
            'resource' => Field::RESOURCE_FORMS,
            'tab_id' => $this->tab->id,
            'type' => 'formula',
            'configuration' => json_encode(['formula' => "=IF({{$this->visibleField->slug}}='', 'value is empty', 'value is not empty')"]),
        ]);
    }

    /**
     * @Then I should be able to see the formula field
     */
    public function iShouldBeAbleToSeeTheFormulaField(): void
    {
        $this->route('GET', 'entry-form.entrant.fields', ['tab' => $this->tab->slug, 'entry' => $this->entry->slug]);

        $this->assertResponseOk();
        $this->assertResponseContains('"slug":"'.(string) $this->formulaField->slug.'"');
    }

    /**
     * @Given I have confirmed my account
     */
    public function iHaveConfirmedMyAccount()
    {
        $this->user->confirm();
    }

    /**
     * @Given an entry exists
     */
    public function entryExists()
    {
        $this->entry = $this->muffin(Entry::class);
    }

    /**
     * @Given it has an attachment
     */
    public function itHasAnAttachment()
    {
        $tab = $this->muffin(Tab::class, ['type' => Tab::TYPE_ATTACHMENTS]);
        $this->attachment = $this->muffin(Attachment::class, [
            'submittable_id' => $this->entry->id,
            'tab_id' => $tab->id,
        ]);
    }

    /**
     * @Then I should be able to delete a specific entry attachment
     */
    public function iShouldBeAbleToDeleteASpecificEntryAttachment()
    {
        app()->instance(Factory::class, new Storage);
        $this->route(
            'DELETE',
            'entry.manager.delete.attachment',
            ['entry' => $this->entry->slug, 'id' => $this->attachment->file->id]
        );

        $this->assertResponseOk();
    }

    /**
     * @Given a number of completed entries exist
     */
    public function aNumberOfCompletedEntriesExist()
    {
        $this->numberOfEntriesStarted();

        foreach ($this->entries as $entry) {
            $entry->submit();
            $entry->save();
        }
    }

    /**
     * @Given there is an active review stage
     */
    public function thereIsAnActiveReviewStage()
    {
        $this->reviewStage = $this->muffin(ReviewStage::class, ['review_by' => ReviewStage::REVIEW_MANAGER, 'start_on_submit' => true]);
    }

    /**
     * @Given there is an active review stage with review by field
     */
    public function thereIsAnActiveReviewStageWithReviewByField()
    {
        $emailField = $this->muffin(Field::class, ['type' => 'email', 'resource' => Field::RESOURCE_FORMS]);

        $this->reviewStage = $this->muffin(ReviewStage::class, ['review_by' => ReviewStage::REVIEW_FIELD, 'start_on_submit' => true, 'nominee_email_field_id' => $emailField->id]);
    }

    /**
     * @Then I should be able to initiate the review stage for those entries
     */
    public function iShouldBeAbleToInitiateTheReviewStageForThoseEntries()
    {
        $postData = [
            'review-stage' => $this->reviewStage->id,
            'selected' => collect($this->entries)->pluck('id')->toArray(),
        ];

        $this->route('POST', 'entry.manager.initiate-review-stage', [], $this->withInput($postData));

        $this->assertRedirectedToRoute('entry.manager.index');
    }

    /**
     * @Then I should not be able to initiate the review stage for entry with missing details
     */
    public function IShouldNotBeAbleToInitiateTheReviewStageForEntryWithMissingDetails()
    {
        $postData = [
            'review-stage' => $this->reviewStage->id,
            'selected' => [$this->entry->id],
        ];

        $response = $this->route('POST', 'entry.manager.initiate-review-stage', [], $this->withInput($postData));

        $response->assertRedirectedTo(route('entry.manager.index'), ['message' => trans('review-flow.initiate_review_stage.errors.missing_details'), 'type' => 'error']);
    }

    /**
     * @Given Entry IDs are not displayed to Entrants
     */
    public function entryIdsAreNotDisplayedToEntrants()
    {
        $form = FormSelector::get();
        $settings = FormSettings::create(array_merge($form->settings->toArray(), ['displayId' => false]));
        $form->settings = $settings;
        $form->save();
    }

    /**
     * @Given Entry IDs are displayed to Entrants
     */
    public function entryIdsAreDisplayedToEntrants()
    {
        $form = FormSelector::get();
        $settings = FormSettings::create(array_merge($form->settings->toArray(), ['displayId' => true]));
        $form->settings = $settings;
        $form->save();
    }

    /**
     * @Then I should not be able to see the Entry ID column
     */
    public function iShouldNotBeAbleToSeeTheEntryIdColumn()
    {
        $this->assertResponseNotContains(trans('entries.table.columns.entryid'));
        $this->assertResponseNotContains(local_id(translate($this->entry)));
    }

    /**
     * @Then I should be able to see the Entry ID column
     */
    public function iShouldBeAbleToSeeTheEntryIdColumn()
    {
        $this->assertResponseContains(trans('entries.export.heading.entry-id'));
        $this->assertResponseContains(local_id(translate($this->entry)));
    }

    /**
     * @When I submit an entry
     */
    public function iSubmitAnEntry()
    {
        $this->entry = $this->muffin(Entry::class);
        $this->entry->submit();
        $this->entry->save();

        array_map(function ($event) {
            app('events')->fire($event);
        }, $this->entry->releaseEvents());
    }

    /**
     * @Then I should be able to see the review task for that entry
     */
    public function iShouldBeAbleToSeeTheReviewTaskForThatEntry()
    {
        $tasks = app(ReviewTaskRepository::class)->getForEntrySorted($this->entry->id);
        PHPUnit::assertCount(1, $tasks);

        $this->route('GET', 'entry.manager.view', [$this->entry->slug]);
        $this->assertResponseOk();
        $this->assertResponseContains((string) $tasks->first()->token);
    }

    /**
     * @Then /^I should be able to see the export contributors option$/
     */
    public function iShouldBeAbleToSeeTheExportContributorsOption()
    {
        $this->assertResponseContains('Export');
        $this->assertResponseContains('Contributors Excel');
        $this->assertResponseContains('Contributors CSV');
    }

    /**
     * @Given /^I should be able to export contributors$/
     */
    public function iShouldBeAbleToExportContributors()
    {
        $this->route('GET', 'entry.manager.export.contributors', ['type' => 'csv']);
        $this->assertRedirectedToRoute('entry.manager.index');
    }

    /**
     * @When /^I manage entries$/
     */
    public function iManageEntries()
    {
        $this->setGlobalCommunicationChannels();
        $this->route('GET', 'entry.manager.index');
        $this->assertResponseOk();
    }

    /**
     * @Given /^duplicate entries have been detected$/
     */
    public function duplicateEntriesHaveBeenDetected()
    {
        $this->entries = [
            $entryA = $this->muffin(Entry::class, ['title' => 'aaaaaaaaaa']),
            $this->muffin(Entry::class, ['title' => $entryA->title, 'category_id' => $entryA->categoryId]),
            $this->muffin(Entry::class, ['title' => $entryA->title, 'category_id' => $entryA->categoryId]),
            $entryB = $this->muffin(Entry::class, ['title' => 'bbbbbbbbbb']),
            $this->muffin(Entry::class, ['title' => $entryB->title, 'category_id' => $entryB->categoryId]),
        ];

        (new DuplicateFinder(new SameCategorySimilarTitle(80)))->findInSeason($entryA->seasonId);
    }

    /**
     * @When /^I should be able to manage duplicates$/
     */
    public function iShouldBeAbleToManageDuplicates()
    {
        $this->route('GET', 'entry.duplicates.manage');
        $this->assertResponseOk();

        PHPUnit::assertNotEmpty($this->entries);
        foreach ($this->entries as $entry) {
            $this->assertResponseContains((string) $entry->slug);
        }
    }

    /**
     * @Given there is at least one deleted entry with :slug slug
     */
    public function thereIsAtLeastOneDeletedEntry($slug)
    {
        $this->trashedEntry = $this->muffin(Entry::class, ['user_id' => $this->user->id]);
        $this->trashedEntry->setSlugValue(new Slug($slug));
        $this->trashedEntry->update();
        $this->trashedEntry->delete();
        $this->notTrashedEntry = $this->muffin(Entry::class, ['user_id' => $this->user->id]);
    }

    /**
     * @Then /^I should not be able to undelete invalid entries/
     */
    public function iShouldNotBeAbleToUndeleteInvalidEntries()
    {
        $input = $this->withInput(['selected' => [$this->trashedEntry->id, $this->notTrashedEntry->id]]);
        $this->route('PUT', 'entry.entrant.undelete', [], $input);
        $this->assertViewErrorsMatch([
            'selected.1' => '/^.*entry #1.*$/i',
        ]);
    }

    /**
     * @Then I should not see trashed entry with :slug slug
     */
    public function iShouldNotSeeTrashedEntry($slug)
    {
        Assert::assertEquals($slug, $this->trashedEntry->slug);
        $this->assertResponseNotContains($slug);
    }

    /**
     * @Then I should see undeleted entry
     */
    public function iShouldSeeUndeletedEntry()
    {
        $this->assertResponseContains((string) $this->trashedEntry->slug);
    }

    /**
     * @Then /^I should be able to undelete my trashed entry/
     */
    public function iShouldBeAbleToUndeleteMyTrashedEntry()
    {
        $input = $this->withInput(['selected' => [$this->trashedEntry->id]]);
        $this->route('PUT', 'entry.entrant.undelete', [], $input);

        $this->assertRedirectedToRoute('entry.entrant.index');
    }

    /**
     * @When /^I start and submit an entry/
     */
    public function iStartAndSubmitAnEntry()
    {
        $category = $this->muffin(Category::class);
        $chapterId = $category->chapters->first()->id;
        $this->round->chapters()->attach($chapterId);

        $title = 'Some entry - '.str_random();

        $postData = [
            'formId' => FormSelector::getId(),
            'title' => $title,
            'categoryId' => $category->id,
            'chapterId' => $chapterId,
            'values' => [],
            'links' => [],
        ];

        $this->route('PUT', 'entry-form.entrant.startAndSubmit', [], $this->withInput($postData));
        $this->assertResponseOk();

        $this->entry = app(EntryRepository::class)->getByTitle($title)->first();
        $this->entries[] = $this->entry;
    }

    /**
     * @Then /^I should see the entry/
     */
    public function iShouldSeeTheEntry()
    {
        $this->assertTrue($this->entry->submitted());

        $this->assertResponseContains((string) $this->entry->slug);
    }

    /**
     * @Then /^I should be able to require resubmission only for the completed entries$/
     */
    public function iShouldBeAbleToRequireResubmissionOnlyForTheCompletedEntries()
    {
        $entries = collect($this->entries);
        $ids = $entries->pluck('id')->toArray();

        $submitted = collect();
        $inProgress = collect();

        foreach ($entries as $entry) {
            if (! is_null($entry->submittedAt)) {
                $submitted->push($entry);
            } else {
                $inProgress->push($entry);
            }
        }

        $this->route('POST', 'entry.manager.require-resubmission', ['selected' => $ids]);

        $this->assertSessionHas('message', trans_choice('entries.actions.require_resubmission.message.count', $submitted->count()).' '.
            trans('entries.actions.require_resubmission.message.success').' '.
            trans_choice('entries.actions.require_resubmission.message.count', $inProgress->count()).' '.
            trans_choice('entries.actions.require_resubmission.message.is', $inProgress->count()).' '.
            trans('entries.actions.require_resubmission.message.failure'));

        foreach ($submitted as $s) {
            $this->assertTrue(! is_null($s->fresh()->resubmissionRequiredAt));
        }

        foreach ($inProgress as $i) {
            $this->assertTrue(is_null($i->fresh()->resubmissionRequiredAt));
        }
    }

    /**
     * @Given /^there is a draft season$/
     */
    public function thereIsADraftSeason()
    {
        $this->draftSeason = $this->muffin(Season::class, ['status' => Season::STATUS_DRAFT]);
    }

    /**
     * @Given /^there is an active season$/
     */
    public function thereIsAnActiveSeason()
    {
        $oldSeason = current_account()->activeSeason();
        $activeSeason = $this->muffin(Season::class);
        $activeSeason->swapActiveFrom($oldSeason);
        $this->season = $activeSeason;
    }

    /**
     * @Given /^the filtered season is archived$/
     */
    public function theFilteredSeasonIsArchived()
    {
        $this->archivedSeason = $this->muffin(Season::class, ['status' => Season::STATUS_ARCHIVED]);
        SeasonFilter::set($this->archivedSeason);
    }

    /**
     * @Given A form exists
     */
    public function aFormExists()
    {
        FormSelector::set($this->muffin(Form::class));
    }

    /**
     * @Given A collaborative form exists
     */
    public function aCollaborativeFormExists()
    {
        FormSelector::set($this->muffin(Form::class, ['settings' => FormSettings::create(['collaborative' => true])]));
    }

    /**
     * @Then /^I should be able to see the active season in season selector$/
     */
    public function iShouldBeAbleToSeeTheActiveSeasonInSeasonSelector()
    {
        $this->assertResponseContainsEncodedJson(translate(current_account()->activeSeason())->name);
    }

    /**
     * @Given /^I should not be able to see the draft season in season selector$/
     */
    public function iShouldNotBeAbleToSeeTheDraftSeasonInSeasonSelector()
    {
        $this->assertResponseNotContainsEncodedJson(translate($this->draftSeason)->name);
    }

    /**
     * @Given /^there is an eligibility tab$/
     */
    public function thereIsAnEligibilityTab()
    {
        $this->eligibilityTabs[] = $tab = $this->muffin(Tab::class, [
            'type' => Tab::TYPE_ELIGIBILITY,
            'resource' => Tab::RESOURCE_ENTRIES,
            'visible_to_entrants' => 1,
            'settings' => [
                'ineligible-hide-tabs' => 1,
                'min-eligibility-score' => count($this->eligibilityTabs) + 10,
                'ineligible-content-block' => (string) $this->ineligibleContentBlock->slug,
                'eligible-content-block' => (string) $this->eligibleContentBlock->slug,
                'ineligible-notification' => (string) $this->ineligibleNotification->slug,
                'eligible-notification' => (string) $this->eligibleNotification->slug,
            ],
            'order' => count($this->eligibilityTabs) + 10,
            'created_at' => Carbon::now()->startOfMinute(),
        ]);
        $this->eligibilityFields[] = $this->muffin(Field::class, [
            'tab_id' => $tab->id,
            'auto_scoring' => 1,
            'options' => '{"one": 5, "two": 10}',
            'type' => 'drop-down-list',
            'resource' => Field::RESOURCE_FORMS,
        ]);
    }

    /**
     * @Then I should be able to submit for eligibility with :option
     */
    public function iShouldBeAbleToSubmitForEligibilityWith($option)
    {
        $postData = [
            'title' => $this->entry->title.' updated',
            'categoryId' => $this->entry->categoryId,
            'chapterId' => $this->entry->chapterId,
            'values' => [
                (string) $this->eligibilityFields[0]->slug => $option,
            ],
            'links' => [],
            'updatedAt' => now()->toDateTimeString(),
        ];

        \Event::spy();

        $this->route('POST', 'entry-form.entrant.updateAndSubmitEligibility', [
            'entry' => (string) $this->entry->slug,
            'eligibilityTabSlug' => (string) $this->eligibilityTabs[0]->slug,
        ], $this->withInput($postData));
    }

    /**
     * @Then I should be able to submit for eligibility with two tabs :option1 :option2
     */
    public function iShouldBeAbleToSubmitForEligibilityWithTwoTabs($option1, $option2)
    {
        $postData = [
            'title' => $this->entry->title.' updated',
            'categoryId' => $this->entry->categoryId,
            'chapterId' => $this->entry->chapterId,
            'values' => [
                (string) $this->eligibilityFields[0]->slug => $option1,
                (string) $this->eligibilityFields[1]->slug => $option2,
            ],
            'links' => [],
            'updatedAt' => now()->toDateTimeString(),
        ];

        \Event::spy();

        $this->route('POST', 'entry-form.entrant.updateAndSubmitEligibility', [
            'entry' => (string) $this->entry->slug,
            'eligibilityTabSlug' => (string) $this->eligibilityTabs[1]->slug,
        ], $this->withInput($postData));
    }

    /**
     * @Then I should be able to submit for eligibility with three tabs :option1 :option2 :option3
     */
    public function iShouldBeAbleToSubmitForEligibilityWithThreeTabs($option1, $option2, $option3)
    {
        $postData = [
            'title' => $this->entry->title.' updated',
            'categoryId' => $this->entry->categoryId,
            'chapterId' => $this->entry->chapterId,
            'values' => [
                (string) $this->eligibilityFields[0]->slug => $option1,
                (string) $this->eligibilityFields[1]->slug => $option2,
                (string) $this->eligibilityFields[2]->slug => $option3,
            ],
            'links' => [],
            'updatedAt' => now()->toDateTimeString(),
        ];

        \Event::fake([EntryWasIneligible::class, EntryWasEligible::class]);

        $this->route('POST', 'entry-form.entrant.updateAndSubmitEligibility', [
            'entry' => (string) $this->entry->slug,
            'eligibilityTabSlug' => (string) $this->eligibilityTabs[2]->slug,
        ], $this->withInput($postData));
    }

    /**
     * @Given /^I should be redirected to the ineligible complete page$/
     */
    public function iShouldBeRedirectedToTheIneligibleCompletePage()
    {
        $this->response->assertJsonFragment(['redirect' => 'eligibility']);
        $this->response->assertJsonFragment(['contentBlock' => (string) $this->ineligibleContentBlock->slug]);
    }

    /**
     * @Given /^my entry should be ineligible$/
     */
    public function myEntryShouldBeIneligible()
    {
        \Event::assertDispatched(EntryWasIneligible::class, function (EntryWasIneligible $event) {
            return (string) $this->entry->slug === (string) $event->entry->slug &&
                (string) $this->ineligibleNotification->slug === (string) $event->notification()->slug;
        });
        $this->assertTrue($this->entry->fresh()->isIneligible());
    }

    /**
     * @Given /^I should be redirected to the eligible complete page$/
     */
    public function iShouldBeRedirectedToTheEligibleCompletePage()
    {
        $this->response->assertJsonFragment(['redirect' => 'eligibility']);
        $this->response->assertJsonFragment(['contentBlock' => (string) $this->eligibleContentBlock->slug]);
    }

    /**
     * @Given /^my entry should be eligible$/
     */
    public function myEntryShouldBeEligible()
    {
        \Event::assertDispatched(EntryWasEligible::class, function (EntryWasEligible $event) {
            return (string) $this->entry->slug === (string) $event->entry->slug &&
                (string) $this->eligibleNotification->slug === (string) $event->notification()->slug;
        });
        $this->assertTrue($this->entry->fresh()->isEligible());
    }

    /**
     * @Given /^my entry should be submitted$/
     */
    public function myEntryShouldBeSubmitted()
    {
        $this->assertTrue($this->entry->fresh()->submitted());
    }

    /**
     * @Given /^my entry should not be submitted$/
     */
    public function myEntryShouldNotBeSubmitted()
    {
        $this->assertFalse($this->entry->fresh()->submitted());
    }

    /**
     * @Given /^I should get success content block$/
     */
    public function iShouldGetSuccessContentBlock()
    {
        $this->response->assertJsonFragment(['eligibilityContentBlock' => $this->eligibleContentBlock->content()]);
    }

    /**
     * @Given /^there are eligibility content blocks and notifications$/
     */
    public function thereAreEligibilityContentBlocksAndNotifications()
    {
        $this->ineligibleContentBlock = $this->muffin(ContentBlock::class, [
            'key' => 'ineligible-content-block',
        ]);
        $this->eligibleContentBlock = $this->muffin(ContentBlock::class, [
            'key' => 'eligible-content-block',
        ]);
        $this->ineligibleNotification = $this->muffin(Notification::class, [
            'trigger' => 'entry.ineligible',
        ]);
        $this->eligibleNotification = $this->muffin(Notification::class, [
            'trigger' => 'entry.eligible',
        ]);
    }

    /**
     * @Given There is ineligibility content block
     */
    public function thereIsIneligibilityContentBlock()
    {
        $this->ineligibleContentBlock = $this->muffin(ContentBlock::class, [
            'key' => 'ineligible-content-block',
        ]);
    }

    /**
     * @Given /^there is tab after eligibility$/
     */
    public function thereIsTabAfterEligibility()
    {
        $this->muffin(Tab::class, ['order' => count($this->eligibilityTabs) + 10]);
    }

    /**
     * @Given /^my entry should be eligible with no new event dispatched$/
     */
    public function myEntryShouldBeEligibleWithNoNewEventDispatched()
    {
        \Event::assertNotDispatched(EntryWasEligible::class);
        $this->assertTrue($this->entry->fresh()->isEligible());
    }

    /**
     * @Given /^the entries have invoices$/
     */
    public function theyHaveInvoices()
    {
        app(Settings::class)->updateSingleGeneralSetting('next-invoice-number', 9999);
        foreach ($this->entries as $entry) {
            $order = $this->muffin(Order::class, ['invoice_number' => app(NextInvoiceNumber::class)->get()]);
            $this->muffin(OrderItem::class, ['order_id' => $order->id, 'entry_id' => $entry->id]);
        }
    }

    /**
     * @Given /^I should be able to see correct invoice numbers$/
     */
    public function iShouldBeAbleToSeeCorrectInvoiceNumbers()
    {
        $this->assertResponseContains('INV-9999');
    }

    /**
     * @Given /^I have a signed contract$/
     */
    public function iHaveASignedContract()
    {
        $this->signedContract = translate($this->muffin(Contract::class, [
            'entry_id' => $this->entry->id,
            'signed_at' => now(),
        ]));
    }

    /**
     * @Given /^I should be able to see the signed contract$/
     */
    public function iShouldBeAbleToSeeTheSignedContractLink()
    {
        $this->assertResponseContains($this->signedContract->title);
    }

    /**
     * @Given pay to :type entry is enabled
     */
    public function payForEntryIsEnabled($type)
    {
        app(Settings::class)->saveSettings(['entry-payment' => $type, 'paid-entries' => true], current_account());
    }

    /**
     * @Given I have configured prices
     */
    public function iHavepricesConfigured()
    {
        $this->price = $this->muffin(Price::class, ['type' => Price::TYPE_ENTRY]);
        $this->muffin(PriceAmount::class, [
            'price_id' => $this->price->id,
            'amounts' => [
                'USD' => 56.78,
                'AUD' => 12.34,
            ],
        ]);
        $this->muffin(Tax::class, ['country' => 'Rest of world', 'account_id' => current_account()->id]);
    }

    /** @Then I should be redirected to cart */
    public function iShouldBeRedirectedToCart()
    {
        $this->response->assertJsonFragment(['redirect' => 'cart']);
    }

    /**
     * ITS E.4.h
     *
     * @Then I should be able to view my cart
     */
    public function iShouldBeAbleToViewMyCart()
    {
        $this->route('GET', 'cart.view');
        $this->assertResponseOk();
    }

    /**
     * @Given I paid an entry with invoice
     */
    public function iPaidEntryWithInvoice()
    {
        $postData = [
            'entryFee' => $this->price->id,
            'currency' => 'AUD',
            'country' => 'AR',
            'streetAddress' => 'street',
            'city' => 'city',
            'state' => 'state',
            'postcode' => '1442',
            'paymentMethod' => 'invoice',
        ];
        $this->route('POST', 'cart.pay', $postData);
    }

    /**
     * @Then I should be redirected to edit entry with awaiting payment message
     */
    public function iShouldBeRedirectedToEditEntryWithAwaitingPaymentMessage()
    {
        $this->assertRedirectedToRoute('entry-form.entrant.edit', ['entry' => $this->entry->slug, 'tabSlug' => (string) $this->tab->slug ?? null]);
        $this->route('GET', 'entry-form.entrant.edit', ['entry' => $this->entry->slug, 'tabSlug' => (string) $this->tab->slug ?? null]);
        $this->assertResponseOk();
        $this->response->assertSee(trans('entries.messages.awaiting-payment'));
    }

    /**
     * @Given order has been paid
     */
    public function orderHasBeenPaid()
    {
        $order = $this->entry->orders()->first();
        $order->markAsPaid();
    }

    /**
     * @Given has submission completed content block
     */
    public function hasSubmissionCompletedContentBlock()
    {
        $this->muffin(ContentBlock::class, ['key' => 'submission-completed']);
    }

    /**
     * @Given I update a paid entry
     */
    public function iUpdatePaidEntry()
    {
        $data = [
            'seasonId' => $this->entry->season_id,
            'chapterId' => $this->entry->chapterId,
            'categoryId' => $this->entry->categoryId,
            'formId' => $this->entry->formId,
            'title' => $this->entry->title,
            'updatedAt' => Carbon::now(),
        ];

        $this->route('PUT', 'entry-form.entrant.updateAndSubmit', ['entry' => $this->entry->slug], $data);
    }

    /** @Then I should be redirected to complete entry
     */
    public function iShouldBeRedirectedToCompleteEntry()
    {
        $this->response->assertJson(['redirect' => 'complete', 'submittableSlug' => $this->entry->slug, 'submittable' => 'entry-form']);
        $this->route('GET', 'entry.entrant.complete', ['entry' => $this->entry->slug]);
        $this->assertResponseOk();
    }

    /**
     * @Then I should see payment received message
     */
    public function iShouldSeePaymentReceivedMessage()
    {
        $this->response->assertSee(trans('ecommerce.cart.titles.payment_received'));
    }

    /**
     * ITS E.1.c
     *
     * @Then /^I should be able to download an Entrant entry PDF$/
     */
    public function iShouldBeAbleToDownloadAnEntrantEntryPDF()
    {
        \Bus::fake();

        $response = $this->route('GET', 'entry.manager.entrant-pdf', [$this->entries[0]]);

        $response->assertSuccessful();

        \Bus::assertDispatched(
            GenerateEntrantEntryPDFCommand::class,
            fn($command) => $command->entry()->id === $this->entries[0]->id
        );
    }

    /**
     * ITS E.1.c
     *
     * @Then /^I should be able to download a Manager entry PDF$/
     */
    public function iShouldBeAbleToDownloadAManagerEntryPDF()
    {
        \Bus::fake();

        $response = $this->route('GET', 'entry.manager.manager-pdf', [$this->entries[0]]);

        $response->assertSuccessful();

        \Bus::assertDispatched(
            GenerateManagerEntryPDFCommand::class,
            fn($command) => $command->entry()->id === $this->entries[0]->id
        );
    }

    /**
     * @Given /^I should be able to update its deadline$/
     */
    public function iShouldBeAbleToUpdateItsDeadline()
    {
        $entry = $this->entries[0];
        $this->route('POST', 'entry.manager.deadline', [
            'entry' => (string) $entry->slug,
        ], $this->withInput(['deadline' => '2022-09-01 10:00']));

        $entry->fresh();
        $this->assertResponseContains(\Html::localisedDateTime($entry->deadlineAt));
    }

    /**
     * @Given /^it has deadline$/
     */
    public function itHasDeadline()
    {
        $this->entry->setDeadline('2060-01-01 10:00');
        $this->entry->save();
    }

    /**
     * @Given /^I should be able to submit it with any chapter of the selected form$/
     */
    public function iShouldBeAbleToSubmitItWithAnyChapterOfTheSelectedForm()
    {
        $category = $this->muffin(Category::class);
        $chapterId = $category->chapters->first()->id;
        FormSelector::get()->chapters()->attach($chapterId);

        $postData = [
            'formId' => FormSelector::getId(),
            'title' => $this->entry->title,
            'categoryId' => $category->id,
            'chapterId' => $chapterId,
            'values' => [],
            'links' => [],
            'updatedAt' => now()->toDateTimeString(),
        ];

        $this->route('PUT', 'entry-form.entrant.updateAndSubmit', ['entry' => (string) $this->entry->slug], $this->withInput($postData));

        $this->assertResponseOk();

        $this->entry->fresh();
    }

    /**
     * @Given /^the entry round is closed$/
     */
    public function theEntryRoundIsClosed()
    {
        $this->round->endAt(Carbon::now()->addDay(3)->format('Y-m-d H:i'), 'UTC');
        $this->round->save();
    }

    /**
     * @Given /^it was invited for chapter and category$/
     */
    public function itWasInvitedForChapterAndCategory()
    {
        $category = $this->muffin(Category::class);
        $chapterId = $category->chapters->first()->id;
        $this->entry->invitedAt = now();
        $this->entry->invitedCategoryId = $category->id;
        $this->entry->invitedChapterId = $chapterId;
        FormSelector::get()->chapters()->attach($chapterId);
        $this->entry->save();
    }

    /**
     * ITS E.4.j
     *
     * @Then /^I should not be able to change the chapter$/
     */
    public function iShouldNotBeAbleToChangeTheChapter()
    {
        $category = $this->muffin(Category::class);
        $chapterId = $category->chapters->first()->id;
        FormSelector::get()->chapters()->attach($chapterId);
        $this->round->chapters()->attach($chapterId);

        $postData = [
            'formId' => FormSelector::getId(),
            'title' => $this->entry->title,
            'categoryId' => $category->id,
            'chapterId' => $chapterId,
            'values' => [],
            'links' => [],
            'updatedAt' => now()->toDateTimeString(),
        ];

        $this->route('PUT', 'entry-form.entrant.updateAndSubmit', ['entry' => (string) $this->entry->slug], $this->withInput($postData));

        $this->assertResponseStatus(302);
    }

    /**
     * ITS E.4.j
     *
     * @Given /^I should be able to submit to that chapter and category$/
     */
    public function iShouldBeAbleToSubmitToThatChapterAndCategory()
    {
        $postData = [
            'formId' => FormSelector::getId(),
            'title' => $this->entry->title,
            'categoryId' => $this->entry->categoryId,
            'chapterId' => $this->entry->chapterId,
            'values' => [],
            'links' => [],
            'updatedAt' => now()->toDateTimeString(),
        ];

        $this->route('PUT', 'entry-form.entrant.updateAndSubmit', ['entry' => (string) $this->entry->slug], $this->withInput($postData));

        $this->assertResponseStatus(200);
    }

    /**
     * ITS E.4.j
     *
     * @Then /^I should not be able to change the category$/
     */
    public function iShouldNotBeAbleToChangeTheCategory()
    {
        $category = $this->muffin(Category::class);

        $postData = [
            'formId' => FormSelector::getId(),
            'title' => $this->entry->title,
            'categoryId' => $category->id,
            'chapterId' => $this->entry->chapterId,
            'values' => [],
            'links' => [],
            'updatedAt' => now()->toDateTimeString(),
        ];

        $this->route('PUT', 'entry-form.entrant.updateAndSubmit', ['entry' => (string) $this->entry->slug], $this->withInput($postData));

        $this->assertResponseStatus(302);
    }

    /**
     * @Given /^I have an entry invited with chapter and category$/
     */
    public function iHaveAnEntryInvitedWithChapterAndCategory()
    {
        $category = $this->muffin(Category::class);
        $chapterId = $category->chapters->first()->id;
        FormSelector::get()->chapters()->attach($chapterId);
        $this->round->chapters()->attach($chapterId);

        $this->entry = $this->muffin(Entry::class, [
            'user_id' => $this->user->id,
            'chapterId' => $chapterId,
            'invitedChapterId' => $chapterId,
            'categoryId' => $category->id,
            'invitedCategoryId' => $category->id,
            'invitedAt' => now()->subDay(),
        ]);
    }

    /**
     * @Given There is an active feedback round
     */
    public function thereIsAnActiveFeedbackRound()
    {
        $this->round = $this->muffin(Round::class, [
            'season_id' => $this->season->id,
            'round_type' => Round::ROUND_TYPE_FEEDBACK,
            'enabled' => true,
            'anonymise_judges' => false,
            'entrant_visibility' => true,
        ]);

        $this->round->chapters()->sync([$this->entry->chapterId]);
    }

    /**
     * @Given The round is configured with anonymise judges
     */
    public function theRoundIsConfiguredWithAnonymiseJudges()
    {
        $this->round->anonymiseJudges = true;
        $this->round->update();
    }

    /**
     * @Given The feedback round has comments enabled for my entry category
     */
    public function theFeedbackRoundHasCommentsEnabledForMyEntry()
    {
        $feedbackOption = new RoundFeedbackOption;
        $feedbackOption->categoryId = $this->entry->categoryId;
        $feedbackOption->displayJudgeComments = true;
        $this->round->feedbackOptions()->save($feedbackOption);
    }

    /**
     * @Given There is a :mode ScoreSet with comments on feedback view enabled
     */
    public function thereIsAScoreSetWithCommentsOnFeedbackViewEnabled($mode)
    {
        $this->scoreSet = $this->muffin(ScoreSet::class, ['commentsOnFeedbackView' => true, 'mode' => $mode]);
    }

    /**
     * @Given A judge has an assigment for my entry
     */
    public function aJudgeHasAnAssigmentForMyEntry()
    {
        $this->judge = $this->setupUserWithRole('Judge');
        $this->assigment = $this->muffin(Assignment::class, [
            'score_set_id' => $this->scoreSet->id,
            'judge_id' => $this->judge->id,
            'entry_id' => $this->entry->id,
            'status' => 'complete',
        ]);
    }

    /**
     * @Given A judge made a comment on the score set
     */
    public function aJudgeMadeACommentOnTheScoreSet()
    {
        $this->judge->currentScoreSetComment = 'This is a comment!';
        app(CommentManager::class)->create($this->judge->id, $this->judge->currentScoreSetComment, false, new EntryTag($this->entry), new StringTag('judging'), new ScoreSetTag($this->scoreSet));
    }

    /**
     * @Given There is a Scoring Criterion
     */
    public function thereIsAScoringCriterion()
    {
        $this->scoringCriterion = $this->muffin(ScoringCriterion::class, ['scoreset_id' => $this->scoreSet->id, 'field_id' => null]);
    }

    /**
     * @Given A judge made a comment on the criterion
     */
    public function aJudgeMadeACommentOnTheCriterion()
    {
        $this->judge->currentCriterionComment = '!This is a criterion comment!!';
        app(CommentManager::class)->create($this->judge->id, $this->judge->currentCriterionComment, false, new EntryTag($this->entry), new StringTag('judging'), new CriterionTag($this->scoringCriterion));
    }

    /**
     * Integrity test E.2
     *
     * @Then I Should be able to view feedback without judges name
     */
    public function iShouldBeAblteToViewFeedbackWithoutJudgesName()
    {
        $this->route('GET', 'entry.feedback.view', ['entry' => $this->entry->slug]);
        $this->assertResponseOk();
        $this->assertResponseNotContains($this->judge->name);
        $this->assertResponseContains($this->judge->currentScoreSetComment);
        $this->assertResponseContains($this->judge->currentCriterionComment);
    }

    /**
     * @Given another entrant with an entry exists
     */
    public function existsAnotherEntrantWithAnEntry()
    {
        $this->newEntrant = $this->setupUserWithRole('Entrant');
        $this->newEntrantEntry = $this->muffin(Entry::class, ['user_id' => $this->newEntrant->id]);
    }

    /**
     * ITS E.3.a
     *
     * @Then I should not be able to see other entrants entries in entries entrant's view
     */
    public function iShouldNotBeAbleToSeeOtherEntrantsEntriesInEntrantsEntriesView()
    {
        $this->assertResponseNotContains((string) $this->newEntrantEntry->slug);
    }

    /**
     * ITS E.3.b
     *
     * @Then I should not be able to view someone's else entry
     */
    public function iShouldNotBeAbleToViewSomeonesElseEntry()
    {
        $this->route('GET', 'entry-form.entrant.edit', ['entry' => $this->newEntrantEntry->slug]);

        $this->assertResponseStatus(404);
    }

    /**
     * ITS E.3.c
     *
     * @Then I should not be able to preview someone's else entry
     */
    public function iShouldNotBeAbleToPreviewSomeonesElseEntry()
    {
        $this->route('GET', 'entry.entrant.preview', ['entry' => $this->newEntrantEntry->slug]);

        $this->assertResponseStatus(404);
    }

    /**
     * ITS E.3.d
     *
     * @Then I should not be able to PDF someone's else entry
     */
    public function iShouldNotBeAbleToPDFSomeonesElseEntry()
    {
        $this->route('GET', 'entry.entrant.pdf', [$this->newEntrantEntry->slug]);

        $this->assertResponseStatus(404);
    }

    /**
     * ITS E.3.d
     *
     * @Then I should be able to view the PDF on the new entrant entry
     */
    public function iShouldBeAbleToViewPDFOnNewEntrantEntry()
    {
        \Bus::fake();

        $this->route('GET', 'entry.entrant.pdf', [$this->newEntrantEntry->slug]);

        $this->assertResponseOk();

        \Bus::assertDispatched(
            GenerateEntrantEntryPDFCommand::class
        );
    }

    /**
     * ITS E.3.d
     *
     * @Then I should not be able to bulk download someone's else entry
     */
    public function iShouldNotBeAbleToBulkDownloadSomeonesElseEntry()
    {
        \Bus::fake();

        $postData = [
            'selected' => [$this->newEntrantEntry->id],
        ];

        $this->route('POST', 'entry.entrant.download', [], $this->withInput($postData));

        \Bus::assertNotDispatched(
            BulkDownloadEntriesCommand::class
        );
    }

    /**
     * ITS E.3.e
     *
     * @Then I should not be able to copy someone's else entry
     */
    public function iShouldNotBeAbleToCopySomeonesElseEntry()
    {
        \Bus::fake();

        $postData = [
            'selected' => [$this->newEntrantEntry->id],
        ];

        $this->route('POST', 'entry.entrant.copy', [], $this->withInput($postData));

        \Bus::assertNotDispatched(
            BulkDownloadEntriesCommand::class
        );
    }

    /**
     * ITS E.4.c
     *
     * @Then I should be able to see entries list
     */
    public function iShouldBeAbleToSeeEntriesList()
    {
        $this->setGlobalCommunicationChannels();
        $this->route('GET', 'entry.entrant.index');
        $this->assertResponseOk();
    }

    /**
     * @Then There is a previous export being executed for :area area
     */
    public function thereIsAPreviousExportBeingExecuted($area)
    {
        (new LocksExportDownload($area))->acquireLock();
    }

    /**
     * @Then I should not be able to generate another one for :area area
     */
    public function iShouldNotBeAbleToGenerateAnotherOne($area)
    {
        $params = $this->exportParams($area);
        $originUrl = url()->previous();

        $response = $this->route('GET', 'export.default', $params);

        $response->assertRedirectedTo($originUrl, ['message' => trans('exports.export-in-progress'), 'type' => 'warning']);
    }

    /**
     * @Then I should be able to generate another one for :param area
     */
    public function iShouldBeAbleToGenerateAnotherOne($param)
    {
        \Bus::fake(ExportCommand::class);
        $params = $this->exportParams($param);
        $originUrl = url()->previous();

        $response = $this->route('GET', 'export.default', $params);

        $response->assertRedirectedTo($originUrl, ['message' => trans('exports.flash-message'), 'type' => 'info']);
        \Bus::assertDispatched(ExportCommand::class);
    }

    /**
     * @Then The export :area area was released
     */
    public function thePreviousExportWasReleased($area)
    {
        (new LocksExportDownload($area))->releaseLock();
    }

    private function exportParams($area = '')
    {
        return [
            'format' => 'xlsx',
            'area' => $area,
            '_token' => csrf_token(),
        ];
    }

    /**
     * @Then /^I should be able to delete a specific entry$/
     */
    public function iShouldBeAbleToDeleteASpecificEntry()
    {
        \Bus::fake();

        $postData = [
            'selected' => [$this->entries[0]->id],
        ];

        $this->route('delete', 'entry.manager.delete', [], $this->withInput($postData));

        \Bus::assertDispatched(
            DeleteModelsCommand::class
        );
    }

    /**
     * @Then /^I should be able to undelete a specific entry$/
     */
    public function iShouldBeAbleToUndeleteASpecificEntry()
    {
        \Bus::fake();

        $postData = [
            'selected' => [$this->entries[0]->id],
        ];

        $this->route('put', 'entry.manager.undelete', [], $this->withInput($postData));

        \Bus::assertDispatched(
            UndeleteModelsCommand::class
        );
    }

    /**
     * @Then /^I should(?: "(not)")? be able to see the deleted button$/
     */
    public function iShouldBeAbleToSeeTheDeletedButton($shouldSee = null)
    {
        $response = $this->route('GET', 'entry.manager.view', [$this->entries[0]->slug, 'vtab' => 'entry.manager.tabs.entry']);

        if ($shouldSee === 'not') {
            $response->assertDontSee('Delete this entry permanently');
        } else {
            $response->assertSee('Delete this entry permanently');
        }
    }

    /**
     * @Given The entry have an extended deadline
     */
    public function theEntryHaveAnExtendedDeadline()
    {
        $this->entry->setDeadline(Carbon::now()->addDays(3)->format('Y-m-d H:i'), 'UTC');
        $this->entry->update();
    }

    /**
     * @Given The round deadline is expired
     */
    public function theRoundDeadlineIsExpired()
    {
        $this->round->endAt(Carbon::now()->subDays(3)->format('Y-m-d H:i'), 'UTC');
        $this->round->update();
    }

    /**
     * @Then I should not see the submit button
     */
    public function iShouldNotSeeTheSubmitButton()
    {
        $this->assertResponseNotContains('\u0022hasDeadline\u0022:true,\u0022deadlinePassed\u0022:false');
    }

    /**
     * @Then I should see the submit button
     */
    public function iShouldSeeTheSubmitButton()
    {
        $this->assertResponseContains('\u0022hasDeadline\u0022:true,\u0022deadlinePassed\u0022:false');
        $this->assertResponseContains(trans('entries.form.submit'));
    }

    /**
     * @Then /^I should not be able to require resubmission without required permission$/
     */
    public function iShouldNotBeAbleToRequireResubmissionWithoutRequiredPermission()
    {
        $response = $this->route('PUT', 'entry.manager.require_resubmission', ['entry' => $this->newEntrantEntry->slug]);
        $response->assertForbidden();
    }

    /**
     * @Then /^I should be able to require resubmission with required permission$/
     */
    public function iShouldBeAbleToRequireResubmissionWithRequiredPermission()
    {
        $this->newEntrantEntry->submit();
        $this->newEntrantEntry->save();

        $response = $this->route('PUT', 'entry.manager.require_resubmission', ['entry' => $this->newEntrantEntry->slug]);
        $response->assertRedirect();

        $entry = $this->newEntrantEntry->fresh();
        $this->assertTrue((bool) $entry->resubmissionRequiredAt);
        $this->assertTrue($entry->resubmittedAt === null);
        $this->assertTrue($entry->ineligibleAt === null);
        $this->assertTrue($entry->eligibleAt === null);
        $this->assertTrue($entry->eligibleTabs === []);
    }

    /**
     * @Then /^I should not be able to revert entry in progress without required permission$/
     */
    public function iShouldNotBeAbleToRevertEntryInProgressWithoutRequiredPermission()
    {
        $response = $this->route('PUT', 'entry.manager.revert_to_in_progress', ['entry' => $this->newEntrantEntry->slug]);
        $response->assertForbidden();
    }

    /**
     * @Then /^I should be able to revert entry in progress with required permission$/
     */
    public function iShouldBeAbleToRevertEntryInProgressWithRequiredPermission()
    {
        $this->newEntrantEntry->submit();
        $this->newEntrantEntry->save();

        $response = $this->route('PUT', 'entry.manager.revert_to_in_progress', ['entry' => $this->newEntrantEntry->slug]);
        $response->assertRedirect();

        $entry = $this->newEntrantEntry->fresh();
        $this->assertTrue($entry->submittedAt === null);
        $this->assertTrue($entry->resubmissionRequiredAt === null);
        $this->assertTrue($entry->resubmittedAt === null);
        $this->assertTrue($entry->ineligibleAt === null);
        $this->assertTrue($entry->eligibleAt === null);
        $this->assertTrue($entry->eligibleTabs === []);
        $this->assertTrue($entry->invitedAt === null);
    }

    /**
     * ITS E.4.f
     *
     * @Then I should be able to PDF my own entry
     */
    public function iShouldBeAbletoPDFMyOwnEntry()
    {
        \Bus::fake();
        $this->route('GET', 'entry.entrant.pdf', [$this->entry->slug]);

        $this->assertResponseOk();

        \Bus::assertDispatched(
            GenerateEntrantEntryPDFCommand::class
        );
    }

    /**
     * @Given I have been assigned as collaborator on the new entrant entry
     */
    public function iHaveBeenAssignedAsCollaborator()
    {
        $this->muffin(Membership::class, ['user_id' => consumer_id()]);

        $this->muffin(Collaborator::class, [
            'submittable_id' => $this->newEntrantEntry->id,
            'submittable_type' => $this->newEntrantEntry->getMorphClass(),
            'user_id' => consumer_id(),
        ]);
    }

    /**
     * @Given I access :route
     */
    public function iAccessRoute(string $route)
    {
        $this->route('GET', $route);
    }

    /**
     * @Then I should receive status :code
     */
    public function iShouldNotBeAbleToAccessManagerPage(int $code)
    {
        $this->assertResponseStatus($code);
    }

    /**
     * ITS E.4.j
     *
     * @Then I should not be able to modify the season
     */
    public function iShouldNotBeAbleToModifyTheSeason()
    {
        $this->route('PUT', 'season.update', ['season' => current_account()->activeSeason()->slug], ['updatedAt' => now()->toDateTimeString()]);
        $this->assertResponseStatus(302);
    }

    /**
     * ITS E.4.j
     *
     * @Then I should not be able to see the season
     */
    public function iShouldNotBeAbleToSeeTheSeason()
    {
        $this->route('GET', 'season.edit', ['season' => current_account()->activeSeason()->slug]);
        $this->assertResponseStatus(403);
    }

    /**
     * ITS E.4.j
     *
     * @Then I should not be able to see the form
     */
    public function iShouldNotBeAbleToSeeTheForm()
    {
        $this->route('GET', 'forms.edit', ['form' => (string) FormSelector::get()->slug]);
        $this->assertResponseStatus(403);
    }

    /**
     * ITS E.4.j
     *
     * @Then I should not be able to modify the form
     */
    public function iShouldNotBeAbleToModifyTheForm()
    {
        $this->route('POST', 'forms.save_continue', ['form' => (string) FormSelector::get()->slug]);
        $this->assertResponseStatus(302);
    }

    /**
     * ITS E.4.j
     *
     * @Then I should not be able to see the form settings
     */
    public function iShouldNotBeAbleToSeeTheFormSettings()
    {
        $this->route('GET', 'forms.settings', ['form' => (string) FormSelector::get()->slug]);
        $this->assertResponseStatus(403);
    }

    /**
     * ITS E.4.j
     *
     * @Then I should not be able to edit form settings
     */
    public function iShouldNotBeAbleToEditFormSettings()
    {
        $this->route('POST', 'forms.save_settings', ['form' => (string) FormSelector::get()->slug]);
        $this->assertResponseStatus(302);
    }

    /**
     * @Then Eligibility should be true
     */
    public function iShouldntSeeTheCheckEligibilityButton()
    {
        $this->assertResponseContains('\u0022resubmissionRequired\u0022:false,\u0022isEligible\u0022:true');
    }

    /**
     * @Given An assigment exists for my entry
     */
    public function anAssignmentExistsForMyEntry()
    {
        $this->assigment = $this->muffin(Assignment::class, [
            'score_set_id' => $this->scoreSet->id,
            'judge_id' => $this->user->id,
            'entry_id' => $this->entry->id,
        ]);
    }

    /**
     * @Then I should see a validation error when I try to delete an attachment with incorrect id
     */
    public function IShouldSeeAValidationErrorWhenITryToDeleteAnAttachmentWithIncorrectId(): void
    {
        $this->route('DELETE', 'entry.entrant.delete.attachment', ['entry' => (string) $this->entry->slug, 'id' => 'incorrect-id']);
        $this->assertSessionHasErrors(['error']);
    }

    /**
     * @Then I should see custom exports
     */
    public function iShouldSeeCustomExports()
    {
        $this->assertResponseContains(':custom-export-layouts="JSON.parse(\'[{');
    }

    /**
     * @Then I should not see custom exports
     */
    public function iShouldNotSeeCustomExports()
    {
        $this->assertResponseContains(':custom-export-layouts="[]"');
    }

    /**
     * @Given the entries category has packing slips enabled
     */
    public function theEntriesCategoryHasPackingSlipsEnabled()
    {
        $category = $this->entries[0]->category;
        $category->packingSlip = true;
        $category->save();
    }

    /**
     * @Then I should not see the packing slip button :key
     */
    public function iShouldNotSeeThePackingSlipButton($key)
    {
        $this->assertResponseNotContains(trans($key));
    }

    /**
     * @Then I should see the packing slip button :key
     */
    public function iShouldSeeThePackingSlipButton($key)
    {
        $this->assertResponseContains(trans($key));
    }

    /**
     * @Then I should see the save view button
     */
    public function iShouldSeeTheSaveViewButton()
    {
        $this->assertResponseContains('href="javascript:;" class="action-button search-save"');
    }

    /**
     * @Then save view button should redirect to feature disabled page
     */
    public function saveViewButtonShouldRedirectToFeatureDisablePage()
    {
        $this->assertResponseContains(trans('miscellaneous.search.save-view'));
        $this->assertResponseContains('href="/feature-disabled/saved_views" class="action-button search-save"');
    }

    /**
     * @Given a saved view exists
     */
    public function aSavedViewExists()
    {
        $this->savedView = $this->muffin(Search::class);
    }

    /**
     * @Then I should not be able to access the saved view
     */
    public function iShouldNotBeAbleToAccessTheSavedView()
    {
        $this->route('GET', 'search.settings.load', ['search' => $this->savedView->slug]);
        $this->assertRedirectedToRoute('feature.disabled', ['feature' => 'saved_views']);
    }

    /**
     * @Given An entrant was invited without a category
     */
    public function anEntrantWasInvitedWithoutACategory()
    {
        $this->entry = $this->muffin(Entry::class, [
            'user_id' => $this->user->id,
            'category_id' => null,
            'invitedAt' => now()->subDay(),
        ]);
    }

    /**
     * @Then I should be able to view the entry
     */
    public function iShouldBeAbleToViewTheEntry()
    {
        $this->route('GET', 'entry.manager.view', [$this->entry->slug]);
        $this->assertResponseOk();
    }

    /**
     * @Then I start an entry
     */
    public function iStartAnEntry()
    {
        $this->route('GET', 'entry.manager.start');
        $this->assertRedirectedToRoute('entry-form.manager.start');
        $this->route('GET', 'entry-form.manager.start');
        $this->assertResponseOk();
    }

    /**
     * @Then I should be starting an entry for the default form
     */
    public function iShouldBeStartingAnEntryForTheDefaultForm()
    {
        $formTranslation = translate(FormSelector::get())->translated;
        $this->assertResponseContains($formTranslation['en_GB']['name']);
    }

    /**
     * @Then I want to start and entry for a different form
     */
    public function iWantToStartAndEntryForADifferentForm()
    {
        $form = $this->forms[2];
        $this->route('GET', 'entry.manager.start', [], ['formSlug' => $form->slug]);
        $this->assertRedirectedToRoute('entry-form.manager.start', ['formSlug' => $form->slug]);
        $this->route('GET', 'entry-form.manager.start', [], ['formSlug' => $form->slug]);
        $this->assertResponseOk();
    }

    /**
     * @Then I should be starting an entry for a different form
     */
    public function iShouldBeStartingAnEntryForADifferentForm()
    {
        $currentFormTranslation = translate(FormSelector::get())->translated;
        $setFormTranslation = translate($this->forms[2])->translated;

        Assert::assertEquals($currentFormTranslation['en_GB']['name'], $currentFormTranslation['en_GB']['name']);
        $this->assertResponseContains($setFormTranslation['en_GB']['name']);
    }

    /**
     * @Then I should be able to set a primary entry
     */
    public function iShouldBeAbleToSetPrimaryEntry()
    {
        $this->route('PUT', 'entry.duplicates.confirm-group', ['entry' => $this->entries[0]->slug]);
    }

    /**
     * @Then I should be able to confirm duplicates
     */
    public function iShouldBeAbleToConfirmDuplicates()
    {
        $this->route('PUT', 'entry.duplicates.confirm-group', ['entry' => $this->entries[0]->slug]);
    }

    /**
     * @Then I should be redirected to manage duplicates list
     */
    public function iShouldBeRedirectedToManageDuplicatesList()
    {
        $this->assertRedirectedToRoute('entry.duplicates.manage');
    }

    /**
     * @Then I submit my entry
     */
    public function iSubmitMyEntry()
    {
        $data = [
            'seasonId' => $this->entry->season_id,
            'chapterId' => $this->entry->chapterId,
            'categoryId' => $this->entry->categoryId,
            'formId' => $this->entry->formId,
            'title' => $this->entry->title,
            'updatedAt' => Carbon::now(),
        ];

        $this->route('PUT', 'entry-form.entrant.updateAndSubmit', ['entry' => $this->entry->slug], $data);
        $this->assertResponseOk();
    }

    /**
     * @Then I should be redirected to entry complete
     */
    public function iShouldBeRedirectedToEntryComplete()
    {
        $this->assertRedirectedToRoute('entry.entrant.complete');
        $this->route('GET', 'entry.entrant.complete', ['entry' => $this->entry->slug]);
        $this->assertResponseOk();
    }

    /**
     * @Then I should see ready for payment message
     */
    public function iShouldSeeReadyForPaymentMessage()
    {
        $this->response->assertSee(trans('ecommerce.cart.titles.ready_for_payment'));
    }

    /**
     * @Then The entry went back to in progress
     */
    public function theEntryWentBackToInProgress()
    {
        $this->entry->revertToInProgress();
        $this->entry->save();
    }

    /**
     * @Then I should see the screen reader text for the entry
     */
    public function iShouldSeeTheScreeReaderTextForTheEntry()
    {
        $this->assertResponseContains(trans('buttons.action_overflow', ['resource' => $this->entry->resourceLabel()]));
        $this->assertResponseContains(trans('buttons.checkbox_for_resource', ['resource' => $this->entry->resourceLabel()]));
    }

    /**
     * @Then I should see the star column of entry with the screen reader text
     */
    public function iShouldSeeTheStarColumnOfEntryWithTheScreenReaderText()
    {
        $this->assertResponseContains(trans('buttons.star_action', ['resource' => $this->entry->resourceLabel()]));
    }

    /**
     * @Then I should see the cog icon
     */
    public function iShouldSeeTheCogIcon()
    {
        $this->assertResponseContains('columnator-th-icon');
    }

    /**
     * @Then I should be able to add the grant status column
     */
    public function iShouldBeAbleToAddTheGrantStatusColumn()
    {
        $this->route('POST', 'search.columns.save', [
            'area' => 'my_entries.search',
            'columns' => ['grant_status_label'],
            'fixedColumns' => ['marker', 'action-overflow', 'my_entries.thumbnail'],
        ]);

        $this->assertRedirectedToRoute('entry.entrant.index');
    }

    /**
     * @then I should see the list of available columns
     */
    public function iShouldSeeTheListOfAvailableColumns()
    {
        $this->route('GET', 'search.columns', [], ['area' => 'my_entries.search']);
        $this->assertResponseOk();

        $columnsResponse = json_decode($this->response->baseResponse->content(), true);

        Assert::assertArrayHasKey('columns', $columnsResponse);
        Assert::assertArrayHasKey('availableColumns', $columnsResponse);
        Assert::assertArrayHasKey('fixedColumns', $columnsResponse);
    }

    /**
     * @Given /^the entry has some field values$/
     */
    public function theEntryHasSomeFieldValues()
    {
        $this->fieldA = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);
        $this->fieldB = $this->muffin(Field::class, ['resource' => Field::RESOURCE_FORMS]);

        app(ValuesService::class)->setValuesForObject([
            (string) $this->fieldA->slug => 'old value A',
            (string) $this->fieldB->slug => 'old value B',
        ], $this->entry);
    }

    /**
     * @Then /^I should be able to update a specific field value as an entrant$/
     */
    public function iShouldBeAbleToUpdateASpecificFieldValueAsAnEntrant()
    {
        $this->updateFieldValue(
            'entry-form.entrant.update-field',
            ['entry' => (string) $this->entry->slug],
            $this->fieldA,
            'new value A'
        );
    }

    protected function updateFieldValue(string $route, array $routeParameters, Field $field, string $value, array $additionalInput = [])
    {
        $this->route('PUT', $route,
            array_merge($routeParameters, ['field' => (string) $field->slug]),
            $this->withInput(array_merge($additionalInput, ['value' => $value]))
        );

        $this->assertResponseOk();
    }

    /**
     * @Given /^I should see the updated field value$/
     */
    public function iShouldSeeTheUpdatedFieldValue()
    {
        $this->entry->refresh();

        Assert::assertEquals('new value A', $this->entry->values[(string) $this->fieldA->slug]);
        Assert::assertEquals('old value B', $this->entry->values[(string) $this->fieldB->slug]);
    }

    /**
     * @Given /^the attachment has some field values$/
     */
    public function theAttachmentHasSomeFieldValues()
    {
        $this->attachmentFieldA = $this->muffin(Field::class, ['resource' => Field::RESOURCE_ATTACHMENTS]);
        $this->attachmentFieldB = $this->muffin(Field::class, ['resource' => Field::RESOURCE_ATTACHMENTS]);

        app(ValuesService::class)->setValuesForObject([
            (string) $this->attachmentFieldA->slug => 'old value A',
            (string) $this->attachmentFieldB->slug => 'old value B',
        ], $this->attachment);
    }

    /**
     * @Then /^I should be able to update a specific attachment field value as an entrant$/
     */
    public function iShouldBeAbleToUpdateASpecificAttachmentFieldValueAsAnEntrant()
    {
        $this->updateFieldValue(
            'entry-form.entrant.update-attachment',
            ['entry' => (string) $this->entry->slug, 'attachment' => $this->attachment->id],
            $this->attachmentFieldA,
            'new value A',
        );
    }

    /**
     * @Then /^I should be able to update a specific attachment field value as a manager$/
     */
    public function iShouldBeAbleToUpdateASpecificAttachmentFieldValueAsAManager()
    {
        $this->updateFieldValue(
            'entry-form.manager.update-attachment',
            ['entry' => (string) $this->entry->slug, 'attachment' => $this->attachment->id],
            $this->attachmentFieldA,
            'new value A',
        );
    }

    /**
     * @Given /^I should see the updated attachment field value$/
     */
    public function iShouldSeeTheUpdatedAttachmentFieldValue()
    {
        $this->attachment->refresh();

        Assert::assertEquals('new value A', $this->attachment->values[(string) $this->attachmentFieldA->slug]);
        Assert::assertEquals('old value B', $this->attachment->values[(string) $this->attachmentFieldB->slug]);
    }

    /**
     * @Then /^I should be able to update the title as an entrant$/
     */
    public function iShouldBeAbleToUpdateTheTitleAsAnEntrant()
    {
        $this->updateTitle('entry-form.entrant.update-title');
    }

    protected function updateTitle($route)
    {
        $this->route('PUT', $route,
            [
                'entry' => (string) $this->entry->slug,
            ],
            $this->withInput([
                'title' => 'a new title',
            ])
        );

        $this->assertResponseOk();
    }

    /**
     * @Given /^I should see the updated title$/
     */
    public function iShouldSeeTheUpdatedTitle()
    {
        $this->entry->refresh();

        Assert::assertEquals('a new title', $this->entry->title);
    }

    /**
     * @Then /^I should be able to update the category as an entrant$/
     */
    public function iShouldBeAbleToUpdateTheCategoryAsAnEntrant()
    {
        $this->updateCategory('entry-form.entrant.update-category');
    }

    protected function updateCategory($route)
    {
        $this->round->chapters()->attach($this->entry->chapterId);

        $this->newCategory = $this->muffin(Category::class);
        $this->newCategory->chapters()->attach($this->entry->chapterId);

        $this->route('PUT', $route,
            [
                'entry' => (string) $this->entry->slug,
            ],
            $this->withInput([
                'categoryId' => $this->newCategory->id,
            ])
        );

        $this->assertResponseOk();
    }

    /**
     * @Given /^I should see the updated category$/
     */
    public function iShouldSeeTheUpdatedCategory()
    {
        $this->entry->refresh();

        Assert::assertEquals($this->newCategory->id, $this->entry->categoryId);
    }

    /**
     * @Then /^I should be able to update the chapter as an entrant$/
     */
    public function iShouldBeAbleToUpdateTheChapterAsAnEntrant()
    {
        $this->updateChapter('entry-form.entrant.update-chapter');
    }

    protected function updateChapter($route)
    {
        $this->newChapter = $this->muffin(Chapter::class, ['season_id' => $this->entry->seasonId]);
        $this->round->chapters()->attach($this->newChapter->id);

        $this->route('PUT', $route,
            [
                'entry' => (string) $this->entry->slug,
            ],
            $this->withInput([
                'chapterId' => (string) $this->newChapter->id,
            ])
        );

        $this->assertResponseOk();
    }

    /**
     * @Given /^I should see the updated chapter$/
     */
    public function iShouldSeeTheUpdatedChapter()
    {
        $this->entry->refresh();

        Assert::assertEquals($this->newChapter->id, $this->entry->chapterId);
    }

    /**
     * @Then /^I should be able to update a specific field value as a manager$/
     */
    public function iShouldBeAbleToUpdateASpecificFieldValueAsAManager()
    {
        $this->updateFieldValue(
            'entry-form.manager.update-field',
            ['entry' => (string) $this->entry->slug],
            $this->fieldA,
            'new value A'
        );
    }

    /**
     * @Then /^I should be able to update the title as a manager$/
     */
    public function iShouldBeAbleToUpdateTheTitleAsAManager()
    {
        $this->updateTitle('entry-form.manager.update-title');
    }

    /**
     * @Then /^I should be able to update the category as a manager$/
     */
    public function iShouldBeAbleToUpdateTheCategoryAsAManager()
    {
        $this->updateCategory('entry-form.manager.update-category');
    }

    /**
     * @Then /^I should be able to update the chapter as a manager$/
     */
    public function iShouldBeAbleToUpdateTheChapterAsAManager()
    {
        $this->updateChapter('entry-form.manager.update-chapter');
    }

    /**
     * @Given /^I should be able to create a new contributor as an entrant$/
     */
    public function iShouldBeAbleToCreateANewContributorAsAnEntrant()
    {
        $this->createNewContributor('entry-form.entrant.create-contributor', ['entry' => (string) $this->entry->slug, 'tab' => (string) $this->entry->form->tabs()->first()->slug], $this->entry);
    }

    /**
     * @Given /^I should be able to create a new referee as an entrant$/
     */
    public function iShouldBeAbleToCreateANewRefereeAsAnEntrant(): void
    {
        $this->createNewReferee('entry-form.entrant.create-referee', ['entry' => (string) $this->entry->slug, 'tab' => (string) $this->entry->form->tabs()->first()->slug], $this->entry);
    }

    /**
     * @Then /^I should be able to update a specific contributor field value as an entrant$/
     */
    public function iShouldBeAbleToUpdateASpecificContributorFieldValueAsAnEntrant()
    {
        $this->updateContributor('entry-form.entrant.update-contributor', ['entry' => (string) $this->entry->slug]);
    }

    /**
     * @Then /^I should be able to update a specific referee field value as an entrant$/
     */
    public function iShouldBeAbleToUpdateASpecificRefereeFieldValueAsAnEntrant(): void
    {
        $this->updateRefereeField('entry-form.entrant.update-referee-field', ['entry' => (string) $this->entry->slug]);
    }

    /**
     * @Then /^I should be able to update a specific referee as an entrant$/
     */
    public function iShouldBeAbleToUpdateASpecificRefereeAsAnEntrant(): void
    {
        $this->updateReferee('entry-form.entrant.update-referee', ['entry' => (string) $this->entry->slug]);
    }

    /**
     * @Given /^I should be able to create a new contributor as a manager$/
     */
    public function iShouldBeAbleToCreateANewContributorAsAManager()
    {
        $this->createNewContributor('entry-form.manager.create-contributor', ['entry' => (string) $this->entry->slug, 'tab' => (string) $this->entry->form->tabs()->first()->slug], $this->entry);
    }

    /**
     * @Given /^I should be able to create a new referee as a manager$/
     */
    public function iShouldBeAbleToCreateANewRefereeAsAManager(): void
    {
        $this->createNewReferee('entry-form.manager.create-referee', ['entry' => (string) $this->entry->slug, 'tab' => (string) $this->entry->form->tabs()->first()->slug], $this->entry);
    }

    /**
     * @Then /^I should be able to update a specific contributor field value as a manager$/
     */
    public function iShouldBeAbleToUpdateASpecificContributorFieldValueAsAManager()
    {
        $this->updateContributor('entry-form.manager.update-contributor', ['entry' => (string) $this->entry->slug]);
    }

    /**
     * @Then /^I should be able to update a specific referee field value as a manager$/
     */
    public function iShouldBeAbleToUpdateASpecificRefereeFieldValueAsAManager(): void
    {
        $this->updateRefereeField('entry-form.manager.update-referee-field', ['entry' => (string) $this->entry->slug]);
    }

    /**
     * @Then /^I should be able to update a specific referee as a manager$/
     */
    public function iShouldBeAbleToUpdateASpecificRefereeAsAManager(): void
    {
        $this->updateReferee('entry-form.manager.update-referee', ['entry' => (string) $this->entry->slug]);
    }

    protected function createNewContributor($route, $routeParameters, $submittable)
    {
        $this->route('PUT', $route, $routeParameters, $this->withInput([]));

        $this->assertResponseOk();

        Assert::assertCount(1, $contributors = $submittable->refresh()->contributors);
        $this->contributor = $contributors[0];
    }

    protected function createNewReferee($route, $routeParameters, $submittable): void
    {
        $this->route('PUT', $route, $routeParameters, $this->withInput([]));

        $this->assertResponseOk();

        $referees = $submittable->refresh()->referees;
        Assert::assertTrue(count($referees) >= 1);
        $this->referee = $referees[0];
    }

    public function updateContributor($route, $routeParameters)
    {
        $this->updateFieldValue(
            $route,
            array_merge($routeParameters, ['contributor' => $this->contributor->id]),
            $this->contributorFieldB,
            'new value B',
        );

        $this->contributor->refresh();

        Assert::assertEquals('new value B', $this->contributor->values[(string) $this->contributorFieldB->slug]);
    }

    public function updateRefereeField(string $route, array $routeParameters): void
    {
        $this->updateFieldValue(
            $route,
            array_merge($routeParameters, ['referee' => $this->referee->id]),
            $this->refereeFieldB,
            'new value B',
        );

        $this->referee->refresh();

        Assert::assertEquals('new value B', $this->referee->values[(string) $this->refereeFieldB->slug]);
    }

    public function updateReferee(string $route, array $routeParameters): void
    {
        $this->route(
            'PUT',
            $route,
            array_merge($routeParameters, ['referee' => $this->referee->id]),
            [
                'name' => $name = 'Yoda',
                'email' => $email = '<EMAIL>',
            ],
        )
            ->assertResponseOk()
            ->assertSessionHasNoErrors();

        $this->referee->refresh();

        Assert::assertEquals($name, $this->referee->name);
        Assert::assertEquals($email, $this->referee->email);
    }

    /**
     * @Given /^there are contributor fields$/
     */
    public function thereAreContributorFields()
    {
        $this->contributorFieldA = $this->muffin(Field::class, ['resource' => Field::RESOURCE_CONTRIBUTORS]);
        $this->contributorFieldB = $this->muffin(Field::class, ['resource' => Field::RESOURCE_CONTRIBUTORS]);
    }

    /**
     * @Given /^there is a referee tab$/
     */
    public function thereIsARefereeTab(): void
    {
        $this->muffin(Tab::class, ['type' => Tab::TYPE_REFEREES, 'form_id' => $this->entry->formId]);
    }

    /**
     * @Given /^there are referee fields$/
     */
    public function thereAreRefereeFields(): void
    {
        $this->referee = $this->muffin(Referee::class, ['submittable_id' => $this->entry->id]);
        $this->refereeFieldA = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES]);
        $this->refereeFieldB = $this->muffin(Field::class, ['resource' => Field::RESOURCE_REFEREES]);
    }

    /**
     * @Given /^I should be able to remove the contributor as an entrant$/
     */
    public function iShouldBeAbleToRemoveTheContributorAsAnEntrant()
    {
        $this->route('PUT', 'entry-form.entrant.remove-contributor',
            [
                'entry' => (string) $this->entry->slug,
                'contributor' => $this->contributor->id,
            ]
        );

        $this->assertResponseOk();

        Assert::assertEmpty($this->entry->refresh()->contributors);
    }

    /**
     * @Given /^I should be able to remove the referee as an entrant$/
     */
    public function iShouldBeAbleToRemoveTheRefereeAsAnEntrant(): void
    {
        $this->route('PUT', 'entry-form.entrant.remove-referee',
            [
                'entry' => (string) $this->entry->slug,
                'referee' => $this->referee->id,
            ]
        );

        $this->assertResponseOk();

        Assert::assertEmpty($this->entry->refresh()->referees);
    }

    /**
     * @Given /^I should be able to remove the contributor as a manager$/
     */
    public function iShouldBeAbleToRemoveTheContributorAsAManager()
    {
        $this->route('PUT', 'entry-form.manager.remove-contributor',
            [
                'entry' => (string) $this->entry->slug,
                'contributor' => $this->contributor->id,
            ]
        );

        $this->assertResponseOk();

        Assert::assertEmpty($this->entry->refresh()->contributors);
    }

    /**
     * @Given /^I should be able to remove the referee as a manager$/
     */
    public function iShouldBeAbleToRemoveTheRefereeAsAManager(): void
    {
        $this->route('PUT', 'entry-form.manager.remove-referee',
            [
                'entry' => (string) $this->entry->slug,
                'referee' => $this->referee->id,
            ]
        );

        $this->assertResponseOk();

        Assert::assertEmpty($this->entry->refresh()->referees);
    }

    /**
     * @Given /^price tags apply$/
     */
    public function priceTagsApply()
    {
        $order = $this->muffin(Order::class, ['user_id' => $this->user->id]);
        $match = $this->muffin(Price::class, ['type' => Price::TYPE_TAG]);

        $this->muffin(OrderItem::class, ['order_id' => $order->id, 'entry_id' => $this->entry->id]);

        app(TagManager::class)->tag($this->entry, ['tagPrice']);
        app(TagManager::class)->tag($match, ['tagPrice']);
    }

    /**
     * @given a referee tab exists
     */
    public function aRefereeTabExists()
    {
        $this->refereeTab = $this->muffin(Tab::class, ['type' => Tab::TYPE_REFEREES]);
    }

    /**
     * @Then I should be able to add a referee
     */
    public function iShouldBeAbleToAddAReferee()
    {
        $category = $this->muffin(Category::class);
        $chapterId = $category->chapters->first()->id;
        $this->round->chapters()->attach($chapterId);

        $title = 'Some entry - '.str_random();

        $postData = [
            'formId' => FormSelector::getId(),
            'title' => $title,
            'chapterId' => $chapterId,
            'categoryId' => $category->id,
            'values' => [],
            'links' => [],
            'referees' => [
                $this->refereeTab->id => [
                    [
                        'tabId' => $this->refereeTab->id,
                        'name' => 'RefereeName',
                        'email' => '<EMAIL>',
                        'fields' => [],
                    ],
                ],
            ],
            'updatedAt' => now(),
        ];

        $this->route('PUT', 'entry-form.entrant.autosave', ['entry' => $this->entry->slug], $this->withInput($postData), server: ['HTTP_Accept' => 'application/json']);

        $this->assertResponseOk();
        $this->assertResponseContains('referees');
        $this->assertTrue(count($this->response->json('referees')) === 1);
        $this->assertTrue($this->response->json('referees')[0]['name'] === 'RefereeName');
    }

    /**
     * @Then I should see the apply grant status button
     */
    public function iShouldSeeTheApplyGrantStatusButton()
    {
        $this->assertResponseContains(trans('grants.status.add'));
        $this->assertResponseContains(trans_elliptic('grants.status.add'));
    }

    /**
     * @Then Permission to delete entries is set to deny
     */
    public function permissionToDeleteEntrieIsSetToDeny()
    {
        Permission::where('role_id', $this->user->roles->first()->id)
            ->where('resource', 'EntriesOwner')
            ->where('action', 'delete')
            ->update(['mode' => 'deny']);
    }

    /**
     * @Then I should be able to delete the attachment
     */
    public function iShouldBeAbleToDeleteTheAttachment()
    {
        app()->instance(Factory::class, new Storage);
        $this->route(
            'DELETE',
            'entry.entrant.delete.attachment',
            ['entry' => $this->entry->slug, 'id' => $this->attachment->file->id]

        );

        $this->assertResponseOk();
    }

    /**
     * @Given the other entrant has an attachment
     */
    public function theOtherEntrantHasAnAttachment()
    {
        $tab = $this->muffin(Tab::class, ['type' => Tab::TYPE_ATTACHMENTS]);
        $this->newEntrantEntryAttachment = $this->muffin(Attachment::class, [
            'submittable_id' => $this->newEntrantEntry->id,
            'tab_id' => $tab->id,
        ]);
    }

    /**
     * @Then I should not be able to delete the other entrant attachment
     */
    public function iShouldNotBeAbleToDeleteTheOtherEntrantAttachment()
    {
        app()->instance(Factory::class, new Storage);
        $this->route(
            'DELETE',
            'entry.entrant.delete.attachment',
            ['entry' => $this->entry->slug, 'id' => $this->newEntrantEntryAttachment->file->id]

        );
        $this->assertResponseStatus(302);
        $this->assertSessionHasErrors(['error']);
    }

    /**
     * @Then I should see the schedule grant report button
     */
    public function iShouldSeeTheScheduleGrantReportButton()
    {
        $this->assertResponseContains(trans('form.report.schedule-report.title'));
    }

    /**
     * @Then I should not see the schedule grant report button
     */
    public function iShouldNotSeeTheScheduleGrantReportButton()
    {
        $this->assertResponseNotContains(trans('form.report.schedule-report.title'));
    }

    /**
     * @Then it should update the submittable state to firebase in eligibility page
     */
    public function itShouldUpdateTheFlagsSubmittableToFirebaseInEligibilityPage()
    {
        $collaboration = m::mock(Collaboration::class);
        $collaboration->shouldReceive('updateFlagSubmittable');

        $database = m::mock(Database::class);
        $database->shouldReceive('set')->once()->andReturn(true);
        app()->instance(Database::class, $database);

        $this->route('GET', 'entry.entrant.eligibility', [
            'entry' => (string) $this->entry->slug,
            'contentBlock' => (string) $this->ineligibleContentBlock->slug,
        ]);
    }

    /**
     * @Given A form exists with selected roles
     */
    public function aFormExistsWithoutSelectedRoles()
    {
        FormSelector::set($form = $this->muffin(Form::class, ['settings' => FormSettings::create(['roleVisibility' => ScopeOption::Select->value])]));
        $this->round = $this->muffin(Round::class, [
            'enabled' => true,
            'round_type' => Round::ROUND_TYPE_ENTRY,
            'form_id' => $form->id,
        ]);
    }

    /**
     * @Then I should not be able to start an entry
     */
    public function iShouldNotBeAbleToStartAnEntry()
    {
        $response = $this->route('GET', 'entry-form.entrant.start');
        $this->assertRedirectedToRoute('home');
        $response->assertSessionHas('message', trans('form.validation.invalid'));
    }

    /**
     * @Then I should not be able to save my entry
     */
    public function iShouldNotBeAbleToSaveMyEntry()
    {
        $category = $this->muffin(Category::class);
        $chapterId = $category->chapters->first()->id;

        $this->round->chapters()->attach($chapterId);

        $title = 'Some entry - '.str_random();

        $postData = [
            'formId' => FormSelector::getId(),
            'title' => $title,
            'categoryId' => $category->id,
            'chapterId' => $chapterId,
            'values' => [],
            'links' => [],
        ];

        $response = $this->route('POST', 'entry-form.entrant.startCreate', [], $postData);
        $this->assertResponseStatus(302);
        $response->assertSessionHas('message', trans('form.validation.invalid'));

    }
}
