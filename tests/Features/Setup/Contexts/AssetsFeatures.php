<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Files\Models\File;
use AwardForce\Modules\Theme\Models\Theme;
use AwardForce\Modules\Theme\Services\Favicons;
use Aws\CloudFront\CloudFrontClient;
use Illuminate\Support\Facades\Http;
use Mockery as m;

trait AssetsFeatures
{
    /**
     * @When /^I fetch the favicon asset from an account without custom favicon no content should be downloaded$/
     */
    public function iFetchTheFaviconAssetFromAnAccountWithoutCustomFaviconNoContentShouldBeDownloaded(): void
    {
        $this->route('GET', 'favicon-logo');

        $this->assertResponseStatus(204);
    }

    /**
     * @When /^I fetch the favicon asset from an account with custom favicon the favicon should be downloaded$/
     */
    public function iFetchTheFaviconAssetFromAnAccountWithCustomFaviconTheFaviconShouldBeDownloaded(): void
    {
        $favicon = m::mock(Favicons::class);
        $favicon->shouldReceive('getFaviconValue')->once()->andReturn('favicon.png');
        app()->instance(Favicons::class, $favicon);

        $response = $this->route('GET', 'favicon-logo');

        $this->assertResponseStatus(200);
        $response->assertHeader('Content-Disposition', 'attachment; filename=favicon');
    }

    /**
     * @Given /^I have a custom favicon/
     */
    public function iHaveACustomFavicon(): void
    {
        config()->set(['services.aws.cloudfront.'.current_account()->region.'.domain' => 'd1abcdefgh1234.cloudfront.net']);
        config()->set(['filesystems.disks.s3-'.current_account()->region.'.region' => 'ap-southeast-2']);

        $this->faviconUrl = 'https://d1abcdefgh1234.cloudfront.net/files/favicon.png';
        $cloudfrontClientMock = m::mock('overload:Aws\CloudFront\CloudFrontClient');
        $cloudfrontClientMock->shouldReceive('getSignedUrl')
            ->andReturn($this->faviconUrl);
        app()->instance(CloudFrontClient::class, $cloudfrontClientMock);

        $file = $this->muffin(File::class, [
            'resource' => 'Theme',
            'resource_id' => null,
            'file' => 'files/X/8/x/B/2/D/e62E0f559P/file.png',
            'original' => 'favicon.png',
            'size' => 12345,
            'mime' => 'image/png',
        ]);

        Theme::create([
            'key' => 'favicon',
            'value' => $file->id,
            'account_id' => current_account_id(),
        ]);
    }

    /**
     * @Then /^I fetch the favicon ico without error/
     */
    public function iFetchTheFaviconIcoWithoutError(): void
    {
        Http::fake([
            $this->faviconUrl => Http::response('BINARY', 200),
        ]);

        $response = $this->route('GET', 'favicon.ico');
        $response->streamedContent();

        $response->assertStatus(200);
        $response->assertHeader('Content-Disposition', 'attachment; filename=favicon.ico');
        $response->assertStreamed();
        $response->assertStreamedContent('BINARY');
        Http::assertSent(fn($req) => $req->url() === $this->faviconUrl);
    }
}
