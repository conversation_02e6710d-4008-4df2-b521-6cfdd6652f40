<?php

namespace Features\Setup\Contexts;

use AwardForce\Modules\Categories\Models\Category;
use AwardForce\Modules\Forms\Forms\Services\Facades\FormSelector;
use AwardForce\Modules\Notifications\Data\Notification;
use PHPUnit\Framework\Assert as PHPUnit;

trait NotificationFeatures
{
    private $notifications;

    /**
     * @When a :trigger notification exists
     */
    public function aNotificationExists($trigger): void
    {
        $this->notification = $this->muffin(Notification::class, [
            'trigger' => $trigger,
        ]);
    }

    /**
     * @Given /^a category exists$/
     */
    public function aCategoryExists(): void
    {
        $this->category = $this->muffin(Category::class);
    }

    /**
     * @Given there are several notifications in the system
     */
    public function severalNotificationsInTheSystem(): void
    {
        $this->notifications = $this->muffins(mt_rand(1, 10), Notification::class);
    }

    /**
     * @Then I should be able to see a list of notifications
     */
    public function iShouldBeAbleToSeeAListOfNotifications(): void
    {
        $this->route('GET', 'notification.index', ['per_page' => 100]);

        $this->assertResponseOk();
        $this->assertResponseContains($this->notifications[0]->subject);
        $this->assertResponseContains($this->notifications[0]->description);
    }

    /**
     * @Then I should be able to create a notifications
     */
    public function iShouldBeAbleToCreateANotification(): void
    {
        $this->route('POST', 'notification.create', $this->notificationData());

        $this->assertNoErrors();
        $this->assertRedirectedTo(route('notification.index'));

        $this->notification = Notification::orderBy('id', 'desc')->first();
    }

    /**
     * @Then I should be able to create a notifications with form, category and score set
     */
    public function iShouldBeAbleToCreateANotificationWithFormCategoryAndScoreSet(): void
    {
        $this->route('POST', 'notification.create', [
            'seasonId' => current_account()->activeSeason()->id,
            'trigger' => 'assignment.judging.completed',
            'legalBasis' => 'consent',
            'formOption' => 'select',
            'forms' => [(string) FormSelector::get()->slug],
            'categoryOption' => 'select',
            'categories' => [(string) $this->category->slug],
            'scoreSetOption' => 'select',
            'scoreSets' => [(string) $this->scoreSet->slug],
            'recipientOption' => 'user',
            'translated' => [
                'subject' => [
                    'en_GB' => 'Test subject',
                ],
                'body' => [
                    'en_GB' => 'Test body',
                ],
                'description' => [
                    'en_GB' => 'Test description',
                ],
            ],
        ]);

        $this->assertNoErrors();
        $this->assertRedirectedTo(route('notification.index'));

        $this->notification = Notification::orderBy('id', 'desc')->first();
    }

    /**
     * @Then I should be able to update the notification with form, category and score set
     */
    public function iShouldBeAbleToUpdateNotificationWithFormCategoryAndScoreSet(): void
    {
        $this->route('PUT', 'notification.update', $this->notification, [
            'updatedAt' => now(),
            'trigger' => 'assignment.judging.completed',
            'legalBasis' => 'consent',
            'formOption' => 'select',
            'forms' => [(string) FormSelector::get()->slug],
            'categoryOption' => 'select',
            'categories' => [(string) $this->category->slug],
            'scoreSetOption' => 'select',
            'scoreSets' => [(string) $this->scoreSet->slug],
            'recipientOption' => 'user',
            'translated' => [
                'subject' => [
                    'en_GB' => 'Test subject',
                ],
                'body' => [
                    'en_GB' => 'Test body',
                ],
                'description' => [
                    'en_GB' => 'Test description',
                ],
            ],
        ]);

        $this->assertNoErrors();
        $this->assertRedirectedTo(route('notification.index'));
    }

    /**
     * @Given /^I should be able to see the score set in the notification$/
     */
    public function iShouldBeAbleToSeeTheScoreSetInTheNotification(): void
    {
        PHPUnit::assertEquals([$this->scoreSet->id], $this->notification->scoreSets->pluck('id')->toArray());
    }

    /**
     * @Given /^I should be able to see the description in the notification$/
     */
    public function iShouldBeAbleToSeeTheDescriptionInTheNotification()
    {
        PHPUnit::assertEquals('Test description', translate($this->notification, 'en_GB')->description);
    }

    /**
     * @Given /^I should be able to see the form in the notification$/
     */
    public function iShouldBeAbleToSeeTheFormInTheNotification(): void
    {
        PHPUnit::assertEquals([FormSelector::getId()], $this->notification->forms->pluck('id')->toArray());
    }

    /**
     * @Given /^I should be able to see the category in the notification$/
     */
    public function iShouldBeAbleToSeeTheCategoryInTheNotification(): void
    {
        PHPUnit::assertEquals([$this->category->id], $this->notification->categories->pluck('id')->toArray());
    }

    /**
     * @Then I should not be able to save notification without mandatory email addresses
     */
    public function iShouldNotBeAbleToSaveNotificationWithoutMandatoryEmailAddresses(): void
    {
        $this->route('POST', 'notification.create', array_merge($this->notificationData(), [
            'recipientOption' => 'recipients',
        ]));

        $this->assertSessionHasErrors([
            'recipients' => trans('validation.required', ['attribute' => 'recipients']),
        ]);
    }

    /**
     * @Then I should not be able to save notification with an invalid email address
     */
    public function iShouldNotBeAbleToSaveNotificationWithAnInvalidEmailAddress(): void
    {
        $this->route('POST', 'notification.create', array_merge($this->notificationData(), [
            'recipientOption' => 'recipients',
            'recipients' => 'thisIsNotValidMail@',
        ]));

        $this->assertSessionHasErrors([
            'recipients' => trans('validation.multiple_emails', ['attribute' => 'recipients']),
        ]);
    }

    /**
     * @Then I should not be able to save notification with a mobile number
     */
    public function iShouldNotBeAbleToSaveNotificationWithAMobileNumber(): void
    {
        $this->route('POST', 'notification.create', array_merge([
            'recipientOption' => 'recipients',
            'recipients' => '+54 9 11 23323322',
        ]));

        $this->assertSessionHasErrors([
            'recipients' => trans('validation.multiple_emails', ['attribute' => 'recipients']),
        ]);
    }

    /**
     * @Then I should be able to create a notification with a valid email address
     */
    public function iShouldBeAbleToCreateANotificationWithAValidEmailAddress(): void
    {
        $this->route('POST', 'notification.create', array_merge($this->notificationData(), [
            'recipientOption' => 'recipients',
            'recipients' => '<EMAIL>,<EMAIL>',
        ]));

        $this->assertNoErrors();
        $this->assertRedirectedTo(route('notification.index'));
    }

    /**
     *@Then I should be able to create a notification without recipient parameters
     */
    public function iShouldBeAbleToCreateANotificationWithoutRecipientParameters(): void
    {
        $notificationData = array_merge($this->notificationData(), [
            'trigger' => 'review.stage.started', // This trigger does not require recipient parameters
        ]);
        unset($notificationData['recipientOption']);

        $this->route('POST', 'notification.create', $notificationData);

        $this->assertNoErrors();
        $this->assertRedirectedTo(route('notification.index'));
    }

    private function notificationData(): array
    {
        return [
            'seasonId' => current_account()->activeSeason()->id,
            'trigger' => 'entry.submitted',
            'legalBasis' => 'consent',
            'recipientOption' => 'user',
            'translated' => [
                'subject' => [
                    'en_GB' => 'Test subject',
                ],
                'body' => [
                    'en_GB' => 'Test body',
                ],
                'description' => [
                    'en_GB' => 'Test description',
                ],
            ],
        ];
    }
}
