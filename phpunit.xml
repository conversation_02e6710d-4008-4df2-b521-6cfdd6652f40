<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.4/phpunit.xsd" backupGlobals="false" bootstrap="bootstrap/testing.php" colors="true" beStrictAboutTestsThatDoNotTestAnything="false" processIsolation="false" stopOnFailure="false" stopOnError="false" cacheDirectory=".phpunit.cache" backupStaticProperties="false">
  <coverage/>
  <testsuites>
    <testsuite name="accounts">
      <directory suffix=".test.php">./app/Modules/Accounts/</directory>
    </testsuite>
    <testsuite name="ai-agents">
      <directory suffix=".test.php">./app/Modules/AIAgents/</directory>
    </testsuite>
    <testsuite name="api">
      <directory suffix=".test.php">./app/Modules/Api/</directory>
    </testsuite>
    <testsuite name="assignments">
      <directory suffix=".test.php">./app/Modules/Assignments/</directory>
    </testsuite>
    <testsuite name="authentication">
      <directory suffix=".test.php">./app/Modules/Authentication/</directory>
    </testsuite>
    <testsuite name="billing">
      <directory suffix=".test.php">./app/Modules/Billing/</directory>
    </testsuite>
    <testsuite name="broadcasts">
      <directory suffix=".test.php">./app/Modules/Broadcasts/</directory>
    </testsuite>
    <testsuite name="console">
      <directory suffix=".test.php">./app/Console/</directory>
    </testsuite>
    <testsuite name="controllers">
      <directory suffix=".test.php">./app/Http/Controllers/</directory>
    </testsuite>
    <testsuite name="dashboard">
      <directory suffix=".test.php">./app/Modules/Dashboard/</directory>
    </testsuite>
    <testsuite name="downloads">
      <directory suffix=".test.php">./app/Modules/Downloads/</directory>
    </testsuite>
    <testsuite name="comments">
      <directory suffix=".test.php">./app/Modules/Comments/</directory>
    </testsuite>
    <testsuite name="ecommerce">
      <directory suffix=".test.php">./app/Modules/Ecommerce/</directory>
    </testsuite>
    <testsuite name="entries">
      <directory suffix=".test.php">./app/Modules/Entries/</directory>
    </testsuite>
    <testsuite name="eventSourcing">
      <directory suffix=".test.php">./app/Library/Database/EventSourcing/</directory>
      <directory suffix=".test.php">./app/Library/EventSourcing/</directory>
    </testsuite>
    <testsuite name="exports">
      <directory suffix=".test.php">./app/Modules/Exports/</directory>
    </testsuite>
    <testsuite name="features">
      <directory suffix=".test.php">./app/Modules/Features/</directory>
    </testsuite>
    <testsuite name="files">
      <directory suffix=".test.php">./app/Modules/Files/</directory>
    </testsuite>
    <testsuite name="forms">
      <directory suffix=".test.php">./app/Modules/Forms/</directory>
    </testsuite>
    <testsuite name="funding">
      <directory suffix=".test.php">./app/Modules/Funding/</directory>
    </testsuite>
    <testsuite name="grant-reports">
      <directory suffix=".test.php">./app/Modules/GrantReports/</directory>
    </testsuite>
    <testsuite name="grants">
      <directory suffix=".test.php">./app/Modules/Grants/</directory>
    </testsuite>
    <testsuite name="holocron">
      <directory suffix=".test.php">./app/Modules/Holocron/</directory>
    </testsuite>
    <testsuite name="identifier">
      <directory suffix=".test.php">./app/Library/Identifier/</directory>
    </testsuite>
    <testsuite name="identity">
      <directory suffix=".test.php">./app/Modules/Identity/</directory>
    </testsuite>
    <testsuite name="judging">
      <directory suffix=".test.php">./app/Modules/Judging/</directory>
    </testsuite>
    <testsuite name="library">
      <directory suffix="Test.php">./tests/Tests/Library/</directory>
      <directory suffix=".test.php">./app/Library/</directory>
    </testsuite>
    <testsuite name="menu">
      <directory suffix=".test.php">./app/Modules/Menu/</directory>
    </testsuite>
    <testsuite name="middleware">
      <directory suffix=".test.php">./app/Http/Middleware/</directory>
    </testsuite>
    <testsuite name="new-dashboard">
      <directory suffix=".test.php">./app/Modules/NewDashboard/</directory>
    </testsuite>
    <testsuite name="notifications">
      <directory suffix=".test.php">./app/Modules/Notifications/</directory>
    </testsuite>
    <testsuite name="organisation-members">
      <directory suffix=".test.php">./app/Modules/Organisations/Members/</directory>
    </testsuite>
    <testsuite name="organisations">
      <directory suffix=".test.php">./app/Modules/Organisations/Organisations</directory>
    </testsuite>
    <testsuite name="payments">
      <directory suffix=".test.php">./app/Modules/Payments/</directory>
    </testsuite>
    <testsuite name="referees">
      <directory suffix=".test.php">./app/Modules/Referees/</directory>
    </testsuite>
    <testsuite name="reports">
      <directory suffix=".test.php">./app/Modules/Reports/</directory>
    </testsuite>
    <testsuite name="requests">
      <directory suffix=".test.php">./app/Http/Requests/</directory>
    </testsuite>
    <testsuite name="review-flow">
      <directory suffix=".test.php">./app/Modules/ReviewFlow/</directory>
    </testsuite>
    <testsuite name="scoreSets">
      <directory suffix=".test.php">./app/Modules/ScoreSets/</directory>
    </testsuite>
    <testsuite name="scoring-criteria">
      <directory suffix=".test.php">./app/Modules/ScoringCriteria/</directory>
    </testsuite>
    <testsuite name="seasons">
      <directory suffix=".test.php">./app/Modules/Seasons/</directory>
    </testsuite>
    <testsuite name="settings">
      <directory suffix=".test.php">./app/Modules/Settings/</directory>
    </testsuite>
    <testsuite name="tags">
      <directory suffix=".test.php">./app/Modules/Tags/</directory>
    </testsuite>
    <testsuite name="webhooks">
      <directory suffix=".test.php">./app/Modules/Webhooks/</directory>
    </testsuite>
    <testsuite name="no-modules">
      <directory suffix="Test.php">./tests/Tests/Auth/</directory>
      <directory suffix="Test.php">./tests/Tests/Console/</directory>
      <directory suffix="Test.php">./tests/Tests/Factories/</directory>
      <directory suffix="Test.php">./tests/Tests/Http/</directory>
      <directory suffix="Test.php">./tests/Tests/Migrations/</directory>
      <directory suffix="Test.php">./tests/Tests/Provisioning/</directory>
      <directory suffix="Test.php">./tests/Tests/Resources/</directory>
      <directory suffix="Test.php">./tests/Tests/Support/</directory>
      <directory suffix="Test.php">./tests/Tests/Swaps/</directory>
      <directory suffix="Test.php">./tests/Tests/Views/</directory>
    </testsuite>
    <testsuite name="modules">
      <directory suffix="Test.php">./app/Modules/*</directory>
      <directory suffix="Test.php">./tests/Tests/Modules/</directory>
      <directory suffix="TestAbstract.php">./tests/Tests/Modules/</directory>
      <exclude>./tests/Tests/Modules/Audit/</exclude>
    </testsuite>
  </testsuites>
  <php>
    <env name="AF_REGIONS" value="au,eu,us,ca"/>
    <env name="APP_ENV" value="testing"/>
    <env name="APP_KEY" value="ABCDEFGHIJKLMNOPQRSTUVQXYZ123456"/>
    <env name="APP_KEY_NEW" value="ABCDEFGHIJKLMNOPQRSTUVQXYZ123456"/>
    <env name="MAILGUN_AF_KEY" value="ABCDEFGHIJKLMNOPQRSTUVQXYZ123456"/>
    <env name="MAILGUN_GG_KEY" value="ABCDEFGHIJKLMNOPQRSTUVQXYZ123456"/>
    <env name="MAILGUN_AF_DOMAIN" value="awardforce.local"/>
    <env name="MAILGUN_GG_DOMAIN" value="goodgrants.local"/>
    <env name="API_DOMAIN" value="api.awardforce.local"/>
    <env name="DB_TESTING_CONNECTION" value="testing"/>
    <env name="ENABLE_MIDDLEWARE" value="true"/>
    <env name="CACHE_DRIVER" value="array"/>
    <env name="IMGIX_DOMAIN_IRELAND" value="ireland.net"/>
    <env name="IMGIX_DOMAIN_CALIFORNIA" value="california.net"/>
    <env name="IMGIX_DOMAIN_SYDNEY" value="sydney.net"/>
    <env name="IMGIX_DOMAIN_AU" value="au.net"/>
    <env name="IMGIX_DOMAIN_EU" value="eu.net"/>
    <env name="IMGIX_DOMAIN_US" value="us.net"/>
    <env name="S3_REGION_AU" value="au"/>
    <env name="S3_REGION_EU" value="eu"/>
    <env name="S3_REGION_US" value="us"/>
    <env name="S3_REGION_CA" value="ca"/>
    <env name="QUEUE_DRIVER" value="sync"/>
    <env name="WHITE_LABEL_DOMAINS" value="staging.awardsplatform.com,awardsplatform.com,grantplatform.com"/>
    <env name="WHITE_LABEL_DOMAINS_AWARDFORCE" value="staging.awardsplatform.com,awardsplatform.com"/>
    <env name="WHITE_LABEL_DOMAINS_GOODGRANTS" value="grantplatform.com"/>
    <env name="IMGIX_HOLOCRON_DOMAIN_IRELAND" value="example.net"/>
    <env name="IMGIX_HOLOCRON_SECURE_KEY_IRELAND" value=""/>
    <env name="IDENTITY_DB_DATABASE" value=":memory:"/>
    <env name="IDENTITY_DB_DRIVER" value="sqlite"/>
    <env name="CHARGEBEE_CF_SITE" value="test-site"/>
    <env name="CHARGEBEE_CF_FULL_ACCESS_KEY" value="test_key"/>
    <env name="CHARGEBEE_CF_PUBLISHABLE_KEY" value="test_key"/>
    <env name="CHARGEBEE_AF_SITE" value="test-site"/>
    <env name="CHARGEBEE_AF_FULL_ACCESS_KEY" value="test_key"/>
    <env name="CHARGEBEE_AF_PUBLISHABLE_KEY" value="test_key"/>
  </php>
  <source>
    <include>
      <directory suffix=".php">./app/</directory>
    </include>
    <exclude>
      <directory suffix="Command.php">./app/Modules/</directory>
      <directory suffix="CommandHandler.php">./app/Modules/</directory>
      <directory suffix="ServiceProvider.php">./app/</directory>
      <directory suffix=".php">./app/Console/</directory>
      <directory suffix=".php">./app/Http/</directory>
      <directory suffix=".php">./app/Library/Imports/</directory>
      <directory suffix=".php">./app/Modules/*/Commands/</directory>
    </exclude>
  </source>
</phpunit>
