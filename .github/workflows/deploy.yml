name: Deploy

on:
  deployment:
  push:
    branches:
      - staging

jobs:
  jsunittest:
    name: JS Unit Test
    runs-on: ubuntu-24.04
    container:
      image: ghcr.io/tectonic/jsunittest:the-force
      options: --user 1001 --privileged
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    steps:
    - name: work around permission issue
      run: git config --global --add safe.directory /__w/the-force/the-force
    - name: Checkout
      uses: actions/checkout@v4
      with:
        persist-credentials: false
      if: github.event.deployment.task != 'no'

    - name: Reconfigure git to use HTTPS authentication
      uses: GuillaumeFalourd/SSH-to-HTTPS@v1
      with:
        github_token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    - name: JS test
      env:
        npm_config_cache: /home/<USER>/npm-cache
        PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
      shell: bash
      run: |
        set -o pipefail
        user=$(id -u)
        echo $user
        sudo rm -rf ~/.npm
        sudo cp -r /root/.npm ~/
        sudo chown -R $user:$user ~/.npm
        export NODE_OPTIONS=--max_old_space_size=4096
        echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
        echo "//npm.pqina.nl/:_authToken=${PQINA_NPM_TOKEN}" >> .npmrc
        NODE_ENV=development npm ci
        npm run test 2>&1 | tee -a js_test.txt
      timeout-minutes: 30  # This job will fail if it takes longer than 30 minutes
      if: github.event.deployment.task != 'no'

    - uses: actions/upload-artifact@v4
      with:
        path: js_test.txt
        name: test_output
      if: always() && github.event.deployment.task != 'no'

  phpunittest:
    name: PHP Unit
    container:
      image: ghcr.io/tectonic/phpunittest:php-8-3
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    runs-on: ubuntu-20.04-core08-php
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: "tectonic123"
        ports:
          - 3306:3306
        options: >-
          --user 0
          --health-cmd="mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch-oss:6.8.22
        env:
          discovery.type: "single-node"
        ports:
        - 9200:9200
        options: >-
          --user 0
          --health-cmd "curl http://localhost:9200/_cluster/health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 10
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      if: github.event.deployment.task != 'no'

    - name: Get Composer Cache Directory
      id: composer-cache
      run: |
        echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      if: github.event.deployment.task != 'no'

    - name: Composer cache Restore
      uses: actions/cache@v4
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-composer-
      if: github.event.deployment.task != 'no'

    - name: Run composer install
      run: |
        php -v
        jq '.config["github-oauth"]["github.com"] = "${{ secrets.GIT_TECHDEPLOY_TOKEN }}"' composer.json > composer.temp.json
        mv composer.temp.json composer.json
        composer install --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose
      if: github.event.deployment.task != 'no'

    - name: Setup MySQL
      run: |
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists build"
        mysql -hmysql -uroot -ptectonic123 -e "create database build"
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_1"
        mysql -hmysql -uroot -ptectonic123 -e "create database uild_1"
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_2"
        mysql -hmysql -uroot -ptectonic123 -e "create database uild_2"
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_3"
        mysql -hmysql -uroot -ptectonic123 -e "create database uild_3"
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_4"
        mysql -hmysql -uroot -ptectonic123 -e "create database uild_4"
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_5"
        mysql -hmysql -uroot -ptectonic123 -e "create database uild_5"
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_6"
        mysql -hmysql -uroot -ptectonic123 -e "create database uild_6"
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_7"
        mysql -hmysql -uroot -ptectonic123 -e "create database uild_7"
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists uild_8"
        mysql -hmysql -uroot -ptectonic123 -e "create database uild_8"
      if: github.event.deployment.task != 'no'

    - name: Run Unit test
      shell: bash
      run: |
        set -o pipefail
        mkdir -p /tmp/php-opcache
        echo "RUNNERNAME=$(echo $PARALLEL_TEST_RUNNER|sed 's@/@@')" >> $GITHUB_ENV
        ant clean
        ant prepare
        php artisan test --env=testing --parallel -p8 --colors -c phpunit.xml | tee -a ant_php_test.txt
        rm -rf /tmp/php-opcache
      timeout-minutes: 30  # This job will fail if it takes longer than 30 minutes
      env:
        PARALLEL_TEST_RUNNER: ${{ matrix.parallel-runner }}
        APP_KEY_NEW: ${{secrets.APP_KEY_NEW}}
        TESTING_DB_HOST: mysql
        TESTING_DB_USERNAME: root
        TESTING_DB_PASSWORD: tectonic123
        TESTING_DB_DATABASE: build
        DB_CONNECTIONS: mysql://root:tectonic123@mysql:3306/build
        DB_DEFAULT_CONNECTION: build
        TESTING_DB_CONNECTION: mysql://root:tectonic123@mysql:3306/build
        API_DOMAIN: api.awardforce.app
        APP_ENV: testing
        ELASTIC_HOST: elasticsearch
      if: github.event.deployment.task != 'no'

    - name: Upload php main tests
      uses: actions/upload-artifact@v4
      with:
        path: ant_php_test.txt
        name: php_main_output
      if: always() && github.event.deployment.task != 'no'

    - name: Upload the-force main logs
      uses: actions/upload-artifact@v4
      with:
        path: storage/logs
        name: theforce_storage_logs_php
      if: always() && github.event.deployment.task != 'no'

  phpothertest:
    name: PHP Other
    container:
      image: ghcr.io/tectonic/phpunittest:php-8-3
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    runs-on: ubuntu-24.04
    env:
      TESTING_DB_HOST: mysql
      TESTING_DB_USERNAME: root
      TESTING_DB_PASSWORD: tectonic123
      TESTING_DB_DATABASE: build
      DB_CONNECTIONS: mysql://root:tectonic123@mysql:3306/build
      TESTING_DB_CONNECTION: mysql://root:tectonic123@mysql:3306/build
      API_DOMAIN: api.awardforce.app
      APP_ENV: testing
      ELASTIC_HOST: elasticsearch
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: "tectonic123"
        ports:
          - 3306:3306
        options: >-
          --user 0
          --health-cmd="mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch-oss:6.8.22
        env:
          discovery.type: "single-node"
        ports:
        - 9200:9200
        options: >-
          --user 0
          --health-cmd "curl http://localhost:9200/_cluster/health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 10
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      if: github.event.deployment.task != 'no'

    - name: Get Composer Cache Directory
      id: composer-cache
      run: |
        echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      if: github.event.deployment.task != 'no'

    - name: Composer cache Restore
      uses: actions/cache@v4
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-composer-
      if: github.event.deployment.task != 'no'

    - name: Run composer install
      run: |
        php -v
        jq '.config["github-oauth"]["github.com"] = "${{ secrets.GIT_TECHDEPLOY_TOKEN }}"' composer.json > composer.temp.json
        mv composer.temp.json composer.json
        composer install --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose
      if: github.event.deployment.task != 'no'

    - name: Setup MySQL
      run: |
        mysql -hmysql -uroot -ptectonic123 -e "drop database if exists build"
        mysql -hmysql -uroot -ptectonic123 -e "create database build"
      if: github.event.deployment.task != 'no'

    - name: Run PHPUnit Other test
      shell: bash
      run: |
        set -o pipefail
        mkdir -p /tmp/php-opcache
        ant unit-other 2>&1 | tee -a ant_test_php_other.txt
        rm -rf /tmp/php-opcache
      timeout-minutes: 30  # This job will fail if it takes longer than 30 minutes
      env:
        APP_KEY_NEW: ${{secrets.APP_KEY_NEW}}
      if: github.event.deployment.task != 'no'

    - name: Upload php other tests
      uses: actions/upload-artifact@v4
      with:
        path: ant_test_php_other.txt
        name: php_other_output
      if: always() && github.event.deployment.task != 'no'

    - name: Upload the-force other logs
      uses: actions/upload-artifact@v4
      with:
        path: storage/logs
        name: theforce_storage_logs_php_other
      if: always() && github.event.deployment.task != 'no'

  behattest:
    name: Behat Tests
    runs-on: ubuntu-20.04-core08
    env:
      TESTING_DB_HOST: 127.0.0.1
      TESTING_DB_USERNAME: root
      TESTING_DB_PASSWORD: tectonic123
      TESTING_DB_DATABASE: build
      DB_CONNECTIONS: mysql://root:tectonic123@127.0.0.1:3306/build
      TESTING_DB_CONNECTION: mysql://root:tectonic123@127.0.0.1:3306/build
      API_DOMAIN: api.awardforce.app
      ELASTIC_HOST: 127.0.0.1
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: "tectonic123"
        ports:
          - 3306:3306
        options: >-
          --user 0
          --health-cmd="mysqladmin ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      elasticsearch:
        image: docker.elastic.co/elasticsearch/elasticsearch-oss:6.8.22
        env:
          discovery.type: "single-node"
        ports:
        - 9200:9200
        options: >-
          --user 0
          --health-cmd "curl http://localhost:9200/_cluster/health"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 10
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      if: github.event.deployment.task != 'no'

    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: '8.3'
        extensions: bcmath, :php-psr, calendar, Core, ctype, curl, date, dom, exif, FFI, fileinfo, filter, ftp, gd, gettext, hash, iconv, intl, json, libxml, mbstring, mysqli, mysqlnd, openssl, pcntl, pcre, PDO, pdo_mysql, Phar, posix, readline, Reflection, session, shmop, SimpleXML, soap, sockets, sodium, SPL, standard, sysvmsg, sysvsem, sysvshm, tokenizer, xml, xmlreader, xmlwriter, xsl, zip, zlib, grpc, rdkafka
        coverage: none
        tools: composer:2.2.0
      if: github.event.deployment.task != 'no'

    - name: Get Composer Cache Directory
      id: composer-cache
      run: |
        echo "dir=$(composer config cache-files-dir)" >> $GITHUB_OUTPUT
      if: github.event.deployment.task != 'no'

    - name: Composer cache Restore
      uses: actions/cache@v4
      with:
        path: ${{ steps.composer-cache.outputs.dir }}
        key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
        restore-keys: |
          ${{ runner.os }}-composer-
      if: github.event.deployment.task != 'no'

    - name: Run composer install
      run: |
        php -v
        jq '.config["github-oauth"]["github.com"] = "${{ secrets.GIT_TECHDEPLOY_TOKEN }}"' composer.json > composer.temp.json
        mv composer.temp.json composer.json
        composer install --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose
      if: github.event.deployment.task != 'no'

    - name: Setup MySQL
      run: |
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_1"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_1"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_2"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_2"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_3"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_3"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_4"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_4"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_5"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_5"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_6"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_6"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_7"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_7"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "drop database if exists build_8"
        mysql -h127.0.0.1 -uroot -ptectonic123 -e "create database build_8"
      if: github.event.deployment.task != 'no'

    - name: Run Behat test
      shell: bash
      run: |
        set -o pipefail
        mkdir -p /tmp/php-opcache
        ant behavioral 2>&1 | tee -a ant_test_behat.txt
        rm -rf /tmp/php-opcache
      timeout-minutes: 30  # This job will fail if it takes longer than 30 minutes
      if: github.event.deployment.task != 'no'

    - name: Upload behat tests
      uses: actions/upload-artifact@v4
      with:
        path: ant_test_behat.txt
        name: test_output_behat
      if: always() && github.event.deployment.task != 'no'

    - name: Upload the-force logs
      uses: actions/upload-artifact@v4
      with:
        path: storage/logs
        name: theforce_storage_logs_behat
      if: always() && github.event.deployment.task != 'no'

  upload:
    container:
      image: ghcr.io/tectonic/github-upload-8:the-force-8-3
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    name: Upload code
    runs-on: ubuntu-20.04-core08-upload
    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      S3_ACCESS_KEY: ${{ secrets.AWS_ACCESS_KEY_ID }}
      S3_SECRET_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}

    steps:
    - name: work around permission issue
      run: git config --global --add safe.directory /__w/the-force/the-force
    - name: Checkout
      uses: actions/checkout@v4
      with:
        persist-credentials: false

    - name: Create deploy and set deploy status - auto
      uses: ./.github/create-deployment
      id: deployment-auto
      with:
        ref: ${{ github.ref }}
        token: ${{ github.token }}
        sha: ${{ github.sha }}
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Set deployment status to in_progress - k8sslack
      uses: chrnorm/deployment-status@v2
      with:
        state: 'in_progress'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        token: '${{ github.token }}'
        deployment-id: ${{ github.event.deployment.id }}
      if: github.event.deployment.payload.deployment == 'yes'

    - run: echo "${{ steps.deployment-auto.outputs.deployid }}" > artifact
      if: github.event.deployment.payload.deployment != 'yes'

    - uses: actions/upload-artifact@v4
      with:
        path: artifact
        name: deployid
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Setup SSH
      run: |
        echo "$SSH_KEY" > /root/.ssh/id_rsa
        chmod 400 /root/.ssh/id_rsa
      shell: bash
      env:
        SSH_KEY: ${{secrets.SSH_KEY}}

    - name: Reconfigure git to use HTTPS authentication
      uses: GuillaumeFalourd/SSH-to-HTTPS@v1
      with:
        github_token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    - name: Setup NPM - staging
      env:
        npm_config_cache: /home/<USER>/npm-cache
        PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
        CKEDITOR_LICENSE_KEY: ${{ secrets.CKEDITOR_LICENSE_KEY }}
      run: |
        cp -r /root/.npm ~/
        echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
        echo "//npm.pqina.nl/:_authToken=${PQINA_NPM_TOKEN}" >> .npmrc
        npm ci --production
        npm run staging
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Setup NPM - UAT
      env:
        npm_config_cache: /home/<USER>/npm-cache
        PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
        CKEDITOR_LICENSE_KEY: ${{ secrets.CKEDITOR_LICENSE_KEY }}
      run: |
        cp -r /root/.npm ~/
        echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
        echo "//npm.pqina.nl/:_authToken=${PQINA_NPM_TOKEN}" >> .npmrc
        npm ci --production
        npm run uat
      if: github.event.deployment.environment == 'uat'

    - name: Setup NPM - prod
      env:
        npm_config_cache: /home/<USER>/npm-cache
        PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
        CKEDITOR_LICENSE_KEY: ${{ secrets.CKEDITOR_LICENSE_KEY }}
      run: |
        cp -r /root/.npm ~/
        echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
        echo "//npm.pqina.nl/:_authToken=${PQINA_NPM_TOKEN}" >> .npmrc
        npm ci --production
        npm run prod
      if: github.event.deployment.environment == 'production'

    - name: Setup NPM - pentest
      env:
        npm_config_cache: /home/<USER>/npm-cache
        PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
        CKEDITOR_LICENSE_KEY: ${{ secrets.CKEDITOR_LICENSE_KEY }}
      run: |
        cp -r /root/.npm ~/
        echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
        echo "//npm.pqina.nl/:_authToken=${PQINA_NPM_TOKEN}" >> .npmrc
        npm ci --production
        npm run pentest
      if: github.event.deployment.environment == 'pt'

    - name: Setup NPM - php8
      env:
        npm_config_cache: /home/<USER>/npm-cache
        PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
        CKEDITOR_LICENSE_KEY: ${{ secrets.CKEDITOR_LICENSE_KEY }}
      run: |
        cp -r /root/.npm ~/
        echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
        echo "//npm.pqina.nl/:_authToken=${PQINA_NPM_TOKEN}" >> .npmrc
        npm ci --production
        npm run staging
      if: github.event.deployment.environment == 'stage2'

    - name: Setup NPM - adhoc
      env:
        npm_config_cache: /home/<USER>/npm-cache
        PQINA_NPM_TOKEN: ${{ secrets.PQINA_NPM_TOKEN }}
        CKEDITOR_LICENSE_KEY: ${{ secrets.CKEDITOR_LICENSE_KEY }}
      run: |
        cp -r /root/.npm ~/
        echo "@pqina:registry=https://npm.pqina.nl/" >> .npmrc
        echo "//npm.pqina.nl/:_authToken=${PQINA_NPM_TOKEN}" >> .npmrc
        npm ci --production
        npm run staging
      if: github.event.deployment.environment == 'adhoc'

    - name: Run composer install
      env:
        COMPOSER_CACHE_DIR: /home/<USER>/composer-cache
      run: |
        php -v
        jq '.config["github-oauth"]["github.com"] = "${{ secrets.GIT_TECHDEPLOY_TOKEN }}"' composer.json > composer.temp.json
        mv composer.temp.json composer.json
        composer install --no-dev --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose
      if: github.event.deployment.environment != 'uat' 

    - name: Run composer install - UAT
      env:
        COMPOSER_CACHE_DIR: /home/<USER>/composer-cache
      run: |
        php -v
        jq '.config["github-oauth"]["github.com"] = "${{ secrets.GIT_TECHDEPLOY_TOKEN }}"' composer.json > composer.temp.json
        mv composer.temp.json composer.json
        composer install --optimize-autoloader --no-interaction --no-progress --prefer-dist --verbose
      if: github.event.deployment.environment == 'uat'

    - name: Deploy assets - staging
      run: |
        echo "${{ github.sha }}" > .git_commit_nr
        echo "Build: ${{ github.run_number }}" > .git_fetch
        echo "Job: af4" >> .git_fetch
        echo "Branch: ${GIT_BRANCH}" >> .git_fetch
        echo "Commit: ${{ github.sha }}" >> .git_fetch
        ./artisan awardforce:deploy-assets
      env:
        S3_REGION: ap-southeast-2
        S3_BUCKET: af4-sydney-staging
        REDIS_HOST: staging.bwk05l.0001.euc1.cache.amazonaws.com
        GIT_BRANCH: staging
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Deploy assets - UAT
      run: |
        echo "${{ github.sha }}" > .git_commit_nr
        echo "Build: ${{ github.run_number }}" > .git_fetch
        echo "Job: af4" >> .git_fetch
        echo "Branch: ${GIT_BRANCH}" >> .git_fetch
        echo "Commit: ${{ github.sha }}" >> .git_fetch
        ./artisan awardforce:deploy-assets
      env:
        S3_BUCKET: af4-sydney-production
        S3_REGION: ap-southeast-2
        REDIS_HOST: uat.bwk05l.0001.euc1.cache.amazonaws.com
        GIT_BRANCH: master
      if: github.event.deployment.environment == 'uat'

    - name: Deploy assets - prod
      run: |
        echo "${{ github.sha }}" > .git_commit_nr
        echo "Build: ${{ github.run_number }}" > .git_fetch
        echo "Job: af4" >> .git_fetch
        echo "Branch: ${GIT_BRANCH}" >> .git_fetch
        echo "Commit: ${{ github.sha }}" >> .git_fetch
        ./artisan awardforce:deploy-assets
      env:
        S3_BUCKET: af4-sydney-production
        S3_REGION: ap-southeast-2
        REDIS_HOST: prod.bwk05l.0001.euc1.cache.amazonaws.com
        GIT_BRANCH: master
      if: github.event.deployment.environment == 'production'

    - name: Deploy assets - pentest
      run: |
        echo "${{ github.sha }}" > .git_commit_nr
        echo "Build: ${{ github.run_number }}" > .git_fetch
        echo "Job: af4" >> .git_fetch
        echo "Branch: ${GIT_BRANCH}" >> .git_fetch
        echo "Commit: ${{ github.sha }}" >> .git_fetch
        ./artisan awardforce:deploy-assets
      env:
        S3_BUCKET: af4-sydney-staging
        S3_REGION: ap-southeast-2
        REDIS_HOST: pentest.bwk05l.0001.euc1.cache.amazonaws.com
        GIT_BRANCH: staging
      if: github.event.deployment.environment == 'pt'

    - name: Deploy assets - php8
      run: |
        echo "${{ github.sha }}" > .git_commit_nr
        echo "Build: ${{ github.run_number }}" > .git_fetch
        echo "Job: af4" >> .git_fetch
        echo "Branch: ${GIT_BRANCH}" >> .git_fetch
        echo "Commit: ${{ github.sha }}" >> .git_fetch
        ./artisan awardforce:deploy-assets
      env:
        S3_BUCKET: af4-sydney-stage2
        S3_REGION: ap-southeast-2
        REDIS_HOST: staging.bwk05l.0001.euc1.cache.amazonaws.com
        GIT_BRANCH: staging
      if: github.event.deployment.environment == 'stage2'

    - name: Deploy assets - adhoc
      run: |
        echo "${{ github.sha }}" > .git_commit_nr
        echo "Build: ${{ github.run_number }}" > .git_fetch
        echo "Job: af4" >> .git_fetch
        echo "Branch: ${GIT_BRANCH}" >> .git_fetch
        echo "Commit: ${{ github.sha }}" >> .git_fetch
        ./artisan awardforce:deploy-assets
      env:
        S3_BUCKET: af4-${{ github.event.deployment.payload.adhoc }}
        S3_REGION: eu-central-1
        GIT_BRANCH: staging
      if: github.event.deployment.environment == 'adhoc'

    - name: Copy over code - staging
      run: |
        mkdir code
        tar --exclude=./storage/framework/views --exclude=.git --exclude=./storage/logs --exclude=./storage/app/exports --exclude=./bootstrap/cache --exclude=./code -c --use-compress-program=pigz -f ./code/theforce_staging_${{ github.sha }}_app_data.tgz .
        md5sum ./code/theforce_staging_${{ github.sha }}_app_data.tgz > ./code/theforce_staging_${{ github.sha }}_app_data.tgz.md5
        aws s3 cp ./code/theforce_staging_${{ github.sha }}_app_data.tgz s3://eu-af4-app-data/theforce_staging_${{ github.sha }}_app_data.tgz
        aws s3 cp ./code/theforce_staging_${{ github.sha }}_app_data.tgz.md5 s3://eu-af4-app-data/theforce_staging_${{ github.sha }}_app_data.tgz.md5
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Copy over code - UAT
      run: |
        mkdir code
        tar --exclude=./storage/framework/views --exclude=.git --exclude=./storage/logs --exclude=./storage/app/exports --exclude=./bootstrap/cache --exclude=./code -c --use-compress-program=pigz -f ./code/theforce_uat_${{ github.sha }}_app_data.tgz .
        md5sum ./code/theforce_uat_${{ github.sha }}_app_data.tgz > ./code/theforce_uat_${{ github.sha }}_app_data.tgz.md5
        aws s3 cp ./code/theforce_uat_${{ github.sha }}_app_data.tgz s3://eu-af4-app-data/theforce_uat_${{ github.sha }}_app_data.tgz
        aws s3 cp ./code/theforce_uat_${{ github.sha }}_app_data.tgz.md5 s3://eu-af4-app-data/theforce_uat_${{ github.sha }}_app_data.tgz.md5
      if: github.event.deployment.environment == 'uat'

    - name: Copy over code - prod
      run: |
        mkdir code
        tar --exclude=./storage/framework/views --exclude=.git --exclude=./storage/logs --exclude=./storage/app/exports --exclude=./bootstrap/cache --exclude=./code -c --use-compress-program=pigz -f ./code/theforce_prod_${{ github.sha }}_app_data.tgz .
        md5sum ./code/theforce_prod_${{ github.sha }}_app_data.tgz > ./code/theforce_prod_${{ github.sha }}_app_data.tgz.md5
        aws s3 cp ./code/theforce_prod_${{ github.sha }}_app_data.tgz s3://eu-af4-app-data/theforce_prod_${{ github.sha }}_app_data.tgz
        aws s3 cp ./code/theforce_prod_${{ github.sha }}_app_data.tgz.md5 s3://eu-af4-app-data/theforce_prod_${{ github.sha }}_app_data.tgz.md5
      if: github.event.deployment.environment == 'production'

    - name: Copy over code - pentest
      run: |
        mkdir code
        tar --exclude=./storage/framework/views --exclude=.git --exclude=./storage/logs --exclude=./storage/app/exports --exclude=./bootstrap/cache --exclude=./code -c --use-compress-program=pigz -f ./code/theforce_pentest_${{ github.sha }}_app_data.tgz .
        md5sum ./code/theforce_pentest_${{ github.sha }}_app_data.tgz > ./code/theforce_pentest_${{ github.sha }}_app_data.tgz.md5
        aws s3 cp ./code/theforce_pentest_${{ github.sha }}_app_data.tgz s3://eu-af4-app-data/theforce_pentest_${{ github.sha }}_app_data.tgz
        aws s3 cp ./code/theforce_pentest_${{ github.sha }}_app_data.tgz.md5 s3://eu-af4-app-data/theforce_pentest_${{ github.sha }}_app_data.tgz.md5
      if: github.event.deployment.environment == 'pt'

    - name: Copy over code - php8
      run: |
        mkdir code
        tar --exclude=./storage/framework/views --exclude=.git --exclude=./storage/logs --exclude=./storage/app/exports --exclude=./bootstrap/cache --exclude=./code -c --use-compress-program=pigz -f ./code/theforce_php8_${{ github.sha }}_app_data.tgz .
        md5sum ./code/theforce_php8_${{ github.sha }}_app_data.tgz > ./code/theforce_php8_${{ github.sha }}_app_data.tgz.md5
        aws s3 cp ./code/theforce_php8_${{ github.sha }}_app_data.tgz s3://eu-af4-app-data/theforce_php8_${{ github.sha }}_app_data.tgz
        aws s3 cp ./code/theforce_php8_${{ github.sha }}_app_data.tgz.md5 s3://eu-af4-app-data/theforce_php8_${{ github.sha }}_app_data.tgz.md5
      if: github.event.deployment.environment == 'stage2'

    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: eu-central-1

    - name: Login to Amazon ECR
      id: login-ecr
      uses: aws-actions/amazon-ecr-login@v2

    - name: Build and upload docker image
      run: |
        docker build -t 481530657064.dkr.ecr.eu-central-1.amazonaws.com/app:${{ github.sha }}-${{ github.run_id }} .
        docker push 481530657064.dkr.ecr.eu-central-1.amazonaws.com/app:${{ github.sha }}-${{ github.run_id }}
    - name: Build and upload docker image
      run: |
        docker build -t 481530657064.dkr.ecr.eu-central-1.amazonaws.com/app:${{ github.sha }}-${{ github.run_id }}-nginx -f Dockerfile-nginx .
        docker push 481530657064.dkr.ecr.eu-central-1.amazonaws.com/app:${{ github.sha }}-${{ github.run_id }}-nginx
  deploy-CA:
    container:
      image: ghcr.io/tectonic/github-deploy:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    name: Deploy-CA
    runs-on: ubuntu-24.04
    needs: [jsunittest, phpunittest, phpothertest, behattest, upload]
    if: success()
    env:
      XDG_DATA_HOME: /root/.local/share
      KUBECONFIG: /root/kubeconfig

    steps:
    - name: Configure AWS credentials
      uses: aws-actions/configure-aws-credentials@v4
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: eu-central-1
    - name: work around permission issue
      run: git config --global --add safe.directory /__w/the-force/the-force
    - name: Checkout
      uses: actions/checkout@v4
      with:
        path: main

    - name: Checkout private tools
      uses: actions/checkout@v4
      with:
        repository: tectonic/infrastructure-helm
        token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
        path: infrastructure-helm
        fetch-depth: 0

    - name: Replace commit hash - staging - eu
      run: yq w -i infrastructure-helm/dev/values/app-staging.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Replace commit hash - staging - australia
      run: yq w -i infrastructure-helm/prod-australia/values/app-staging-au.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Replace commit hash - staging - us
      run: yq w -i infrastructure-helm/prod-us/values/app-staging-us.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Replace commit hash - staging - ca
      run: yq w -i infrastructure-helm/prod-ca/values/app-staging-ca.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Replace commit hash - staging - hk
      run: yq w -i infrastructure-helm/prod-hk/values/app-staging-hk.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Replace commit hash - UAT - eu
      run: yq w -i infrastructure-helm/dev/values/app-uat.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'uat'

    - name: Replace commit hash - UAT - au
      run: yq w -i infrastructure-helm/prod-australia/values/app-uat-au.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'uat'

    - name: Replace commit hash - UAT - us
      run: yq w -i infrastructure-helm/prod-us/values/app-uat-us.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'uat'

    - name: Replace commit hash - UAT - ca
      run: yq w -i infrastructure-helm/prod-ca/values/app-uat-ca.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'uat'

    - name: Replace commit hash - UAT - hk
      run: yq w -i infrastructure-helm/prod-hk/values/app-uat-hk.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'uat'

    - name: Run helm command - prod - eu
      run: yq w -i infrastructure-helm/prod/values/app-prod.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_eu == 'yes'

    - name: Run helm command - prod - australia
      run: yq w -i infrastructure-helm/prod-australia/values/app-prod-australia.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_australia == 'yes'

    - name: Run helm command - prod - us
      run: yq w -i infrastructure-helm/prod-us/values/app-prod-us.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_us == 'yes'

    - name: Run helm command - prod - ca
      run: yq w -i infrastructure-helm/prod-ca/values/app-prod-ca.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_ca == 'yes'

    - name: Run helm command - prod - hk
      run: yq w -i infrastructure-helm/prod-hk/values/app-prod-hk.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_hk == 'yes'

    - name: Replace commit hash - pentest
      run: yq w -i infrastructure-helm/dev/values/app-pentest.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'pt'

    - name: Replace commit hash - stage2 - eu
      run: yq w -i infrastructure-helm/dev/values/app-stage2.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'stage2'

    - name: Replace commit hash - stage2 - australia
      run: yq w -i infrastructure-helm/prod-australia/values/app-stage2-au.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'stage2'

    - name: Replace commit hash - stage2 - us
      run: yq w -i infrastructure-helm/prod-us/values/app-stage2-us.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'stage2'

    - name: Replace commit hash - adhoc - eu
      run: yq w -i infrastructure-helm/dev/values/app-ad-hoc.yaml theForce.commit ${{ github.sha }}-${{ github.run_id }}
      if: github.event.deployment.environment == 'adhoc'

    - name: Commit files
      run: |
        cd infrastructure-helm
        ls -la
        git config --global user.email "<EMAIL>"
        git config --global user.name "GitHub Action"
        git commit -m "Automatic theForce deploy" -a --allow-empty
    - name: Push changes
      uses: ad-m/github-push-action@master
      with:
        github_token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
        directory: infrastructure-helm
        repository: tectonic/infrastructure-helm

    - name: Run helm command - staging - ca
      run: |
        echo ${XDG_DATA_HOME}
        yq w -i infrastructure-helm/prod-ca/values/app-staging-ca.yaml theForce.migrations.enabled true
        helmfile -f infrastructure-helm/prod-ca/app-staging-ca.yaml apply
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

    - name: Run helm command - UAT - ca
      run: |
        yq w -i infrastructure-helm/prod-ca/values/app-uat-ca.yaml theForce.migrations.enabled true
        helmfile -f infrastructure-helm/prod-ca/app-uat-ca.yaml apply
      if: github.event.deployment.environment == 'uat'

    - name: Run helm command - prod - ca
      run: |
        yq w -i infrastructure-helm/prod-ca/values/app-prod-ca.yaml theForce.migrations.enabled true
        helmfile -f infrastructure-helm/prod-ca/app-prod-ca.yaml apply
      if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_ca == 'yes'

  deploy-HK:
    container:
      image: ghcr.io/tectonic/github-deploy:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    name: Deploy HK
    runs-on: ubuntu-24.04
    needs: deploy-CA
    if: success()
    env:
      XDG_DATA_HOME: /root/.local/share
      KUBECONFIG: /root/kubeconfig

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1
      - name: work around permission issue
        run: git config --global --add safe.directory /__w/the-force/the-force
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: main

      - name: Checkout private tools
        uses: actions/checkout@v4
        with:
          repository: tectonic/infrastructure-helm
          token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
          path: infrastructure-helm
          fetch-depth: 0

      - name: Run helm command - staging - hk
        run: |
          echo ${XDG_DATA_HOME}
          yq w -i infrastructure-helm/prod-hk/values/app-staging-hk.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-hk/app-staging-hk.yaml apply
        if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

      - name: Run helm command - UAT - hk
        run: |
          yq w -i infrastructure-helm/prod-hk/values/app-uat-hk.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-hk/app-uat-hk.yaml apply
        if: github.event.deployment.environment == 'uat'

      - name: Run helm command - prod - hk
        run: |
          yq w -i infrastructure-helm/prod-hk/values/app-prod-hk.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-hk/app-prod-hk.yaml apply
        if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_hk == 'yes'

  deploy-US:
    container:
      image: ghcr.io/tectonic/github-deploy:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    name: Deploy US
    runs-on: ubuntu-24.04
    needs: deploy-CA
    if: success()
    env:
      XDG_DATA_HOME: /root/.local/share
      KUBECONFIG: /root/kubeconfig

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1
      - name: work around permission issue
        run: git config --global --add safe.directory /__w/the-force/the-force
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: main

      - name: Checkout private tools
        uses: actions/checkout@v4
        with:
          repository: tectonic/infrastructure-helm
          token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
          path: infrastructure-helm
          fetch-depth: 0

      - name: Run helm command - staging - us
        run: |
          echo ${XDG_DATA_HOME}
          yq w -i infrastructure-helm/prod-us/values/app-staging-us.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-us/app-staging-us.yaml apply
        if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

      - name: Run helm command - UAT - us
        run: |
          yq w -i infrastructure-helm/prod-us/values/app-uat-us.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-us/app-uat-us.yaml apply
        if: github.event.deployment.environment == 'uat'

      - name: Run helm command - prod - us
        run: |
          yq w -i infrastructure-helm/prod-us/values/app-prod-us.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-us/app-prod-us.yaml apply
        if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_us == 'yes'

      - name: Run helm command - stage2 - us
        run: |
          yq w -i infrastructure-helm/prod-us/values/app-stage2-us.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-us/app-stage2-us.yaml apply
        if: github.event.deployment.environment == 'stage2'

  deploy-AU:
    container:
      image: ghcr.io/tectonic/github-deploy:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    name: Deploy AU
    runs-on: ubuntu-24.04
    needs: deploy-CA
    if: success()
    env:
      XDG_DATA_HOME: /root/.local/share
      KUBECONFIG: /root/kubeconfig

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1
      - name: work around permission issue
        run: git config --global --add safe.directory /__w/the-force/the-force
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: main

      - name: Checkout private tools
        uses: actions/checkout@v4
        with:
          repository: tectonic/infrastructure-helm
          token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
          path: infrastructure-helm
          fetch-depth: 0

      - name: Run helm command - staging - australia
        run: |
          echo ${XDG_DATA_HOME}
          yq w -i infrastructure-helm/prod-australia/values/app-staging-au.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-australia/app-staging-au.yaml apply
        if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

      - name: Run helm command - UAT - australia
        run: |
          yq w -i infrastructure-helm/prod-australia/values/app-uat-au.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-australia/app-uat-au.yaml apply
        if: github.event.deployment.environment == 'uat'

      - name: Run helm command - prod - australia
        run: |
          yq w -i infrastructure-helm/prod-australia/values/app-prod-australia.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-australia/app-prod-australia.yaml apply
        if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_australia == 'yes'

      - name: Run helm command - stage2 - australia
        run: |
          yq w -i infrastructure-helm/prod-australia/values/app-stage2-au.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod-australia/app-stage2-au.yaml apply
        if: github.event.deployment.environment == 'stage2'

  deploy-EU:
    container:
      image: ghcr.io/tectonic/github-deploy:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
    name: Deploy EU
    runs-on: ubuntu-24.04
    needs: deploy-CA
    if: success()
    env:
      XDG_DATA_HOME: /root/.local/share
      KUBECONFIG: /root/kubeconfig

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: eu-central-1
      - name: work around permission issue
        run: git config --global --add safe.directory /__w/the-force/the-force
      - name: Checkout
        uses: actions/checkout@v4
        with:
          path: main

      - name: Checkout private tools
        uses: actions/checkout@v4
        with:
          repository: tectonic/infrastructure-helm
          token: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
          path: infrastructure-helm
          fetch-depth: 0

      - name: Run helm command - staging - main
        run: |
          echo ${XDG_DATA_HOME}
          yq w -i infrastructure-helm/dev/values/app-staging.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/dev/app-staging.yaml apply
        if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'

      - name: Run helm command - UAT - main
        run: |
          yq w -i infrastructure-helm/dev/values/app-uat.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/dev/app-uat.yaml apply
        if: github.event.deployment.environment == 'uat'

      - name: Run helm command - prod - main
        run: |
          yq w -i infrastructure-helm/prod/values/app-prod.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/prod/app-prod.yaml apply
        if: github.event.deployment.environment == 'production' && github.event.deployment.payload.region_eu == 'yes'

      - name: Run helm command - pentest - main
        run: |
          yq w -i infrastructure-helm/dev/values/app-pentest.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/dev/app-pentest.yaml apply
        if: github.event.deployment.environment == 'pt'

      - name: Run helm command - stage2 - main
        run: |
          yq w -i infrastructure-helm/dev/values/app-stage2.yaml theForce.migrations.enabled true
          helmfile -f infrastructure-helm/dev/app-stage2.yaml apply
        if: github.event.deployment.environment == 'stage2'

      - name: Run helm command - adhoc - eu
        run: |
          yq w -i infrastructure-helm/dev/values/app-ad-hoc.yaml theForce.migrations.enabled true
          bash infrastructure-helm/scripts/adhoc_replace_placeholders.sh ${ENV_NAME}
          export ad_hoc_env_name=$ENV_NAME
          export ad_hoc_redis=$ENV_NAME.bwk05l.ng.0001.euc1.cache.amazonaws.com
          helmfile -f infrastructure-helm/dev/app-ad-hoc.yaml apply
        env:
          ENV_NAME: ${{ github.event.deployment.payload.adhoc }}
        if: github.event.deployment.environment == 'adhoc'

  slack-success:
    needs: [deploy-CA, deploy-US, deploy-AU, deploy-EU, deploy-HK]
    runs-on: ubuntu-24.04
    if: success()
    container:
      image: ghcr.io/tectonic/github-slack:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Set unittest status - success
      uses: ./.github/set-status
      with:
        status: success
        token: ${{ github.token }}
        sha: ${{ github.sha }}

    - name: Update NR - staging
      run: python /slack-notify/nr.py ${{ github.run_number }} ${APP_ID} ${{ secrets.NR_API_KEY }}
      if: github.ref == 'refs/heads/staging' && github.event.deployment.environment != 'uat'
      env:
        APP_ID: 8641929

    - name: Update NR - UAT
      run: python /slack-notify/nr.py ${{ github.run_number }} ${APP_ID} ${{ secrets.NR_API_KEY }}
      if: github.event.deployment.environment == 'uat'
      env:
        APP_ID: 18582886

    - name: Update NR - prod
      run: python /slack-notify/nr.py ${{ github.run_number }} ${APP_ID} ${{ secrets.NR_API_KEY }}
      if: github.event.deployment.environment == 'production'
      env:
        APP_ID: 8761120

    - name: Set deployment status to success
      uses: chrnorm/deployment-status@v2
      with:
        state: 'success'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        token: '${{ github.token }}'
        deployment-id: ${{ github.event.deployment.id }}
      if: github.event.deployment.payload.deployment == 'yes'

    - uses: actions/download-artifact@v4
      with:
        path: artifact
        name: deployid
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Read release URL
      id: get_release_url
      run: echo "id=$(cat artifact/artifact)" >> $GITHUB_OUTPUT
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Set deployment status to success - auto/staging
      uses: chrnorm/deployment-status@v2
      with:
        state: 'success'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        deployment-id: ${{ steps.get_release_url.outputs.id }}
        token: '${{ github.token }}'
      if: github.ref == 'refs/heads/staging' && github.event.deployment.payload.deployment != 'yes'

    - name: Run slack - auto/staging
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} staging staging ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} good
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Run slack
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} ${{ github.event.deployment.environment }} ${{ github.ref }} ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} good
      if: github.event.deployment.payload.deployment == 'yes'

    - name: Slack to general channel - prod
      run: python /slack-notify/slack-general.py ${{ secrets.SLACK_WEBHOOK_URL_GENERAL }} checked
      if: github.event.deployment.environment == 'production'

  slack-cancelled:
    needs: [deploy-CA, deploy-US, deploy-AU, deploy-EU, deploy-HK]
    runs-on: ubuntu-24.04
    if: cancelled()
    container:
      image: ghcr.io/tectonic/github-slack:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
    - name: Set deployment status to failure
      uses: chrnorm/deployment-status@v2
      with:
        state: 'failure'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        token: '${{ github.token }}'
        deployment-id: ${{ github.event.deployment.id }}
      if: github.event.deployment.payload.deployment == 'yes'

    - uses: actions/download-artifact@v4
      with:
        path: artifact
        name: deployid
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Read release URL
      id: get_release_url
      run: echo "id=$(cat artifact/artifact)" >> $GITHUB_OUTPUT
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Set deployment status to failure - auto/staging
      uses: chrnorm/deployment-status@v2
      with:
        state: 'failure'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        deployment-id: ${{ steps.get_release_url.outputs.id }}
        token: '${{ github.token }}'
      if: github.ref == 'refs/heads/staging' && github.event.deployment.payload.deployment != 'yes'

    - name: Run slack - auto/staging
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} staging staging ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} warning
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Run slack
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} ${{ github.event.deployment.environment }} ${{ github.ref }} ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} warning
      if: github.event.deployment.payload.deployment == 'yes'

  slack-failure-unittest:
    needs: [jsunittest, phpunittest, phpothertest, behattest]
    runs-on: ubuntu-24.04
    if: failure()
    container:
      image: ghcr.io/tectonic/github-slack:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Set unittest status - success
      uses: ./.github/set-status
      with:
        status: success
        token: ${{ github.token }}
        sha: ${{ github.sha }}

    - name: Set deployment status to failure
      uses: chrnorm/deployment-status@v2
      with:
        state: 'failure'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        token: '${{ github.token }}'
        deployment-id: ${{ github.event.deployment.id }}
      if: github.event.deployment.payload.deployment == 'yes'

    - uses: actions/download-artifact@v4
      with:
        path: artifact
        name: deployid
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Read release URL
      id: get_release_url
      run: echo "id=$(cat artifact/artifact)" >> $GITHUB_OUTPUT
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Set deployment status to failure - auto/staging
      uses: chrnorm/deployment-status@v2
      with:
        state: 'failure'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        token: '${{ github.token }}'
        deployment-id: ${{ steps.get_release_url.outputs.id }}
      env:
        GITHUB_TOKEN: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
      if: github.ref == 'refs/heads/staging' && github.event.deployment.payload.deployment != 'yes'

    - name: Run slack - auto/staging
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} staging staging ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} danger Tests
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Run slack
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} ${{ github.event.deployment.environment }} ${{ github.ref }} ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} danger Tests
      if: github.event.deployment.payload.deployment == 'yes'

  slack-failure-upload:
    needs: upload
    runs-on: ubuntu-24.04
    if: failure()
    container:
      image: ghcr.io/tectonic/github-slack:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
    - name: Set deployment status to failure
      uses: chrnorm/deployment-status@v2
      with:
        state: 'failure'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        token: '${{ github.token }}'
        deployment-id: ${{ github.event.deployment.id }}
      env:
        GITHUB_TOKEN: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}
      if: github.event.deployment.payload.deployment == 'yes'

    - uses: actions/download-artifact@v4
      with:
        path: artifact
        name: deployid
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Read release URL
      id: get_release_url
      run: echo "id=$(cat artifact/artifact)" >> $GITHUB_OUTPUT
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Set deployment status to failure - auto/staging
      uses: chrnorm/deployment-status@v2
      with:
        state: 'failure'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        deployment-id: ${{ steps.get_release_url.outputs.id }}
        token: '${{ github.token }}'
      if: github.ref == 'refs/heads/staging' && github.event.deployment.payload.deployment != 'yes'

    - name: Run slack - auto/staging
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} staging staging ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} danger Upload
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Run slack
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} ${{ github.event.deployment.environment }} ${{ github.ref }}  danger Upload
      if: github.event.deployment.payload.deployment == 'yes'

  slack-failure-deploy:
    needs: [deploy-CA, deploy-US, deploy-AU, deploy-EU, deploy-HK]
    runs-on: ubuntu-24.04
    if: failure()
    container:
      image: ghcr.io/tectonic/github-slack:latest
      credentials:
        username: techdeploy
        password: ${{ secrets.GIT_TECHDEPLOY_TOKEN }}

    steps:
    - name: Set deployment status to failure
      uses: chrnorm/deployment-status@v2
      with:
        state: 'failure'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        token: '${{ github.token }}'
        deployment-id: ${{ github.event.deployment.id }}
      if: github.event.deployment.payload.deployment == 'yes'

    - uses: actions/download-artifact@v4
      with:
        path: artifact
        name: deployid
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Read release URL
      id: get_release_url
      run: echo "id=$(cat artifact/artifact)" >> $GITHUB_OUTPUT
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Set deployment status to failure - auto/staging
      uses: chrnorm/deployment-status@v2
      with:
        state: 'failure'
        log-url: "https://github.com/tectonic/the-force/commit/${{ github.sha }}/checks"
        deployment-id: ${{ steps.get_release_url.outputs.id }}
        token: '${{ github.token }}'
      if: github.ref == 'refs/heads/staging' && github.event.deployment.payload.deployment != 'yes'

    - name: Run slack - auto/staging
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} staging staging ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} danger Deploy
      if: github.event.deployment.payload.deployment != 'yes'

    - name: Run slack
      run: python /slack-notify/slack.py the-force ${{ github.run_number }} ${{ github.sha }} ${{ github.event.deployment.environment }} ${{ github.ref }} ${{ secrets.SLACK_WEBHOOK_URL_ENG_DEPLOYMENTS }} danger Deploy
      if: github.event.deployment.payload.deployment == 'yes'
